// Onboarding Type Definitions for Quildora
// Shared types for frontend and backend onboarding flow

export type OnboardingStep =
  | "business_info"
  | "admin_account"
  | "warehouse_setup"
  | "team_setup"
  | "completion";

export interface BusinessInfo {
  companyName: string;
  industry?: string;
  companySize?: string;
  primaryUseCase?: string;
}

export interface AdminAccount {
  fullName: string;
  email: string;
  password: string;
  phoneNumber?: string;
}

export interface WarehouseSetup {
  warehouseName: string;
  address?: string;
  warehouseType?: string;
  expectedVolume?: string;
}

export interface TeamSetup {
  inviteEmails: string[];
  defaultRole: "WAREHOUSE_MEMBER" | "WAREHOUSE_MANAGER";
}

export interface OnboardingState {
  step: OnboardingStep;
  businessInfo?: BusinessInfo;
  adminAccount?: AdminAccount;
  warehouseSetup?: WarehouseSetup;
  teamSetup?: TeamSetup;
  sessionId?: string;
}

// API Request/Response Types
export interface StartBusinessOnboardingRequest {
  businessInfo: BusinessInfo;
}

export interface StartBusinessOnboardingResponse {
  sessionId: string;
  nextStep: OnboardingStep;
}

export interface CreateAdminAccountRequest {
  sessionId: string;
  adminAccount: AdminAccount;
}

export interface CreateAdminAccountResponse {
  userId: string;
  supabaseUser: any; // Supabase User type
  nextStep: OnboardingStep;
}

export interface SetupWarehouseRequest {
  sessionId: string;
  warehouseSetup: WarehouseSetup;
}

export interface SetupWarehouseResponse {
  warehouseId: string;
  tenantId: string;
  nextStep: OnboardingStep;
}

export interface CompleteOnboardingRequest {
  sessionId: string;
  teamSetup?: TeamSetup;
}

export interface CompleteOnboardingResponse {
  accessToken: string;
  user: any; // User type from existing types
  warehouse: any; // Warehouse type from existing types
  tenant: any; // Tenant type from existing types
  invitations?: CreateInvitationResponse[];
}

// Invitation Types
export interface CreateInvitationRequest {
  email: string;
  role: "WAREHOUSE_MEMBER" | "WAREHOUSE_MANAGER";
  warehouseIds: string[];
}

export interface CreateInvitationResponse {
  invitationId: string;
  invitationCode: string;
  expiresAt: string;
}

export interface ValidateInvitationResponse {
  valid: boolean;
  tenantInfo?: {
    id: string;
    name: string;
  };
  role?: string;
  warehouseIds?: string[];
  expiresAt?: string;
}

export interface AcceptInvitationRequest {
  invitationCode: string;
  userAccount: AdminAccount;
}

export interface AcceptInvitationResponse {
  accessToken: string;
  user: any; // User type
  warehouses: any[]; // Warehouse array type
  tenant: any; // Tenant type
}

// Onboarding Session Types
export interface OnboardingSession {
  id: string;
  userId?: string;
  tenantId?: string;
  currentStep: OnboardingStep;
  data: OnboardingState;
  completedAt?: Date;
  expiresAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Context Types for Frontend
export interface OnboardingContextType {
  currentStep: OnboardingStep;
  sessionData: OnboardingState;
  isLoading: boolean;
  error: string | null;

  // Error recovery
  recoveryAvailable?: boolean;
  lastError?: any; // OnboardingError type from error recovery
  retryLastAction?: () => Promise<void>;
  clearError?: () => void;

  // Actions
  updateBusinessInfo: (info: BusinessInfo) => Promise<void>;
  createAdminAccount: (account: AdminAccount) => Promise<void>;
  setupWarehouse: (warehouse: WarehouseSetup) => Promise<void>;
  completeOnboarding: (team?: TeamSetup) => Promise<CompleteOnboardingResponse>;

  // Navigation
  goToStep: (step: OnboardingStep) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;

  // State management
  resetOnboarding: () => void;
  saveProgress: () => void;
  loadProgress: () => void;
}

// Progress tracking
export interface OnboardingProgress {
  totalSteps: number;
  currentStep: number;
  completedSteps: OnboardingStep[];
  estimatedTimeRemaining: number;
  progressPercentage: number;
  stepTitles: string[];
  stepDescriptions: string[];
}

// Error types
export interface OnboardingError {
  code: string;
  message: string;
  field?: string;
  step?: OnboardingStep;
}

// Validation schemas (for use with Zod)
export interface OnboardingValidationSchemas {
  businessInfo: any; // Zod schema
  adminAccount: any; // Zod schema
  warehouseSetup: any; // Zod schema
  teamSetup: any; // Zod schema
}

// Route configuration
export interface OnboardingRouteConfig {
  path: string;
  step: OnboardingStep;
  title: string;
  description: string;
  allowedFromSteps: OnboardingStep[];
}

export const ONBOARDING_ROUTES: OnboardingRouteConfig[] = [
  {
    path: "/auth/signup/business/info",
    step: "business_info",
    title: "Business Information",
    description: "Tell us about your company",
    allowedFromSteps: [],
  },
  {
    path: "/auth/signup/business/account",
    step: "admin_account",
    title: "Admin Account",
    description: "Create your administrator account",
    allowedFromSteps: ["business_info"],
  },
  {
    path: "/auth/signup/business/warehouse",
    step: "warehouse_setup",
    title: "Warehouse Setup",
    description: "Configure your first warehouse",
    allowedFromSteps: ["admin_account"],
  },
  {
    path: "/auth/signup/business/team",
    step: "team_setup",
    title: "Team Setup",
    description: "Invite your team members",
    allowedFromSteps: ["warehouse_setup"],
  },
  {
    path: "/auth/signup/business/complete",
    step: "completion",
    title: "Welcome to Quildora",
    description: "Your account is ready!",
    allowedFromSteps: ["team_setup"],
  },
];

// Constants
export const ONBOARDING_SESSION_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
export const INVITATION_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

export const ONBOARDING_STEP_TITLES: Record<OnboardingStep, string> = {
  business_info: "Business Information",
  admin_account: "Admin Account",
  warehouse_setup: "Warehouse Setup",
  team_setup: "Team Setup",
  completion: "Welcome to Quildora",
};

export const ONBOARDING_STEP_DESCRIPTIONS: Record<OnboardingStep, string> = {
  business_info: "Tell us about your company and industry",
  admin_account: "Create your administrator account",
  warehouse_setup: "Configure your first warehouse location",
  team_setup: "Invite team members to join your workspace",
  completion: "Your account is ready to use!",
};
