import {
  OnboardingStep,
  BusinessInfo,
  AdminAccount,
  WarehouseSetup,
  TeamSetup,
  OnboardingState,
} from "./onboarding";

export enum Role {
  TENANT_ADMIN = "TENANT_ADMIN",
  WAREHOUSE_MANAGER = "WAREHOUSE_MANAGER",
  WAREHOUSE_MEMBER = "WAREHOUSE_MEMBER",
}

export enum UserStatus {
  ACTIVE = "ACTIVE",
  INVITED = "INVITED",
  INACTIVE = "INACTIVE",
}

export interface User {
  id: string;
  name: string | null;
  email: string;
  role: Role;
  status: UserStatus;
  tenantId?: string | null;
  authUserId?: string | null;
  createdAt: Date;
  updatedAt: Date;
  warehouseUsers?: WarehouseUser[];
}

// Frontend-specific user interface
export interface FrontendAppUser {
  id: string;
  email: string;
  role: Role;
  name: string | null;
  authUserId: string | null;
  tenantId: string | null;
  createdAt: string;
  updatedAt: string;
  warehouseUsers?: { warehouseId: string; role: Role }[];
}

export interface Item {
  id: string;
  name: string;
  sku: string | null;
  description: string | null;
  unitOfMeasure?: string | null;
  defaultCost?: number | null;
  lowStockThreshold?: number | null;
  status?: string;
  createdAt: string; // Assuming ISO string format from JSON
  updatedAt: string; // Assuming ISO string format from JSON
  quantityOnHand?: number;
}

export interface WarehouseBasic {
  id: string;
  name: string;
  address?: string;
  currentUserRole?: Role;
}

// Full warehouse interface with additional details
export interface Warehouse extends WarehouseBasic {
  status: string;
  createdAt: Date;
  updatedAt: Date;
  tenantId: string;
  locationSummary?: WarehouseLocationSummary;
  palletSummary?: WarehousePalletSummary;
  userAccess?: WarehouseUserAccess[];
}

// Warehouse user access information
export interface WarehouseUserAccess {
  userId: string;
  userName: string;
  userEmail: string;
  role: Role;
}

// Warehouse location summary statistics
export interface WarehouseLocationSummary {
  totalLocations: number;
  activeLocations: number;
  occupiedLocations: number;
}

// Warehouse pallet summary statistics
export interface WarehousePalletSummary {
  totalPallets: number;
  storedPallets: number;
  receivingPallets: number;
  pickingPallets: number;
  shippingPallets: number;
}

// User-warehouse relationship
export interface WarehouseUser {
  id: string;
  userId: string;
  warehouseId: string;
  role: Role;
  assignedAt: Date;
}

export enum LocationCategory {
  Receiving = "Receiving",
  Storage = "Storage",
  Shipping = "Shipping",
  Other = "Other",
}

export interface Location {
  id: string;
  name: string;
  locationType: string;
  warehouseId: string;
  status?: string;
  createdAt: string;
  updatedAt: string;
  warehouse: WarehouseBasic;
  category?: LocationCategory;
}

export interface PalletItem {
  id?: string;
  palletId: string;
  itemId: string;
  quantity: number;
  item: Item;
}

export enum LocationType {
  AISLE = "AISLE",
  RACK = "RACK",
  SHELF = "SHELF",
  BIN = "BIN",
  ZONE = "ZONE",
  DOCK = "DOCK",
  STAGING_AREA = "STAGING_AREA",
  OFFICE = "OFFICE",
  OTHER = "OTHER",
}

export enum LocationStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  MAINTENANCE = "MAINTENANCE",
}

export interface Pallet {
  id: string;
  label: string;
  status: string;
  locationId: string | null;
  location?: Location;
  description?: string | null;
  barcode?: string | null;
  palletItems?: PalletItem[];
  dateCreated?: string;
  lastMovedDate?: string;
  shipToDestination?: string | null;
  destinationCode?: string | null;
}

export interface PalletItemWithItem extends PalletItem {
  item: Item;
}

export interface PalletWithPalletItems extends Pallet {
  palletItems: PalletItemWithItem[];
}

export interface ShipmentSummaryResponseDto {
  shipmentId: string;
  referenceNumber: string;
  palletsByDestination: Record<string, PalletWithPalletItems[]>;
}

// Warehouse context interfaces
export interface WarehouseBasicInfo {
  id: string;
  name: string;
  address?: string;
}

export interface WarehouseContext {
  warehouseId?: string;
  isWarehouseSpecific: boolean;
  source: "params" | "query" | "headers" | "body" | "none";
}

// Add other shared types and interfaces here as needed

export interface PlacardWarehouseDto {
  id: string;
  name: string;
}

export interface PlacardLocationDto {
  name: string;
  warehouse?: PlacardWarehouseDto;
}

export interface PlacardContentDto {
  itemName: string;
  quantity: number;
  unit: string;
}

export interface PlacardDataResponseDto {
  palletId: string;
  barcode: string;
  shipToDestination: string;
  currentLocation: PlacardLocationDto;
  description: string;
  contents: PlacardContentDto[];
}

export interface PurchaseOrder {
  id: string;
  poNumber: string;
  status: string;
  supplier: string | null;
}

export interface Shipment {
  id: string;
  referenceNumber: string;
  status: string;
  purchaseOrderId: string;
  purchaseOrder?: PurchaseOrder;
  pallets?: Pallet[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ShipmentListItem {
  id: string;
  referenceNumber: string | null;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  poNumber: string;
  palletCount: number;
  supplier: string | null;
}

export interface AuditLogUser {
  id: string;
  name: string | null;
  email: string;
}

export interface AuditLog {
  id: string;
  timestamp: Date;
  userId: string | null;
  userEmail: string | null;
  action: string;
  entity: string;
  entityId: string;
  details: Record<string, any> | null;
  tenantId: string;
  warehouseId?: string | null;
  user: AuditLogUser | null;
}

export interface ShipmentListResponse {
  data: ShipmentListItem[];
  count: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Generic pagination response interface
export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  page?: number;
  limit?: number;
  totalPages?: number;
}

// Warehouse list response
export interface WarehouseListResponse extends PaginatedResponse<Warehouse> {}

// API filter interfaces
export interface PalletFilters {
  status?: string;
  locationId?: string;
  search?: string;
  warehouseId?: string;
  shipToDestination?: string;
  destinationCode?: string;
}

// Destination-related types for the destination codes feature
export interface Destination {
  name: string;
  code?: string | null;
  displayName: string;
}

export interface DestinationResponse {
  name: string;
  code?: string | null;
  displayName: string;
}

export interface LocationFilters {
  category?: LocationCategory;
  type?: string;
  search?: string;
  warehouseId?: string;
}

export interface ShipmentFilters {
  status?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  warehouseId?: string;
}

export interface PurchaseOrderFilters {
  status?: string;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  warehouseId?: string;
}

export interface AuditLogFilters {
  entityType?: string;
  entityId?: string;
  userId?: string;
  action?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
  warehouseId?: string;
}

// API Hook return types
export interface UseQueryResult<T> {
  data: T | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
}

export interface UseMutationResult<T, V> {
  mutate: (variables: V) => void;
  mutateAsync: (variables: V) => Promise<T>;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  isSuccess: boolean;
  data: T | undefined;
}

// Warehouse context provider types
export interface WarehouseContextType {
  currentWarehouse: WarehouseBasic | null;
  accessibleWarehouses: WarehouseBasic[];
  isLoadingWarehouses: boolean;
  isChangingWarehouse: boolean;
  warehouseError: string | null;
  loadingProgress: number;
  retryCount: number;
  setCurrentWarehouse: (warehouse: WarehouseBasic | null) => Promise<void>;
  refreshWarehouses: () => Promise<void>;
  retryLoadWarehouses: () => Promise<void>;
  hasWarehouseAccess: (warehouseId: string) => boolean;
  getUserWarehouseRole: (warehouseId: string) => Role | null;
  canManageWarehouse: (warehouseId: string) => boolean;
}

// Authentication context types
export interface AuthContextType {
  supabaseSession: any | null;
  supabaseUser: any | null;
  appUser: FrontendAppUser | null;
  appToken: string | null;
  isLoading: boolean;
  onboardingStatus: string | null;

  // New onboarding properties
  isOnboarding: boolean;
  onboardingStep: OnboardingStep | null;
  onboardingData: OnboardingState | null;

  // Enhanced methods
  startBusinessOnboarding: (businessInfo: BusinessInfo) => Promise<void>;
  joinTenantWithInvitation: (invitationCode: string) => Promise<void>;
  completeOnboarding: () => Promise<void>;

  // Existing methods
  loginWithSupabaseToken: (supabaseAccessToken: string) => Promise<void>;
  logout: () => Promise<void>;
}

export * from './onboarding';
