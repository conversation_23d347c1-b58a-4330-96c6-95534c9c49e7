"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationStatus = exports.LocationType = exports.LocationCategory = exports.UserStatus = exports.Role = void 0;
var Role;
(function (Role) {
    Role["TENANT_ADMIN"] = "TENANT_ADMIN";
    Role["WAREHOUSE_MANAGER"] = "WAREHOUSE_MANAGER";
    Role["WAREHOUSE_MEMBER"] = "WAREHOUSE_MEMBER";
})(Role || (exports.Role = Role = {}));
var UserStatus;
(function (UserStatus) {
    UserStatus["ACTIVE"] = "ACTIVE";
    UserStatus["INVITED"] = "INVITED";
    UserStatus["INACTIVE"] = "INACTIVE";
})(UserStatus || (exports.UserStatus = UserStatus = {}));
var LocationCategory;
(function (LocationCategory) {
    LocationCategory["Receiving"] = "Receiving";
    LocationCategory["Storage"] = "Storage";
    LocationCategory["Shipping"] = "Shipping";
    LocationCategory["Other"] = "Other";
})(LocationCategory || (exports.LocationCategory = LocationCategory = {}));
var LocationType;
(function (LocationType) {
    LocationType["AISLE"] = "AISLE";
    LocationType["RACK"] = "RACK";
    LocationType["SHELF"] = "SHELF";
    LocationType["BIN"] = "BIN";
    LocationType["ZONE"] = "ZONE";
    LocationType["DOCK"] = "DOCK";
    LocationType["STAGING_AREA"] = "STAGING_AREA";
    LocationType["OFFICE"] = "OFFICE";
    LocationType["OTHER"] = "OTHER";
})(LocationType || (exports.LocationType = LocationType = {}));
var LocationStatus;
(function (LocationStatus) {
    LocationStatus["ACTIVE"] = "ACTIVE";
    LocationStatus["INACTIVE"] = "INACTIVE";
    LocationStatus["MAINTENANCE"] = "MAINTENANCE";
})(LocationStatus || (exports.LocationStatus = LocationStatus = {}));
__exportStar(require("./onboarding"), exports);
