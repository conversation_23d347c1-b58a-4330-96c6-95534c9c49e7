"use strict";
// Onboarding Type Definitions for Quildora
// Shared types for frontend and backend onboarding flow
Object.defineProperty(exports, "__esModule", { value: true });
exports.ONBOARDING_STEP_DESCRIPTIONS = exports.ONBOARDING_STEP_TITLES = exports.INVITATION_DURATION = exports.ONBOARDING_SESSION_DURATION = exports.ONBOARDING_ROUTES = void 0;
exports.ONBOARDING_ROUTES = [
    {
        path: "/auth/signup/business/info",
        step: "business_info",
        title: "Business Information",
        description: "Tell us about your company",
        allowedFromSteps: [],
    },
    {
        path: "/auth/signup/business/account",
        step: "admin_account",
        title: "Admin Account",
        description: "Create your administrator account",
        allowedFromSteps: ["business_info"],
    },
    {
        path: "/auth/signup/business/warehouse",
        step: "warehouse_setup",
        title: "Warehouse Setup",
        description: "Configure your first warehouse",
        allowedFromSteps: ["admin_account"],
    },
    {
        path: "/auth/signup/business/team",
        step: "team_setup",
        title: "Team Setup",
        description: "Invite your team members",
        allowedFromSteps: ["warehouse_setup"],
    },
    {
        path: "/auth/signup/business/complete",
        step: "completion",
        title: "Welcome to Quildora",
        description: "Your account is ready!",
        allowedFromSteps: ["team_setup"],
    },
];
// Constants
exports.ONBOARDING_SESSION_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
exports.INVITATION_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
exports.ONBOARDING_STEP_TITLES = {
    business_info: "Business Information",
    admin_account: "Admin Account",
    warehouse_setup: "Warehouse Setup",
    team_setup: "Team Setup",
    completion: "Welcome to Quildora",
};
exports.ONBOARDING_STEP_DESCRIPTIONS = {
    business_info: "Tell us about your company and industry",
    admin_account: "Create your administrator account",
    warehouse_setup: "Configure your first warehouse location",
    team_setup: "Invite team members to join your workspace",
    completion: "Your account is ready to use!",
};
