export type OnboardingStep = "business_info" | "admin_account" | "warehouse_setup" | "team_setup" | "completion";
export interface BusinessInfo {
    companyName: string;
    industry?: string;
    companySize?: string;
    primaryUseCase?: string;
}
export interface AdminAccount {
    fullName: string;
    email: string;
    password: string;
    phoneNumber?: string;
}
export interface WarehouseSetup {
    warehouseName: string;
    address?: string;
    warehouseType?: string;
    expectedVolume?: string;
}
export interface TeamSetup {
    inviteEmails: string[];
    defaultRole: "WAREHOUSE_MEMBER" | "WAREHOUSE_MANAGER";
}
export interface OnboardingState {
    step: OnboardingStep;
    businessInfo?: BusinessInfo;
    adminAccount?: AdminAccount;
    warehouseSetup?: WarehouseSetup;
    teamSetup?: TeamSetup;
    sessionId?: string;
}
export interface StartBusinessOnboardingRequest {
    businessInfo: BusinessInfo;
}
export interface StartBusinessOnboardingResponse {
    sessionId: string;
    nextStep: OnboardingStep;
}
export interface CreateAdminAccountRequest {
    sessionId: string;
    adminAccount: AdminAccount;
}
export interface CreateAdminAccountResponse {
    userId: string;
    supabaseUser: any;
    nextStep: OnboardingStep;
}
export interface SetupWarehouseRequest {
    sessionId: string;
    warehouseSetup: WarehouseSetup;
}
export interface SetupWarehouseResponse {
    warehouseId: string;
    tenantId: string;
    nextStep: OnboardingStep;
}
export interface CompleteOnboardingRequest {
    sessionId: string;
    teamSetup?: TeamSetup;
}
export interface CompleteOnboardingResponse {
    accessToken: string;
    user: any;
    warehouse: any;
    tenant: any;
    invitations?: CreateInvitationResponse[];
}
export interface CreateInvitationRequest {
    email: string;
    role: "WAREHOUSE_MEMBER" | "WAREHOUSE_MANAGER";
    warehouseIds: string[];
}
export interface CreateInvitationResponse {
    invitationId: string;
    invitationCode: string;
    expiresAt: string;
}
export interface ValidateInvitationResponse {
    valid: boolean;
    tenantInfo?: {
        id: string;
        name: string;
    };
    role?: string;
    warehouseIds?: string[];
    expiresAt?: string;
}
export interface AcceptInvitationRequest {
    invitationCode: string;
    userAccount: AdminAccount;
}
export interface AcceptInvitationResponse {
    accessToken: string;
    user: any;
    warehouses: any[];
    tenant: any;
}
export interface OnboardingSession {
    id: string;
    userId?: string;
    tenantId?: string;
    currentStep: OnboardingStep;
    data: OnboardingState;
    completedAt?: Date;
    expiresAt: Date;
    createdAt: Date;
    updatedAt: Date;
}
export interface OnboardingContextType {
    currentStep: OnboardingStep;
    sessionData: OnboardingState;
    isLoading: boolean;
    error: string | null;
    recoveryAvailable?: boolean;
    lastError?: any;
    retryLastAction?: () => Promise<void>;
    clearError?: () => void;
    updateBusinessInfo: (info: BusinessInfo) => Promise<void>;
    createAdminAccount: (account: AdminAccount) => Promise<void>;
    setupWarehouse: (warehouse: WarehouseSetup) => Promise<void>;
    completeOnboarding: (team?: TeamSetup) => Promise<CompleteOnboardingResponse>;
    goToStep: (step: OnboardingStep) => void;
    goToNextStep: () => void;
    goToPreviousStep: () => void;
    resetOnboarding: () => void;
    saveProgress: () => void;
    loadProgress: () => void;
}
export interface OnboardingProgress {
    totalSteps: number;
    currentStep: number;
    completedSteps: OnboardingStep[];
    estimatedTimeRemaining: number;
    progressPercentage: number;
    stepTitles: string[];
    stepDescriptions: string[];
}
export interface OnboardingError {
    code: string;
    message: string;
    field?: string;
    step?: OnboardingStep;
}
export interface OnboardingValidationSchemas {
    businessInfo: any;
    adminAccount: any;
    warehouseSetup: any;
    teamSetup: any;
}
export interface OnboardingRouteConfig {
    path: string;
    step: OnboardingStep;
    title: string;
    description: string;
    allowedFromSteps: OnboardingStep[];
}
export declare const ONBOARDING_ROUTES: OnboardingRouteConfig[];
export declare const ONBOARDING_SESSION_DURATION: number;
export declare const INVITATION_DURATION: number;
export declare const ONBOARDING_STEP_TITLES: Record<OnboardingStep, string>;
export declare const ONBOARDING_STEP_DESCRIPTIONS: Record<OnboardingStep, string>;
