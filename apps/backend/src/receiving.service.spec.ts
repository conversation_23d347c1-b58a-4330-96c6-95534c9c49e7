import { Test, TestingModule } from "@nestjs/testing";
import { ReceivingService } from "./receiving.service";
import { PrismaService } from "./prisma/prisma.service";
import { BadRequestException, NotFoundException } from "@nestjs/common";
import { Role, Tenant } from "@prisma/client"; // Import Tenant
import { ReceiveItemsDto } from "./receiving/dto/receive-items.dto";

// Mock data (will be expanded and customized per test)
const mockTenantId = "tenant-123";
const mockWarehouseId = "warehouse-abc";
const mockLocationId = "location-xyz";
const mockItemId1 = "item-001";
const mockItemId2 = "item-002";

const mockUser = {
  id: "user-789",
  tenantId: mockTenantId,
  warehouseUsers: [
    { warehouseId: mockWarehouseId, role: "WAREHOUSE_MEMBER" as any },
  ],
  // Add other PrismaUser fields if needed by the service, or use a partial type
  email: "<EMAIL>",
  role: Role.WAREHOUSE_MEMBER, // Assuming Role is imported or defined
  name: "Test User",
  authUserId: "auth-user-123",
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockLocation = {
  id: mockLocationId,
  warehouseId: mockWarehouseId,
  locationType: "Receiving",
  tenantId: mockTenantId, // Location itself doesn't have tenantId directly in schema, but its warehouse does
  warehouse: {
    // nested warehouse
    tenantId: mockTenantId,
  },
};

const mockItem1 = {
  id: mockItemId1,
  tenantId: mockTenantId,
  // ... other item properties
};

const mockNewPallet = {
  id: "pallet-new-123",
  tenantId: mockTenantId,
  status: "RECEIVED",
  locationId: mockLocationId,
  PalletItems: [], // Will be populated
};

describe("ReceivingService", () => {
  let service: ReceivingService;
  let prisma: DeepMocked<PrismaService>;

  // Helper type for deep mocking Prisma
  type DeepMocked<T> = {
    [K in keyof T]: T[K] extends (...args: any[]) => any
      ? jest.Mock<ReturnType<T[K]>, Parameters<T[K]>>
      : DeepMocked<T[K]>;
  };

  beforeEach(async () => {
    const mockPrismaService = {
      location: {
        findUnique: jest.fn(),
      },
      item: {
        findMany: jest.fn(),
      },
      warehouseItem: {
        findUnique: jest.fn(),
        create: jest.fn(),
      },
      pallet: {
        create: jest.fn(),
        findUniqueOrThrow: jest.fn(),
      },
      palletItem: {
        createMany: jest.fn(),
      },
      $transaction: jest.fn().mockImplementation(async (callback) => {
        // Mock the transaction client (tx)
        const mockTx = {
          warehouseItem: {
            findUnique: jest.fn(),
            create: jest.fn(),
          },
          pallet: {
            create: jest.fn(),
            findUniqueOrThrow: jest.fn(),
          },
          palletItem: {
            createMany: jest.fn(),
          },
          // Add other models used within transaction if necessary
        };
        return callback(mockTx);
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ReceivingService,
        { provide: PrismaService, useValue: mockPrismaService },
      ],
    }).compile();

    service = module.get<ReceivingService>(ReceivingService);
    prisma = module.get(PrismaService) as DeepMocked<PrismaService>;
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("receiveItems", () => {
    const receiveItemsDto: ReceiveItemsDto = {
      poNumber: "PO-TEST-001",
      receivingLocationId: mockLocationId,
      barcode: "TEST-12345",
      description: "Test pallet description",
      items: [{ itemId: mockItemId1, quantity: 10 }],
    };

    it("should throw BadRequestException if no items and no description are provided", async () => {
      const dto: ReceiveItemsDto = {
        ...receiveItemsDto,
        items: [],
        description: "",
      };

      await expect(service.receiveItems(dto, mockUser)).rejects.toThrow(
        new BadRequestException(
          "A pallet must have at least one item or a description."
        )
      );
    });

    it("should succeed with a description but no items", async () => {
      const dto: ReceiveItemsDto = {
        poNumber: "PO-TEST-002",
        receivingLocationId: mockLocationId,
        barcode: "TEST-12345",
        items: [],
        description: "Empty pallet received for future use",
      };

      // Mock Prisma calls
      (prisma.location.findUnique as jest.Mock).mockResolvedValue(mockLocation);
      (prisma.item.findMany as jest.Mock).mockResolvedValue([]); // No items

      // Transaction mocks
      const mockTx = {
        pallet: {
          create: jest
            .fn()
            .mockResolvedValue({ ...mockNewPallet, PalletItems: [] }),
          findUniqueOrThrow: jest.fn().mockResolvedValue({
            ...mockNewPallet,
            PalletItems: [],
          }),
        },
        palletItem: {
          createMany: jest.fn(),
        },
      };
      (prisma.$transaction as jest.Mock).mockImplementation(async (callback) =>
        callback(mockTx)
      );

      const result = await service.receiveItems(dto, mockUser);

      // Assertions
      expect(result).toBeDefined();
      expect(mockTx.pallet.create).toHaveBeenCalled();
      expect(mockTx.palletItem.createMany).not.toHaveBeenCalled();
    });

    it("should successfully receive items and create pallet", async () => {
      // Mock Prisma calls
      (prisma.location.findUnique as jest.Mock).mockResolvedValue(mockLocation);
      (prisma.item.findMany as jest.Mock).mockResolvedValue([mockItem1]);

      // Transaction mocks
      const mockTx = {
        warehouseItem: {
          findUnique: jest.fn().mockResolvedValue(null), // Simulate WarehouseItem does not exist
          create: jest.fn().mockResolvedValue({
            id: "wh-item-1",
            tenantId: mockTenantId,
            warehouseId: mockWarehouseId,
            itemId: mockItemId1,
          }),
        },
        pallet: {
          create: jest.fn().mockResolvedValue(mockNewPallet),
          findUniqueOrThrow: jest.fn().mockResolvedValue({
            ...mockNewPallet,
            PalletItems: [{ itemId: mockItemId1, quantity: 10 }],
          }),
        },
        palletItem: {
          createMany: jest.fn().mockResolvedValue({ count: 1 }),
        },
      };
      (prisma.$transaction as jest.Mock).mockImplementation(async (callback) =>
        callback(mockTx)
      );

      const result = await service.receiveItems(receiveItemsDto, mockUser);

      // Assertions
      expect(prisma.location.findUnique).toHaveBeenCalledWith({
        where: { id: mockLocationId },
        include: { warehouse: { select: { tenantId: true } } },
      });
      expect(prisma.item.findMany).toHaveBeenCalledWith({
        where: { id: { in: [mockItemId1] }, tenantId: mockTenantId },
        select: { id: true },
      });

      // Transactional call assertions
      expect(mockTx.warehouseItem.findUnique).toHaveBeenCalledWith({
        where: {
          tenant_warehouse_item_unique: {
            tenantId: mockTenantId,
            warehouseId: mockWarehouseId,
            itemId: mockItemId1,
          },
        },
      });
      expect(mockTx.warehouseItem.create).toHaveBeenCalledWith({
        data: {
          tenantId: mockTenantId,
          itemId: mockItemId1,
          warehouseId: mockWarehouseId,
        },
      });
      expect(mockTx.pallet.create).toHaveBeenCalledWith({
        data: {
          tenantId: mockTenantId,
          status: "RECEIVED",
          locationId: mockLocationId,
        },
      });
      expect(mockTx.palletItem.createMany).toHaveBeenCalledWith({
        data: [
          {
            palletId: mockNewPallet.id,
            itemId: mockItemId1,
            quantity: 10,
            tenantId: mockTenantId,
          },
        ],
      });
      expect(mockTx.pallet.findUniqueOrThrow).toHaveBeenCalledWith({
        where: { id: mockNewPallet.id },
        include: { palletItems: true, location: true },
      });

      expect(result.id).toBe(mockNewPallet.id);
      expect(result.palletItems).toHaveLength(1);
      expect(result.palletItems[0].quantity).toBe(10);
    });

    // Add more tests for:
    // - WarehouseItem already exists
    // - Location not found
    // - Location not 'Receiving' type
    // - User not authorized for location (warehouse mismatch)
    // - User not authorized for location (tenant mismatch with location's warehouse)
    // - Item not found
    // - Item from different tenant
    // - Multiple items in DTO
  });
});
