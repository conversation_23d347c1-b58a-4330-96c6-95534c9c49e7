import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UsePipes,
  ValidationPipe,
  NotFoundException,
  UseGuards,
  Req,
} from "@nestjs/common";
import { ItemsService } from "./items.service";
import { CreateItemDto } from "./dto/create-item.dto";
import { UpdateItemDto } from "./dto/update-item.dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { WarehouseMemberGuard } from "../auth/guards/role-based.guard";
import { WarehouseMemberOnly } from "../auth/decorators/role-based.decorator";
import { EnhancedUserPayload } from "../auth/types";

@Controller("items")
@WarehouseMemberOnly()
export class ItemsController {
  constructor(private readonly itemsService: ItemsService) {}

  @Post()
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  create(
    @Body() createItemDto: CreateItemDto,
    @Req() req: { user: EnhancedUserPayload }
  ) {
    return this.itemsService.create(createItemDto, req.user);
  }

  @Get()
  findAll(@Req() req: { user: EnhancedUserPayload }) {
    return this.itemsService.findAll(req.user);
  }

  @Get(":id")
  async findOne(
    @Param("id") id: string,
    @Req() req: { user: EnhancedUserPayload }
  ) {
    const item = await this.itemsService.findOne(id, req.user);
    return item;
  }

  @Patch(":id")
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  update(
    @Param("id") id: string,
    @Body() updateItemDto: UpdateItemDto,
    @Req() req: { user: EnhancedUserPayload }
  ) {
    return this.itemsService.update(id, updateItemDto, req.user);
  }

  @Delete(":id")
  remove(@Param("id") id: string, @Req() req: { user: EnhancedUserPayload }) {
    return this.itemsService.remove(id, req.user);
  }
}
