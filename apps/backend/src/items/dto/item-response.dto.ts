import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Item } from "@prisma/client";

/**
 * Warehouse information for item stock tracking
 */
export class ItemWarehouseDto {
  @ApiProperty({ description: "Warehouse ID" })
  id: string;

  @ApiProperty({ description: "Warehouse name" })
  name: string;
}

/**
 * Location information for item stock tracking
 */
export class ItemLocationDto {
  @ApiProperty({ description: "Location ID" })
  id: string;

  @ApiProperty({ description: "Location name" })
  name: string;

  @ApiProperty({
    description: "Warehouse information",
    type: ItemWarehouseDto,
  })
  warehouse: ItemWarehouseDto;
}

/**
 * Pallet stock information for items
 */
export class PalletLocationStockDto {
  @ApiProperty({ description: "Pallet ID" })
  palletId: string;

  @ApiProperty({ description: "Pallet barcode" })
  palletBarcode: string;

  @ApiPropertyOptional({ description: "Location name where pallet is stored" })
  locationName?: string;

  @ApiPropertyOptional({ description: "Warehouse name where pallet is stored" })
  warehouseName?: string;

  @ApiProperty({ description: "Quantity of this item on the pallet" })
  quantity: number;

  @ApiPropertyOptional({
    description: "Detailed location information",
    type: ItemLocationDto,
  })
  location?: ItemLocationDto;
}

/**
 * Warehouse stock summary for items
 */
export class ItemWarehouseStockDto {
  @ApiProperty({
    description: "Warehouse information",
    type: ItemWarehouseDto,
  })
  warehouse: ItemWarehouseDto;

  @ApiProperty({ description: "Total quantity in this warehouse" })
  totalQuantity: number;

  @ApiProperty({ description: "Number of pallets in this warehouse" })
  palletCount: number;

  @ApiProperty({ description: "Number of locations with this item" })
  locationCount: number;
}

/**
 * Response DTO for item details with warehouse context
 */
export class ItemDetailResponseDto {
  @ApiProperty({ description: "Item ID" })
  id: string;

  @ApiProperty({ description: "Item name" })
  name: string;

  @ApiPropertyOptional({ description: "Item SKU" })
  sku?: string;

  @ApiPropertyOptional({ description: "Item description" })
  description?: string;

  @ApiPropertyOptional({ description: "Unit of measure" })
  unitOfMeasure?: string;

  @ApiPropertyOptional({ description: "Default cost" })
  defaultCost?: number;

  @ApiPropertyOptional({ description: "Low stock threshold" })
  lowStockThreshold?: number;

  @ApiProperty({ description: "Item status" })
  status: string;

  @ApiProperty({ description: "Creation timestamp" })
  createdAt: Date;

  @ApiProperty({ description: "Last update timestamp" })
  updatedAt: Date;

  @ApiProperty({ description: "Tenant ID" })
  tenantId: string;

  @ApiProperty({ description: "Total quantity on hand across all warehouses" })
  quantityOnHand: number;

  @ApiProperty({ description: "Total number of pallets containing this item" })
  numberOfPallets: number;

  @ApiProperty({
    description: "Stock breakdown by pallet and location",
    type: [PalletLocationStockDto],
  })
  stockByLocationAndPallet: PalletLocationStockDto[];

  @ApiPropertyOptional({
    description: "Stock summary by warehouse",
    type: [ItemWarehouseStockDto],
  })
  stockByWarehouse?: ItemWarehouseStockDto[];
}

/**
 * Basic item response DTO
 */
export class ItemResponseDto {
  @ApiProperty({ description: "Item ID" })
  id: string;

  @ApiProperty({ description: "Item name" })
  name: string;

  @ApiPropertyOptional({ description: "Item SKU" })
  sku?: string;

  @ApiPropertyOptional({ description: "Item description" })
  description?: string;

  @ApiPropertyOptional({ description: "Unit of measure" })
  unitOfMeasure?: string;

  @ApiPropertyOptional({ description: "Default cost" })
  defaultCost?: number;

  @ApiPropertyOptional({ description: "Low stock threshold" })
  lowStockThreshold?: number;

  @ApiProperty({ description: "Item status" })
  status: string;

  @ApiProperty({ description: "Creation timestamp" })
  createdAt: Date;

  @ApiProperty({ description: "Last update timestamp" })
  updatedAt: Date;

  @ApiProperty({ description: "Tenant ID" })
  tenantId: string;

  @ApiPropertyOptional({ description: "Total quantity on hand" })
  quantityOnHand?: number;
}

/**
 * Paginated response for item queries
 */
export class ItemListResponseDto {
  @ApiProperty({
    description: "Array of items",
    type: [ItemResponseDto],
  })
  data: ItemResponseDto[];

  @ApiProperty({ description: "Total number of items" })
  count: number;

  @ApiPropertyOptional({ description: "Current page number" })
  page?: number;

  @ApiPropertyOptional({ description: "Number of items per page" })
  limit?: number;

  @ApiPropertyOptional({ description: "Total number of pages" })
  totalPages?: number;
}
