import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsInt,
  Min,
  <PERSON><PERSON>umber,
  IsArray,
} from "class-validator";

export class CreateItemDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsOptional()
  sku?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  unitOfMeasure?: string; // Defaults to 'Each' in schema

  @IsNumber()
  @IsOptional()
  defaultCost?: number;

  @IsInt()
  @Min(0)
  @IsOptional()
  lowStockThreshold?: number;

  @IsString()
  @IsOptional()
  status?: string; // Defaults to 'Active' in schema

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  warehouseIds?: string[];
}
