// apps/backend/src/items/items.service.spec.ts
import "reflect-metadata"; // Required for NestJS testing
import { Test, TestingModule } from "@nestjs/testing";
import { PrismaService } from "../prisma/prisma.service";
import { ItemsService } from "./items.service";
import { ItemsModule } from "./items.module";
import { PrismaModule } from "../prisma/prisma.module";
import { cleanupDatabase } from "../../test/utils/db-cleanup";
import { CreateItemDto } from "./dto/create-item.dto";
import { UpdateItemDto } from "./dto/update-item.dto"; // Added for update tests
import {
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from "@nestjs/common"; // Add BadRequestException if used, ForbiddenException
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";
import { Role as AppRole } from "../auth/entities/role.enum"; // Use local app Role for general logic
import {
  Prisma,
  Tenant,
  Warehouse,
  Item,
  Role as PrismaRole,
} from "@prisma/client"; // Added Item, aliased Prisma.Role

describe("ItemsService Integration", () => {
  let service: ItemsService;
  let prisma: PrismaService; // Use injected PrismaService
  let module: TestingModule;

  let testTenant: Tenant;
  let testWarehouse1: Warehouse;
  let testWarehouse2: Warehouse;

  let mockAdminUser: AuthenticatedUser;
  let mockWarehouseAdminUser: AuthenticatedUser;
  let mockWarehouseMemberUser: AuthenticatedUser;
  let mockUserNoWarehouses: AuthenticatedUser;

  beforeAll(async () => {
    // No separate client connection needed here
  });

  afterAll(async () => {
    await module?.close(); // Close module (disconnects injected PrismaService via lifecycle hook)
  });

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [PrismaModule, ItemsModule],
    }).compile();

    prisma = module.get<PrismaService>(PrismaService);
    service = module.get<ItemsService>(ItemsService);

    // Ensure injected prisma IS connected by the module lifecycle before cleanup
    await cleanupDatabase(prisma); // Pass injected prisma to cleanup

    testTenant = await prisma.tenant.create({
      data: { name: "Test Tenant for Items" },
    });

    testWarehouse1 = await prisma.warehouse.create({
      data: { name: "Test Warehouse 1", tenantId: testTenant.id },
    });
    testWarehouse2 = await prisma.warehouse.create({
      data: { name: "Test Warehouse 2", tenantId: testTenant.id },
    });

    // Mock Users
    mockAdminUser = {
      id: "admin-user-id",
      email: "<EMAIL>",
      role: PrismaRole.TENANT_ADMIN, // Use an existing role from PrismaRole, e.g. TENANT_ADMIN as admin equivalent
      tenantId: testTenant.id,
      name: "Admin User",
      authUserId: "auth-admin-user-id",
      warehouseUsers: [
        { warehouseId: testWarehouse1.id, role: PrismaRole.WAREHOUSE_MEMBER },
        { warehouseId: testWarehouse2.id, role: PrismaRole.WAREHOUSE_MEMBER },
      ],
    };

    mockWarehouseAdminUser = {
      id: "whadmin-user-id",
      email: "<EMAIL>",
      role: PrismaRole.WAREHOUSE_MANAGER, // Use an existing role from PrismaRole, e.g. WAREHOUSE_MANAGER as WH_ADMIN equivalent
      tenantId: testTenant.id,
      name: "Warehouse Admin User",
      authUserId: "auth-whadmin-user-id",
      warehouseUsers: [
        { warehouseId: testWarehouse1.id, role: PrismaRole.WAREHOUSE_MEMBER },
      ],
    };

    mockWarehouseMemberUser = {
      id: "whmember-user-id",
      email: "<EMAIL>",
      role: PrismaRole.WAREHOUSE_MEMBER, // This one exists
      tenantId: testTenant.id,
      name: "Warehouse Member User",
      authUserId: "auth-whmember-user-id",
      warehouseUsers: [
        { warehouseId: testWarehouse2.id, role: PrismaRole.WAREHOUSE_MEMBER },
      ],
    };

    mockUserNoWarehouses = {
      id: "nowh-user-id",
      email: "<EMAIL>",
      role: PrismaRole.WAREHOUSE_MEMBER, // This one exists
      tenantId: testTenant.id,
      name: "No Warehouse User",
      authUserId: "auth-nowh-user-id",
      warehouseUsers: [],
    };
  });

  afterEach(async () => {
    // Module closing in afterAll should handle disconnect
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  // --- Test Cases ---

  describe("create()", () => {
    const createDto: CreateItemDto = {
      name: "Test Item Global",
      sku: "TEST_ITEM_001",
      description: "A test item description",
      unitOfMeasure: "pcs",
    };

    it("ADMIN should create a new item without warehouse associations", async () => {
      const item = await service.create(createDto, mockAdminUser);
      expect(item).toBeDefined();
      expect(item.name).toBe(createDto.name);
      expect(item.tenantId).toBe(mockAdminUser.tenantId);

      const dbItem = await prisma.item.findUnique({ where: { id: item.id } });
      expect(dbItem).toBeDefined();
      // If items were tenant-scoped directly, we'd check item.tenantId === mockAdminUser.tenantId

      const warehouseItems = await prisma.warehouseItem.findMany({
        where: { itemId: item.id },
      });
      expect(warehouseItems.length).toBe(0);
    });

    it("ADMIN should create a new item and associate with warehouses in their tenant", async () => {
      const dtoWithWarehouses: CreateItemDto = {
        ...createDto,
        sku: "TEST_ITEM_002",
        warehouseIds: [testWarehouse1.id, testWarehouse2.id],
      };
      const item = await service.create(dtoWithWarehouses, mockAdminUser);
      expect(item).toBeDefined();
      const warehouseItems = await prisma.warehouseItem.findMany({
        where: { itemId: item.id, tenantId: mockAdminUser.tenantId },
      });
      expect(warehouseItems.length).toBe(2);
      expect(warehouseItems.map((wi) => wi.warehouseId).sort()).toEqual(
        [testWarehouse1.id, testWarehouse2.id].sort()
      );
      warehouseItems.forEach((wi) =>
        expect(wi.tenantId).toBe(mockAdminUser.tenantId)
      );
    });

    it("should throw ForbiddenException if ADMIN tries to associate with warehouse from another tenant", async () => {
      const otherTenant = await prisma.tenant.create({
        data: { name: "Other Tenant" },
      });
      const otherTenantWarehouse = await prisma.warehouse.create({
        data: { name: "Other Tenant Warehouse", tenantId: otherTenant.id },
      });
      const dtoWithInvalidWarehouse: CreateItemDto = {
        ...createDto,
        sku: "TEST_ITEM_003",
        warehouseIds: [otherTenantWarehouse.id],
      };
      await expect(
        service.create(dtoWithInvalidWarehouse, mockAdminUser)
      ).rejects.toThrow(ForbiddenException);
    });

    it("should throw ForbiddenException if SKU already exists globally", async () => {
      await service.create(
        { ...createDto, sku: "DUPLICATE_SKU_GLOBAL" },
        mockAdminUser
      );
      await expect(
        service.create(
          { name: "Another Item", sku: "DUPLICATE_SKU_GLOBAL" },
          mockAdminUser
        )
      ).rejects.toThrow(ForbiddenException); // Service throws ForbiddenException for SKU conflict
    });

    // Non-ADMIN users cannot create master items as per current service logic (implicit by not exposing create to them or explicit check)
    // The service's create method is used by ADMIN. Other roles might use a different flow if they can create items within their context.
    // For now, assuming only ADMINs call this `ItemsService.create`.
  });

  describe("findAll()", () => {
    let item1: Item, item2: Item, item3: Item;

    beforeEach(async () => {
      // Items are global, but associated via WarehouseItem
      item1 = await prisma.item.create({
        data: { name: "Alpha Item", tenantId: testTenant.id },
      });
      item2 = await prisma.item.create({
        data: { name: "Bravo Item", tenantId: testTenant.id },
      });
      item3 = await prisma.item.create({
        data: { name: "Charlie Item - No WH", tenantId: testTenant.id },
      });

      // Associate item1 with testWarehouse1 (mockWarehouseAdminUser's warehouse) for the current tenant
      await prisma.warehouseItem.create({
        data: {
          itemId: item1.id,
          warehouseId: testWarehouse1.id,
          tenantId: testTenant.id,
        },
      });
      // Associate item2 with testWarehouse2 (mockWarehouseMemberUser's warehouse) for the current tenant
      await prisma.warehouseItem.create({
        data: {
          itemId: item2.id,
          warehouseId: testWarehouse2.id,
          tenantId: testTenant.id,
        },
      });
    });

    it("WAREHOUSE_ADMIN should find items associated with their warehouses in their tenant", async () => {
      const items = await service.findAll(mockWarehouseAdminUser);
      expect(items.length).toBe(1);
      expect(items[0].id).toBe(item1.id);
    });

    it("WAREHOUSE_MEMBER should find items associated with their warehouses in their tenant", async () => {
      const items = await service.findAll(mockWarehouseMemberUser);
      expect(items.length).toBe(1);
      expect(items[0].id).toBe(item2.id);
    });

    it("ADMIN should find all items associated with any of their warehouses in their tenant", async () => {
      // mockAdminUser is associated with testWarehouse1 and testWarehouse2
      const items = await service.findAll(mockAdminUser);
      expect(items.length).toBe(2);
      expect(items.map((i) => i.id).sort()).toEqual(
        [item1.id, item2.id].sort()
      );
    });

    it("should return empty array if user has no associated warehouses with items", async () => {
      const items = await service.findAll(mockUserNoWarehouses);
      expect(items).toEqual([]);
    });

    it("should return empty array if no items are associated with user's warehouses, even if items exist", async () => {
      // Create a user associated with a new warehouse that has no items.
      const newWarehouse = await prisma.warehouse.create({
        data: { name: "Empty WH", tenantId: testTenant.id },
      });
      const userWithEmptyWarehouse: AuthenticatedUser = {
        ...mockWarehouseMemberUser,
        id: "empty-wh-user",
        warehouseUsers: [
          { warehouseId: newWarehouse.id, role: PrismaRole.WAREHOUSE_MEMBER },
        ],
      };
      const items = await service.findAll(userWithEmptyWarehouse);
      expect(items).toEqual([]);
    });
  });

  describe("findOne()", () => {
    let itemLinkedToWh1: Item; // Linked to testWarehouse1
    let itemLinkedToWh2: Item; // Linked to testWarehouse2
    let itemNotLinked: Item; // Global item, not linked to any warehouse in this tenant

    beforeEach(async () => {
      itemLinkedToWh1 = await prisma.item.create({
        data: { name: "Item For WH1", tenantId: testTenant.id },
      });
      itemLinkedToWh2 = await prisma.item.create({
        data: { name: "Item For WH2", tenantId: testTenant.id },
      });
      itemNotLinked = await prisma.item.create({
        data: { name: "Unlinked Item", tenantId: testTenant.id },
      });

      await prisma.warehouseItem.create({
        data: {
          itemId: itemLinkedToWh1.id,
          warehouseId: testWarehouse1.id,
          tenantId: testTenant.id,
        },
      });
      await prisma.warehouseItem.create({
        data: {
          itemId: itemLinkedToWh2.id,
          warehouseId: testWarehouse2.id,
          tenantId: testTenant.id,
        },
      });
    });

    it("WAREHOUSE_ADMIN should find an item linked to their warehouse in their tenant", async () => {
      const found = await service.findOne(
        itemLinkedToWh1.id,
        mockWarehouseAdminUser
      );
      expect(found.id).toBe(itemLinkedToWh1.id);
    });

    it("WAREHOUSE_ADMIN should NOT find an item linked to another warehouse in their tenant", async () => {
      await expect(
        service.findOne(itemLinkedToWh2.id, mockWarehouseAdminUser)
      ).rejects.toThrow(NotFoundException);
    });

    it("WAREHOUSE_ADMIN should NOT find an unlinked item (even if it exists globally)", async () => {
      await expect(
        service.findOne(itemNotLinked.id, mockWarehouseAdminUser)
      ).rejects.toThrow(NotFoundException);
    });

    it("ADMIN should find an item linked to any of their warehouses in their tenant", async () => {
      const found1 = await service.findOne(itemLinkedToWh1.id, mockAdminUser); // Wh1
      expect(found1.id).toBe(itemLinkedToWh1.id);
      const found2 = await service.findOne(itemLinkedToWh2.id, mockAdminUser); // Wh2
      expect(found2.id).toBe(itemLinkedToWh2.id);
    });

    it("should throw NotFoundException if item ID does not exist globally", async () => {
      await expect(
        service.findOne("non-existent-id", mockAdminUser)
      ).rejects.toThrow(NotFoundException);
    });

    it("User with no warehouses should not find any item", async () => {
      await expect(
        service.findOne(itemLinkedToWh1.id, mockUserNoWarehouses)
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe("update()", () => {
    let itemToUpdate: Item;
    const updateDto: UpdateItemDto = { name: "Updated Item Name" };

    beforeEach(async () => {
      itemToUpdate = await prisma.item.create({
        data: {
          name: "Original Name",
          sku: "UPDATE_SKU_001",
          tenantId: testTenant.id,
        },
      });
      // ADMIN user will be used for these tests as per service logic
    });

    it("ADMIN should update master item data", async () => {
      const updated = await service.update(
        itemToUpdate.id,
        updateDto,
        mockAdminUser
      );
      expect(updated.name).toBe(updateDto.name);
      const dbItem = await prisma.item.findUnique({
        where: { id: itemToUpdate.id },
      });
      expect(dbItem.name).toBe(updateDto.name);
    });

    it("ADMIN should update item and sync warehouse associations within their tenant", async () => {
      // Initially, itemToUpdate is not associated with any warehouse
      const dtoWithWarehouses: UpdateItemDto = {
        ...updateDto,
        name: "Updated and Associated",
        warehouseIds: [testWarehouse1.id], // Associate with WH1 in ADMIN's tenant
      };

      const updated = await service.update(
        itemToUpdate.id,
        dtoWithWarehouses,
        mockAdminUser
      );
      expect(updated.name).toBe(dtoWithWarehouses.name);

      const warehouseItems = await prisma.warehouseItem.findMany({
        where: { itemId: itemToUpdate.id, tenantId: mockAdminUser.tenantId },
      });
      expect(warehouseItems.length).toBe(1);
      expect(warehouseItems[0].warehouseId).toBe(testWarehouse1.id);
      expect(warehouseItems[0].tenantId).toBe(mockAdminUser.tenantId);

      // Now, update to associate with WH2 and disassociate from WH1
      const dtoSwitchWarehouses: UpdateItemDto = {
        warehouseIds: [testWarehouse2.id],
      };
      await service.update(itemToUpdate.id, dtoSwitchWarehouses, mockAdminUser);
      const finalWarehouseItems = await prisma.warehouseItem.findMany({
        where: { itemId: itemToUpdate.id, tenantId: mockAdminUser.tenantId },
        orderBy: { warehouseId: "asc" },
      });
      expect(finalWarehouseItems.length).toBe(1);
      expect(finalWarehouseItems[0].warehouseId).toBe(testWarehouse2.id);

      // Update to remove all associations
      await service.update(
        itemToUpdate.id,
        { warehouseIds: [] },
        mockAdminUser
      );
      const noWarehouseItems = await prisma.warehouseItem.findMany({
        where: { itemId: itemToUpdate.id, tenantId: mockAdminUser.tenantId },
      });
      expect(noWarehouseItems.length).toBe(0);
    });

    it("ADMIN should throw ForbiddenException if trying to associate item with a warehouse from another tenant", async () => {
      const otherTenant = await prisma.tenant.create({
        data: { name: "Other Tenant For Update" },
      });
      const otherTenantWarehouse = await prisma.warehouse.create({
        data: { name: "Other WH Update", tenantId: otherTenant.id },
      });
      const dtoWithInvalidWarehouse: UpdateItemDto = {
        warehouseIds: [otherTenantWarehouse.id],
      };
      await expect(
        service.update(itemToUpdate.id, dtoWithInvalidWarehouse, mockAdminUser)
      ).rejects.toThrow(ForbiddenException);
    });

    it("should throw ForbiddenException if non-ADMIN tries to update", async () => {
      await expect(
        service.update(itemToUpdate.id, updateDto, mockWarehouseAdminUser)
      ).rejects.toThrow(ForbiddenException);
      await expect(
        service.update(itemToUpdate.id, updateDto, mockWarehouseMemberUser)
      ).rejects.toThrow(ForbiddenException);
    });

    it("should throw NotFoundException if item to update does not exist", async () => {
      await expect(
        service.update("non-existent-id", updateDto, mockAdminUser)
      ).rejects.toThrow(NotFoundException);
    });

    it("ADMIN should throw ForbiddenException if updating to a SKU that already exists globally on another item", async () => {
      const item1 = await service.create(
        { name: "Item SKU1", sku: "UNIQUE_SKU1" },
        mockAdminUser
      );
      const item2 = await service.create(
        { name: "Item SKU2", sku: "UNIQUE_SKU2" },
        mockAdminUser
      );
      await expect(
        service.update(item2.id, { sku: "UNIQUE_SKU1" }, mockAdminUser)
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe("remove()", () => {
    let itemToRemove: Item;

    beforeEach(async () => {
      itemToRemove = await prisma.item.create({
        data: { name: "Item to Delete", tenantId: testTenant.id },
      });
      // ADMIN user will be used for these tests
      // Associate with a warehouse in the admin's tenant
      await prisma.warehouseItem.create({
        data: {
          itemId: itemToRemove.id,
          warehouseId: testWarehouse1.id,
          tenantId: mockAdminUser.tenantId,
        },
      });
      // Associate with a warehouse in another tenant to test global WarehouseItem cleanup
      const otherTenant = await prisma.tenant.create({
        data: { name: "Other Tenant For Remove" },
      });
      const otherTenantWarehouse = await prisma.warehouse.create({
        data: { name: "Other WH Remove", tenantId: otherTenant.id },
      });
      await prisma.warehouseItem.create({
        data: {
          itemId: itemToRemove.id,
          warehouseId: otherTenantWarehouse.id,
          tenantId: otherTenant.id,
        },
      });
    });

    it("ADMIN should delete an existing master item and its WarehouseItem associations globally", async () => {
      const warehouseItemCountBefore = await prisma.warehouseItem.count({
        where: { itemId: itemToRemove.id },
      });
      expect(warehouseItemCountBefore).toBe(2); // One for mockAdminUser's tenant, one for otherTenant

      await service.remove(itemToRemove.id, mockAdminUser);

      const dbItem = await prisma.item.findUnique({
        where: { id: itemToRemove.id },
      });
      expect(dbItem).toBeNull();

      const warehouseItemCountAfter = await prisma.warehouseItem.count({
        where: { itemId: itemToRemove.id },
      });
      expect(warehouseItemCountAfter).toBe(0);
    });

    it("should throw ForbiddenException if non-ADMIN tries to delete", async () => {
      await expect(
        service.remove(itemToRemove.id, mockWarehouseAdminUser)
      ).rejects.toThrow(ForbiddenException);
      await expect(
        service.remove(itemToRemove.id, mockWarehouseMemberUser)
      ).rejects.toThrow(ForbiddenException);
    });

    it("should throw NotFoundException if item to delete does not exist", async () => {
      await expect(
        service.remove("non-existent-id", mockAdminUser)
      ).rejects.toThrow(NotFoundException);
    });

    // Constraint violation (e.g. item on a pallet) is not tested here, but service should handle or document it.
    // Prisma would throw P2003 if PalletItem references it and onDelete is RESTRICT.
  });
});
