import {
  <PERSON>,
  Get,
  Patch,
  Req,
  UseGuards,
  Param,
  Body,
  UsePipes,
  ValidationPipe,
} from "@nestjs/common";
import { UsersService } from "./users.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { TenantAdminGuard } from "../auth/guards/role-based.guard";
import { TenantAdminOnly } from "../auth/decorators/role-based.decorator";
import { EnhancedUserPayload } from "../auth/types";
import { UserResponseDto } from "./dto/user-response.dto";
import { UpdateUserDto } from "./dto/update-user.dto";

@Controller("users")
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  @TenantAdminOnly()
  async findAll(
    @Req() req: { user: EnhancedUserPayload }
  ): Promise<UserResponseDto[]> {
    const tenantId = req.user.tenantId;
    if (!tenantId) {
      throw new Error("Tenant ID not found on authenticated user.");
    }
    return this.usersService.findAllByTenant(tenantId);
  }

  @Patch("profile")
  @UseGuards(JwtAuthGuard)
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  async updateProfile(
    @Body() updateUserDto: UpdateUserDto,
    @Req() req: { user: EnhancedUserPayload }
  ): Promise<UserResponseDto> {
    return this.usersService.updateProfile(
      req.user.id,
      updateUserDto,
      req.user
    );
  }
}
