import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { UserResponseDto } from "./dto/user-response.dto";
import { UpdateUserDto } from "./dto/update-user.dto";
import { Role, UserStatus } from "@quildora/types";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";

@Injectable()
export class UsersService {
  constructor(private prisma: PrismaService) {}

  async findAllByTenant(tenantId: string): Promise<UserResponseDto[]> {
    const usersFromDb = await this.prisma.user.findMany({
      where: { tenantId },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        name: "asc",
      },
    });

    return usersFromDb.map((user) => ({
      ...user,
      role: user.role as Role, // Cast to shared Role type
      status: user.status as UserStatus, // Cast to shared UserStatus type
    }));
  }

  async updateProfile(
    userId: string,
    updateUserDto: UpdateUserDto,
    currentUser: AuthenticatedUser
  ): Promise<UserResponseDto> {
    // Users can only update their own profile
    if (currentUser.id !== userId) {
      throw new ForbiddenException("You can only update your own profile.");
    }

    // Check if user exists and belongs to the same tenant
    const existingUser = await this.prisma.user.findFirst({
      where: {
        id: userId,
        tenantId: currentUser.tenantId,
      },
    });

    if (!existingUser) {
      throw new NotFoundException("User not found.");
    }

    // Update the user
    const updatedUser = await this.prisma.user.update({
      where: { id: userId },
      data: {
        name: updateUserDto.name,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return {
      ...updatedUser,
      role: updatedUser.role as Role,
      status: updatedUser.status as UserStatus,
    };
  }
}
