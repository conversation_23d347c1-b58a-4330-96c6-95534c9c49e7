import { IsString, <PERSON>NotEmpty, <PERSON>Optional, IsEnum } from 'class-validator';
import { LocationType, LocationCategory } from '@prisma/client';

export class CreateLocationDto {
  @IsString()
  @IsNotEmpty()
  name: string; // e.g., "A1-R2-S3"

  @IsEnum(LocationType)
  @IsNotEmpty()
  locationType: LocationType;

  @IsString()
  @IsNotEmpty() // warehouseId is required to create a location
  warehouseId: string;

  @IsString()
  @IsOptional()
  status?: string;

  @IsOptional()
  @IsEnum(LocationCategory)
  category?: LocationCategory; // Defaults to 'Active' in schema
}
