import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { LocationType, LocationCategory } from '@prisma/client';

/**
 * Warehouse information included in location responses
 */
export class LocationWarehouseDto {
  @ApiProperty({ description: 'Warehouse ID' })
  id: string;

  @ApiProperty({ description: 'Warehouse name' })
  name: string;

  @ApiPropertyOptional({ description: 'Warehouse address' })
  address?: string;
}

/**
 * Basic pallet information for location responses
 */
export class LocationPalletDto {
  @ApiProperty({ description: 'Pallet ID' })
  id: string;

  @ApiProperty({ description: 'Pallet barcode' })
  barcode: string;

  @ApiProperty({ description: 'Pallet status' })
  status: string;

  @ApiPropertyOptional({ description: 'Ship to destination' })
  shipToDestination?: string;
}

/**
 * Response DTO for location with warehouse context
 */
export class LocationResponseDto {
  @ApiProperty({ description: 'Location ID' })
  id: string;

  @ApiProperty({ description: 'Location name' })
  name: string;

  @ApiProperty({ 
    description: 'Location type',
    enum: LocationType 
  })
  locationType: LocationType;

  @ApiProperty({ 
    description: 'Location category',
    enum: LocationCategory 
  })
  category: LocationCategory;

  @ApiProperty({ description: 'Location status' })
  status: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;

  @ApiProperty({ 
    description: 'Warehouse information',
    type: LocationWarehouseDto 
  })
  warehouse: LocationWarehouseDto;

  @ApiPropertyOptional({ 
    description: 'Pallets currently at this location',
    type: [LocationPalletDto] 
  })
  pallets?: LocationPalletDto[];

  @ApiPropertyOptional({ description: 'Number of pallets at this location' })
  palletCount?: number;
}

/**
 * Paginated response for location queries
 */
export class LocationListResponseDto {
  @ApiProperty({ 
    description: 'Array of locations',
    type: [LocationResponseDto] 
  })
  data: LocationResponseDto[];

  @ApiProperty({ description: 'Total number of locations' })
  count: number;

  @ApiPropertyOptional({ description: 'Current page number' })
  page?: number;

  @ApiPropertyOptional({ description: 'Number of items per page' })
  limit?: number;

  @ApiPropertyOptional({ description: 'Total number of pages' })
  totalPages?: number;
}
