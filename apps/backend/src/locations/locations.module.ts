import { Module } from "@nestjs/common";
import { LocationsService } from "./locations.service";
import { LocationsController } from "./locations.controller";
import { PrismaModule } from "../prisma/prisma.module";
import { AuthModule } from "../auth/auth.module";
import { WarehousesModule } from "@/warehouses/warehouses.module";

@Module({
  imports: [PrismaModule, AuthModule, WarehousesModule],
  providers: [LocationsService],
  controllers: [LocationsController],
})
export class LocationsModule {}
