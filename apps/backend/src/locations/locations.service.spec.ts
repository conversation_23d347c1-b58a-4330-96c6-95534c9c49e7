import "reflect-metadata";
import { Test, TestingModule } from "@nestjs/testing";
import { PrismaService } from "../prisma/prisma.service";
import { LocationsService } from "./locations.service";
import { LocationsModule } from "./locations.module";
import { PrismaModule } from "../prisma/prisma.module";
import { cleanupDatabase } from "../../test/utils/db-cleanup";
import { CreateLocationDto } from "./dto/create-location.dto";
import { UpdateLocationDto } from "./dto/update-location.dto";
import { NotFoundException, BadRequestException } from "@nestjs/common";
import {
  Warehouse,
  Role,
  LocationType,
  LocationCategory,
} from "@prisma/client";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";

const mockTenantId = "test-tenant-id-locations";

const mockCurrentUser: AuthenticatedUser = {
  id: "test-user-id",
  email: "<EMAIL>",
  role: Role.WAREHOUSE_MANAGER,
  tenantId: mockTenantId,
  name: "Test User",
  authUserId: "test-auth-user-id",
  warehouseUsers: [],
};

describe("LocationsService Integration", () => {
  let service: LocationsService;
  let prisma: PrismaService;
  let module: TestingModule;
  let testWarehouse: Warehouse;
  let testWarehouse2: Warehouse;

  beforeAll(async () => {
    // No separate client
  });

  afterAll(async () => {
    await module?.close();
  });

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [PrismaModule, LocationsModule],
    }).compile();

    prisma = module.get<PrismaService>(PrismaService);
    service = module.get<LocationsService>(LocationsService);

    await cleanupDatabase(prisma);

    testWarehouse = await prisma.warehouse.create({
      data: {
        name: "Test Warehouse",
        tenantId: mockCurrentUser.tenantId,
      },
    });

    testWarehouse2 = await prisma.warehouse.create({
      data: {
        name: "Test Warehouse 2",
        tenantId: mockCurrentUser.tenantId,
      },
    });

    mockCurrentUser.warehouseUsers = [
      { warehouseId: testWarehouse.id, role: Role.WAREHOUSE_MEMBER },
      { warehouseId: testWarehouse2.id, role: Role.WAREHOUSE_MEMBER },
    ];
  });

  afterEach(async () => {
    // Module closing in afterAll should handle disconnect
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  // --- Test Cases ---

  describe("create()", () => {
    it("should create a new location associated with the current user's tenant", async () => {
      const createDto: CreateLocationDto = {
        name: "Test Location A1",
        locationType: LocationType.ZONE,
        category: LocationCategory.Storage,
        warehouseId: testWarehouse.id,
      };
      const createdLocation = await service.create(createDto, mockCurrentUser);

      expect(createdLocation).toBeDefined();
      expect(createdLocation.id).toBeDefined();
      expect(createdLocation.name).toEqual(createDto.name);
      expect(createdLocation.locationType).toEqual(createDto.locationType);
      expect(createdLocation.category).toEqual(createDto.category);
      expect(createdLocation.warehouseId).toEqual(testWarehouse.id);
      expect(createdLocation.status).toEqual("Active");

      const dbLocation = await prisma.location.findUnique({
        where: { id: createdLocation.id },
        include: { warehouse: true },
      });
      expect(dbLocation).toBeDefined();
      expect(dbLocation.name).toEqual(createDto.name);
      expect(dbLocation.warehouse.tenantId).toEqual(mockCurrentUser.tenantId);
    });

    it("should throw BadRequestException if warehouseId does not exist in the current tenant", async () => {
      const nonExistentWarehouseId = "clxxxxxxxxxnonexistent";
      const createDto: CreateLocationDto = {
        name: "Test Location Invalid WH",
        locationType: LocationType.DOCK,
        category: LocationCategory.Receiving,
        warehouseId: nonExistentWarehouseId,
      };

      await expect(service.create(createDto, mockCurrentUser)).rejects.toThrow(
        BadRequestException
      );
      await expect(service.create(createDto, mockCurrentUser)).rejects.toThrow(
        `Warehouse with ID \"${nonExistentWarehouseId}\" not found within your tenant.`
      );
    });

    it("should throw BadRequestException if user is not authorized for the warehouseId (not in warehouseUsers)", async () => {
      const otherWarehouseForTenant = await prisma.warehouse.create({
        data: {
          name: "Other WH For Tenant",
          tenantId: mockCurrentUser.tenantId,
        },
      });
      const currentUserWithNoAccessToThisWH: AuthenticatedUser = {
        ...mockCurrentUser,
        warehouseUsers: [
          { warehouseId: testWarehouse.id, role: Role.WAREHOUSE_MEMBER },
        ],
      };

      const createDto: CreateLocationDto = {
        name: "Location in Other WH",
        locationType: LocationType.ZONE,
        category: LocationCategory.Storage,
        warehouseId: otherWarehouseForTenant.id,
      };

      await expect(
        service.create(createDto, currentUserWithNoAccessToThisWH)
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.create(createDto, currentUserWithNoAccessToThisWH)
      ).rejects.toThrow(
        `You are not authorized to create locations in warehouse ID \"${otherWarehouseForTenant.id}\".`
      );
    });
  });

  describe("findAll()", () => {
    it("should return an array of locations for the current user's tenant and accessible warehouses", async () => {
      await service.create(
        {
          name: "Loc A",
          locationType: LocationType.ZONE,
          category: LocationCategory.Storage,
          warehouseId: testWarehouse.id,
        },
        mockCurrentUser
      );
      await service.create(
        {
          name: "Loc B",
          locationType: LocationType.STAGING,
          category: LocationCategory.Receiving,
          warehouseId: testWarehouse.id,
        },
        mockCurrentUser
      );
      await service.create(
        {
          name: "Loc C in WH2",
          locationType: LocationType.ZONE,
          category: LocationCategory.Storage,
          warehouseId: testWarehouse2.id,
        },
        mockCurrentUser
      );

      await prisma.warehouse
        .create({
          data: { name: "Different Tenant WH", tenantId: "other-tenant-id" },
        })
        .then(async (otherTenantWH) => {
          await prisma.location.create({
            data: {
              name: "Loc in Other Tenant",
              locationType: LocationType.ZONE,
              category: LocationCategory.Storage,
              warehouseId: otherTenantWH.id,
            },
          });
        });

      const locations = await service.findAll(mockCurrentUser);
      expect(locations).toBeInstanceOf(Array);
      expect(locations.length).toBe(3);
      locations.forEach((loc) =>
        expect(loc.warehouse.tenantId).toEqual(mockCurrentUser.tenantId)
      );
      expect(locations.map((l) => l.name).sort()).toEqual(
        ["Loc A", "Loc B", "Loc C in WH2"].sort()
      );
      expect(locations[0].warehouse).toBeDefined();
    });

    it("should return an empty array if no locations exist for the user's tenant/warehouses", async () => {
      const locations = await service.findAll(mockCurrentUser);
      expect(locations).toEqual([]);
    });

    it("should return an empty array if user has no assigned warehouses", async () => {
      const userWithNoWarehouses: AuthenticatedUser = {
        ...mockCurrentUser,
        warehouseUsers: [],
      };
      await service.create(
        {
          name: "Loc A",
          locationType: LocationType.ZONE,
          category: LocationCategory.Storage,
          warehouseId: testWarehouse.id,
        },
        mockCurrentUser
      );
      const locations = await service.findAll(userWithNoWarehouses);
      expect(locations).toEqual([]);
    });

    it("should return a list of locations filtered by category", async () => {
      const category = LocationCategory.Receiving;
      await service.create(
        {
          name: "Loc A",
          locationType: LocationType.ZONE,
          category: LocationCategory.Storage,
          warehouseId: testWarehouse.id,
        },
        mockCurrentUser
      );
      await service.create(
        {
          name: "Loc B",
          locationType: LocationType.STAGING,
          category: LocationCategory.Receiving,
          warehouseId: testWarehouse.id,
        },
        mockCurrentUser
      );
      await service.create(
        {
          name: "Loc C in WH2",
          locationType: LocationType.ZONE,
          category: LocationCategory.Storage,
          warehouseId: testWarehouse2.id,
        },
        mockCurrentUser
      );

      const locations = await service.findAll(
        mockCurrentUser,
        undefined,
        undefined,
        undefined,
        category
      );
      expect(locations).toBeInstanceOf(Array);
      expect(locations.length).toBe(1);
      locations.forEach((loc) =>
        expect(loc.warehouse.tenantId).toEqual(mockCurrentUser.tenantId)
      );
      expect(locations.map((l) => l.name).sort()).toEqual(["Loc B"].sort());
      expect(locations[0].warehouse).toBeDefined();
    });
  });

  describe("findOne()", () => {
    it("should return a single location by ID if it belongs to the current user's tenant and accessible warehouse", async () => {
      const createdLocation = await service.create(
        {
          name: "Find Me Loc",
          locationType: LocationType.DOCK,
          category: LocationCategory.Receiving,
          warehouseId: testWarehouse.id,
        },
        mockCurrentUser
      );
      const foundLocation = await service.findOne(
        createdLocation.id,
        mockCurrentUser
      );

      expect(foundLocation).toBeDefined();
      expect(foundLocation.id).toEqual(createdLocation.id);
      expect(foundLocation.name).toEqual("Find Me Loc");
      expect(foundLocation.warehouse.tenantId).toEqual(
        mockCurrentUser.tenantId
      );
      expect(foundLocation.warehouse.id).toEqual(testWarehouse.id);
    });

    it("should throw NotFoundException if location ID does not exist", async () => {
      const nonExistentId = "clxxxxxxxxxnonexistent";
      await expect(
        service.findOne(nonExistentId, mockCurrentUser)
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.findOne(nonExistentId, mockCurrentUser)
      ).rejects.toThrow(
        `Location with ID \"${nonExistentId}\" not found or not accessible.`
      );
    });

    it("should throw NotFoundException if location ID exists but belongs to a different tenant", async () => {
      // Create a warehouse for another tenant first
      const otherTenantWarehouse = await prisma.warehouse.create({
        data: {
          name: "Other Tenant Warehouse",
          tenantId: "some-other-tenant-id",
        },
      });
      const otherTenantLocation = await prisma.location.create({
        data: {
          name: "Other Tenant Loc",
          locationType: LocationType.ZONE,
          warehouseId: otherTenantWarehouse.id,
        },
      });
      await expect(
        service.findOne(otherTenantLocation.id, mockCurrentUser)
      ).rejects.toThrow(NotFoundException);
    });

    it("should throw NotFoundException if location ID exists in tenant but user not authorized for its warehouse", async () => {
      const userWithLimitedAccess: AuthenticatedUser = {
        ...mockCurrentUser,
        warehouseUsers: [
          { warehouseId: testWarehouse2.id, role: Role.WAREHOUSE_MEMBER },
        ],
      };
      const createdLocationInWH1 = await service.create(
        {
          name: "Loc In WH1",
          locationType: LocationType.ZONE,
          warehouseId: testWarehouse.id,
        },
        mockCurrentUser
      );
      await expect(
        service.findOne(createdLocationInWH1.id, userWithLimitedAccess)
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe("update()", () => {
    it("should update an existing location if it belongs to tenant and accessible warehouse", async () => {
      const createdLocation = await service.create(
        {
          name: "Original Loc Name",
          locationType: LocationType.ZONE,
          warehouseId: testWarehouse.id,
        },
        mockCurrentUser
      );
      const updateDto: UpdateLocationDto = {
        name: "Updated Loc Name",
        status: "Inactive",
      };

      const updatedLocation = await service.update(
        createdLocation.id,
        updateDto,
        mockCurrentUser
      );

      expect(updatedLocation).toBeDefined();
      expect(updatedLocation.id).toEqual(createdLocation.id);
      expect(updatedLocation.name).toEqual("Updated Loc Name");
      expect(updatedLocation.status).toEqual("Inactive");
      const dbLocation = await prisma.location.findUnique({
        where: { id: createdLocation.id },
        include: { warehouse: true },
      });
      expect(dbLocation.name).toEqual("Updated Loc Name");
      expect(dbLocation.status).toEqual("Inactive");
    });

    it("should throw NotFoundException if location to update does not exist in tenant", async () => {
      const nonExistentId = "clxxxxxxxxxnonexistent";
      await expect(
        service.update(nonExistentId, { name: "Wont Work" }, mockCurrentUser)
      ).rejects.toThrow(NotFoundException);
    });

    it("should throw BadRequestException if updating to a non-existent warehouseId in tenant", async () => {
      const createdLocation = await service.create(
        {
          name: "Loc To Update WH",
          locationType: LocationType.ZONE,
          warehouseId: testWarehouse.id,
        },
        mockCurrentUser
      );
      const nonExistentWarehouseId = "clxxxxxxxxxnonexistent";
      const updateDto: UpdateLocationDto = {
        warehouseId: nonExistentWarehouseId,
      };

      await expect(
        service.update(createdLocation.id, updateDto, mockCurrentUser)
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.update(createdLocation.id, updateDto, mockCurrentUser)
      ).rejects.toThrow(
        `Warehouse with ID \"${nonExistentWarehouseId}\" not found within your tenant.`
      );
    });

    it("should throw BadRequestException if updating to a warehouseId user is not authorized for", async () => {
      const createdLocation = await service.create(
        {
          name: "Loc To Move",
          locationType: LocationType.ZONE,
          warehouseId: testWarehouse.id,
        },
        mockCurrentUser
      );

      const anotherValidWarehouseInTenant = await prisma.warehouse.create({
        data: {
          name: "Another WH for Tenant",
          tenantId: mockCurrentUser.tenantId,
        },
      });

      const userWithOnlyTestWarehouseAccess: AuthenticatedUser = {
        ...mockCurrentUser,
        warehouseUsers: [
          { warehouseId: testWarehouse.id, role: Role.WAREHOUSE_MEMBER },
        ],
      };

      const updateDto: UpdateLocationDto = {
        warehouseId: anotherValidWarehouseInTenant.id,
      };

      await expect(
        service.update(
          createdLocation.id,
          updateDto,
          userWithOnlyTestWarehouseAccess
        )
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.update(
          createdLocation.id,
          updateDto,
          userWithOnlyTestWarehouseAccess
        )
      ).rejects.toThrow(
        `You are not authorized to move locations to warehouse ID \"${anotherValidWarehouseInTenant.id}\".`
      );
    });

    it("should allow updating to a different existing and accessible warehouseId", async () => {
      const createdLocation = await service.create(
        {
          name: "Loc Move WH",
          locationType: LocationType.ZONE,
          warehouseId: testWarehouse.id,
        },
        mockCurrentUser
      );
      const updateDto: UpdateLocationDto = { warehouseId: testWarehouse2.id };

      const updatedLocation = await service.update(
        createdLocation.id,
        updateDto,
        mockCurrentUser
      );
      expect(updatedLocation.warehouseId).toEqual(testWarehouse2.id);
      // Warehouse relationship validates tenant access
    });
  });

  describe("remove()", () => {
    it("should delete an existing location if it belongs to tenant and accessible warehouse", async () => {
      const createdLocation = await service.create(
        {
          name: "Delete Me Loc",
          locationType: LocationType.ZONE,
          warehouseId: testWarehouse.id,
        },
        mockCurrentUser
      );

      const deletedLocation = await service.remove(
        createdLocation.id,
        mockCurrentUser
      );
      expect(deletedLocation).toBeDefined();
      expect(deletedLocation.id).toEqual(createdLocation.id);
      const dbLocation = await prisma.location.findUnique({
        where: { id: createdLocation.id },
      });
      expect(dbLocation).toBeNull();
    });

    it("should throw NotFoundException if location to delete does not exist in tenant", async () => {
      const nonExistentId = "clxxxxxxxxxnonexistent";
      await expect(
        service.remove(nonExistentId, mockCurrentUser)
      ).rejects.toThrow(NotFoundException);
    });

    it("should throw BadRequestException if location has associated records (e.g., pallets)", async () => {
      const createdLocation = await service.create(
        {
          name: "Loc With Pallet",
          locationType: LocationType.ZONE,
          warehouseId: testWarehouse.id,
        },
        mockCurrentUser
      );
      await prisma.pallet.create({
        data: {
          label: `Pallet for Loc ${createdLocation.id}`,
          locationId: createdLocation.id,
          barcode: `TEST-PALLET-${Date.now()}`,
          shipToDestination: "Test Destination",
        },
      });

      await expect(
        service.remove(createdLocation.id, mockCurrentUser)
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.remove(createdLocation.id, mockCurrentUser)
      ).rejects.toThrow(
        "Cannot delete location because it has associated records (e.g., pallets)."
      );
    });
  });
});
