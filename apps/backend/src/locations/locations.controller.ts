import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  NotFoundException,
  UseGuards,
  UsePipes,
  ValidationPipe,
  Req,
  Query,
} from "@nestjs/common";
import { LocationsService } from "./locations.service";
import { CreateLocationDto } from "./dto/create-location.dto";
import { UpdateLocationDto } from "./dto/update-location.dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { WarehousePermissionGuard } from "../auth/guards/warehouse-permission.guard";
import {
  AllowWarehouseFiltering,
  RequireLocationAccess,
  RequireWarehouseAccess,
  RequireWarehouseManager,
  RequestWithWarehouseContext,
} from "../auth/decorators/warehouse-permission.decorator";
import { EnhancedUserPayload } from "../auth/types";
import { Role } from "@prisma/client";
import { LocationCategory } from "@prisma/client";

@Controller("locations")
@UseGuards(JwtAuthGuard, WarehousePermissionGuard)
export class LocationsController {
  constructor(private readonly locationsService: LocationsService) {}

  @Post()
  @RequireWarehouseManager()
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  create(
    @Body() createLocationDto: CreateLocationDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.locationsService.create(createLocationDto, req.user);
  }

  @Get()
  @AllowWarehouseFiltering()
  findAll(
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>,
    @Query("type") type?: string,
    @Query("warehouseId") warehouseId?: string,
    @Query("search") search?: string,
    @Query("category") category?: LocationCategory
  ) {
    return this.locationsService.findAll(
      req.user,
      type,
      warehouseId,
      search,
      category
    );
  }

  @Get(":id")
  @RequireLocationAccess()
  async findOne(
    @Param("id") id: string,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    const location = await this.locationsService.findOne(id, req.user);
    if (!location) {
      throw new NotFoundException(`Location with ID "${id}" not found`);
    }
    return location;
  }

  @Patch(":id")
  @RequireLocationAccess({ minimumRole: Role.WAREHOUSE_MANAGER })
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  update(
    @Param("id") id: string,
    @Body() updateLocationDto: UpdateLocationDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.locationsService.update(id, updateLocationDto, req.user);
  }

  @Delete(":id")
  @RequireLocationAccess({ minimumRole: Role.WAREHOUSE_MANAGER })
  remove(
    @Param("id") id: string,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.locationsService.remove(id, req.user);
  }
}
