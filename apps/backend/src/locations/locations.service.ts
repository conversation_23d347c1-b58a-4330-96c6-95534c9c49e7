import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { CreateLocationDto } from "./dto/create-location.dto";
import { UpdateLocationDto } from "./dto/update-location.dto";
import {
  Location,
  Prisma,
  Role,
  LocationType,
  LocationCategory,
} from "@prisma/client";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface"; // Import centralized type

// Define a type that includes the relation
const locationWithWarehouse = Prisma.validator<Prisma.LocationDefaultArgs>()({
  include: { warehouse: true },
});
type LocationWithWarehouse = Prisma.LocationGetPayload<
  typeof locationWithWarehouse
>;

// Removed local AuthenticatedUser type

@Injectable()
export class LocationsService {
  constructor(private prisma: PrismaService) {}

  // Helper function to check if warehouse exists within the current tenant
  private async checkWarehouseExists(
    warehouseId: string,
    tenantId: string
  ): Promise<void> {
    const warehouse = await this.prisma.warehouse.findFirst({
      where: {
        id: warehouseId,
        tenantId, // Security: ensure tenant access
      },
    });
    if (!warehouse) {
      throw new BadRequestException(
        `Warehouse with ID \"${warehouseId}\" not found within your tenant.`
      );
    }
  }

  async create(
    createLocationDto: CreateLocationDto,
    currentUser: AuthenticatedUser
  ): Promise<Location> {
    const { name, warehouseId, locationType, status, category } =
      createLocationDto;

    // First, ensure the target warehouse exists and belongs to the user's tenant.
    await this.checkWarehouseExists(
      createLocationDto.warehouseId,
      currentUser.tenantId
    );

    // TENANT_ADMIN can create locations in any warehouse within their tenant.
    // Other roles must be explicitly assigned to the warehouse.
    if (currentUser.role !== Role.TENANT_ADMIN) {
      const userWarehouseIds =
        currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
      if (!userWarehouseIds.includes(createLocationDto.warehouseId)) {
        throw new BadRequestException(
          `You are not authorized to create locations in warehouse ID "${createLocationDto.warehouseId}".`
        );
      }
    }

    return this.prisma.location.create({
      data: {
        name,
        warehouseId,
        locationType,

        status,
        category,
        // Note: tenantId removed from Location - access controlled through warehouse->tenant
      },
    });
  }

  async findAll(
    currentUser: AuthenticatedUser,
    type?: string,
    warehouseId?: string,
    search?: string,
    category?: LocationCategory
  ): Promise<LocationWithWarehouse[]> {
    // Build warehouse-scoped where clause with security validation
    const whereClause: Prisma.LocationWhereInput = {
      warehouse: {
        tenantId: currentUser.tenantId, // Security: ensure tenant access
      },
    };

    if (type) {
      if (!Object.values(LocationType).includes(type as LocationType)) {
        throw new BadRequestException(`Invalid location type: ${type}`);
      }
      whereClause.locationType = type as LocationType;
    }

    if (search) {
      whereClause.name = { contains: search, mode: "insensitive" };
    }

    if (category) {
      whereClause.category = category;
    }

    if (warehouseId) {
      // If a specific warehouseId is provided, filter by it.
      // For non-TENANT_ADMIN, also ensure they have access to this specific warehouse.
      if (currentUser.role !== Role.TENANT_ADMIN) {
        const userWarehouseIds =
          currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
        if (!userWarehouseIds.includes(warehouseId)) {
          // User does not have access to the requested warehouseId
          return [];
        }
      }
      whereClause.warehouseId = warehouseId;
    } else {
      // If no specific warehouseId is provided, apply default role-based filtering.
      if (currentUser.role !== Role.TENANT_ADMIN) {
        const warehouseIdsFilter =
          currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
        if (warehouseIdsFilter.length === 0) {
          return []; // No assigned warehouses, so no locations to show
        }
        whereClause.warehouseId = { in: warehouseIdsFilter };
      }
      // For TENANT_ADMIN without a specific warehouseId, no additional warehouse filter is applied (shows all in tenant)
    }

    return this.prisma.location.findMany({
      where: whereClause,
      include: { warehouse: true },
      orderBy: [{ warehouse: { name: "asc" } }, { name: "asc" }],
    });
  }

  async findOne(
    id: string,
    currentUser: AuthenticatedUser
  ): Promise<LocationWithWarehouse> {
    // Build warehouse-scoped query with security validation
    const userWarehouseIds =
      currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];

    const whereClause: Prisma.LocationWhereInput = {
      id,
      warehouse: {
        tenantId: currentUser.tenantId, // Security: ensure tenant access
      },
    };

    // Apply warehouse filtering for non-admin users
    if (currentUser.role !== Role.TENANT_ADMIN) {
      if (userWarehouseIds.length === 0) {
        throw new NotFoundException(
          `You do not have access to any warehouses.`
        );
      }
      whereClause.warehouseId = { in: userWarehouseIds };
    }

    const location = await this.prisma.location.findFirst({
      where: whereClause,
      include: { warehouse: true },
    });

    if (!location) {
      throw new NotFoundException(
        `Location with ID "${id}" not found or not accessible within your warehouses.`
      );
    }
    return location;
  }

  async update(
    id: string,
    updateLocationDto: UpdateLocationDto,
    currentUser: AuthenticatedUser
  ): Promise<Location> {
    // findOne will check if the current user (TENANT_ADMIN or other) can access the existing location.
    const existingLocation = await this.findOne(id, currentUser);

    // If warehouseId is being changed, specific checks are needed.
    if (
      updateLocationDto.warehouseId &&
      updateLocationDto.warehouseId !== existingLocation.warehouseId
    ) {
      // Ensure the target warehouse exists within the tenant.
      await this.checkWarehouseExists(
        updateLocationDto.warehouseId,
        currentUser.tenantId
      );

      // If not TENANT_ADMIN, user must be assigned to the new target warehouse.
      if (currentUser.role !== Role.TENANT_ADMIN) {
        const userWarehouseIds =
          currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
        if (!userWarehouseIds.includes(updateLocationDto.warehouseId)) {
          throw new BadRequestException(
            `You are not authorized to move locations to warehouse ID "${updateLocationDto.warehouseId}".`
          );
        }
      }
    }

    // Prevent tenantId from being updated through this DTO.
    const { tenantId, ...dtoWithoutTenantId } = updateLocationDto as any; // eslint-disable-line @typescript-eslint/no-unused-vars

    try {
      return await this.prisma.location.update({
        where: {
          id,
          // tenantId: currentUser.tenantId, // Already scoped by findOne and a direct update here is redundant/risky
        },
        data: dtoWithoutTenantId, // Use DTO without tenantId
      });
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === "P2025"
      ) {
        throw new NotFoundException(
          `Location with ID "${id}" not found within your warehouses to update.`
        );
      }
      throw new Error(`Could not update location: ${error.message}`);
    }
  }

  async remove(id: string, currentUser: AuthenticatedUser): Promise<Location> {
    // findOne will check if the current user (TENANT_ADMIN or other) can access the location.
    await this.findOne(id, currentUser);

    try {
      return await this.prisma.location.delete({
        where: {
          id,
          // tenantId: currentUser.tenantId, // findOne already scoped this to the tenant.
        },
      });
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === "P2003" // Foreign key constraint failed
      ) {
        throw new BadRequestException( // Changed to BadRequestException for better HTTP semantics
          "Cannot delete location because it has associated records (e.g., pallets)."
        );
      }
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === "P2025"
      ) {
        throw new NotFoundException(
          `Location with ID "${id}" not found within your warehouses to delete.`
        );
      }
      throw error;
    }
  }
}
