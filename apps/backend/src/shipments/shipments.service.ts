import { Injectable, NotFoundException } from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import {
  Item,
  Location,
  Pallet,
  PalletItem,
  Prisma,
  Role,
} from "@prisma/client";
import { QueryShipmentDto } from "./dto/query-shipment.dto";
import { ShipmentListItemDto } from "./dto/shipment-list-response.dto";
import { UpdateShipmentDto } from "./dto/update-shipment.dto";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";

@Injectable()
export class ShipmentsService {
  constructor(private readonly prisma: PrismaService) {}

  async getShipmentSummary(
    id: string,
    currentUser: AuthenticatedUser,
    warehouseId?: string
  ) {
    // Build warehouse-scoped where clause with security validation
    const whereClause: Prisma.ShipmentWhereInput = {
      id: id,
      purchaseOrder: {
        warehouse: {
          tenantId: currentUser.tenantId, // Security: ensure tenant access
        },
      },
    };

    // Apply warehouse filtering for non-admin users
    if (currentUser.role !== Role.TENANT_ADMIN) {
      const userWarehouseIds =
        currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
      if (userWarehouseIds.length === 0) {
        throw new NotFoundException(`Shipment with ID ${id} not found`);
      }
      whereClause.purchaseOrder.warehouseId = { in: userWarehouseIds };
    }

    // Apply specific warehouse filter if provided
    if (warehouseId) {
      whereClause.purchaseOrder.warehouseId = warehouseId;
    }

    const shipment = await this.prisma.shipment.findFirst({
      where: whereClause,
      include: {
        purchaseOrder: true,
        pallets: {
          include: {
            location: true,
            palletItems: {
              include: {
                item: true,
              },
            },
          },
        },
      },
    });

    if (!shipment) {
      throw new NotFoundException(`Shipment with ID ${id} not found`);
    }

    const palletsByDestination = shipment.pallets.reduce(
      (acc, pallet) => {
        const dest = pallet.shipToDestination || "Unassigned";
        if (!acc[dest]) {
          acc[dest] = [];
        }
        acc[dest].push(pallet);
        return acc;
      },
      {} as Record<
        string,
        (Pallet & {
          location: Location | null;
          palletItems: (PalletItem & { item: Item })[];
        })[]
      >
    );

    return {
      shipmentId: shipment.id,
      purchaseOrderNumber: shipment.purchaseOrder.poNumber,
      status: shipment.status,
      palletsByDestination,
    };
  }

  async findByPoNumber(
    poNumber: string,
    currentUser: AuthenticatedUser,
    warehouseId?: string
  ) {
    console.log(
      `[ShipmentsService] Searching for PO #${poNumber} for user ${currentUser.id}`
    );

    // Build warehouse-scoped where clause with security validation
    const whereClause: Prisma.PurchaseOrderWhereInput = {
      poNumber: poNumber,
      warehouse: {
        tenantId: currentUser.tenantId, // Security: ensure tenant access
      },
    };

    // Apply warehouse filtering for non-admin users
    if (currentUser.role !== Role.TENANT_ADMIN) {
      const userWarehouseIds =
        currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
      if (userWarehouseIds.length === 0) {
        throw new NotFoundException(
          `Shipment with PO Number #${poNumber} not found`
        );
      }
      whereClause.warehouseId = { in: userWarehouseIds };
    }

    // Apply specific warehouse filter if provided
    if (warehouseId) {
      whereClause.warehouseId = warehouseId;
    }

    const purchaseOrder = await this.prisma.purchaseOrder.findFirst({
      where: whereClause,
      include: {
        shipments: true,
      },
    });

    console.log(
      "[ShipmentsService] Found PO:",
      JSON.stringify(purchaseOrder, null, 2)
    );

    if (
      !purchaseOrder ||
      !purchaseOrder.shipments ||
      purchaseOrder.shipments.length === 0
    ) {
      console.error(
        "[ShipmentsService] PO found, but no shipment associated, or PO not found."
      );
      throw new NotFoundException(
        `Shipment with PO Number #${poNumber} not found`
      );
    }

    return purchaseOrder.shipments[0];
  }

  async findAll(
    currentUser: AuthenticatedUser,
    queryDto: QueryShipmentDto,
    warehouseId?: string
  ): Promise<{ data: ShipmentListItemDto[]; count: number }> {
    const {
      page = 1,
      limit = 10,
      status,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = queryDto;
    const skip = (page - 1) * limit;

    // Build warehouse-scoped where clause with security validation
    const whereClause: Prisma.ShipmentWhereInput = {
      purchaseOrder: {
        warehouse: {
          tenantId: currentUser.tenantId, // Security: ensure tenant access
        },
      },
    };

    // Apply warehouse filtering for non-admin users
    if (currentUser.role !== Role.TENANT_ADMIN) {
      const userWarehouseIds =
        currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
      if (userWarehouseIds.length === 0) {
        return { data: [], count: 0 }; // No warehouse access
      }
      whereClause.purchaseOrder.warehouseId = { in: userWarehouseIds };
    }

    // Apply specific warehouse filter if provided
    if (warehouseId) {
      whereClause.purchaseOrder.warehouseId = warehouseId;
    }

    if (status) {
      whereClause.status = status;
    }

    const shipments = await this.prisma.shipment.findMany({
      where: whereClause,
      include: {
        purchaseOrder: true,
        _count: {
          select: {
            pallets: true,
          },
        },
      },
      orderBy: {
        [sortBy]: sortOrder,
      },
      skip: skip,
      take: limit,
    });

    const totalCount = await this.prisma.shipment.count({
      where: whereClause,
    });

    const data: ShipmentListItemDto[] = shipments.map((shipment) => ({
      id: shipment.id,
      referenceNumber: shipment.referenceNumber,
      status: shipment.status,
      createdAt: shipment.createdAt,
      updatedAt: shipment.updatedAt,
      poNumber: shipment.purchaseOrder.poNumber,
      palletCount: shipment._count.pallets,
      supplier: shipment.purchaseOrder.supplier,
    }));

    return {
      data,
      count: totalCount,
    };
  }

  async updateShipment(
    id: string,
    currentUser: AuthenticatedUser,
    updateShipmentDto: UpdateShipmentDto,
    warehouseId?: string
  ) {
    // Build warehouse-scoped where clause with security validation
    const whereClause: Prisma.ShipmentWhereInput = {
      id: id,
      purchaseOrder: {
        warehouse: {
          tenantId: currentUser.tenantId, // Security: ensure tenant access
        },
      },
    };

    // Apply warehouse filtering for non-admin users
    if (currentUser.role !== Role.TENANT_ADMIN) {
      const userWarehouseIds =
        currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
      if (userWarehouseIds.length === 0) {
        throw new NotFoundException(`Shipment with ID ${id} not found`);
      }
      whereClause.purchaseOrder.warehouseId = { in: userWarehouseIds };
    }

    // Apply specific warehouse filter if provided
    if (warehouseId) {
      whereClause.purchaseOrder.warehouseId = warehouseId;
    }

    // First verify the shipment exists and user has access
    const existingShipment = await this.prisma.shipment.findFirst({
      where: whereClause,
    });

    if (!existingShipment) {
      throw new NotFoundException(`Shipment with ID ${id} not found`);
    }

    // Update the shipment
    return this.prisma.shipment.update({
      where: { id },
      data: updateShipmentDto,
      include: {
        purchaseOrder: true,
      },
    });
  }
}
