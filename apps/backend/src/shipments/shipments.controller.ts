import {
  Controller,
  Get,
  Param,
  Query,
  Req,
  UseGuards,
  Patch,
  Body,
} from "@nestjs/common";
import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { WarehousePermissionGuard } from "../auth/guards/warehouse-permission.guard";
import {
  AllowWarehouseFiltering,
  RequireShipmentAccess,
  RequireWarehouseAccess,
  RequestWithWarehouseContext,
} from "../auth/decorators/warehouse-permission.decorator";
import { EnhancedUserPayload } from "../auth/types";
import { Role, Shipment } from "@quildora/types";
import { ShipmentsService } from "./shipments.service";
import { ShipmentSummaryResponseDto } from "../purchase-orders/dto/shipment-summary.dto";
import { QueryShipmentDto } from "./dto/query-shipment.dto";
import { ShipmentListResponseDto } from "./dto/shipment-list-response.dto";
import { UpdateShipmentDto } from "./dto/update-shipment.dto";

@ApiTags("Shipments")
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, WarehousePermissionGuard)
@Controller("shipments")
export class ShipmentsController {
  constructor(private readonly shipmentsService: ShipmentsService) {}

  @Get()
  @AllowWarehouseFiltering()
  @ApiOperation({ summary: "Get all shipments for the tenant" })
  @ApiQuery({
    name: "page",
    required: false,
    type: Number,
    description: "Page number for pagination",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Number of items per page",
  })
  @ApiQuery({
    name: "status",
    required: false,
    type: String,
    description: "Filter by shipment status",
  })
  @ApiQuery({
    name: "sortBy",
    required: false,
    type: String,
    description: "Field to sort by (e.g., createdAt)",
  })
  @ApiQuery({
    name: "sortOrder",
    required: false,
    enum: ["asc", "desc"],
    description: "Sort order (asc or desc)",
  })
  @ApiQuery({
    name: "warehouseId",
    required: false,
    type: String,
    description: "Filter by warehouse ID",
  })
  @ApiResponse({
    status: 200,
    description: "List of shipments.",
    type: ShipmentListResponseDto,
  })
  async findAll(
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>,
    @Query() queryDto: QueryShipmentDto,
    @Query("warehouseId") warehouseId?: string
  ) {
    const result = await this.shipmentsService.findAll(
      req.user,
      queryDto,
      warehouseId
    );

    const { page = 1, limit = 10 } = queryDto;
    const totalPages = Math.ceil(result.count / limit);

    return {
      ...result,
      page,
      limit,
      totalPages,
    };
  }

  @Get("summary/:id")
  @RequireShipmentAccess({
    extractFromEntity: { entityType: "shipment", paramName: "id" },
  })
  @ApiOperation({ summary: "Get a summary of a shipment" })
  @ApiResponse({
    status: 200,
    description: "The shipment summary.",
    type: ShipmentSummaryResponseDto,
  })
  getSummary(
    @Param("id") id: string,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>,
    @Query("warehouseId") warehouseId?: string
  ) {
    return this.shipmentsService.getShipmentSummary(id, req.user, warehouseId);
  }

  @Get("by-po/:poNumber")
  @RequireWarehouseAccess()
  @ApiOperation({ summary: "Find a shipment by its Purchase Order number" })
  @ApiResponse({ status: 200, description: "The shipment record." })
  @ApiResponse({ status: 404, description: "Shipment not found." })
  findByPoNumber(
    @Param("poNumber") poNumber: string,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>,
    @Query("warehouseId") warehouseId?: string
  ) {
    return this.shipmentsService.findByPoNumber(
      poNumber,
      req.user,
      warehouseId
    );
  }

  @Patch(":id")
  @RequireShipmentAccess()
  @ApiOperation({ summary: "Update a shipment" })
  @ApiResponse({ status: 200, description: "The updated shipment." })
  @ApiResponse({ status: 404, description: "Shipment not found." })
  updateShipment(
    @Param("id") id: string,
    @Body() updateShipmentDto: UpdateShipmentDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>,
    @Query("warehouseId") warehouseId?: string
  ) {
    return this.shipmentsService.updateShipment(
      id,
      req.user,
      updateShipmentDto,
      warehouseId
    );
  }
}
