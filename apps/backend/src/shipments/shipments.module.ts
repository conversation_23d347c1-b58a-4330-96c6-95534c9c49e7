import { Module } from "@nestjs/common";
import { ShipmentsController } from "./shipments.controller";
import { ShipmentsService } from "./shipments.service";
import { PrismaService } from "../prisma/prisma.service";
import { WarehousesModule } from "../warehouses/warehouses.module";

@Module({
  imports: [WarehousesModule],
  controllers: [ShipmentsController],
  providers: [ShipmentsService, PrismaService],
})
export class ShipmentsModule {}
