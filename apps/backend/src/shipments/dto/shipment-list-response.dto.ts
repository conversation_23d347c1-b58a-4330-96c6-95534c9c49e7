import { ApiProperty } from '@nestjs/swagger';

export class ShipmentListItemDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  referenceNumber: string | null;

  @ApiProperty()
  status: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  poNumber: string;

  @ApiProperty()
  palletCount: number;

  @ApiProperty()
  supplier: string | null;
}

export class ShipmentListResponseDto {
  @ApiProperty({ type: [ShipmentListItemDto] })
  data: ShipmentListItemDto[];

  @ApiProperty()
  count: number;

  @ApiProperty()
  page: number;

  @ApiProperty()
  limit: number;

  @ApiProperty()
  totalPages: number;
}
