// apps/backend/src/prisma/prisma.module.ts

import { Global, Module } from "@nestjs/common";
import { PrismaService } from "./prisma.service";

@Global() // Makes PrismaService available globally without importing PrismaModule everywhere
@Module({
  providers: [PrismaService], // Register PrismaService as a provider within this module
  exports: [PrismaService], // Export PrismaService so other modules can inject it
})
export class PrismaModule {}
