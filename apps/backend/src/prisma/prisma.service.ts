import {
  Injectable,
  OnModuleInit,
  OnApplicationShutdown,
} from "@nestjs/common";
import { PrismaClient } from "@prisma/client";

@Injectable()
export class PrismaService
  extends PrismaClient
  implements OnModuleInit, OnApplicationShutdown
{
  async onModuleInit() {
    // Optional: Log or perform actions on connection
    await this.$connect();
  }

  async onApplicationShutdown(signal?: string) {
    console.log(`Prisma client disconnecting on signal: ${signal}`);
    await this.$disconnect();
  }
}
