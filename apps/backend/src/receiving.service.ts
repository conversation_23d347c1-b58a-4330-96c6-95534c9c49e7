import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { PrismaService } from "./prisma/prisma.service";
import { ReceiveItemsDto } from "./receiving/dto/receive-items.dto";
import { LocationCategory, Prisma } from "@prisma/client";
import { AuthenticatedUser } from "./auth/types/authenticated-user.interface";
import { AuditLogService } from "./audit-log/audit-log.service";

// Define the return type to include pallet items
const palletWithItems = Prisma.validator<Prisma.PalletDefaultArgs>()({
  include: { palletItems: true },
});
type PalletWithItems = Prisma.PalletGetPayload<typeof palletWithItems>;

@Injectable()
export class ReceivingService {
  constructor(
    private prisma: PrismaService,
    private auditLogService: AuditLogService
  ) {}

  async receiveItems(
    dto: ReceiveItemsDto,
    currentUser: AuthenticatedUser
  ): Promise<PalletWithItems> {
    const {
      poNumber,
      receivingLocationId,
      items,
      barcode,
      shipToDestination,
      destinationCode,
      description,
    } = dto;

    if (
      (!items || items.length === 0) &&
      (!description || description.trim().length === 0)
    ) {
      throw new BadRequestException(
        "A pallet must have at least one item or a description."
      );
    }

    // 2. Validate Location first to get warehouse information
    const location = await this.prisma.location.findUnique({
      where: { id: receivingLocationId },
      include: { warehouse: { select: { tenantId: true, id: true } } },
    });

    if (!location) {
      throw new NotFoundException(
        `Receiving location with ID "${receivingLocationId}" not found.`
      );
    }
    if (location.category !== LocationCategory.Receiving) {
      throw new BadRequestException(
        "Invalid location. Pallets can only be received at a Receiving location."
      );
    }

    // 2b. Enforce warehouse scoping.
    if (location.warehouse.tenantId !== currentUser.tenantId) {
      throw new BadRequestException(
        `Receiving location with ID "${receivingLocationId}" is not in your tenant.`
      );
    }

    // 1. Find or create the Purchase Order and its associated Shipment.
    const purchaseOrder = await this.prisma.purchaseOrder.upsert({
      where: {
        tenantId_poNumber: {
          tenantId: currentUser.tenantId,
          poNumber: poNumber,
        },
      },
      update: {
        status: "Receiving",
        warehouseId: location.warehouse.id, // Ensure warehouse is set on update
      },
      create: {
        poNumber: poNumber,
        tenantId: currentUser.tenantId,
        warehouseId: location.warehouse.id, // Set warehouse from receiving location
        status: "Receiving",
        shipments: {
          create: {
            status: "Processing",
            tenantId: currentUser.tenantId,
          },
        },
      },
      include: {
        shipments: true,
      },
    });

    if (!purchaseOrder.shipments || purchaseOrder.shipments.length === 0) {
      // This should theoretically not happen with the upsert logic, but it's a good safeguard.
      throw new Error(
        "Failed to find or create a shipment for the purchase order."
      );
    }
    const shipmentId = purchaseOrder.shipments[0].id;

    if (currentUser.role !== "TENANT_ADMIN") {
      const userWarehouseIds =
        currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
      if (!userWarehouseIds.includes(location.warehouseId)) {
        throw new BadRequestException(
          `You are not authorized to receive items into warehouse ID "${location.warehouseId}".`
        );
      }
    }

    // 3. Validate all Items exist
    const itemIds = items.map((item) => item.itemId);
    if (itemIds.length > 0) {
      const foundItems = await this.prisma.item.findMany({
        where: {
          id: { in: itemIds },
          tenantId: currentUser.tenantId,
        },
        select: { id: true },
      });
      if (foundItems.length !== itemIds.length) {
        const foundItemIds = new Set(foundItems.map((item) => item.id));
        const missingItemIds = itemIds.filter((id) => !foundItemIds.has(id));
        throw new NotFoundException(
          `Items with IDs not found: ${missingItemIds.join(", ")}`
        );
      }
    }

    // 4. Perform creation within a transaction
    return this.prisma.$transaction(async (tx) => {
      // Ensure WarehouseItem links exist for all received items
      if (items && items.length > 0) {
        for (const itemDetail of items) {
          await tx.warehouseItem.upsert({
            where: {
              tenant_warehouse_item_unique: {
                tenantId: currentUser.tenantId,
                warehouseId: location.warehouseId,
                itemId: itemDetail.itemId,
              },
            },
            update: {},
            create: {
              tenantId: currentUser.tenantId,
              warehouseId: location.warehouseId,
              itemId: itemDetail.itemId,
            },
          });
        }
      }

      // Create the new Pallet and its PalletItems
      const newPallet = await tx.pallet.create({
        data: {
          barcode,
          label: `Pallet ${barcode}`,
          description,
          shipToDestination,
          destinationCode,
          status: "Receiving",
          locationId: receivingLocationId,
          shipmentId: shipmentId, // Link the pallet to the shipment
          palletItems: {
            create: items.map((item) => ({
              itemId: item.itemId,
              quantity: item.quantity,
            })),
          },
        },
        include: { palletItems: true },
      });

      // Create audit log entry for pallet creation via receiving workflow
      await this.auditLogService.create(
        {
          userId: currentUser.id,
          userEmail: currentUser.email,
          action: "CREATE_PALLET_RECEIVING",
          entity: "Pallet",
          entityId: newPallet.id,
          tenantId: currentUser.tenantId,
          details: {
            barcode: newPallet.barcode,
            label: newPallet.label,
            description: newPallet.description,
            shipToDestination: newPallet.shipToDestination,
            status: newPallet.status,
            locationName: location.name,
            poNumber: poNumber,
            itemCount: items.length,
            createdVia: "receiving_workflow",
          },
        },
        tx
      );

      return newPallet;
    });
  }
}
