import {
  Controller,
  Post,
  Body,
  UsePipes,
  ValidationPipe,
  UseGuards,
  Req,
} from "@nestjs/common";
import { ReceivingService } from "./receiving.service";
import { ReceiveItemsDto } from "./receiving/dto/receive-items.dto";
import { JwtAuthGuard } from "./auth/guards/jwt-auth.guard";
import { WarehousePermissionGuard } from "./auth/guards/warehouse-permission.guard";
import {
  RequireWarehouseAccess,
  RequestWithWarehouseContext,
} from "./auth/decorators/warehouse-permission.decorator";
import { EnhancedUserPayload } from "./auth/types";

@Controller("receiving")
@UseGuards(JwtAuthGuard, WarehousePermissionGuard)
export class ReceivingController {
  constructor(private readonly receivingService: ReceivingService) {}

  @Post()
  @RequireWarehouseAccess()
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  receiveItems(
    @Body() receiveItemsDto: ReceiveItemsDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.receivingService.receiveItems(receiveItemsDto, req.user);
  }
}
