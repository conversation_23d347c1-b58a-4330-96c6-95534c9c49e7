import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
  ForbiddenException,
} from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { CreatePalletDto } from "./dto/create-pallet.dto";
import { UpdatePalletDto } from "./dto/update-pallet.dto";
import { QueryPalletDto } from "./dto/query-pallet.dto";
import { MovePalletDto } from "./dto/move-pallet.dto";
import { ReleasePalletDto } from "./dto/release-pallet.dto";
import { PickItemsDto } from "./dto/pick-items.dto";
import { Pallet, Location, Prisma } from "@prisma/client";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";
import { AuditLogService } from "../audit-log/audit-log.service";
import { PlacardDataResponseDto } from "./dto/placard.dto";
import { PlacardPdfService } from "./placard-pdf.service";
import { PDFDocument } from "pdf-lib";
import { PrintPlacardsQueryDto } from "./dto/print-placards.dto";

const palletWithAllRelations = Prisma.validator<Prisma.PalletDefaultArgs>()({
  include: {
    location: true,
    palletItems: {
      include: {
        item: true,
      },
    },
  },
});
type PalletWithAllRelations = Prisma.PalletGetPayload<
  typeof palletWithAllRelations
>;

@Injectable()
export class PalletsService {
  private readonly logger = new Logger(PalletsService.name);
  constructor(
    private prisma: PrismaService,
    private auditLogService: AuditLogService,
    private placardPdfService: PlacardPdfService
  ) {}

  private mapPalletToPlacardDto(
    pallet: PalletWithAllRelations
  ): PlacardDataResponseDto {
    return {
      palletId: pallet.id,
      barcode: pallet.barcode,
      shipToDestination: pallet.shipToDestination,
      destinationCode: pallet.destinationCode,
      dateCreated: pallet.dateCreated.toISOString(),
      currentLocation: {
        name: pallet.location?.name || "N/A",
      },
      description: pallet.description,
      contents: pallet.palletItems.map((pi) => ({
        itemId: pi.item.id,
        itemName: pi.item.name,
        quantity: pi.quantity,
        unit: pi.item.unitOfMeasure,
      })),
    };
  }

  private async checkLocationExists(
    locationId: string,
    tenantId: string
  ): Promise<Location & { warehouse: { id: string; tenantId: string } }> {
    if (!locationId) {
      throw new BadRequestException("Location ID must be provided.");
    }
    const location = await this.prisma.location.findFirst({
      where: {
        id: locationId,
        warehouse: {
          tenantId, // Security: ensure tenant access through warehouse
        },
      },
      include: {
        warehouse: true,
      },
    });
    if (!location) {
      throw new NotFoundException(
        `Location with ID "${locationId}" not found within your tenant's warehouses.`
      );
    }
    return location;
  }

  async generatePlacardsPdf(
    query: PrintPlacardsQueryDto,
    currentUser: AuthenticatedUser
  ): Promise<Uint8Array> {
    this.logger.log(
      `Generating placards PDF for tenant ${
        currentUser.tenantId
      } with query: ${JSON.stringify(query)}`
    );

    let palletsToPrint: PalletWithAllRelations[];
    let requestedPalletIds: string[] = [];

    if (query.palletId) {
      // For single pallet requests, we need to get the pallet's destination first,
      // then get ALL pallets with the same destination for proper numbering
      const requestedPallet = await this.prisma.pallet.findFirst({
        where: {
          id: query.palletId,
          location: {
            warehouse: {
              tenantId: currentUser.tenantId, // Security: ensure tenant access
              // Note: No warehouse filtering for placard generation - shows all tenant pallets
            },
          },
        },
        include: palletWithAllRelations.include,
      });

      if (!requestedPallet) {
        throw new NotFoundException(
          `Pallet with ID "${query.palletId}" not found or not accessible.`
        );
      }

      // Get all pallets with the same destination for proper numbering
      palletsToPrint = await this.prisma.pallet.findMany({
        where: {
          location: {
            warehouse: {
              tenantId: currentUser.tenantId, // Security: ensure tenant access
              // Note: No warehouse filtering for placard generation - shows all tenant pallets
            },
          },
          shipToDestination: {
            equals: requestedPallet.shipToDestination,
            mode: "insensitive",
          },
        },
        include: palletWithAllRelations.include,
        orderBy: [{ shipToDestination: "asc" }, { dateCreated: "asc" }],
      });

      // Track which pallet was specifically requested
      requestedPalletIds = [query.palletId];
    } else if (query.destination) {
      // For destination requests, get all pallets for that destination
      palletsToPrint = await this.prisma.pallet.findMany({
        where: {
          location: {
            warehouse: {
              tenantId: currentUser.tenantId, // Security: ensure tenant access
              // Note: No warehouse filtering for placard generation - shows all tenant pallets
            },
          },
          shipToDestination: {
            equals: query.destination,
            mode: "insensitive",
          },
        },
        include: palletWithAllRelations.include,
        orderBy: [{ shipToDestination: "asc" }, { dateCreated: "asc" }],
      });

      // All pallets are requested for destination queries
      requestedPalletIds = palletsToPrint.map((p) => p.id);
    } else {
      throw new BadRequestException(
        "Either palletId or destination must be provided for placard generation."
      );
    }

    if (palletsToPrint.length === 0) {
      throw new NotFoundException(
        "No pallets found matching the specified criteria."
      );
    }

    // Group pallets by destination with case-insensitive handling
    const palletsByDestination = palletsToPrint.reduce((acc, pallet) => {
      // Use trimmed and normalized destination as key
      const destination = pallet.shipToDestination?.trim() || "unknown";
      const key = destination.toLowerCase();
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(pallet);
      return acc;
    }, {} as Record<string, PalletWithAllRelations[]>);

    // Sort pallets within each destination group by dateCreated for consistent numbering
    Object.values(palletsByDestination).forEach((pallets) => {
      pallets.sort((a, b) => a.dateCreated.getTime() - b.dateCreated.getTime());
    });

    // Debug logging for pallet grouping
    this.logger.debug(
      `Grouped ${palletsToPrint.length} pallets into ${
        Object.keys(palletsByDestination).length
      } destinations:`
    );
    Object.entries(palletsByDestination).forEach(([dest, pallets]) => {
      const originalDestinations = pallets
        .map((p) => p.shipToDestination)
        .join(", ");
      this.logger.debug(
        `  "${dest}": ${pallets.length} pallets (original: ${originalDestinations})`
      );
    });

    const pdfDoc = await PDFDocument.create();

    // Only generate placards for the specifically requested pallets
    const palletsToGenerate = palletsToPrint.filter((pallet) =>
      requestedPalletIds.includes(pallet.id)
    );

    for (const pallet of palletsToGenerate) {
      // Use the same normalization logic for lookup
      const destination = pallet.shipToDestination?.trim() || "unknown";
      const destinationKey = destination.toLowerCase();
      const destinationGroup = palletsByDestination[destinationKey];

      if (!destinationGroup) {
        throw new Error(`Destination group not found for pallet ${pallet.id}`);
      }

      const palletIndex = destinationGroup.findIndex((p) => p.id === pallet.id);
      const palletCountStr = `Pallet ${palletIndex + 1} of ${
        destinationGroup.length
      }`;

      this.logger.debug(
        `Generating placard for pallet ${pallet.id}: ${palletCountStr} (destination: ${destination})`
      );

      const placardData = this.mapPalletToPlacardDto(pallet);
      await this.placardPdfService.createPlacardPage(
        pdfDoc,
        placardData,
        palletCountStr
      );
    }

    this.logger.log("PDF generation complete.");
    return pdfDoc.save();
  }

  async create(
    createPalletDto: CreatePalletDto,
    currentUser: AuthenticatedUser
  ): Promise<Pallet> {
    const { locationId, items, ...palletData } = createPalletDto;

    if (!locationId) {
      throw new BadRequestException(
        "Initial Location ID is required to create a pallet."
      );
    }
    const location = await this.checkLocationExists(
      locationId,
      currentUser.tenantId
    );

    if (currentUser.role !== "TENANT_ADMIN") {
      const userWarehouseIds =
        currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
      if (!userWarehouseIds.includes(location.warehouseId)) {
        throw new ForbiddenException(
          `You are not authorized to create pallets in the warehouse of location ID "${location.id}".`
        );
      }
    }

    const newPallet = await this.prisma.pallet.create({
      data: {
        ...palletData,
        label: palletData.label || `Pallet-${new Date().getTime()}`,
        // Note: tenantId removed - access controlled through location->warehouse->tenant
        locationId: locationId,
        palletItems: items
          ? {
              create: items.map((item) => ({
                ...item,
                // Note: tenantId removed - access controlled through pallet->location->warehouse->tenant
              })),
            }
          : undefined,
      },
    });

    await this.auditLogService.create({
      userId: currentUser.id,
      userEmail: currentUser.email,
      action: "CREATE_PALLET",
      entity: "PALLET",
      entityId: newPallet.id,
      tenantId: currentUser.tenantId,
      details: {
        barcode: newPallet.barcode,
        label: newPallet.label,
        description: newPallet.description,
        shipToDestination: newPallet.shipToDestination,
        status: newPallet.status,
        locationName: location.name,
        itemCount: items?.length || 0,
        createdVia: "create_pallet_dialog",
      },
    });

    return newPallet;
  }

  async findAll(
    currentUser: AuthenticatedUser,
    query: QueryPalletDto,
    warehouseId?: string
  ): Promise<Pallet[]> {
    const userWarehouseIds =
      currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];

    // Build warehouse-scoped where clause with security validation
    const whereClause: Prisma.PalletWhereInput = {
      location: {
        warehouse: {
          tenantId: currentUser.tenantId, // Security: ensure tenant access
        },
      },
    };

    if (query.shipToDestination) {
      whereClause.shipToDestination = {
        contains: query.shipToDestination,
        mode: "insensitive",
      };
    }

    if (query.description) {
      whereClause.description = {
        contains: query.description,
        mode: "insensitive",
      };
    }

    if (query.locationId) {
      whereClause.locationId = query.locationId;
    }

    if (query.status) {
      whereClause.status = query.status;
    }

    // Apply warehouse filtering for non-admin users
    if (currentUser.role !== "TENANT_ADMIN") {
      if (userWarehouseIds.length === 0) return [];
      whereClause.location = {
        warehouse: {
          tenantId: currentUser.tenantId, // Security: ensure tenant access
        },
        warehouseId: { in: userWarehouseIds },
      };
    }

    // Apply specific warehouse filter if provided
    if (warehouseId) {
      whereClause.location = {
        warehouse: {
          tenantId: currentUser.tenantId, // Security: ensure tenant access
        },
        warehouseId: warehouseId,
      };
    }

    return this.prisma.pallet.findMany({
      where: whereClause,
      include: {
        location: {
          include: { warehouse: true },
        },
        palletItems: {
          select: {
            id: true,
            quantity: true,
            dateAdded: true,
            item: true,
          },
        },
      },
      orderBy: { lastMovedDate: "desc" },
    });
  }

  async getDestinations(
    currentUser: AuthenticatedUser,
    warehouseId?: string
  ): Promise<string[]> {
    this.logger.log(
      `[getDestinations] Fetching unique destinations for tenant: ${currentUser.tenantId}`
    );

    // Get user's warehouse access for authorization
    const userWarehouseIds =
      currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];

    // Build warehouse-scoped where clause with security validation
    const whereClause: Prisma.PalletWhereInput = {
      location: {
        warehouse: {
          tenantId: currentUser.tenantId, // Security: ensure tenant access
        },
      },
      shipToDestination: { not: null },
      status: { not: "Released" }, // Only include non-released pallets
    };

    // Apply warehouse filtering for non-admin users
    if (currentUser.role !== "TENANT_ADMIN") {
      if (userWarehouseIds.length === 0) return [];
      whereClause.location = {
        warehouse: {
          tenantId: currentUser.tenantId,
        },
        warehouseId: { in: userWarehouseIds },
      };
    }

    // Apply specific warehouse filter if provided
    if (warehouseId) {
      whereClause.location = {
        warehouse: {
          tenantId: currentUser.tenantId, // Security: ensure tenant access
        },
        warehouseId: warehouseId,
      };
    }

    const pallets = await this.prisma.pallet.findMany({
      where: whereClause,
      select: { shipToDestination: true },
      distinct: ["shipToDestination"],
    });

    // Extract and filter out null values, then sort
    const destinations = pallets
      .map((p) => p.shipToDestination)
      .filter((dest): dest is string => dest !== null)
      .sort();

    this.logger.log(
      `[getDestinations] Found ${destinations.length} unique destinations`
    );

    return destinations;
  }

  async getDestinationsWithCodes(
    currentUser: AuthenticatedUser,
    warehouseId?: string
  ): Promise<{ name: string; code?: string | null; displayName: string }[]> {
    this.logger.log(
      `[getDestinationsWithCodes] Fetching destinations with codes for tenant: ${currentUser.tenantId}`
    );

    // Get user's warehouse access for authorization
    const userWarehouseIds =
      currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];

    // Build warehouse-scoped where clause with security validation
    const whereClause: Prisma.PalletWhereInput = {
      location: {
        warehouse: {
          tenantId: currentUser.tenantId, // Security: ensure tenant access
        },
      },
      shipToDestination: { not: null },
      status: { not: "Released" }, // Only include non-released pallets
    };

    // Apply warehouse filtering for non-admin users
    if (currentUser.role !== "TENANT_ADMIN") {
      if (userWarehouseIds.length === 0) return [];
      whereClause.location = {
        warehouse: {
          tenantId: currentUser.tenantId,
        },
        warehouseId: { in: userWarehouseIds },
      };
    }

    // Apply specific warehouse filter if provided
    if (warehouseId) {
      whereClause.location = {
        warehouse: {
          tenantId: currentUser.tenantId, // Security: ensure tenant access
        },
        warehouseId: warehouseId,
      };
    }

    const pallets = await this.prisma.pallet.findMany({
      where: whereClause,
      select: {
        shipToDestination: true,
        destinationCode: true,
      },
      distinct: ["shipToDestination", "destinationCode"],
    });

    // Transform to destination response format
    const destinations = pallets
      .filter((p) => p.shipToDestination !== null)
      .map((p) => ({
        name: p.shipToDestination!,
        code: p.destinationCode,
        displayName: p.destinationCode
          ? `${p.shipToDestination} (${p.destinationCode})`
          : p.shipToDestination!,
      }))
      .sort((a, b) => a.name.localeCompare(b.name));

    this.logger.log(
      `[getDestinationsWithCodes] Found ${destinations.length} unique destinations with codes`
    );

    return destinations;
  }

  async findDestinationByCode(
    code: string,
    currentUser: AuthenticatedUser,
    warehouseId?: string
  ): Promise<{
    name: string;
    code?: string | null;
    displayName: string;
  } | null> {
    this.logger.log(
      `[findDestinationByCode] Looking up destination by code: ${code} for tenant: ${currentUser.tenantId}`
    );

    // Get user's warehouse access for authorization
    const userWarehouseIds =
      currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];

    // Build warehouse-scoped where clause with security validation
    const whereClause: Prisma.PalletWhereInput = {
      location: {
        warehouse: {
          tenantId: currentUser.tenantId, // Security: ensure tenant access
        },
      },
      destinationCode: code,
      shipToDestination: { not: null },
    };

    // Apply warehouse filtering for non-admin users
    if (currentUser.role !== "TENANT_ADMIN") {
      if (userWarehouseIds.length === 0) return null;
      whereClause.location = {
        warehouse: {
          tenantId: currentUser.tenantId,
        },
        warehouseId: { in: userWarehouseIds },
      };
    }

    // Apply specific warehouse filter if provided
    if (warehouseId) {
      whereClause.location = {
        warehouse: {
          tenantId: currentUser.tenantId, // Security: ensure tenant access
        },
        warehouseId: warehouseId,
      };
    }

    const pallet = await this.prisma.pallet.findFirst({
      where: whereClause,
      select: {
        shipToDestination: true,
        destinationCode: true,
      },
    });

    if (!pallet) {
      this.logger.log(
        `[findDestinationByCode] No destination found for code: ${code}`
      );
      return null;
    }

    const result = {
      name: pallet.shipToDestination!,
      code: pallet.destinationCode,
      displayName: `${pallet.shipToDestination} (${code})`,
    };

    this.logger.log(
      `[findDestinationByCode] Found destination: ${result.displayName}`
    );

    return result;
  }

  async findDestinationsByName(
    nameQuery: string,
    currentUser: AuthenticatedUser,
    warehouseId?: string
  ): Promise<{ name: string; code?: string | null; displayName: string }[]> {
    this.logger.log(
      `[findDestinationsByName] Searching destinations by name: ${nameQuery} for tenant: ${currentUser.tenantId}`
    );

    // Get user's warehouse access for authorization
    const userWarehouseIds =
      currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];

    // Build warehouse-scoped where clause with security validation
    const whereClause: Prisma.PalletWhereInput = {
      location: {
        warehouse: {
          tenantId: currentUser.tenantId, // Security: ensure tenant access
        },
      },
      shipToDestination: {
        contains: nameQuery,
        mode: "insensitive",
      },
    };

    // Apply warehouse filtering for non-admin users
    if (currentUser.role !== "TENANT_ADMIN") {
      if (userWarehouseIds.length === 0) return [];
      whereClause.location = {
        warehouse: {
          tenantId: currentUser.tenantId,
        },
        warehouseId: { in: userWarehouseIds },
      };
    }

    // Apply specific warehouse filter if provided
    if (warehouseId) {
      whereClause.location = {
        warehouse: {
          tenantId: currentUser.tenantId, // Security: ensure tenant access
        },
        warehouseId: warehouseId,
      };
    }

    const pallets = await this.prisma.pallet.findMany({
      where: whereClause,
      select: {
        shipToDestination: true,
        destinationCode: true,
      },
      distinct: ["shipToDestination", "destinationCode"],
      take: 10, // Limit results for autocomplete
    });

    // Transform to destination response format
    const destinations = pallets
      .filter((p) => p.shipToDestination !== null)
      .map((p) => ({
        name: p.shipToDestination!,
        code: p.destinationCode,
        displayName: p.destinationCode
          ? `${p.shipToDestination} (${p.destinationCode})`
          : p.shipToDestination!,
      }))
      .sort((a, b) => a.name.localeCompare(b.name));

    this.logger.log(
      `[findDestinationsByName] Found ${destinations.length} destinations matching: ${nameQuery}`
    );

    return destinations;
  }

  async releasePallet(
    id: string,
    releasePalletDto: ReleasePalletDto,
    currentUser: AuthenticatedUser
  ): Promise<Pallet> {
    this.logger.log(
      `[releasePallet] Releasing pallet ID: ${id} for tenant: ${currentUser.tenantId}`
    );

    // Verify pallet exists and user has access
    const pallet = await this.findOne(id, currentUser);

    // Check if pallet is already released
    if (pallet.status === "Released") {
      throw new BadRequestException(
        `Pallet with ID "${id}" is already released.`
      );
    }

    // Update pallet status to Released
    const updatedPallet = await this.prisma.pallet.update({
      where: { id },
      data: { status: "Released" },
      include: {
        location: {
          include: { warehouse: true },
        },
        palletItems: {
          include: { item: true },
        },
      },
    });

    // Create audit log entry
    await this.auditLogService.create({
      userId: currentUser.id,
      userEmail: currentUser.email,
      action: "RELEASE_PALLET",
      entity: "PALLET",
      entityId: id,
      tenantId: currentUser.tenantId,
      details: {
        notes: releasePalletDto.notes,
        releasedTo: releasePalletDto.releasedTo,
        previousStatus: pallet.status,
        newStatus: "Released",
      },
    });

    this.logger.log(`[releasePallet] Successfully released pallet ID: ${id}`);

    return updatedPallet;
  }

  async pickItems(
    palletId: string,
    pickItemsDto: PickItemsDto,
    currentUser: AuthenticatedUser
  ): Promise<Pallet> {
    this.logger.log(
      `[pickItems] Picking items from pallet ID: ${palletId} for tenant: ${currentUser.tenantId}`
    );

    // Verify pallet exists and user has access
    const pallet = await this.findOne(palletId, currentUser);

    // Check if pallet is already released
    if (pallet.status === "Released") {
      throw new BadRequestException(
        `Cannot pick items from released pallet with ID "${palletId}".`
      );
    }

    // Use transaction to ensure data consistency
    const result = await this.prisma.$transaction(async (prisma) => {
      const pickedItemsSummary = [];

      // Process each item to pick
      for (const pickItem of pickItemsDto.items) {
        // Find the pallet item
        const palletItem = await prisma.palletItem.findFirst({
          where: {
            id: pickItem.palletItemId,
            palletId: palletId,
            // Note: tenantId removed from PalletItem - access controlled through pallet->location->warehouse->tenant
          },
          include: { item: true },
        });

        if (!palletItem) {
          throw new NotFoundException(
            `Pallet item with ID "${pickItem.palletItemId}" not found on pallet "${palletId}".`
          );
        }

        // Validate picked quantity
        if (pickItem.pickedQuantity > palletItem.quantity) {
          throw new BadRequestException(
            `Cannot pick ${pickItem.pickedQuantity} of "${palletItem.item.name}" - only ${palletItem.quantity} available.`
          );
        }

        // Calculate new quantity
        const newQuantity = palletItem.quantity - pickItem.pickedQuantity;

        // Add to summary for audit log
        pickedItemsSummary.push({
          itemName: palletItem.item.name,
          itemSku: palletItem.item.sku,
          pickedQuantity: pickItem.pickedQuantity,
          remainingQuantity: newQuantity,
        });

        if (newQuantity === 0) {
          // Delete the pallet item if quantity becomes zero
          await prisma.palletItem.delete({
            where: { id: pickItem.palletItemId },
          });
        } else {
          // Update the quantity
          await prisma.palletItem.update({
            where: { id: pickItem.palletItemId },
            data: { quantity: newQuantity },
          });
        }
      }

      // Create audit log entry
      await this.auditLogService.create({
        userId: currentUser.id,
        userEmail: currentUser.email,
        action: "PICK_ITEMS",
        entity: "PALLET",
        entityId: palletId,
        tenantId: currentUser.tenantId,
        details: {
          notes: pickItemsDto.notes,
          releasedTo: pickItemsDto.releasedTo,
          pickedItems: pickedItemsSummary,
        },
      });

      // Return updated pallet with relations
      return prisma.pallet.findFirst({
        where: {
          id: palletId,
          location: {
            warehouse: {
              tenantId: currentUser.tenantId, // Security: ensure tenant access
            },
          },
        },
        include: {
          location: {
            include: { warehouse: true },
          },
          palletItems: {
            include: { item: true },
          },
        },
      });
    });

    this.logger.log(
      `[pickItems] Successfully picked items from pallet ID: ${palletId}`
    );

    return result;
  }

  async getPlacardData(
    id: string,
    currentUser: AuthenticatedUser
  ): Promise<PlacardDataResponseDto> {
    this.logger.log(
      `[getPlacardData] Fetching placard data for pallet ID: ${id}`
    );
    const pallet = await this.findOne(id, currentUser);
    return this.mapPalletToPlacardDto(pallet);
  }

  async findOne(
    id: string,
    currentUser: AuthenticatedUser
  ): Promise<PalletWithAllRelations> {
    this.logger.log(
      `[findOne] Searching for pallet ID: ${id} for tenant: ${currentUser.tenantId}`
    );
    // Build warehouse-scoped query with security validation
    const userWarehouseIds =
      currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];

    const whereClause: Prisma.PalletWhereInput = {
      id,
      location: {
        warehouse: {
          tenantId: currentUser.tenantId, // Security: ensure tenant access
        },
      },
    };

    // Apply warehouse filtering for non-admin users
    if (currentUser.role !== "TENANT_ADMIN") {
      if (userWarehouseIds.length === 0) {
        throw new ForbiddenException(
          `You do not have access to any warehouses.`
        );
      }
      whereClause.location = {
        warehouse: {
          tenantId: currentUser.tenantId,
        },
        warehouseId: { in: userWarehouseIds },
      };
    }

    const pallet = await this.prisma.pallet.findFirst({
      where: whereClause,
      include: palletWithAllRelations.include,
    });

    if (!pallet) {
      throw new NotFoundException(
        `Pallet with ID "${id}" not found or not accessible within your warehouses.`
      );
    }
    return pallet;
  }

  async update(
    id: string,
    updatePalletDto: UpdatePalletDto,
    currentUser: AuthenticatedUser
  ): Promise<Pallet> {
    await this.findOne(id, currentUser); // Authorization check

    const { locationId, ...dataToUpdate } = updatePalletDto;

    if (locationId) {
      throw new BadRequestException(
        "Use the /pallets/:id/move endpoint to change a pallet's location."
      );
    }

    const updatedPallet = await this.prisma.pallet.update({
      where: { id },
      data: dataToUpdate,
    });

    await this.auditLogService.create({
      userId: currentUser.id,
      userEmail: currentUser.email,
      action: "UPDATE_PALLET",
      entity: "PALLET",
      entityId: id,
      tenantId: currentUser.tenantId,
      details: {
        updatedFields: dataToUpdate,
        previousValues: {
          // Note: In a production system, you might want to capture previous values
          // by fetching the pallet before update
        },
      },
    });

    return updatedPallet;
  }

  async remove(id: string, currentUser: AuthenticatedUser): Promise<Pallet> {
    const pallet = await this.findOne(id, currentUser); // Authorization check

    if (pallet.palletItems && pallet.palletItems.length > 0) {
      throw new BadRequestException(
        "Cannot delete a pallet with items. Please move or remove items first."
      );
    }

    const deletedPallet = await this.prisma.pallet.delete({
      where: { id },
    });

    await this.auditLogService.create({
      userId: currentUser.id,
      userEmail: currentUser.email,
      action: "DELETE_PALLET",
      entity: "PALLET",
      entityId: id,
      tenantId: currentUser.tenantId,
      details: {
        deletedPallet: {
          barcode: pallet.barcode,
          label: pallet.label,
          description: pallet.description,
          status: pallet.status,
          locationName: pallet.location?.name || "unknown",
        },
      },
    });

    return deletedPallet;
  }

  async movePallet(
    id: string,
    movePalletDto: MovePalletDto,
    currentUser: AuthenticatedUser
  ): Promise<Pallet> {
    const { newLocationId } = movePalletDto;

    return this.prisma.$transaction(async (tx) => {
      // 1. Find pallet and its current location, ensuring it's in user's tenant
      const pallet = await tx.pallet.findUniqueOrThrow({
        where: {
          id,
          location: {
            warehouse: {
              tenantId: currentUser.tenantId, // Security: ensure tenant access
            },
          },
        },
        include: { location: true },
      });

      const oldLocationName = pallet.location?.name || "unassigned";
      let finalLocationId = pallet.locationId;
      let newLocationName = oldLocationName;

      // 2. If a new location is provided in the DTO, update it
      if (newLocationId) {
        // Ensure the new location exists and validate permissions
        const newLocation = await this.checkLocationExists(
          newLocationId,
          currentUser.tenantId
        );

        if (currentUser.role !== "TENANT_ADMIN") {
          const userWarehouseIds =
            currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
          if (!userWarehouseIds.includes(newLocation.warehouseId)) {
            throw new ForbiddenException(
              `You are not authorized to move pallets to location ${newLocation.name}.`
            );
          }
        }

        finalLocationId = newLocationId;
        newLocationName = newLocation.name;
      } else {
        // If no new location provided, ensure current location is a valid storage location
        if (pallet.location?.category === "Receiving") {
          throw new BadRequestException(
            "Pallet needs a destination storage location."
          );
        }
      }

      // 3. Update the pallet's location (if changed) and status
      const updatedPallet = await tx.pallet.update({
        where: { id },
        data: {
          locationId: finalLocationId,
          status: "Stored", // The key action is updating the status
          lastMovedDate: new Date(),
        },
        include: {
          location: true,
          palletItems: {
            include: {
              item: true,
            },
          },
        },
      });

      // 4. Create an audit log entry for the move
      await this.auditLogService.create({
        userId: currentUser.id,
        userEmail: currentUser.email,
        action: "MOVE_PALLET",
        entity: "Pallet",
        entityId: id,
        tenantId: currentUser.tenantId,
        details: {
          fromLocation: oldLocationName,
          toLocation: newLocationName,
          statusUpdated: "Stored",
          moveDescription: `Moved from ${oldLocationName} to ${newLocationName}`,
        },
      });

      return updatedPallet;
    });
  }

  async getAuditLogs(id: string, currentUser: AuthenticatedUser) {
    // First verify the pallet exists and user has access
    const pallet = await this.findOne(id, currentUser);
    if (!pallet) {
      throw new NotFoundException(`Pallet with ID "${id}" not found.`);
    }

    // Fetch audit logs for this pallet with warehouse context validation
    return this.auditLogService.findByEntityIdWithWarehouseContext(
      id,
      currentUser,
      "Pallet"
    );
  }
}
