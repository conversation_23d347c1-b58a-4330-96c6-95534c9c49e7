import { Modu<PERSON> } from "@nestjs/common";
import { PalletsService } from "./pallets.service";
import { <PERSON>lletsController } from "./pallets.controller";
import { PlacardPdfService } from "./placard-pdf.service";
import { AuditLogModule } from "../audit-log/audit-log.module";
import { AuthModule } from "../auth/auth.module";
import { WarehousesModule } from "@/warehouses/warehouses.module";

@Module({
  imports: [AuditLogModule, AuthModule, WarehousesModule],
  providers: [PalletsService, PlacardPdfService],
  controllers: [PalletsController],
})
export class PalletsModule {}
