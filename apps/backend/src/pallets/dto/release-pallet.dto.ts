import { IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ReleasePalletDto {
  @ApiProperty({ 
    required: false, 
    description: 'Optional notes about the pallet release' 
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({ 
    required: false, 
    description: 'Name or identifier of the person/entity receiving the pallet' 
  })
  @IsOptional()
  @IsString()
  releasedTo?: string;
}
