import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

class PlacardWarehouseDto {
  @ApiProperty({ example: "clxkrg99f0000abcd1234efgh" })
  id: string;

  @ApiProperty({ example: "Main Warehouse" })
  name: string;
}

class PlacardLocationDto {
  @ApiProperty({ example: "A1-R2-S3" })
  name: string;

  @ApiPropertyOptional()
  warehouse?: PlacardWarehouseDto;
}

class PlacardContentDto {
  @ApiProperty({ example: "clxkrg99f0000abcd1234efgh" })
  itemId: string;

  @ApiProperty({ example: "Drywall Sheet 4x8" })
  itemName: string;

  @ApiProperty({ example: 15 })
  quantity: number;

  @ApiProperty({ example: "sheets" })
  unit: string;
}

export class PlacardDataResponseDto {
  @ApiProperty({ example: "clxkrg99f0000abcd1234efgh" })
  palletId: string;

  @ApiProperty({ example: "P-12345" })
  barcode: string;

  @ApiProperty({ example: "Job Site 42" })
  shipToDestination: string;

  @ApiPropertyOptional({ example: "12345" })
  destinationCode?: string;

  @ApiProperty({ example: "2024-01-15T10:30:00.000Z" })
  dateCreated: string;

  @ApiProperty()
  currentLocation: PlacardLocationDto;

  @ApiProperty({ example: "Description of the pallet" })
  description: string;

  @ApiProperty({ type: [PlacardContentDto] })
  contents: PlacardContentDto[];
}
