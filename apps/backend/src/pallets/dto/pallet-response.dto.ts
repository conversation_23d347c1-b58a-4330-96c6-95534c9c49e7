import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Warehouse information included in pallet responses
 */
export class PalletWarehouseDto {
  @ApiProperty({ description: 'Warehouse ID' })
  id: string;

  @ApiProperty({ description: 'Warehouse name' })
  name: string;
}

/**
 * Location information included in pallet responses
 */
export class PalletLocationDto {
  @ApiProperty({ description: 'Location ID' })
  id: string;

  @ApiProperty({ description: 'Location name' })
  name: string;

  @ApiProperty({ description: 'Location type' })
  locationType: string;

  @ApiProperty({ 
    description: 'Warehouse information',
    type: PalletWarehouseDto 
  })
  warehouse: PalletWarehouseDto;
}

/**
 * Item information included in pallet responses
 */
export class PalletItemDto {
  @ApiProperty({ description: 'Pallet item ID' })
  id: string;

  @ApiProperty({ description: 'Item ID' })
  itemId: string;

  @ApiProperty({ description: 'Item name' })
  itemName: string;

  @ApiProperty({ description: 'Item SKU' })
  itemSku?: string;

  @ApiProperty({ description: 'Quantity of this item on the pallet' })
  quantity: number;

  @ApiProperty({ description: 'Unit of measure' })
  unitOfMeasure: string;
}

/**
 * Response DTO for pallet with warehouse context
 */
export class PalletResponseDto {
  @ApiProperty({ description: 'Pallet ID' })
  id: string;

  @ApiProperty({ description: 'Pallet label' })
  label: string;

  @ApiProperty({ description: 'Pallet barcode' })
  barcode: string;

  @ApiProperty({ description: 'Pallet status' })
  status: string;

  @ApiPropertyOptional({ description: 'Pallet description' })
  description?: string;

  @ApiPropertyOptional({ description: 'Ship to destination' })
  shipToDestination?: string;

  @ApiProperty({ description: 'Creation timestamp' })
  dateCreated: Date;

  @ApiPropertyOptional({ description: 'Last moved timestamp' })
  lastMovedDate?: Date;

  @ApiPropertyOptional({ 
    description: 'Current location information',
    type: PalletLocationDto 
  })
  location?: PalletLocationDto;

  @ApiPropertyOptional({ 
    description: 'Items on this pallet',
    type: [PalletItemDto] 
  })
  palletItems?: PalletItemDto[];

  @ApiPropertyOptional({ description: 'Number of items on this pallet' })
  itemCount?: number;

  @ApiProperty({ description: 'Tenant ID' })
  tenantId: string;
}

/**
 * Paginated response for pallet queries
 */
export class PalletListResponseDto {
  @ApiProperty({ 
    description: 'Array of pallets',
    type: [PalletResponseDto] 
  })
  data: PalletResponseDto[];

  @ApiProperty({ description: 'Total number of pallets' })
  count: number;

  @ApiPropertyOptional({ description: 'Current page number' })
  page?: number;

  @ApiPropertyOptional({ description: 'Number of items per page' })
  limit?: number;

  @ApiPropertyOptional({ description: 'Total number of pages' })
  totalPages?: number;
}
