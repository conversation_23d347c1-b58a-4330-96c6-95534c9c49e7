import {
  IsString,
  <PERSON><PERSON>ptional,
  <PERSON>In,
  IsArray,
  ValidateNested,
  IsNotEmpty,
  IsInt,
  Min,
  <PERSON><PERSON>eng<PERSON>,
  <PERSON>,
} from "class-validator";
import { Type } from "class-transformer";

// Allowed pallet statuses based on schema comments
const allowedPalletStatuses = [
  "Empty",
  "Receiving",
  "Stored",
  "Picking",
  "Shipping",
];

class CreatePalletItemDto {
  @IsString()
  @IsNotEmpty()
  itemId: string;

  @IsInt()
  @Min(1)
  quantity: number;

  // TODO: Add other fields like lotNumber, expiryDate if needed in the future
}

export class CreatePalletDto {
  @IsString()
  @IsOptional()
  label?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  barcode?: string;

  @IsString()
  @IsOptional()
  @IsIn(allowedPalletStatuses)
  status?: string; // Defaults to 'Empty' in schema

  @IsString()
  @IsOptional()
  @MaxLength(255)
  shipToDestination?: string;

  @IsString()
  @IsOptional()
  @Matches(/^\d+$/, { message: "Destination code must contain only numbers" })
  @MaxLength(50)
  destinationCode?: string;

  @IsString()
  @IsOptional() // Pallet might be created without an immediate location (e.g., in receiving area)
  locationId?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreatePalletItemDto)
  @IsOptional()
  items?: CreatePalletItemDto[]; // For creating a pallet with items
}
