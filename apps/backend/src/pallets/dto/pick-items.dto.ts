import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Min,
  <PERSON><PERSON><PERSON>Nested,
} from "class-validator";
import { Type } from "class-transformer";
import { ApiProperty } from "@nestjs/swagger";

export class PickItemDto {
  @ApiProperty({ description: "ID of the pallet item to pick from" })
  @IsString()
  palletItemId: string;

  @ApiProperty({ description: "Quantity to pick (must be positive)" })
  @IsInt()
  @Min(1)
  pickedQuantity: number;
}

export class PickItemsDto {
  @ApiProperty({
    type: [PickItemDto],
    description: "Array of items to pick with their quantities",
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PickItemDto)
  items: PickItemDto[];

  @ApiProperty({
    required: false,
    description: "Optional notes about the item picking",
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    required: false,
    description: "Name or identifier of the person/entity receiving the items",
  })
  @IsOptional()
  @IsString()
  releasedTo?: string;
}
