import "reflect-metadata";
import { Test, TestingModule } from "@nestjs/testing";
import { PrismaService } from "../prisma/prisma.service";
import { PalletsService } from "./pallets.service";
import { PalletsModule } from "./pallets.module";
import { PrismaModule } from "../prisma/prisma.module";
import { cleanupDatabase } from "../../test/utils/db-cleanup";
import { CreatePalletDto } from "./dto/create-pallet.dto";
import { UpdatePalletDto } from "./dto/update-pallet.dto";
import { NotFoundException, BadRequestException } from "@nestjs/common";
import {
  Warehouse,
  Location,
  Item,
  Pallet,
  Role,
  LocationType,
  LocationCategory,
} from "@prisma/client";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";

const mockTenantId = "test-tenant-id-pallets";

const mockCurrentUser: AuthenticatedUser = {
  id: "test-user-pallet-id",
  email: "<EMAIL>",
  role: Role.WAREHOUSE_MANAGER,
  tenantId: mockTenantId,
  name: "Pallet Test User",
  authUserId: "pallet-test-auth-user-id",
  warehouseUsers: [],
};

describe("PalletsService Integration", () => {
  let service: PalletsService;
  let prisma: PrismaService;
  let module: TestingModule;
  let testWarehouse: Warehouse;
  let testLocation1: Location;
  let testLocation2: Location;
  let testItem: Item;

  beforeAll(async () => {});

  afterAll(async () => {
    await module?.close();
  });

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [PrismaModule, PalletsModule],
    }).compile();

    prisma = module.get<PrismaService>(PrismaService);
    service = module.get<PalletsService>(PalletsService);

    await cleanupDatabase(prisma);

    testWarehouse = await prisma.warehouse.create({
      data: {
        name: "Pallet Test WH",
        tenantId: mockCurrentUser.tenantId,
      },
    });
    testLocation1 = await prisma.location.create({
      data: {
        name: "Pallet Test Loc 1",
        locationType: LocationType.ZONE,
        warehouseId: testWarehouse.id,
      },
    });
    testLocation2 = await prisma.location.create({
      data: {
        name: "Pallet Test Loc 2",
        locationType: LocationType.DOCK,
        category: LocationCategory.Receiving,
        warehouseId: testWarehouse.id,
      },
    });
    testItem = await prisma.item.create({
      data: {
        name: "Pallet Test Item",
        tenantId: mockCurrentUser.tenantId,
      },
    });

    mockCurrentUser.warehouseUsers = [
      { warehouseId: testWarehouse.id, role: Role.WAREHOUSE_MEMBER },
    ];
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("create()", () => {
    it("should create a new pallet with a specific status and location within the tenant", async () => {
      const createDto: CreatePalletDto = {
        status: "Receiving",
        locationId: testLocation1.id,
      };
      const createdPallet = await service.create(createDto, mockCurrentUser);

      expect(createdPallet).toBeDefined();
      expect(createdPallet.id).toBeDefined();
      expect(createdPallet.status).toEqual("Receiving");
      expect(createdPallet.locationId).toEqual(testLocation1.id);
      const dbPallet = await prisma.pallet.findUnique({
        where: { id: createdPallet.id },
        include: { location: { include: { warehouse: true } } },
      });
      expect(dbPallet).toBeDefined();
      expect(dbPallet.status).toEqual("Receiving");
      expect(dbPallet.locationId).toEqual(testLocation1.id);
      expect(dbPallet.location.warehouse.tenantId).toEqual(
        mockCurrentUser.tenantId
      );
    });

    it("should throw BadRequestException if locationId is not provided", async () => {
      const createDto: CreatePalletDto = { status: "Empty" };
      await expect(service.create(createDto, mockCurrentUser)).rejects.toThrow(
        BadRequestException
      );
      await expect(service.create(createDto, mockCurrentUser)).rejects.toThrow(
        "Initial Location ID is required to create a pallet."
      );
    });

    it("should throw BadRequestException if locationId does not exist in the current tenant", async () => {
      const nonExistentLocationId = "clxxxxxxxxxnonexistent";
      const createDto: CreatePalletDto = { locationId: nonExistentLocationId };

      await expect(service.create(createDto, mockCurrentUser)).rejects.toThrow(
        BadRequestException
      );
      await expect(service.create(createDto, mockCurrentUser)).rejects.toThrow(
        `Location with ID \"${nonExistentLocationId}\" not found within your tenant.`
      );
    });

    it("should throw BadRequestException if user not authorized for warehouse of the location", async () => {
      const anotherWarehouse = await prisma.warehouse.create({
        data: { name: "Another WH", tenantId: mockCurrentUser.tenantId },
      });
      const locationInAnotherWarehouse = await prisma.location.create({
        data: {
          name: "Loc in Another WH",
          locationType: LocationType.ZONE,
          warehouseId: anotherWarehouse.id,
        },
      });
      const createDto: CreatePalletDto = {
        locationId: locationInAnotherWarehouse.id,
      };
      await expect(service.create(createDto, mockCurrentUser)).rejects.toThrow(
        BadRequestException
      );
      await expect(service.create(createDto, mockCurrentUser)).rejects.toThrow(
        `You are not authorized to create pallets in the warehouse of location ID \"${locationInAnotherWarehouse.id}\".`
      );
    });
  });

  describe("findAll()", () => {
    it("should return pallets from user's tenant and accessible warehouses", async () => {
      const pallet1 = await service.create(
        { locationId: testLocation1.id },
        mockCurrentUser
      );
      const otherWarehouse = await prisma.warehouse.create({
        data: { name: "OtherFindAllWH", tenantId: mockCurrentUser.tenantId },
      });
      const locationInOtherWarehouse = await prisma.location.create({
        data: {
          name: "LocOtherFindAllWH",
          warehouseId: otherWarehouse.id,
          locationType: LocationType.ZONE,
        },
      });
      await prisma.pallet.create({
        data: {
          label: "Pallet In Other WH For FindAll",
          locationId: locationInOtherWarehouse.id,
          barcode: `FINDALL-PALLET-${Date.now()}`,
          shipToDestination: "FindAll Destination",
        },
      });

      const otherTenant = await prisma.tenant.create({
        data: { name: "Other Tenant" },
      });
      const otherTenantWarehouse = await prisma.warehouse.create({
        data: { name: "Other Tenant WH", tenantId: otherTenant.id },
      });
      const otherTenantLocation = await prisma.location.create({
        data: {
          name: "Other Tenant Loc",
          warehouseId: otherTenantWarehouse.id,
          locationType: LocationType.ZONE,
        },
      });
      await prisma.pallet.create({
        data: {
          label: "Other Tenant Pallet For FindAll",
          locationId: otherTenantLocation.id,
          barcode: `OTHER-TENANT-FINDALL-${Date.now()}`,
          shipToDestination: "Other Tenant Destination",
        },
      });

      const pallets = await service.findAll(mockCurrentUser, {});
      expect(pallets.length).toBe(1);
      expect(pallets[0].id).toEqual(pallet1.id);
    });

    it("should return an empty array if no pallets exist in accessible locations/tenant", async () => {
      const pallets = await service.findAll(mockCurrentUser, {});
      expect(pallets).toEqual([]);
    });
  });

  describe("findOne()", () => {
    it("should return a pallet by ID if in user's tenant and accessible warehouse", async () => {
      const createdPallet = await service.create(
        { locationId: testLocation1.id },
        mockCurrentUser
      );
      await prisma.palletItem.create({
        data: {
          palletId: createdPallet.id,
          itemId: testItem.id,
          quantity: 5,
        },
      });

      const foundPallet = (await service.findOne(
        createdPallet.id,
        mockCurrentUser
      )) as Pallet;

      expect(foundPallet).toBeDefined();
      expect(foundPallet.id).toEqual(createdPallet.id);
    });

    it("should throw NotFoundException if pallet ID does not exist in tenant", async () => {
      const nonExistentId = "clxxxxxxxxxnonexistent";
      await expect(
        service.findOne(nonExistentId, mockCurrentUser)
      ).rejects.toThrow(NotFoundException);
    });

    it("should throw NotFoundException if pallet belongs to another tenant", async () => {
      const otherTenant = await prisma.tenant.create({
        data: { name: "Other Tenant findOne" },
      });
      const otherTenantWarehouse = await prisma.warehouse.create({
        data: { name: "OTF1 WH", tenantId: otherTenant.id },
      });
      const otherTenantLocation = await prisma.location.create({
        data: {
          name: "OTF1 Loc",
          warehouseId: otherTenantWarehouse.id,
          locationType: LocationType.ZONE,
        },
      });
      const otherTenantPallet = await prisma.pallet.create({
        data: {
          label: "Other Tenant Pallet For FindOne",
          locationId: otherTenantLocation.id,
          barcode: `OTHER-TENANT-FINDONE-${Date.now()}`,
          shipToDestination: "Other Tenant FindOne Destination",
        },
      });

      await expect(
        service.findOne(otherTenantPallet.id, mockCurrentUser)
      ).rejects.toThrow(NotFoundException);
    });

    it("should throw NotFoundException if pallet is in an inaccessible warehouse within the same tenant", async () => {
      const inaccessibleWarehouse = await prisma.warehouse.create({
        data: { name: "InaccessibleWH", tenantId: mockCurrentUser.tenantId },
      });
      const locationInInaccessibleWarehouse = await prisma.location.create({
        data: {
          name: "LocInaccWH",
          warehouseId: inaccessibleWarehouse.id,
          locationType: LocationType.ZONE,
        },
      });
      const palletInInaccessibleWarehouse = await prisma.pallet.create({
        data: {
          label: "Inaccessible WH Pallet For FindOne",
          locationId: locationInInaccessibleWarehouse.id,
          barcode: `INACCESSIBLE-FINDONE-${Date.now()}`,
          shipToDestination: "Inaccessible FindOne Destination",
        },
      });

      await expect(
        service.findOne(palletInInaccessibleWarehouse.id, mockCurrentUser)
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe("update()", () => {
    it("should update pallet's status and location if accessible", async () => {
      const createdPallet = await service.create(
        { locationId: testLocation1.id },
        mockCurrentUser
      );
      const updateDto: UpdatePalletDto = {
        status: "Stored",
        locationId: testLocation2.id,
      };

      const updatedPallet = await service.update(
        createdPallet.id,
        updateDto,
        mockCurrentUser
      );

      expect(updatedPallet).toBeDefined();
      expect(updatedPallet.status).toEqual("Stored");
      expect(updatedPallet.locationId).toEqual(testLocation2.id);
      // Tenant access validated through warehouse-scoped filtering
    });

    it("should throw NotFoundException if pallet to update does not exist in tenant", async () => {
      const nonExistentId = "clxxxxxxxxxnonexistent";
      await expect(
        service.update(nonExistentId, { status: "Picking" }, mockCurrentUser)
      ).rejects.toThrow(NotFoundException);
    });

    it("should throw BadRequestException if updating to a non-existent locationId in tenant", async () => {
      const createdPallet = await service.create(
        { locationId: testLocation1.id },
        mockCurrentUser
      );
      const nonExistentLocationId = "clxxxxxxxxxnonexistent";
      const updateDto: UpdatePalletDto = { locationId: nonExistentLocationId };

      await expect(
        service.update(createdPallet.id, updateDto, mockCurrentUser)
      ).rejects.toThrow(BadRequestException);
    });

    it("should throw BadRequestException if updating to a location in an inaccessible warehouse", async () => {
      const createdPallet = await service.create(
        { locationId: testLocation1.id },
        mockCurrentUser
      );
      const inaccessibleWarehouse = await prisma.warehouse.create({
        data: { name: "UpdateInaccWH", tenantId: mockCurrentUser.tenantId },
      });
      const locationInInaccessibleWarehouse = await prisma.location.create({
        data: {
          name: "UpdateLocInaccWH",
          warehouseId: inaccessibleWarehouse.id,
          locationType: LocationType.ZONE,
        },
      });

      const updateDto: UpdatePalletDto = {
        locationId: locationInInaccessibleWarehouse.id,
      };
      await expect(
        service.update(createdPallet.id, updateDto, mockCurrentUser)
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("remove()", () => {
    it("should delete an existing pallet if accessible and remove its items", async () => {
      const createdPallet = await service.create(
        { locationId: testLocation1.id },
        mockCurrentUser
      );
      await prisma.palletItem.create({
        data: {
          palletId: createdPallet.id,
          itemId: testItem.id,
          quantity: 10,
        },
      });

      await service.remove(createdPallet.id, mockCurrentUser);

      const dbPallet = await prisma.pallet.findUnique({
        where: { id: createdPallet.id },
      });
      expect(dbPallet).toBeNull();
      const dbPalletItems = await prisma.palletItem.findMany({
        where: {
          palletId: createdPallet.id,
        },
      });
      expect(dbPalletItems.length).toBe(0);
    });

    it("should throw NotFoundException if pallet to delete does not exist in tenant", async () => {
      const nonExistentId = "clxxxxxxxxxnonexistent";
      await expect(
        service.remove(nonExistentId, mockCurrentUser)
      ).rejects.toThrow(NotFoundException);
    });

    describe("shipToDestination", () => {
      it("should create a pallet with shipToDestination", async () => {
        const createDto: CreatePalletDto = {
          locationId: testLocation1.id,
          shipToDestination: "Warehouse A",
        };
        const createdPallet = await service.create(createDto, mockCurrentUser);

        expect(createdPallet).toBeDefined();
        expect(createdPallet.shipToDestination).toEqual("Warehouse A");

        const dbPallet = await prisma.pallet.findUnique({
          where: { id: createdPallet.id },
          select: { shipToDestination: true },
        });
        expect(dbPallet).toBeDefined();
        expect(dbPallet.shipToDestination).toEqual("Warehouse A");
      });

      it("should update pallet's shipToDestination", async () => {
        const createDto: CreatePalletDto = {
          locationId: testLocation1.id,
        };
        const createdPallet = await service.create(createDto, mockCurrentUser);

        const updateDto: UpdatePalletDto = {
          shipToDestination: "New Warehouse",
        };
        const updatedPallet = await service.update(
          createdPallet.id,
          updateDto,
          mockCurrentUser
        );

        expect(updatedPallet.shipToDestination).toEqual("New Warehouse");

        const dbPallet = await prisma.pallet.findUnique({
          where: { id: createdPallet.id },
          select: { shipToDestination: true },
        });
        expect(dbPallet).toBeDefined();
        expect(dbPallet.shipToDestination).toEqual("New Warehouse");
      });

      it("should clear shipToDestination when updating with null", async () => {
        const createDto: CreatePalletDto = {
          locationId: testLocation1.id,
          shipToDestination: "Initial Warehouse",
        };
        const createdPallet = await service.create(createDto, mockCurrentUser);

        const updateDto: UpdatePalletDto = {
          shipToDestination: null,
        };
        const updatedPallet = await service.update(
          createdPallet.id,
          updateDto,
          mockCurrentUser
        );

        expect(updatedPallet.shipToDestination).toBeNull();

        const dbPallet = await prisma.pallet.findUnique({
          where: { id: createdPallet.id },
          select: { shipToDestination: true },
        });
        expect(dbPallet).toBeDefined();
        expect(dbPallet.shipToDestination).toBeNull();
      });

      it("should find pallets by shipToDestination", async () => {
        // Create pallets with different destinations
        const pallet1 = await service.create(
          { locationId: testLocation1.id, shipToDestination: "Warehouse A" },
          mockCurrentUser
        );
        const pallet2 = await service.create(
          { locationId: testLocation1.id, shipToDestination: "Warehouse B" },
          mockCurrentUser
        );
        const pallet3 = await service.create(
          { locationId: testLocation1.id }, // No destination
          mockCurrentUser
        );

        // Find by specific destination
        const palletsWithDestA = await service.findAll(mockCurrentUser, {
          shipToDestination: "Warehouse A",
        });
        expect(palletsWithDestA.length).toBe(1);
        expect(palletsWithDestA[0].id).toBe(pallet1.id);

        // Find by null destination
        const palletsWithNoDest = await service.findAll(mockCurrentUser, {
          shipToDestination: null,
        });
        expect(palletsWithNoDest.length).toBe(1);
        expect(palletsWithNoDest[0].id).toBe(pallet3.id);
      });
    });
  });
});
