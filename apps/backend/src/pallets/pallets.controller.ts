import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UsePipes,
  ValidationPipe,
  NotFoundException,
  UseGuards,
  Req,
  Query,
  UseInterceptors,
  Logger,
  Header,
  StreamableFile,
} from "@nestjs/common";
import { PalletsService } from "./pallets.service";
import { CreatePalletDto } from "./dto/create-pallet.dto";
import { UpdatePalletDto } from "./dto/update-pallet.dto";
import { MovePalletDto } from "./dto/move-pallet.dto";
import { QueryPalletDto } from "./dto/query-pallet.dto";
import { ReleasePalletDto } from "./dto/release-pallet.dto";
import { PickItemsDto } from "./dto/pick-items.dto";
import { PlacardDataResponseDto } from "./dto/placard.dto";
import { PrintPlacardsQueryDto } from "./dto/print-placards.dto";

import { AuditLogInterceptor } from "../audit-log/interceptors/audit-log.interceptor";
import { LogAction } from "../audit-log/decorators/log-action.decorator";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { WarehousePermissionGuard } from "../auth/guards/warehouse-permission.guard";
import {
  AllowWarehouseFiltering,
  RequirePalletAccess,
  RequireWarehouseAccess,
  RequestWithWarehouseContext,
} from "../auth/decorators/warehouse-permission.decorator";
import { EnhancedUserPayload } from "../auth/types";
import { Role } from "@prisma/client";
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiParam,
} from "@nestjs/swagger";

@ApiTags("Pallets")
@ApiBearerAuth()
@Controller("pallets")
@UseGuards(JwtAuthGuard, WarehousePermissionGuard)
@UseInterceptors(AuditLogInterceptor)
export class PalletsController {
  private readonly logger = new Logger(PalletsController.name);
  constructor(private readonly palletsService: PalletsService) {}

  @Post()
  @RequireWarehouseAccess()
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  @LogAction({
    action: "CREATE_PALLET",
    entity: "Pallet",
    getEntityId: (_context, result) => result?.id,
  })
  create(
    @Body() createPalletDto: CreatePalletDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.palletsService.create(createPalletDto, req.user);
  }

  @Get()
  @AllowWarehouseFiltering()
  @ApiOperation({
    summary: "Get all pallets with optional warehouse filtering",
  })
  @ApiQuery({
    name: "warehouseId",
    required: false,
    type: String,
    description: "Filter by warehouse ID",
  })
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  findAll(
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>,
    @Query() query: QueryPalletDto
  ) {
    return this.palletsService.findAll(req.user, query, query.warehouseId);
  }

  @Get("destinations")
  @AllowWarehouseFiltering()
  @ApiOperation({
    summary: "Get all destinations with optional warehouse filtering",
  })
  @ApiQuery({
    name: "warehouseId",
    required: false,
    type: String,
    description: "Filter by warehouse ID",
  })
  getDestinations(
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>,
    @Query("warehouseId") warehouseId?: string
  ) {
    return this.palletsService.getDestinations(req.user, warehouseId);
  }

  @Get("destinations/with-codes")
  @AllowWarehouseFiltering()
  @ApiOperation({
    summary: "Get all destinations with codes and display names",
  })
  @ApiQuery({
    name: "warehouseId",
    required: false,
    type: String,
    description: "Filter by warehouse ID",
  })
  getDestinationsWithCodes(
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>,
    @Query("warehouseId") warehouseId?: string
  ) {
    return this.palletsService.getDestinationsWithCodes(req.user, warehouseId);
  }

  @Get("destinations/by-code/:code")
  @AllowWarehouseFiltering()
  @ApiOperation({
    summary: "Find destination by code",
  })
  @ApiParam({
    name: "code",
    type: String,
    description: "Destination code to lookup",
  })
  @ApiQuery({
    name: "warehouseId",
    required: false,
    type: String,
    description: "Filter by warehouse ID",
  })
  findDestinationByCode(
    @Param("code") code: string,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>,
    @Query("warehouseId") warehouseId?: string
  ) {
    return this.palletsService.findDestinationByCode(
      code,
      req.user,
      warehouseId
    );
  }

  @Get("destinations/by-name")
  @AllowWarehouseFiltering()
  @ApiOperation({
    summary: "Search destinations by name",
  })
  @ApiQuery({
    name: "q",
    required: true,
    type: String,
    description: "Name query to search for",
  })
  @ApiQuery({
    name: "warehouseId",
    required: false,
    type: String,
    description: "Filter by warehouse ID",
  })
  findDestinationsByName(
    @Query("q") nameQuery: string,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>,
    @Query("warehouseId") warehouseId?: string
  ) {
    return this.palletsService.findDestinationsByName(
      nameQuery,
      req.user,
      warehouseId
    );
  }

  @Post(":id/release")
  @RequirePalletAccess({ minimumRole: Role.WAREHOUSE_MEMBER })
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  @LogAction({
    action: "RELEASE_PALLET",
    entity: "Pallet",
    getEntityId: (context) => context.switchToHttp().getRequest().params.id,
  })
  releasePallet(
    @Param("id") id: string,
    @Body() releasePalletDto: ReleasePalletDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.palletsService.releasePallet(id, releasePalletDto, req.user);
  }

  @Post(":palletId/pick-items")
  @RequirePalletAccess({ minimumRole: Role.WAREHOUSE_MEMBER })
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  @LogAction({
    action: "PICK_ITEMS",
    entity: "Pallet",
    getEntityId: (context) =>
      context.switchToHttp().getRequest().params.palletId,
  })
  pickItems(
    @Param("palletId") palletId: string,
    @Body() pickItemsDto: PickItemsDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.palletsService.pickItems(palletId, pickItemsDto, req.user);
  }

  @Get(":id/placard-data")
  @RequirePalletAccess()
  async getPlacardData(
    @Param("id") id: string,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ): Promise<PlacardDataResponseDto> {
    this.logger.log(`Received request for placard data for pallet ID: ${id}`);
    return this.palletsService.getPlacardData(id, req.user);
  }

  @Get("placards/print")
  @AllowWarehouseFiltering()
  @Header("Content-Type", "application/pdf")
  @Header("Content-Disposition", 'inline; filename="placards.pdf"')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async printPlacards(
    @Query() query: PrintPlacardsQueryDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ): Promise<StreamableFile> {
    this.logger.log(
      `Received request to print placards with query: ${JSON.stringify(query)}`
    );
    const pdfBuffer = await this.palletsService.generatePlacardsPdf(
      query,
      req.user
    );
    return new StreamableFile(pdfBuffer);
  }

  @Get(":id")
  @RequirePalletAccess()
  @ApiOperation({ summary: "Get a specific pallet by ID" })
  @ApiParam({ name: "id", type: String, description: "Pallet ID" })
  async findOne(
    @Param("id") id: string,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    const pallet = await this.palletsService.findOne(id, req.user);
    if (!pallet) {
      throw new NotFoundException(
        `Pallet with ID "${id}" not found or not accessible.`
      );
    }
    return pallet;
  }

  @Patch(":id")
  @RequirePalletAccess({ minimumRole: Role.WAREHOUSE_MEMBER })
  @ApiOperation({ summary: "Update a pallet" })
  @ApiParam({ name: "id", type: String, description: "Pallet ID" })
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  @LogAction({
    action: "UPDATE_PALLET",
    entity: "Pallet",
    getEntityId: (context) => context.switchToHttp().getRequest().params.id,
  })
  update(
    @Param("id") id: string,
    @Body() updatePalletDto: UpdatePalletDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.palletsService.update(id, updatePalletDto, req.user);
  }

  @Delete(":id")
  @RequirePalletAccess({ minimumRole: Role.WAREHOUSE_MANAGER })
  @LogAction({
    action: "DELETE_PALLET",
    entity: "Pallet",
    getEntityId: (context) => context.switchToHttp().getRequest().params.id,
  })
  remove(
    @Param("id") id: string,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.palletsService.remove(id, req.user);
  }

  @Post(":id/move")
  @RequirePalletAccess({ minimumRole: Role.WAREHOUSE_MEMBER })
  @ApiOperation({ summary: "Move a pallet to a new location" })
  @ApiParam({ name: "id", type: String, description: "Pallet ID" })
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  // @LogAction decorator for MOVE_PALLET will be handled by the service layer audit log creation
  async movePallet(
    @Param("id") id: string,
    @Body() movePalletDto: MovePalletDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    // The PalletsService.movePallet method already handles audit logging within the transaction.
    // It also returns the full pallet object with relations as per the spec.
    return this.palletsService.movePallet(id, movePalletDto, req.user);
  }

  @Get(":id/audit-logs")
  @RequirePalletAccess()
  getAuditLogs(
    @Param("id") id: string,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ) {
    return this.palletsService.getAuditLogs(id, req.user);
  }
}
