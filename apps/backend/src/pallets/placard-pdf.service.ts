import { Injectable, Logger } from "@nestjs/common";
import { PDFDocument, PDFFont, rgb, StandardFonts, PageSizes } from "pdf-lib";
import * as bwip from "bwip-js";
import { PlacardDataResponseDto } from "./dto/placard.dto";

@Injectable()
export class PlacardPdfService {
  private readonly logger = new Logger(PlacardPdfService.name);

  /**
   * Helper function to wrap text at word boundaries
   */
  private wrapText(
    text: string,
    font: PDFFont,
    fontSize: number,
    maxWidth: number
  ): string[] {
    if (!text) return [];

    const words = text.split(" ");
    const lines: string[] = [];
    let currentLine = "";

    for (const word of words) {
      const testLine = currentLine ? `${currentLine} ${word}` : word;
      const testWidth = font.widthOfTextAtSize(testLine, fontSize);

      if (testWidth <= maxWidth) {
        currentLine = testLine;
      } else {
        if (currentLine) {
          lines.push(currentLine);
          currentLine = word;
        } else {
          // Single word is too long, force it on its own line
          lines.push(word);
        }
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines;
  }

  /**
   * Helper function to check if a field has meaningful content
   */
  private hasContent(value: any): boolean {
    return (
      value !== null && value !== undefined && value !== "" && value !== "N/A"
    );
  }

  async createPlacardPage(
    pdfDoc: PDFDocument,
    placardData: PlacardDataResponseDto,
    palletCount: string
  ): Promise<void> {
    try {
      this.logger.log(
        `Creating placard page for pallet ${placardData.palletId}`
      );
      // Create a landscape page by providing dimensions directly: [width, height]
      const page = pdfDoc.addPage([PageSizes.Letter[1], PageSizes.Letter[0]]);
      const { width, height } = page.getSize();
      const margin = 36; // 0.5 inch

      // Embed fonts
      const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
      const fontBold = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
      const fontMono = await pdfDoc.embedFont(StandardFonts.Courier);

      // --- Draw Header ---
      const headerY = height - margin - 20;
      // Location
      page.drawText("LOCATION", {
        x: margin,
        y: headerY,
        font: fontBold,
        size: 14,
      });
      page.drawText(placardData.currentLocation.name, {
        x: margin,
        y: headerY - 35,
        font: fontBold,
        size: 28,
      });

      // Items List Section (fully right-aligned)
      const rightEdge = width - margin; // Right edge position
      let currentY = headerY;

      if (placardData.contents && placardData.contents.length > 0) {
        // Right-align the "ITEMS" label
        const itemsLabelWidth = fontBold.widthOfTextAtSize("ITEMS", 14);
        page.drawText("ITEMS", {
          x: rightEdge - itemsLabelWidth,
          y: currentY,
          font: fontBold,
          size: 14,
        });
        currentY -= 25;

        const maxItemsToShow = 6; // Limit items to prevent overflow
        const itemsToShow = placardData.contents.slice(0, maxItemsToShow);

        for (const item of itemsToShow) {
          const itemText = `${item.quantity}x ${item.itemName}`;
          const itemTextWidth = font.widthOfTextAtSize(itemText, 12);
          page.drawText(itemText, {
            x: rightEdge - itemTextWidth,
            y: currentY,
            font: font,
            size: 12,
          });
          currentY -= 16;
        }

        // Show "..." if there are more items (right-aligned)
        if (placardData.contents.length > maxItemsToShow) {
          const ellipsisWidth = font.widthOfTextAtSize("...", 12);
          page.drawText("...", {
            x: rightEdge - ellipsisWidth,
            y: currentY,
            font: font,
            size: 12,
          });
        }
      }

      // Pallet Count
      const palletCountWidth = fontBold.widthOfTextAtSize(palletCount, 18);
      page.drawText(palletCount, {
        x: (width - palletCountWidth) / 2,
        y: headerY - 60,
        font: fontBold,
        size: 18,
      });

      // Header line
      page.drawLine({
        start: { x: margin, y: headerY - 80 },
        end: { x: width - margin, y: headerY - 80 },
        thickness: 2,
        color: rgb(0, 0, 0),
        opacity: 0.5,
      });

      // --- Draw Date and Destination Code Section ---
      const dateCodeY = headerY - 110;
      const destinationCodeFontSize = 26;

      // Format and display received date (left side)
      if (placardData.dateCreated) {
        const dateObj = new Date(placardData.dateCreated);
        const formattedDate = dateObj.toLocaleDateString("en-US", {
          month: "2-digit",
          day: "2-digit",
          year: "numeric",
        });


        page.drawText(formattedDate, {
          x: margin,
          y: dateCodeY,
          font: font,
          size: destinationCodeFontSize,
        });
      }

      // Display destination code (right side)
      if (this.hasContent(placardData.destinationCode)) {
        const rightEdge = width - margin;

        // Right-align the destination code
        const codeWidth = font.widthOfTextAtSize(
          placardData.destinationCode,
          destinationCodeFontSize
        );
        page.drawText(placardData.destinationCode, {
          x: rightEdge - codeWidth,
          y: dateCodeY,
          font: font,
          size: destinationCodeFontSize,
        });
      }

      // --- Draw Main Content (SHIP TO) ---
      const shipToY = height / 2 - 20; // Adjusted to accommodate date/code section

      // Handle destination text wrapping
      const maxDestinationWidth = width - margin * 2; // Full width minus margins
      const destinationFontSize = 96;
      const destinationText = placardData.shipToDestination || "";

      // Check if destination text fits on one line
      const singleLineWidth = fontBold.widthOfTextAtSize(
        destinationText,
        destinationFontSize
      );

      if (singleLineWidth <= maxDestinationWidth) {
        // Single line - center it
        page.drawText(destinationText, {
          x: (width - singleLineWidth) / 2,
          y: shipToY,
          font: fontBold,
          size: destinationFontSize,
        });
      } else {
        // Multiple lines - wrap text
        const wrappedLines = this.wrapText(
          destinationText,
          fontBold,
          destinationFontSize,
          maxDestinationWidth
        );
        const lineHeight = destinationFontSize + 10; // Add some spacing between lines
        const totalTextHeight = wrappedLines.length * lineHeight;
        let startY = shipToY + totalTextHeight / 2; // Center the text block vertically

        for (let i = 0; i < wrappedLines.length; i++) {
          const line = wrappedLines[i];
          const lineWidth = fontBold.widthOfTextAtSize(
            line,
            destinationFontSize
          );
          page.drawText(line, {
            x: (width - lineWidth) / 2, // Center each line
            y: startY - i * lineHeight,
            font: fontBold,
            size: destinationFontSize,
          });
        }
      }

      // --- Draw Footer ---
      const footerY = margin + 140;
      page.drawLine({
        start: { x: margin, y: footerY },
        end: { x: width - margin, y: footerY },
        thickness: 2,
        color: rgb(0, 0, 0),
        opacity: 0.5,
      });

      // Barcode
      const barcodePng = await bwip.toBuffer({
        bcid: "code128",
        text: placardData.barcode,
        scale: 3, // Keep scale for high resolution
        height: 15, // Height in mm. The final drawn size is controlled below.
        includetext: false,
        textxalign: "center",
      });
      const barcodeImage = await pdfDoc.embedPng(barcodePng);

      // Define a fixed height for the barcode and scale the width proportionally
      const desiredBarcodeHeight = 75; // points (approx 1.04 inches)
      const scaleFactor = desiredBarcodeHeight / barcodeImage.height;
      const scaledWidth = barcodeImage.width * scaleFactor;

      page.drawImage(barcodeImage, {
        x: (width - scaledWidth) / 2,
        y: footerY - 110, // Y position for the bottom of the barcode
        width: scaledWidth,
        height: desiredBarcodeHeight,
      });

      // Barcode text
      const barcodeTextWidth = fontMono.widthOfTextAtSize(
        placardData.barcode,
        36
      );
      page.drawText(placardData.barcode, {
        x: (width - barcodeTextWidth) / 2,
        y: footerY - 135,
        font: fontMono,
        size: 36,
      });

      // Description section (fully right-aligned in bottom right corner)
      if (this.hasContent(placardData.description)) {
        const rightEdge = width - margin; // Right edge position
        const descriptionY = footerY - 25; // Positioned above the footer line

        // Wrap description text if needed (no "DESCRIPTION" label)
        const maxDescriptionWidth = 180; // Adjusted for new positioning
        const descriptionLines = this.wrapText(
          placardData.description,
          font,
          10,
          maxDescriptionWidth
        );

        // Limit to 3 lines to prevent overflow
        const linesToShow = descriptionLines.slice(0, 3);
        let descCurrentY = descriptionY;

        for (const line of linesToShow) {
          const lineWidth = font.widthOfTextAtSize(line, 10);
          page.drawText(line, {
            x: rightEdge - lineWidth,
            y: descCurrentY,
            font: font,
            size: 10,
          });
          descCurrentY -= 14;
        }

        // Add "..." if description was truncated (right-aligned)
        if (descriptionLines.length > 3) {
          const ellipsisWidth = font.widthOfTextAtSize("...", 10);
          page.drawText("...", {
            x: rightEdge - ellipsisWidth,
            y: descCurrentY,
            font: font,
            size: 10,
          });
        }
      }

      this.logger.log(
        `Successfully created placard page for pallet ${placardData.palletId}`
      );
    } catch (error) {
      this.logger.error(
        `Error creating placard page for pallet ${placardData.palletId}:`,
        error
      );
      throw error;
    }
  }
}
