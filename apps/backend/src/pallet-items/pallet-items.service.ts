import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { PalletItem, Pallet, Item, Role as PrismaRole } from "@prisma/client";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";
import { AuditLogService } from "../audit-log/audit-log.service";

@Injectable()
export class PalletItemsService {
  constructor(
    private prisma: PrismaService,
    private auditLogService: AuditLogService
  ) {}

  /**
   * Adds an item with a specified quantity to a pallet.
   * If the item already exists on the pallet, its quantity is updated.
   * Throws errors if pallet or item not found, or if quantity is invalid.
   */
  async addItemToPallet(
    palletId: string,
    itemId: string,
    quantity: number,
    currentUser: AuthenticatedUser
  ): Promise<PalletItem> {
    if (quantity <= 0) {
      throw new BadRequestException("Quantity must be greater than zero.");
    }

    // 1. Check if <PERSON><PERSON><PERSON> and It<PERSON> exist within the user's tenant
    // Also fetch pallet with location and warehouse for access check
    const palletRecord = await this.prisma.pallet.findFirst({
      where: {
        id: palletId,
        location: {
          warehouse: {
            tenantId: currentUser.tenantId, // Security: ensure tenant access
          },
        },
      },
      include: { location: true },
    });
    const itemRecord = await this.prisma.item.findUnique({
      where: { id: itemId, tenantId: currentUser.tenantId },
    });

    if (!palletRecord) {
      throw new NotFoundException(
        `Pallet with ID "${palletId}" not found in your tenant.`
      );
    }
    if (!itemRecord) {
      throw new NotFoundException(
        `Item with ID "${itemId}" not found in your tenant.`
      );
    }

    // Warehouse Access Check for the pallet
    if (palletRecord.location && palletRecord.location.warehouseId) {
      const userWarehouseIds =
        currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
      if (
        currentUser.role !== PrismaRole.TENANT_ADMIN &&
        !userWarehouseIds.includes(palletRecord.location.warehouseId)
      ) {
        throw new NotFoundException(
          `You do not have access to the warehouse containing pallet "${palletId}".`
        );
      }
    } else if (palletRecord.location === null) {
      // If pallet has no location, it might be in a conceptual "in-transit" or default state.
      // Access depends on policy: if all users in tenant can access unlocated pallets, this is fine.
      // Or, it might be restricted. For now, assume accessible if pallet is in tenant and no specific location denies access.
    }

    const existingPalletItem = await this.prisma.palletItem.findUnique({
      where: {
        pallet_item_unique: {
          palletId: palletId,
          itemId: itemId,
        },
      },
    });

    if (existingPalletItem) {
      const updatedQuantity = existingPalletItem.quantity + quantity;
      const updatedPalletItem = await this.prisma.palletItem.update({
        where: {
          pallet_item_unique: {
            palletId: palletId,
            itemId: itemId,
          },
        },
        data: {
          quantity: updatedQuantity,
        },
      });

      // Create audit log for item quantity update
      await this.auditLogService.create({
        userId: currentUser.id,
        userEmail: currentUser.email,
        action: "UPDATE_PALLET_ITEM_QUANTITY",
        entity: "PALLET",
        entityId: palletId,
        tenantId: currentUser.tenantId,
        details: {
          itemId: itemId,
          itemName: itemRecord.name,
          previousQuantity: existingPalletItem.quantity,
          addedQuantity: quantity,
          newQuantity: updatedQuantity,
          palletBarcode: palletRecord.barcode,
        },
      });

      return updatedPalletItem;
    } else {
      const newPalletItem = await this.prisma.palletItem.create({
        data: {
          palletId: palletId,
          itemId: itemId,
          quantity: quantity,
          // Note: tenantId removed - access controlled through pallet->location->warehouse->tenant
        },
      });

      // Create audit log for new item addition
      await this.auditLogService.create({
        userId: currentUser.id,
        userEmail: currentUser.email,
        action: "ADD_ITEM_TO_PALLET",
        entity: "PALLET",
        entityId: palletId,
        tenantId: currentUser.tenantId,
        details: {
          itemId: itemId,
          itemName: itemRecord.name,
          quantity: quantity,
          palletBarcode: palletRecord.barcode,
        },
      });

      return newPalletItem;
    }
  }

  /**
   * Removes an item completely from a specific pallet.
   * Throws NotFoundException if the item isn't found on the specified pallet.
   */
  async removeItemFromPallet(
    palletId: string,
    itemId: string,
    currentUser: AuthenticatedUser
  ): Promise<PalletItem> {
    // Fetch the pallet first to verify existence and warehouse access
    const palletRecord = await this.prisma.pallet.findFirst({
      where: {
        id: palletId,
        location: {
          warehouse: {
            tenantId: currentUser.tenantId, // Security: ensure tenant access
          },
        },
      },
      include: { location: true },
    });

    if (!palletRecord) {
      throw new NotFoundException(
        `Pallet with ID "${palletId}" not found in your tenant.`
      );
    }

    // Warehouse Access Check
    if (palletRecord.location && palletRecord.location.warehouseId) {
      const userWarehouseIds =
        currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
      if (
        currentUser.role !== PrismaRole.TENANT_ADMIN &&
        !userWarehouseIds.includes(palletRecord.location.warehouseId)
      ) {
        throw new NotFoundException(
          `You do not have access to the warehouse containing pallet "${palletId}".`
        );
      }
    } // Else: pallet has no location, assume accessible within tenant

    // Now that pallet access is confirmed, find and delete the pallet item
    const palletItemToDelete = await this.prisma.palletItem.findUnique({
      where: {
        pallet_item_unique: {
          palletId,
          itemId,
        },
      },
    });

    if (!palletItemToDelete) {
      throw new NotFoundException(
        `Item with ID "${itemId}" not found on pallet "${palletId}" within your tenant.`
      );
    }

    // Get item details for audit log
    const itemRecord = await this.prisma.item.findUnique({
      where: { id: itemId, tenantId: currentUser.tenantId },
      select: { name: true },
    });

    const deletedPalletItem = await this.prisma.palletItem.delete({
      where: {
        pallet_item_unique: {
          palletId,
          itemId,
        },
      },
    });

    // Create audit log for item removal
    await this.auditLogService.create({
      userId: currentUser.id,
      userEmail: currentUser.email,
      action: "REMOVE_ITEM_FROM_PALLET",
      entity: "PALLET",
      entityId: palletId,
      tenantId: currentUser.tenantId,
      details: {
        itemId: itemId,
        itemName: itemRecord?.name || "Unknown Item",
        removedQuantity: palletItemToDelete.quantity,
        palletBarcode: palletRecord.barcode,
      },
    });

    return deletedPalletItem;
  }

  /**
   * Updates the quantity of an existing item on a specific pallet.
   * Throws NotFoundException if the item isn't found on the specified pallet.
   * Throws BadRequestException if the new quantity is invalid.
   */
  async updatePalletItemQuantity(
    palletId: string,
    itemId: string,
    newQuantity: number,
    currentUser: AuthenticatedUser
  ): Promise<PalletItem> {
    if (newQuantity <= 0) {
      throw new BadRequestException("New quantity must be greater than zero.");
    }

    // Fetch the pallet first to verify existence and warehouse access
    const palletRecord = await this.prisma.pallet.findFirst({
      where: {
        id: palletId,
        location: {
          warehouse: {
            tenantId: currentUser.tenantId, // Security: ensure tenant access
          },
        },
      },
      include: { location: true },
    });

    if (!palletRecord) {
      throw new NotFoundException(
        `Pallet with ID "${palletId}" not found in your tenant.`
      );
    }

    // Warehouse Access Check
    if (palletRecord.location && palletRecord.location.warehouseId) {
      const userWarehouseIds =
        currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
      if (
        currentUser.role !== PrismaRole.TENANT_ADMIN &&
        !userWarehouseIds.includes(palletRecord.location.warehouseId)
      ) {
        throw new NotFoundException(
          `You do not have access to the warehouse containing pallet "${palletId}".`
        );
      }
    } // Else: pallet has no location, assume accessible within tenant

    // Check if the pallet item exists for this tenant, pallet, and item
    const existingPalletItem = await this.prisma.palletItem.findUnique({
      where: {
        pallet_item_unique: {
          palletId,
          itemId,
        },
      },
    });

    if (!existingPalletItem) {
      throw new NotFoundException(
        `Item with ID "${itemId}" not found on pallet "${palletId}" within your tenant.`
      );
    }

    // Get item details for audit log
    const itemRecord = await this.prisma.item.findUnique({
      where: { id: itemId, tenantId: currentUser.tenantId },
      select: { name: true },
    });

    const updatedPalletItem = await this.prisma.palletItem.update({
      where: {
        pallet_item_unique: {
          palletId,
          itemId,
        },
      },
      data: {
        quantity: newQuantity,
      },
    });

    // Create audit log for quantity update
    await this.auditLogService.create({
      userId: currentUser.id,
      userEmail: currentUser.email,
      action: "UPDATE_PALLET_ITEM_QUANTITY",
      entity: "PALLET",
      entityId: palletId,
      tenantId: currentUser.tenantId,
      details: {
        itemId: itemId,
        itemName: itemRecord?.name || "Unknown Item",
        previousQuantity: existingPalletItem.quantity,
        newQuantity: newQuantity,
        quantityChange: newQuantity - existingPalletItem.quantity,
        palletBarcode: palletRecord.barcode,
      },
    });

    return updatedPalletItem;
  }
}
