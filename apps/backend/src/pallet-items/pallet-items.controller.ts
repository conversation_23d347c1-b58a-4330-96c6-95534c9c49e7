import {
  Controller,
  Post,
  Body,
  Param,
  Delete,
  Patch,
  HttpCode,
  HttpStatus,
  UsePipes,
  ValidationPipe,
  UseGuards,
  Req,
  UseInterceptors,
} from "@nestjs/common";
import { PalletItemsService } from "./pallet-items.service";
import { AddPalletItemDto } from "../pallets/dto/add-pallet-item.dto"; // Adjust path if needed
import { UpdatePalletItemDto } from "../pallets/dto/update-pallet-item.dto"; // Import the new DTO
import { PalletItem } from "@prisma/client";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { WarehousePermissionGuard } from "../auth/guards/warehouse-permission.guard";
import {
  RequirePalletAccess,
  RequestWithWarehouseContext,
} from "../auth/decorators/warehouse-permission.decorator";
import { EnhancedUserPayload } from "../auth/types";
import { LogAction } from "../audit-log/decorators/log-action.decorator";
import { AuditLogInterceptor } from "../audit-log/interceptors/audit-log.interceptor";

// Suggest nesting under pallets: /api/pallets/:palletId/items
// If we create a top-level controller, adjust the route
// @Controller('pallet-items') // Option 1: Top-level
@Controller("pallets/:palletId/items") // Option 2: Nested (preferred REST pattern)
@UseGuards(JwtAuthGuard, WarehousePermissionGuard)
@UseInterceptors(AuditLogInterceptor)
export class PalletItemsController {
  constructor(private readonly palletItemsService: PalletItemsService) {}

  @Post()
  @RequirePalletAccess()
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  @LogAction({
    action: "ADD_ITEM_TO_PALLET",
    entity: "Pallet",
    getEntityId: (context) =>
      context.switchToHttp().getRequest().params.palletId,
  })
  async addItemToPallet(
    @Param("palletId") palletId: string,
    @Body() addPalletItemDto: AddPalletItemDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ): Promise<PalletItem> {
    return this.palletItemsService.addItemToPallet(
      palletId,
      addPalletItemDto.itemId,
      addPalletItemDto.quantity,
      req.user
    );
  }

  @Delete(":itemId")
  @RequirePalletAccess()
  @HttpCode(HttpStatus.NO_CONTENT)
  @LogAction({
    action: "REMOVE_ITEM_FROM_PALLET",
    entity: "Pallet",
    getEntityId: (context) =>
      context.switchToHttp().getRequest().params.palletId,
  })
  async removeItemFromPallet(
    @Param("palletId") palletId: string,
    @Param("itemId") itemId: string,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ): Promise<void> {
    await this.palletItemsService.removeItemFromPallet(
      palletId,
      itemId,
      req.user
    );
  }

  @Patch(":itemId")
  @RequirePalletAccess()
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  @LogAction({
    action: "UPDATE_PALLET_ITEM_QUANTITY",
    entity: "Pallet",
    getEntityId: (context) =>
      context.switchToHttp().getRequest().params.palletId,
  })
  async updatePalletItemQuantity(
    @Param("palletId") palletId: string,
    @Param("itemId") itemId: string,
    @Body() updatePalletItemDto: UpdatePalletItemDto,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>
  ): Promise<PalletItem> {
    return this.palletItemsService.updatePalletItemQuantity(
      palletId,
      itemId,
      updatePalletItemDto.quantity,
      req.user
    );
  }
}
