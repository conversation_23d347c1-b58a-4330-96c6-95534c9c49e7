import { Modu<PERSON> } from "@nestjs/common";
import { PalletItemsService } from "./pallet-items.service";
import { PalletItemsController } from "./pallet-items.controller";
import { PrismaModule } from "../prisma/prisma.module"; // Import PrismaModule
import { AuditLogModule } from "../audit-log/audit-log.module"; // Import AuditLogModule
import { AuthModule } from "../auth/auth.module"; // Import AuthModule for WarehousePermissionGuard
import { WarehousesModule } from "@/warehouses/warehouses.module";

@Module({
  imports: [PrismaModule, AuditLogModule, AuthModule, WarehousesModule], // Make PrismaService, AuditLogService, and WarehousePermissionGuard available
  controllers: [PalletItemsController],
  providers: [PalletItemsService],
})
export class PalletItemsModule {}
