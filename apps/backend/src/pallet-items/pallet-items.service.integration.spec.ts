import { Test, TestingModule } from "@nestjs/testing";
import { PalletItemsService } from "./pallet-items.service";
import { PrismaService } from "../prisma/prisma.service";
import { PalletItemsModule } from "./pallet-items.module";
import { PrismaModule } from "../prisma/prisma.module";
import { cleanupDatabase } from "../util/cleanup-database";
import {
  Warehouse,
  Location,
  LocationType,
  Item,
  Pallet,
  Tenant,
  Role as PrismaRole,
  PalletItem,
} from "@prisma/client";
import {
  BadRequestException,
  NotFoundException,
  ForbiddenException,
} from "@nestjs/common";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";

describe("PalletItemsService - Integration", () => {
  let service: PalletItemsService;
  let prisma: PrismaService;

  // Tenant and User Mocks
  let testTenant: Tenant;
  let mockAdminUser: AuthenticatedUser; // User with TENANT_ADMIN role
  let mockWarehouseManagerUser: AuthenticatedUser; // User with WAREHOUSE_MANAGER role
  let mockOtherTenant: Tenant;
  let mockOtherTenantUser: AuthenticatedUser;

  // Test Data (will be tenant-scoped)
  let testWarehouse1: Warehouse;
  let testLocation1: Location;
  let testItem1: Item;
  let testItem2: Item;
  let testPallet1: Pallet;

  let testWarehouse2: Warehouse; // For testing cross-warehouse access within the same tenant
  let testLocation2: Location;
  let testPallet2: Pallet;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [PalletItemsModule, PrismaModule], // Import necessary modules
    }).compile();

    service = module.get<PalletItemsService>(PalletItemsService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  // Clean up database before each test
  beforeEach(async () => {
    await cleanupDatabase(prisma);

    // 1. Create Tenants
    testTenant = await prisma.tenant.create({
      data: { name: "Test Tenant PI" },
    });
    mockOtherTenant = await prisma.tenant.create({
      data: { name: "Other Tenant PI" },
    });

    // 2. Create Mock Users
    mockAdminUser = {
      id: "admin-user-pi",
      email: "<EMAIL>",
      role: PrismaRole.TENANT_ADMIN,
      tenantId: testTenant.id,
      name: "Admin User PI",
      authUserId: "auth-admin-user-pi",
      warehouseUsers: [], // TENANT_ADMIN typically has access to all warehouses in their tenant
    };
    mockWarehouseManagerUser = {
      id: "whmanager-user-pi",
      email: "<EMAIL>",
      role: PrismaRole.WAREHOUSE_MANAGER,
      tenantId: testTenant.id,
      name: "Warehouse Manager PI",
      authUserId: "auth-whmanager-user-pi",
      warehouseUsers: [], // Will be populated after warehouse creation
    };
    mockOtherTenantUser = {
      id: "other-tenant-user-pi",
      email: "<EMAIL>",
      role: PrismaRole.TENANT_ADMIN,
      tenantId: mockOtherTenant.id,
      name: "Other Tenant User PI",
      authUserId: "auth-other-tenant-user-pi",
      warehouseUsers: [],
    };

    // Create corresponding User records in the database
    await prisma.user.createMany({
      data: [
        {
          id: mockAdminUser.id,
          email: mockAdminUser.email,
          role: mockAdminUser.role,
          tenantId: mockAdminUser.tenantId,
          name: mockAdminUser.name,
          authUserId: mockAdminUser.authUserId,
        },
        {
          id: mockWarehouseManagerUser.id,
          email: mockWarehouseManagerUser.email,
          role: mockWarehouseManagerUser.role,
          tenantId: mockWarehouseManagerUser.tenantId,
          name: mockWarehouseManagerUser.name,
          authUserId: mockWarehouseManagerUser.authUserId,
        },
        {
          id: mockOtherTenantUser.id,
          email: mockOtherTenantUser.email,
          role: mockOtherTenantUser.role,
          tenantId: mockOtherTenantUser.tenantId,
          name: mockOtherTenantUser.name,
          authUserId: mockOtherTenantUser.authUserId,
        },
      ],
    });

    // 3. Create Tenant-Scoped Warehouses
    testWarehouse1 = await prisma.warehouse.create({
      data: { name: "Test Warehouse 1 PI", tenantId: testTenant.id },
    });
    testWarehouse2 = await prisma.warehouse.create({
      // For same-tenant, different warehouse scenarios
      data: { name: "Test Warehouse 2 PI", tenantId: testTenant.id },
    });

    // 4. Assign Warehouse to mockWarehouseManagerUser
    await prisma.warehouseUser.create({
      data: {
        userId: mockWarehouseManagerUser.id, // Prisma will need a User record if FK constraint is strict. For mock, this is okay.
        // For real tests, ensure User records are also created for mock users or use a different strategy.
        warehouseId: testWarehouse1.id,
        role: PrismaRole.WAREHOUSE_MANAGER,
      },
    });
    // Update the mock user object. In a real scenario where user objects are fetched, this would be automatic.
    mockWarehouseManagerUser.warehouseUsers = [
      { warehouseId: testWarehouse1.id, role: PrismaRole.WAREHOUSE_MANAGER },
    ];

    // 5. Create Tenant-Scoped Locations
    testLocation1 = await prisma.location.create({
      data: {
        name: "Test Location 1 PI",
        warehouseId: testWarehouse1.id,
        locationType: LocationType.ZONE,
      },
    });
    testLocation2 = await prisma.location.create({
      data: {
        name: "Test Location 2 PI",
        warehouseId: testWarehouse2.id, // Belongs to testWarehouse2
        locationType: LocationType.ZONE,
      },
    });

    // 6. Create Tenant-Scoped Items
    testItem1 = await prisma.item.create({
      data: {
        name: "Test Item 1 PI",
        sku: "PI-ITEM-1",
        tenantId: testTenant.id,
      },
    });
    testItem2 = await prisma.item.create({
      data: {
        name: "Test Item 2 PI",
        sku: "PI-ITEM-2",
        tenantId: testTenant.id,
      },
    });

    // 7. Create Warehouse-Scoped Pallets
    testPallet1 = await prisma.pallet.create({
      data: {
        label: "Test Pallet 1",
        locationId: testLocation1.id,
        barcode: `TEST-PALLET-1-${Date.now()}`,
        shipToDestination: "Test Destination 1",
      },
    });
    testPallet2 = await prisma.pallet.create({
      // Pallet in testWarehouse2 (via testLocation2)
      data: {
        label: "Test Pallet 2",
        locationId: testLocation2.id,
        barcode: `TEST-PALLET-2-${Date.now()}`,
        shipToDestination: "Test Destination 2",
      },
    });
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  // --- Test Cases Start Here ---

  describe("addItemToPallet", () => {
    it("should add a new item to an empty pallet for TENANT_ADMIN", async () => {
      const quantityToAdd = 10;
      const palletItem = await service.addItemToPallet(
        testPallet1.id,
        testItem1.id,
        quantityToAdd,
        mockAdminUser // Pass current user
      );

      expect(palletItem).toBeDefined();
      expect(palletItem.palletId).toBe(testPallet1.id);
      expect(palletItem.itemId).toBe(testItem1.id);
      expect(palletItem.quantity).toBe(quantityToAdd);
      const dbItem = await prisma.palletItem.findUnique({
        where: {
          pallet_item_unique: {
            palletId: testPallet1.id,
            itemId: testItem1.id,
          },
        },
      });
      expect(dbItem).not.toBeNull();
      expect(dbItem?.quantity).toBe(quantityToAdd);
    });

    it("should add a new item to an empty pallet for WAREHOUSE_MANAGER with access", async () => {
      const quantityToAdd = 12;
      const palletItem = await service.addItemToPallet(
        testPallet1.id, // Pallet in testWarehouse1
        testItem1.id,
        quantityToAdd,
        mockWarehouseManagerUser // User has access to testWarehouse1
      );
      expect(palletItem.quantity).toBe(quantityToAdd);
      // Tenant access validated through warehouse-scoped filtering
    });

    it("should update quantity for TENANT_ADMIN", async () => {
      const initialQuantity = 5;
      await prisma.palletItem.create({
        data: {
          palletId: testPallet1.id,
          itemId: testItem1.id,
          quantity: initialQuantity,
        },
      });

      const quantityToAdd = 7;
      const palletItem = await service.addItemToPallet(
        testPallet1.id,
        testItem1.id,
        quantityToAdd,
        mockAdminUser // Pass current user
      );

      expect(palletItem.quantity).toBe(initialQuantity + quantityToAdd);
      // Tenant access validated through warehouse-scoped filtering
    });

    it("should throw NotFoundException if pallet does not exist in user's tenant", async () => {
      const nonExistentPalletId = "non-existent-pallet-id";
      await expect(
        service.addItemToPallet(
          nonExistentPalletId,
          testItem1.id,
          10,
          mockAdminUser
        )
      ).rejects.toThrow(
        new NotFoundException(
          `Pallet with ID "${nonExistentPalletId}" not found in your tenant.`
        )
      );
    });

    it("should throw NotFoundException if item does not exist in user's tenant", async () => {
      const nonExistentItemId = "non-existent-item-id";
      await expect(
        service.addItemToPallet(
          testPallet1.id,
          nonExistentItemId,
          10,
          mockAdminUser
        )
      ).rejects.toThrow(
        new NotFoundException(
          `Item with ID "${nonExistentItemId}" not found in your tenant.`
        )
      );
    });

    it("should throw NotFoundException for WAREHOUSE_MANAGER trying to access pallet in another permitted warehouse (if service logic restricts based on specific pallet location)", async () => {
      // Assuming mockWarehouseManagerUser ONLY has access to testWarehouse1. testPallet2 is in testWarehouse2.
      await expect(
        service.addItemToPallet(
          testPallet2.id,
          testItem1.id,
          10,
          mockWarehouseManagerUser
        )
      ).rejects.toThrow(
        new NotFoundException(
          `You do not have access to the warehouse containing pallet "${testPallet2.id}".`
        )
      );
    });

    it("should throw NotFoundException if TENANT_ADMIN tries to access pallet from another tenant", async () => {
      // Create warehouse and location for other tenant first
      const otherTenantWarehouse = await prisma.warehouse.create({
        data: {
          name: "Other Tenant Warehouse",
          tenantId: mockOtherTenant.id,
        },
      });
      const otherTenantLocation = await prisma.location.create({
        data: {
          name: "Other Tenant Location",
          warehouseId: otherTenantWarehouse.id,
          locationType: LocationType.ZONE,
        },
      });
      const otherTenantPallet = await prisma.pallet.create({
        data: {
          label: "Other Tenant Pallet",
          locationId: otherTenantLocation.id,
          barcode: `OTHER-TENANT-PALLET-${Date.now()}`,
          shipToDestination: "Other Tenant Destination",
        }, // Create a pallet in another tenant
      });
      await expect(
        service.addItemToPallet(
          otherTenantPallet.id,
          testItem1.id,
          10,
          mockAdminUser
        ) // mockAdminUser is from testTenant
      ).rejects.toThrow(
        new NotFoundException(
          `Pallet with ID "${otherTenantPallet.id}" not found in your tenant.`
        )
      );
    });

    it("should throw BadRequestException if quantity is zero", async () => {
      await expect(
        service.addItemToPallet(testPallet1.id, testItem1.id, 0, mockAdminUser)
      ).rejects.toThrow(BadRequestException);
    });

    it("should throw BadRequestException if quantity is negative", async () => {
      await expect(
        service.addItemToPallet(testPallet1.id, testItem1.id, -5, mockAdminUser)
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe("removeItemFromPallet", () => {
    beforeEach(async () => {
      await prisma.palletItem.create({
        data: {
          palletId: testPallet1.id,
          itemId: testItem1.id,
          quantity: 15,
        },
      });
    });

    it("should remove an existing item from the pallet for TENANT_ADMIN", async () => {
      const removedItem = await service.removeItemFromPallet(
        testPallet1.id,
        testItem1.id,
        mockAdminUser // Pass current user
      );
      expect(removedItem).toBeDefined();
      // Tenant access validated through warehouse-scoped filtering

      const dbItem = await prisma.palletItem.findUnique({
        where: {
          pallet_item_unique: {
            palletId: testPallet1.id,
            itemId: testItem1.id,
          },
        },
      });
      expect(dbItem).toBeNull();
    });

    it("should throw NotFoundException if WAREHOUSE_MANAGER tries to remove from pallet in non-permitted warehouse", async () => {
      // Pallet 2 is in Warehouse 2, manager only has access to Warehouse 1
      // First, add an item to pallet2 to make the test valid
      await prisma.palletItem.create({
        data: {
          palletId: testPallet2.id,
          itemId: testItem1.id, // Item from the same tenant
          quantity: 5,
        },
      });
      await expect(
        service.removeItemFromPallet(
          testPallet2.id,
          testItem1.id,
          mockWarehouseManagerUser
        )
      ).rejects.toThrow(
        new NotFoundException(
          `You do not have access to the warehouse containing pallet "${testPallet2.id}".`
        )
      );
    });

    it("should throw NotFoundException if item is not on the pallet (TENANT_ADMIN)", async () => {
      await expect(
        service.removeItemFromPallet(
          testPallet1.id,
          testItem2.id,
          mockAdminUser
        ) // testItem2 was not added
      ).rejects.toThrow(
        new NotFoundException(
          `Item with ID "${testItem2.id}" not found on pallet "${testPallet1.id}" within your tenant.`
        )
      );
    });

    it("should throw NotFoundException if pallet does not exist in tenant (TENANT_ADMIN)", async () => {
      const nonExistentPalletId = "non-existent-pallet-id-remove";
      await expect(
        service.removeItemFromPallet(
          nonExistentPalletId,
          testItem1.id,
          mockAdminUser
        )
      ).rejects.toThrow(
        new NotFoundException(
          `Pallet with ID "${nonExistentPalletId}" not found in your tenant.`
        )
      );
    });
  });

  describe("updatePalletItemQuantity", () => {
    const initialQuantity = 10;
    const newQuantity = 25;

    beforeEach(async () => {
      await prisma.palletItem.upsert({
        where: {
          pallet_item_unique: {
            palletId: testPallet1.id,
            itemId: testItem1.id,
          },
        },
        update: { quantity: initialQuantity },
        create: {
          palletId: testPallet1.id,
          itemId: testItem1.id,
          quantity: initialQuantity,
        },
      });
    });

    it("should update quantity for TENANT_ADMIN", async () => {
      const updatedItem = await service.updatePalletItemQuantity(
        testPallet1.id,
        testItem1.id,
        newQuantity,
        mockAdminUser // Pass current user
      );
      expect(updatedItem.quantity).toBe(newQuantity);
      // Tenant access validated through warehouse-scoped filtering

      const dbItem = await prisma.palletItem.findFirst({
        where: {
          palletId: testPallet1.id,
          itemId: testItem1.id,
        },
      });
      expect(dbItem?.quantity).toBe(newQuantity);
    });

    it("should throw NotFoundException for WAREHOUSE_MANAGER trying to update pallet in non-permitted warehouse", async () => {
      // Ensure pallet2 has an item to update
      await prisma.palletItem.create({
        data: {
          palletId: testPallet2.id,
          itemId: testItem1.id,
          quantity: 5,
        },
      });
      await expect(
        service.updatePalletItemQuantity(
          testPallet2.id,
          testItem1.id,
          newQuantity,
          mockWarehouseManagerUser
        )
      ).rejects.toThrow(
        new NotFoundException(
          `You do not have access to the warehouse containing pallet "${testPallet2.id}".`
        )
      );
    });

    it("should throw NotFoundException if item not on pallet (TENANT_ADMIN)", async () => {
      await expect(
        service.updatePalletItemQuantity(
          testPallet1.id,
          testItem2.id,
          newQuantity,
          mockAdminUser
        )
      ).rejects.toThrow(
        new NotFoundException(
          `Item with ID "${testItem2.id}" not found on pallet "${testPallet1.id}" within your tenant.`
        )
      );
    });

    it("should throw NotFoundException if pallet not in tenant (TENANT_ADMIN)", async () => {
      // Create warehouse and location for other tenant first
      const otherTenantWarehouse2 = await prisma.warehouse.create({
        data: {
          name: "Other Tenant Warehouse 2",
          tenantId: mockOtherTenant.id,
        },
      });
      const otherTenantLocation2 = await prisma.location.create({
        data: {
          name: "Other Tenant Location 2",
          warehouseId: otherTenantWarehouse2.id,
          locationType: LocationType.ZONE,
        },
      });
      const otherTenantPallet = await prisma.pallet.create({
        data: {
          label: "Other Tenant Pallet For Contents",
          locationId: otherTenantLocation2.id,
          barcode: `OTHER-TENANT-PALLET-2-${Date.now()}`,
          shipToDestination: "Other Tenant Destination 2",
        },
      });
      await expect(
        service.updatePalletItemQuantity(
          otherTenantPallet.id,
          testItem1.id,
          newQuantity,
          mockAdminUser
        )
      ).rejects.toThrow(
        new NotFoundException(
          `Pallet with ID "${otherTenantPallet.id}" not found in your tenant.`
        )
      );
    });

    it("should throw BadRequestException if new quantity is zero", async () => {
      await expect(
        service.updatePalletItemQuantity(
          testPallet1.id,
          testItem1.id,
          0,
          mockAdminUser
        )
      ).rejects.toThrow(BadRequestException);
    });
    it("should throw BadRequestException if new quantity is negative", async () => {
      await expect(
        service.updatePalletItemQuantity(
          testPallet1.id,
          testItem1.id,
          -5,
          mockAdminUser
        )
      ).rejects.toThrow(BadRequestException);
    });
  });
});
