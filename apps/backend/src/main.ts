// apps/backend/src/main.ts

import { NestFactory } from "@nestjs/core";
import { AppModule } from "./app.module";
import { ValidationPipe } from "@nestjs/common";
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import * as cookieParser from "cookie-parser"; // Import cookie-parser if needed for auth

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Use environment variables for configuration
  const port = process.env.PORT || 3001; // Default to 3001 if PORT not set

  // Enable CORS (configure origins as needed for production)
  app.enableCors({
    origin: "*", // Allow all origins for development - tighten this for production!
    credentials: true, // If you need to handle cookies/sessions
  });

  // Apply global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true, // Strip properties not defined in DTOs
      forbidNonWhitelisted: true, // Throw error if extra properties are sent
      transform: true, // Automatically transform payloads to DTO instances
    })
  );

  // Set global API prefix
  app.setGlobalPrefix("api");

  // Swagger (OpenAPI) setup
  const config = new DocumentBuilder()
    .setTitle('Quildora API')
    .setDescription('API documentation for the Quildora application')
    .setVersion('1.0')
    .addBearerAuth() // If you use Bearer token authentication
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document); // Swagger UI available at /api/docs

  await app.listen(port);
  console.log(`NestJS application is running on: http://localhost:${port}`);
  console.log(`Swagger documentation available at: http://localhost:${port}/api/docs`);
}
bootstrap();
