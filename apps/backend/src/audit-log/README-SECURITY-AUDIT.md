# Security Audit System

The Security Audit System provides comprehensive logging and monitoring of security-related events in the Quildora warehouse management system. It tracks warehouse access attempts, permission violations, authentication events, and security configuration changes.

## Features

- **Warehouse Access Logging**: Track successful and failed warehouse access attempts
- **Authentication Event Logging**: Monitor login/logout events and token validation
- **Permission Violation Tracking**: Log unauthorized access attempts and insufficient permissions
- **Security Configuration Auditing**: Track changes to user roles and warehouse permissions
- **Automatic Security Logging**: Interceptors that automatically log security events
- **Comprehensive Context**: Include IP addresses, user agents, and request details

## Components

### Services

#### SecurityAuditService
Core service for logging security events:
- `logWarehouseAccessGranted()` - Log successful warehouse access
- `logWarehouseAccessDenied()` - Log failed warehouse access attempts
- `logAuthenticationEvent()` - Log authentication events
- `logPermissionViolation()` - Log permission violations
- `logSecurityConfigChange()` - Log security configuration changes

#### AuthAuditService
Authentication-specific audit logging:
- `logTokenValidationSuccess()` - Log successful JWT validation
- `logTokenValidationFailure()` - Log failed JWT validation
- `logLoginSuccess()` - Log successful user login
- `logLoginFailure()` - Log failed login attempts
- `logLogout()` - Log user logout events

### Interceptors

#### SecurityAuditInterceptor
Automatically logs security events for protected endpoints using the `@SecurityAudit` decorator.

### Decorators

#### @SecurityAudit
Main decorator for marking endpoints for security audit logging:

```typescript
@SecurityAudit({
  operation: 'DELETE_PALLET',
  resource: 'Pallet',
  logSuccess: true,
  logFailure: true,
  highRisk: true
})
@Delete(':id')
async deletePallet(@Param('id') id: string) {
  // Implementation
}
```

#### Predefined Decorators
- `@AuditWarehouseAccess()` - For warehouse access operations
- `@AuditHighRisk()` - For high-risk operations (deletions, role changes)
- `@AuditAuthentication()` - For authentication operations
- `@AuditPermissions()` - For permission-sensitive operations
- `@AuditDataModification()` - For data modification operations
- `@AuditCrossTenant()` - For cross-tenant operations
- `@AuditBulkOperation()` - For bulk operations

## Usage Examples

### Basic Security Audit

```typescript
@Controller('pallets')
@UseGuards(JwtAuthGuard, WarehousePermissionGuard)
export class PalletsController {
  
  @AuditWarehouseAccess('VIEW_PALLETS', 'Pallet')
  @Get()
  async findAll() {
    // Implementation
  }

  @AuditHighRisk('DELETE_PALLET', 'Pallet')
  @Delete(':id')
  async remove(@Param('id') id: string) {
    // Implementation
  }
}
```

### Custom Security Audit with Context

```typescript
@SecurityAudit({
  operation: 'MOVE_PALLET',
  resource: 'Pallet',
  getContext: (context, result, error) => {
    const request = context.switchToHttp().getRequest();
    return {
      palletId: request.params.id,
      fromLocation: request.body.fromLocationId,
      toLocation: request.body.toLocationId,
      movedBy: request.user.email,
    };
  }
})
@Put(':id/move')
async movePallet(@Param('id') id: string, @Body() moveDto: MovePalletDto) {
  // Implementation
}
```

### Manual Security Logging

```typescript
@Injectable()
export class WarehouseService {
  constructor(
    private securityAuditService: SecurityAuditService,
    private authAuditService: AuthAuditService
  ) {}

  async validateAccess(warehouseId: string, user: EnhancedUserPayload, request: Request) {
    try {
      // Validation logic
      await this.securityAuditService.logWarehouseAccessGranted(
        warehouseId,
        user,
        request,
        { operation: 'WAREHOUSE_VALIDATION' }
      );
    } catch (error) {
      await this.securityAuditService.logWarehouseAccessDenied(
        warehouseId,
        user,
        request,
        error.message
      );
      throw error;
    }
  }
}
```

## Security Event Types

### Warehouse Access Events
- `WAREHOUSE_ACCESS_GRANTED` - Successful warehouse access
- `WAREHOUSE_ACCESS_DENIED` - Failed warehouse access

### Authentication Events
- `LOGIN_SUCCESS` - Successful user login
- `LOGIN_FAILURE` - Failed login attempt
- `TOKEN_VALIDATION_SUCCESS` - Successful JWT validation
- `TOKEN_VALIDATION_FAILURE` - Failed JWT validation
- `LOGOUT` - User logout

### Permission Violations
- `INSUFFICIENT_ROLE` - User lacks required role
- `UNAUTHORIZED_WAREHOUSE_ACCESS` - Access to unauthorized warehouse
- `INVALID_OPERATION` - Invalid operation attempt
- `CROSS_TENANT_ACCESS` - Cross-tenant access violation

### Security Configuration Changes
- `USER_ROLE_CHANGED` - User role modification
- `WAREHOUSE_ACCESS_MODIFIED` - Warehouse access changes
- `PERMISSION_UPDATED` - Permission updates

## Integration with Middleware

The security audit system is integrated with:

### WarehousePermissionMiddleware
Automatically logs warehouse access attempts and failures.

### JwtStrategy
Logs JWT token validation events.

### Authorization Guards
Log permission violations and access denials.

## Configuration

### Module Setup

```typescript
@Module({
  imports: [AuditLogModule],
  providers: [
    SecurityAuditService,
    AuthAuditService,
    SecurityAuditInterceptor,
  ],
  exports: [
    SecurityAuditService,
    AuthAuditService,
    SecurityAuditInterceptor,
  ],
})
export class SecurityAuditModule {}
```

### Global Interceptor Setup

```typescript
@Module({
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: SecurityAuditInterceptor,
    },
  ],
})
export class AppModule {}
```

## Audit Log Structure

Security audit logs include:

```typescript
{
  action: string,           // Type of security event
  entity: 'Security',       // Always 'Security' for security events
  entityId: string,         // Warehouse ID, user ID, or resource ID
  userId: string,           // User performing the action
  userEmail: string,        // User email
  tenantId: string,         // Tenant ID
  details: {
    warehouseId?: string,   // Warehouse context
    userRole?: Role,        // User's role
    operation?: string,     // Operation being performed
    resource?: string,      // Resource being accessed
    reason?: string,        // Reason for failure (if applicable)
    requestPath: string,    // Request path
    requestMethod: string,  // HTTP method
    userAgent: string,      // User agent
    ipAddress: string,      // Client IP address
    timestamp: string,      // ISO timestamp
    // Additional context-specific fields
  }
}
```

## Best Practices

1. **Use Appropriate Decorators**: Choose the right decorator for your use case
2. **Include Context**: Provide meaningful context in custom getContext functions
3. **Log High-Risk Operations**: Always audit deletions, role changes, and cross-tenant operations
4. **Handle Errors Gracefully**: Security audit failures should not break application functionality
5. **Monitor Logs**: Regularly review security audit logs for suspicious activity
6. **Performance Considerations**: Audit logging is asynchronous and should not impact performance

## Testing

The security audit system includes comprehensive tests:

```bash
# Run security audit tests
npm test -- security-audit.service.spec.ts
npm test -- auth-audit.service.spec.ts
```

## Monitoring and Alerting

Consider setting up monitoring and alerting for:
- Multiple failed authentication attempts
- Permission violations
- Unusual access patterns
- High-risk operations
- Cross-tenant access attempts
