import { Module } from '@nestjs/common';
import { SecurityAuditService } from './services/security-audit.service';
import { AuthAuditService } from './services/auth-audit.service';
import { SecurityAuditInterceptor } from './interceptors/security-audit.interceptor';
import { AuditLogModule } from './audit-log.module';

/**
 * Security Audit Module
 * Provides security-specific audit logging services and interceptors
 */
@Module({
  imports: [AuditLogModule],
  providers: [
    SecurityAuditService,
    AuthAuditService,
    SecurityAuditInterceptor,
  ],
  exports: [
    SecurityAuditService,
    AuthAuditService,
    SecurityAuditInterceptor,
  ],
})
export class SecurityAuditModule {}
