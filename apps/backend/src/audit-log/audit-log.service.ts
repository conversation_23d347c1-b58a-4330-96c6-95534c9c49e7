import { Injectable } from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { Prisma, AuditLog } from "@prisma/client";
import { CreateAuditLogDto } from "./dto/create-audit-log.dto";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";
import { Role } from "../auth/entities/role.enum";

@Injectable()
export class AuditLogService {
  constructor(private readonly prisma: PrismaService) {}

  async create(
    createAuditLogDto: CreateAuditLogDto,
    prismaClient: Prisma.TransactionClient | PrismaService = this.prisma
  ): Promise<void> {
    await prismaClient.auditLog.create({
      data: {
        userId: createAuditLogDto.userId,
        userEmail: createAuditLogDto.userEmail,
        action: createAuditLogDto.action,
        entity: createAuditLogDto.entity,
        entityId: createAuditLogDto.entityId,
        details: createAuditLogDto.details || undefined, // Prisma expects JsonValue | null
        tenantId: createAuditLogDto.tenantId,
      },
    });
  }

  async findByEntityId(
    entityId: string,
    tenantId: string,
    entity?: string
  ): Promise<AuditLog[]> {
    const where: Prisma.AuditLogWhereInput = {
      entityId,
      tenantId,
    };

    if (entity) {
      where.entity = entity;
    }

    return this.prisma.auditLog.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        timestamp: "desc",
      },
    });
  }

  /**
   * Find audit logs for warehouse-scoped entities with proper access control
   * This method handles entities like Pallet, Location that are warehouse-scoped
   */
  async findByEntityIdWithWarehouseContext(
    entityId: string,
    currentUser: AuthenticatedUser,
    entity?: string
  ): Promise<AuditLog[]> {
    // Build base where clause with tenant security
    const where: Prisma.AuditLogWhereInput = {
      entityId,
      tenantId: currentUser.tenantId,
    };

    if (entity) {
      where.entity = entity;
    }

    // For warehouse-scoped entities, we need to verify the user has access
    // to the warehouse that contains the entity
    if (entity && this.isWarehouseScopedEntity(entity)) {
      await this.validateWarehouseAccess(entityId, entity, currentUser);
    }

    return this.prisma.auditLog.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        timestamp: "desc",
      },
    });
  }

  /**
   * Find audit logs filtered by warehouse context
   * Returns logs for entities that belong to the specified warehouse
   */
  async findByWarehouse(
    warehouseId: string,
    currentUser: AuthenticatedUser,
    options?: {
      entity?: string;
      action?: string;
      limit?: number;
      offset?: number;
    }
  ): Promise<{ data: AuditLog[]; count: number }> {
    // Validate user has access to this warehouse
    await this.validateUserWarehouseAccess(warehouseId, currentUser);

    // Build where clause for warehouse-scoped entities
    const where: Prisma.AuditLogWhereInput = {
      tenantId: currentUser.tenantId,
      OR: [
        // Direct warehouse entities
        {
          entity: "Warehouse",
          entityId: warehouseId,
        },
        // Location entities in this warehouse
        {
          entity: "Location",
          entityId: {
            in: await this.getLocationIdsInWarehouse(warehouseId),
          },
        },
        // Pallet entities in this warehouse (through location)
        {
          entity: "Pallet",
          entityId: {
            in: await this.getPalletIdsInWarehouse(warehouseId),
          },
        },
      ],
    };

    if (options?.entity) {
      // Override OR clause if specific entity is requested
      delete where.OR;
      where.entity = options.entity;

      if (options.entity === "Location") {
        where.entityId = {
          in: await this.getLocationIdsInWarehouse(warehouseId),
        };
      } else if (options.entity === "Pallet") {
        where.entityId = {
          in: await this.getPalletIdsInWarehouse(warehouseId),
        };
      } else if (options.entity === "Warehouse") {
        where.entityId = warehouseId;
      }
    }

    if (options?.action) {
      where.action = options.action;
    }

    const [data, count] = await Promise.all([
      this.prisma.auditLog.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          timestamp: "desc",
        },
        take: options?.limit || 50,
        skip: options?.offset || 0,
      }),
      this.prisma.auditLog.count({ where }),
    ]);

    return { data, count };
  }

  /**
   * Helper method to determine if an entity type is warehouse-scoped
   */
  private isWarehouseScopedEntity(entity: string): boolean {
    const warehouseScopedEntities = ["Pallet", "Location", "PalletItem"];
    return warehouseScopedEntities.includes(entity);
  }

  /**
   * Validate that the user has access to the warehouse containing the entity
   */
  private async validateWarehouseAccess(
    entityId: string,
    entity: string,
    currentUser: AuthenticatedUser
  ): Promise<void> {
    if (currentUser.role === Role.TENANT_ADMIN) {
      return; // Tenant admins have access to all warehouses
    }

    let warehouseId: string | null = null;

    switch (entity) {
      case "Pallet":
        const pallet = await this.prisma.pallet.findUnique({
          where: { id: entityId },
          include: { location: true },
        });
        warehouseId = pallet?.location?.warehouseId || null;
        break;
      case "Location":
        const location = await this.prisma.location.findUnique({
          where: { id: entityId },
        });
        warehouseId = location?.warehouseId || null;
        break;
      case "PalletItem":
        const palletItem = await this.prisma.palletItem.findUnique({
          where: { id: entityId },
          include: { pallet: { include: { location: true } } },
        });
        warehouseId = palletItem?.pallet?.location?.warehouseId || null;
        break;
    }

    if (warehouseId) {
      await this.validateUserWarehouseAccess(warehouseId, currentUser);
    }
  }

  /**
   * Validate that the user has access to a specific warehouse
   */
  private async validateUserWarehouseAccess(
    warehouseId: string,
    currentUser: AuthenticatedUser
  ): Promise<void> {
    if (currentUser.role === Role.TENANT_ADMIN) {
      return; // Tenant admins have access to all warehouses
    }

    const userWarehouseIds =
      currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];

    if (!userWarehouseIds.includes(warehouseId)) {
      throw new Error(
        "Access denied: User does not have access to this warehouse"
      );
    }
  }

  /**
   * Get all location IDs in a warehouse
   */
  private async getLocationIdsInWarehouse(
    warehouseId: string
  ): Promise<string[]> {
    const locations = await this.prisma.location.findMany({
      where: { warehouseId },
      select: { id: true },
    });
    return locations.map((l) => l.id);
  }

  /**
   * Get all pallet IDs in a warehouse (through location relationship)
   */
  private async getPalletIdsInWarehouse(
    warehouseId: string
  ): Promise<string[]> {
    const pallets = await this.prisma.pallet.findMany({
      where: {
        location: {
          warehouseId,
        },
      },
      select: { id: true },
    });
    return pallets.map((p) => p.id);
  }
}
