import { <PERSON>du<PERSON> } from "@nestjs/common";
import { AuditLogService } from "./audit-log.service";
import { AuditLogInterceptor } from "./interceptors/audit-log.interceptor";
import { SecurityAuditService } from "./services/security-audit.service";
import { AuthAuditService } from "./services/auth-audit.service";
import { SecurityAuditInterceptor } from "./interceptors/security-audit.interceptor";
import { PrismaModule } from "../prisma/prisma.module"; // Assuming PrismaModule is in 'src/prisma'

@Module({
  imports: [PrismaModule], // Ensure PrismaService is available
  providers: [
    AuditLogService,
    AuditLogInterceptor,
    SecurityAuditService,
    AuthAuditService,
    SecurityAuditInterceptor,
  ],
  exports: [
    AuditLogService,
    AuditLogInterceptor,
    SecurityAuditService,
    AuthAuditService,
    SecurityAuditInterceptor,
  ], // Export if other modules need to inject it directly
})
export class AuditLogModule {}
