import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { AuditLogService } from '../audit-log.service';
import { LOG_ACTION_KEY, LogActionMetadata } from '../decorators/log-action.decorator';
import { AuthenticatedUser } from '../../auth/types/authenticated-user.interface'; // Adjust path as needed

@Injectable()
export class AuditLogInterceptor implements NestInterceptor {
  constructor(
    private readonly reflector: Reflector,
    private readonly auditLogService: AuditLogService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const logActionMetadata = this.reflector.get<LogActionMetadata>(
      LOG_ACTION_KEY,
      context.getHandler(),
    );

    // If no metadata, or action/entity is missing, skip logging
    if (!logActionMetadata || !logActionMetadata.action || !logActionMetadata.entity) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest();
    const user: AuthenticatedUser | undefined = request.user; // Assuming user is attached by JwtAuthGuard

    // Function to process and log
    const processLog = async (data?: any, error?: any) => {
      try {
        const { action, entity, getEntityId, getDetails } = logActionMetadata;

        let entityId: string | undefined;
        if (getEntityId) {
          entityId = getEntityId(context, data);
        } else if (request.params?.id) {
          entityId = request.params.id;
        } else if (data?.id) {
          entityId = data.id; // Assuming result might have an ID
        }
        
        // If entityId is still undefined, we might not be able to log meaningfully for some actions
        if (!entityId) {
            console.warn(`AuditLogInterceptor: Could not determine entityId for action ${action} on ${entity}. Skipping log.`);
            return;
        }

        let details: Record<string, any> | undefined;
        if (getDetails) {
          details = getDetails(context, data, error);
        } else if (error) {
          details = { error: { message: error.message, status: error.status, name: error.name } };
        } else if (action.toUpperCase().includes('CREATE') || action.toUpperCase().includes('UPDATE')) {
            // For create/update, log the body if no specific getDetails is provided
            // Be cautious with sensitive data in request.body
            details = { newValues: request.body }; 
        }


        await this.auditLogService.create({
          action,
          entity,
          entityId,
          userId: user?.id,
          userEmail: user?.email,
          tenantId: user?.tenantId, // Assuming tenantId is on AuthenticatedUser
          details,
        });
      } catch (logError) {
        // Handle cases where logging itself fails
        console.error('Failed to write audit log:', logError);
      }
    };

    return next.handle().pipe(
      tap((data) => { // data is the response from the handler
        if (user?.tenantId) { // Ensure tenantId is available
            processLog(data, undefined);
        } else {
            console.warn('AuditLogInterceptor: tenantId not found on user. Skipping log.');
        }
      }),
      catchError((error) => {
        // Log even if the main action fails, if tenantId is available
        if (user?.tenantId) {
            processLog(undefined, error);
        } else {
            console.warn('AuditLogInterceptor: tenantId not found on user during error. Skipping log.');
        }
        throw error; // Re-throw the original error
      }),
    );
  }
}
