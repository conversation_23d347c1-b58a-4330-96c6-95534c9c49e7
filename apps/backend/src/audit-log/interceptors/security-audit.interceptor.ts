import {
  Injectable,
  NestInterceptor,
  Exec<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ler,
  ForbiddenException,
  UnauthorizedException,
  Logger,
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { Observable, throwError } from "rxjs";
import { tap, catchError } from "rxjs/operators";
import { SecurityAuditService } from "../services/security-audit.service";
import { EnhancedUserPayload } from "../../auth/types";
import { Role } from "@prisma/client";
import { Request } from "express";

/**
 * Metadata for security audit logging
 */
export interface SecurityAuditMetadata {
  // Type of security operation being performed
  operation?: string;
  // Resource being accessed
  resource?: string;
  // Whether to log successful access
  logSuccess?: boolean;
  // Whether to log failed access
  logFailure?: boolean;
  // Custom context extractor
  getContext?: (context: ExecutionContext, result?: any) => Record<string, any>;
}

export const SECURITY_AUDIT_KEY = "security_audit_metadata";

/**
 * Decorator to mark endpoints for security audit logging
 */
export const SecurityAudit = (metadata: SecurityAuditMetadata = {}) =>
  Reflect.defineMetadata(SECURITY_AUDIT_KEY, metadata, Reflect);

/**
 * Interceptor that automatically logs security events for protected endpoints
 */
@Injectable()
export class SecurityAuditInterceptor implements NestInterceptor {
  private readonly logger = new Logger(SecurityAuditInterceptor.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly securityAuditService: SecurityAuditService
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const securityAuditMetadata = this.reflector.get<SecurityAuditMetadata>(
      SECURITY_AUDIT_KEY,
      context.getHandler()
    );

    // If no security audit metadata, skip logging
    if (!securityAuditMetadata) {
      return next.handle();
    }

    const request: Request = context.switchToHttp().getRequest();
    const user: EnhancedUserPayload | undefined =
      request.user as EnhancedUserPayload;
    const warehouseId = this.extractWarehouseId(request);

    // Default metadata values
    const {
      operation = this.getOperationFromRequest(request),
      resource = this.getResourceFromRequest(request),
      logSuccess = true,
      logFailure = true,
      getContext,
    } = securityAuditMetadata;

    return next.handle().pipe(
      tap(async (result) => {
        if (logSuccess && user) {
          try {
            const customContext = getContext ? getContext(context, result) : {};

            await this.securityAuditService.logWarehouseAccessGranted(
              warehouseId || "global",
              user,
              request,
              {
                operation,
                resource,
                userRole: this.getUserWarehouseRole(user, warehouseId),
                ...customContext,
              }
            );
          } catch (error) {
            this.logger.error(
              `Failed to log successful security audit: ${error.message}`,
              error.stack
            );
          }
        }
      }),
      catchError(async (error) => {
        if (logFailure) {
          try {
            const customContext = getContext
              ? getContext(context, undefined)
              : {};

            if (
              error instanceof ForbiddenException ||
              error instanceof UnauthorizedException
            ) {
              // Log as permission violation or access denied
              if (warehouseId && user) {
                await this.securityAuditService.logWarehouseAccessDenied(
                  warehouseId,
                  user,
                  request,
                  error.message,
                  {
                    operation,
                    resource,
                    userRole: this.getUserWarehouseRole(user, warehouseId),
                    ...customContext,
                  }
                );
              } else if (error instanceof UnauthorizedException) {
                await this.securityAuditService.logAuthenticationEvent(
                  "TOKEN_VALIDATION_FAILURE",
                  user,
                  request,
                  {
                    reason: error.message,
                    ...customContext,
                  }
                );
              } else {
                await this.securityAuditService.logPermissionViolation(
                  "UNAUTHORIZED_WAREHOUSE_ACCESS",
                  user,
                  request,
                  {
                    warehouseId,
                    operation,
                    resource,
                    userRole: user?.role,
                    ...customContext,
                  }
                );
              }
            }
          } catch (auditError) {
            this.logger.error(
              `Failed to log security audit for error: ${auditError.message}`,
              auditError.stack
            );
          }
        }

        return throwError(() => error);
      })
    );
  }

  /**
   * Extract warehouse ID from request parameters or body
   */
  private extractWarehouseId(request: Request): string | undefined {
    // Check URL parameters
    if (request.params?.warehouseId) {
      return request.params.warehouseId;
    }

    // Check query parameters
    if (request.query?.warehouseId) {
      return Array.isArray(request.query.warehouseId)
        ? String(request.query.warehouseId)
        : (request.query.warehouseId as string);
    }

    // Check request body
    if (request.body?.warehouseId) {
      return request.body.warehouseId;
    }

    // Check warehouse context set by middleware
    const warehouseRequest = request as any;
    if (warehouseRequest.warehouseContext?.warehouseId) {
      return warehouseRequest.warehouseContext.warehouseId;
    }

    return undefined;
  }

  /**
   * Get operation name from request
   */
  private getOperationFromRequest(request: Request): string {
    const method = request.method.toUpperCase();
    const path = request.path;

    // Extract operation from path patterns
    if (path.includes("/pallets")) {
      switch (method) {
        case "GET":
          return "VIEW_PALLETS";
        case "POST":
          return "CREATE_PALLET";
        case "PUT":
          return "UPDATE_PALLET";
        case "DELETE":
          return "DELETE_PALLET";
        default:
          return `${method}_PALLETS`;
      }
    }

    if (path.includes("/locations")) {
      switch (method) {
        case "GET":
          return "VIEW_LOCATIONS";
        case "POST":
          return "CREATE_LOCATION";
        case "PUT":
          return "UPDATE_LOCATION";
        case "DELETE":
          return "DELETE_LOCATION";
        default:
          return `${method}_LOCATIONS`;
      }
    }

    if (path.includes("/warehouses")) {
      switch (method) {
        case "GET":
          return "VIEW_WAREHOUSES";
        case "POST":
          return "CREATE_WAREHOUSE";
        case "PUT":
          return "UPDATE_WAREHOUSE";
        case "DELETE":
          return "DELETE_WAREHOUSE";
        default:
          return `${method}_WAREHOUSES`;
      }
    }

    return `${method}_${path.split("/")[1]?.toUpperCase() || "UNKNOWN"}`;
  }

  /**
   * Get resource name from request
   */
  private getResourceFromRequest(request: Request): string {
    const path = request.path;

    if (path.includes("/pallets")) return "Pallet";
    if (path.includes("/locations")) return "Location";
    if (path.includes("/warehouses")) return "Warehouse";
    if (path.includes("/items")) return "Item";
    if (path.includes("/users")) return "User";
    if (path.includes("/purchase-orders")) return "PurchaseOrder";
    if (path.includes("/shipments")) return "Shipment";

    return path.split("/")[1] || "Unknown";
  }

  /**
   * Get user's role in specific warehouse
   */
  private getUserWarehouseRole(
    user: EnhancedUserPayload,
    warehouseId?: string
  ): Role {
    if (!warehouseId || !user.getWarehouseRole) {
      return user.role;
    }

    return user.getWarehouseRole(warehouseId) || user.role;
  }
}
