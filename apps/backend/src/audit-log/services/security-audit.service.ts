import { Injectable, Logger } from '@nestjs/common';
import { AuditLogService } from '../audit-log.service';
import { EnhancedUserPayload } from '../../auth/types';
import { Role } from '@prisma/client';
import { Request } from 'express';

/**
 * Security-specific audit logging service
 * Handles logging of security events, access attempts, and permission violations
 */
@Injectable()
export class SecurityAuditService {
  private readonly logger = new Logger(SecurityAuditService.name);

  constructor(private readonly auditLogService: AuditLogService) {}

  /**
   * Log successful warehouse access
   */
  async logWarehouseAccessGranted(
    warehouseId: string,
    user: EnhancedUserPayload,
    request: Request,
    context?: {
      operation?: string;
      resource?: string;
      userRole?: Role;
    }
  ): Promise<void> {
    try {
      await this.auditLogService.create({
        action: 'WAREHOUSE_ACCESS_GRANTED',
        entity: 'Security',
        entityId: warehouseId,
        userId: user.id,
        userEmail: user.email,
        tenantId: user.tenantId,
        details: {
          warehouseId,
          userRole: context?.userRole || user.role,
          operation: context?.operation,
          resource: context?.resource,
          requestPath: request.path,
          requestMethod: request.method,
          userAgent: request.headers['user-agent'],
          ipAddress: this.extractIpAddress(request),
          timestamp: new Date().toISOString(),
        },
      });

      this.logger.debug(
        `Warehouse access granted logged for user ${user.email} to warehouse ${warehouseId}`
      );
    } catch (error) {
      this.logger.error(
        `Failed to log warehouse access granted: ${error.message}`,
        error.stack
      );
    }
  }

  /**
   * Log failed warehouse access attempts
   */
  async logWarehouseAccessDenied(
    warehouseId: string | undefined,
    user: EnhancedUserPayload | undefined,
    request: Request,
    reason: string,
    context?: {
      operation?: string;
      resource?: string;
      requiredRole?: Role;
      userRole?: Role;
    }
  ): Promise<void> {
    try {
      await this.auditLogService.create({
        action: 'WAREHOUSE_ACCESS_DENIED',
        entity: 'Security',
        entityId: warehouseId || 'unknown',
        userId: user?.id,
        userEmail: user?.email,
        tenantId: user?.tenantId || 'unknown',
        details: {
          warehouseId,
          reason,
          userRole: context?.userRole || user?.role,
          requiredRole: context?.requiredRole,
          operation: context?.operation,
          resource: context?.resource,
          requestPath: request.path,
          requestMethod: request.method,
          userAgent: request.headers['user-agent'],
          ipAddress: this.extractIpAddress(request),
          timestamp: new Date().toISOString(),
        },
      });

      this.logger.warn(
        `Warehouse access denied logged for user ${user?.email || 'unknown'} to warehouse ${warehouseId || 'unknown'}: ${reason}`
      );
    } catch (error) {
      this.logger.error(
        `Failed to log warehouse access denied: ${error.message}`,
        error.stack
      );
    }
  }

  /**
   * Log authentication events
   */
  async logAuthenticationEvent(
    eventType: 'LOGIN_SUCCESS' | 'LOGIN_FAILURE' | 'TOKEN_VALIDATION_SUCCESS' | 'TOKEN_VALIDATION_FAILURE' | 'LOGOUT',
    user: EnhancedUserPayload | undefined,
    request: Request,
    details?: {
      reason?: string;
      tokenExpired?: boolean;
      invalidCredentials?: boolean;
    }
  ): Promise<void> {
    try {
      await this.auditLogService.create({
        action: eventType,
        entity: 'Authentication',
        entityId: user?.id || 'unknown',
        userId: user?.id,
        userEmail: user?.email,
        tenantId: user?.tenantId || 'unknown',
        details: {
          eventType,
          reason: details?.reason,
          tokenExpired: details?.tokenExpired,
          invalidCredentials: details?.invalidCredentials,
          requestPath: request.path,
          requestMethod: request.method,
          userAgent: request.headers['user-agent'],
          ipAddress: this.extractIpAddress(request),
          timestamp: new Date().toISOString(),
        },
      });

      this.logger.debug(
        `Authentication event logged: ${eventType} for user ${user?.email || 'unknown'}`
      );
    } catch (error) {
      this.logger.error(
        `Failed to log authentication event: ${error.message}`,
        error.stack
      );
    }
  }

  /**
   * Log permission violations
   */
  async logPermissionViolation(
    violationType: 'INSUFFICIENT_ROLE' | 'UNAUTHORIZED_WAREHOUSE_ACCESS' | 'INVALID_OPERATION' | 'CROSS_TENANT_ACCESS',
    user: EnhancedUserPayload | undefined,
    request: Request,
    context: {
      warehouseId?: string;
      requiredRole?: Role;
      userRole?: Role;
      operation?: string;
      resource?: string;
      targetTenantId?: string;
    }
  ): Promise<void> {
    try {
      await this.auditLogService.create({
        action: 'PERMISSION_VIOLATION',
        entity: 'Security',
        entityId: context.warehouseId || context.targetTenantId || 'unknown',
        userId: user?.id,
        userEmail: user?.email,
        tenantId: user?.tenantId || 'unknown',
        details: {
          violationType,
          warehouseId: context.warehouseId,
          requiredRole: context.requiredRole,
          userRole: context.userRole || user?.role,
          operation: context.operation,
          resource: context.resource,
          targetTenantId: context.targetTenantId,
          requestPath: request.path,
          requestMethod: request.method,
          userAgent: request.headers['user-agent'],
          ipAddress: this.extractIpAddress(request),
          timestamp: new Date().toISOString(),
        },
      });

      this.logger.warn(
        `Permission violation logged: ${violationType} for user ${user?.email || 'unknown'}`
      );
    } catch (error) {
      this.logger.error(
        `Failed to log permission violation: ${error.message}`,
        error.stack
      );
    }
  }

  /**
   * Log security-related configuration changes
   */
  async logSecurityConfigChange(
    changeType: 'USER_ROLE_CHANGED' | 'WAREHOUSE_ACCESS_MODIFIED' | 'PERMISSION_UPDATED',
    user: EnhancedUserPayload,
    request: Request,
    context: {
      targetUserId?: string;
      targetUserEmail?: string;
      warehouseId?: string;
      oldRole?: Role;
      newRole?: Role;
      operation?: string;
    }
  ): Promise<void> {
    try {
      await this.auditLogService.create({
        action: changeType,
        entity: 'SecurityConfig',
        entityId: context.targetUserId || context.warehouseId || 'unknown',
        userId: user.id,
        userEmail: user.email,
        tenantId: user.tenantId,
        details: {
          changeType,
          targetUserId: context.targetUserId,
          targetUserEmail: context.targetUserEmail,
          warehouseId: context.warehouseId,
          oldRole: context.oldRole,
          newRole: context.newRole,
          operation: context.operation,
          performedBy: {
            userId: user.id,
            userEmail: user.email,
            userRole: user.role,
          },
          requestPath: request.path,
          requestMethod: request.method,
          userAgent: request.headers['user-agent'],
          ipAddress: this.extractIpAddress(request),
          timestamp: new Date().toISOString(),
        },
      });

      this.logger.debug(
        `Security config change logged: ${changeType} by user ${user.email}`
      );
    } catch (error) {
      this.logger.error(
        `Failed to log security config change: ${error.message}`,
        error.stack
      );
    }
  }

  /**
   * Extract IP address from request, handling proxies
   */
  private extractIpAddress(request: Request): string {
    const forwarded = request.headers['x-forwarded-for'];
    if (forwarded) {
      return Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0];
    }
    return request.connection.remoteAddress || request.socket.remoteAddress || 'unknown';
  }
}
