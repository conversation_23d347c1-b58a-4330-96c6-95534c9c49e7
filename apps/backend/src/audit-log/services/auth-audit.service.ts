import { Injectable, Logger } from '@nestjs/common';
import { SecurityAuditService } from './security-audit.service';
import { EnhancedUserPayload } from '../../auth/types';
import { Request } from 'express';

/**
 * Authentication-specific audit logging service
 * Handles logging of authentication events and token validation
 */
@Injectable()
export class AuthAuditService {
  private readonly logger = new Logger(AuthAuditService.name);

  constructor(private readonly securityAuditService: SecurityAuditService) {}

  /**
   * Log successful JWT token validation
   */
  async logTokenValidationSuccess(
    user: EnhancedUserPayload,
    request?: Request
  ): Promise<void> {
    try {
      // Create a minimal request object if not provided
      const req = request || this.createMinimalRequest();
      
      await this.securityAuditService.logAuthenticationEvent(
        'TOKEN_VALIDATION_SUCCESS',
        user,
        req,
        {
          reason: 'JWT token successfully validated',
        }
      );

      this.logger.debug(
        `Token validation success logged for user ${user.email}`
      );
    } catch (error) {
      this.logger.error(
        `Failed to log token validation success: ${error.message}`,
        error.stack
      );
    }
  }

  /**
   * Log failed JWT token validation
   */
  async logTokenValidationFailure(
    reason: string,
    userId?: string,
    userEmail?: string,
    tenantId?: string,
    request?: Request
  ): Promise<void> {
    try {
      // Create a minimal request object if not provided
      const req = request || this.createMinimalRequest();
      
      // Create a partial user object for logging
      const partialUser = userId ? {
        id: userId,
        email: userEmail || 'unknown',
        tenantId: tenantId || 'unknown',
      } as EnhancedUserPayload : undefined;

      await this.securityAuditService.logAuthenticationEvent(
        'TOKEN_VALIDATION_FAILURE',
        partialUser,
        req,
        {
          reason,
          tokenExpired: reason.toLowerCase().includes('expired'),
          invalidCredentials: reason.toLowerCase().includes('invalid'),
        }
      );

      this.logger.warn(
        `Token validation failure logged: ${reason} for user ${userEmail || userId || 'unknown'}`
      );
    } catch (error) {
      this.logger.error(
        `Failed to log token validation failure: ${error.message}`,
        error.stack
      );
    }
  }

  /**
   * Log user login success
   */
  async logLoginSuccess(
    user: EnhancedUserPayload,
    request: Request
  ): Promise<void> {
    try {
      await this.securityAuditService.logAuthenticationEvent(
        'LOGIN_SUCCESS',
        user,
        request
      );

      this.logger.debug(
        `Login success logged for user ${user.email}`
      );
    } catch (error) {
      this.logger.error(
        `Failed to log login success: ${error.message}`,
        error.stack
      );
    }
  }

  /**
   * Log user login failure
   */
  async logLoginFailure(
    email: string,
    reason: string,
    request: Request,
    tenantId?: string
  ): Promise<void> {
    try {
      // Create a partial user object for logging
      const partialUser = {
        id: 'unknown',
        email,
        tenantId: tenantId || 'unknown',
      } as EnhancedUserPayload;

      await this.securityAuditService.logAuthenticationEvent(
        'LOGIN_FAILURE',
        partialUser,
        request,
        {
          reason,
          invalidCredentials: reason.toLowerCase().includes('credential') || reason.toLowerCase().includes('password'),
        }
      );

      this.logger.warn(
        `Login failure logged for email ${email}: ${reason}`
      );
    } catch (error) {
      this.logger.error(
        `Failed to log login failure: ${error.message}`,
        error.stack
      );
    }
  }

  /**
   * Log user logout
   */
  async logLogout(
    user: EnhancedUserPayload,
    request: Request
  ): Promise<void> {
    try {
      await this.securityAuditService.logAuthenticationEvent(
        'LOGOUT',
        user,
        request
      );

      this.logger.debug(
        `Logout logged for user ${user.email}`
      );
    } catch (error) {
      this.logger.error(
        `Failed to log logout: ${error.message}`,
        error.stack
      );
    }
  }

  /**
   * Create a minimal request object for cases where request is not available
   */
  private createMinimalRequest(): Request {
    return {
      path: '/auth/validate',
      method: 'POST',
      headers: {
        'user-agent': 'JWT-Strategy',
      },
      connection: {
        remoteAddress: 'unknown',
      },
      socket: {
        remoteAddress: 'unknown',
      },
    } as Request;
  }
}
