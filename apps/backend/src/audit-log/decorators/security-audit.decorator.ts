import { SetMetadata } from '@nestjs/common';
import { ExecutionContext } from '@nestjs/common';

/**
 * Metadata for security audit logging
 */
export interface SecurityAuditOptions {
  // Type of security operation being performed
  operation?: string;
  // Resource being accessed
  resource?: string;
  // Whether to log successful access (default: true)
  logSuccess?: boolean;
  // Whether to log failed access (default: true)
  logFailure?: boolean;
  // Custom context extractor function
  getContext?: (context: ExecutionContext, result?: any, error?: any) => Record<string, any>;
  // Whether this is a high-risk operation that should always be logged
  highRisk?: boolean;
  // Additional metadata for the audit log
  metadata?: Record<string, any>;
}

export const SECURITY_AUDIT_KEY = 'security_audit_options';

/**
 * Decorator to mark endpoints for security audit logging
 * 
 * @example
 * ```typescript
 * @SecurityAudit({
 *   operation: 'DELETE_PALLET',
 *   resource: 'Pallet',
 *   highRisk: true,
 *   getContext: (context, result, error) => ({
 *     palletId: context.switchToHttp().getRequest().params.id,
 *     deletedBy: context.switchToHttp().getRequest().user.email
 *   })
 * })
 * @Delete(':id')
 * async deletePallet(@Param('id') id: string) {
 *   // Implementation
 * }
 * ```
 */
export const SecurityAudit = (options: SecurityAuditOptions = {}) =>
  SetMetadata(SECURITY_AUDIT_KEY, options);

/**
 * Predefined security audit decorators for common operations
 */

/**
 * Audit decorator for warehouse access operations
 */
export const AuditWarehouseAccess = (operation?: string, resource?: string) =>
  SecurityAudit({
    operation: operation || 'WAREHOUSE_ACCESS',
    resource: resource || 'Warehouse',
    logSuccess: true,
    logFailure: true,
  });

/**
 * Audit decorator for high-risk operations (deletions, role changes, etc.)
 */
export const AuditHighRisk = (operation: string, resource: string) =>
  SecurityAudit({
    operation,
    resource,
    logSuccess: true,
    logFailure: true,
    highRisk: true,
  });

/**
 * Audit decorator for authentication operations
 */
export const AuditAuthentication = (operation?: string) =>
  SecurityAudit({
    operation: operation || 'AUTHENTICATION',
    resource: 'Authentication',
    logSuccess: true,
    logFailure: true,
  });

/**
 * Audit decorator for permission-sensitive operations
 */
export const AuditPermissions = (operation: string, resource: string) =>
  SecurityAudit({
    operation,
    resource,
    logSuccess: true,
    logFailure: true,
    getContext: (context) => {
      const request = context.switchToHttp().getRequest();
      return {
        userRole: request.user?.role,
        warehouseContext: request.warehouseContext,
        requestedResource: resource,
      };
    },
  });

/**
 * Audit decorator for data modification operations
 */
export const AuditDataModification = (operation: string, resource: string) =>
  SecurityAudit({
    operation,
    resource,
    logSuccess: true,
    logFailure: true,
    getContext: (context, result, error) => {
      const request = context.switchToHttp().getRequest();
      const auditContext: Record<string, any> = {
        operation,
        resource,
        userId: request.user?.id,
        userEmail: request.user?.email,
      };

      // Include request body for create/update operations
      if (operation.includes('CREATE') || operation.includes('UPDATE')) {
        auditContext.requestData = request.body;
      }

      // Include result data for successful operations
      if (result && !error) {
        auditContext.resultData = result;
      }

      // Include error details for failed operations
      if (error) {
        auditContext.errorDetails = {
          message: error.message,
          status: error.status,
          name: error.name,
        };
      }

      return auditContext;
    },
  });

/**
 * Audit decorator for cross-tenant operations (high security risk)
 */
export const AuditCrossTenant = (operation: string, resource: string) =>
  SecurityAudit({
    operation,
    resource,
    logSuccess: true,
    logFailure: true,
    highRisk: true,
    getContext: (context) => {
      const request = context.switchToHttp().getRequest();
      return {
        userTenantId: request.user?.tenantId,
        targetTenantId: request.params?.tenantId || request.body?.tenantId,
        crossTenantOperation: true,
      };
    },
  });

/**
 * Audit decorator for bulk operations
 */
export const AuditBulkOperation = (operation: string, resource: string) =>
  SecurityAudit({
    operation,
    resource,
    logSuccess: true,
    logFailure: true,
    getContext: (context, result) => {
      const request = context.switchToHttp().getRequest();
      const bulkData = request.body;
      
      return {
        bulkOperation: true,
        itemCount: Array.isArray(bulkData) ? bulkData.length : 1,
        affectedItems: Array.isArray(bulkData) 
          ? bulkData.map(item => item.id || item.barcode || 'unknown')
          : [bulkData.id || bulkData.barcode || 'unknown'],
        resultCount: result ? (Array.isArray(result) ? result.length : 1) : 0,
      };
    },
  });
