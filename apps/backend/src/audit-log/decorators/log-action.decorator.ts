import { SetMetadata } from '@nestjs/common';
import { ExecutionContext } from '@nestjs/common'; // Will be needed by interceptor

// Defines the structure of metadata our decorator will attach
export interface LogActionMetadata {
  action: string; // e.g., "CREATE_PALLET", "UPDATE_PALLET_SHIP_TO_DESTINATION"
  entity: string; // e.g., "Pallet"
  // Optional: A function to extract the entity ID from the request or response.
  // If not provided, the interceptor might default to 'id' from params or response.
  getEntityId?: (context: ExecutionContext, result?: any) => string | undefined;
  // Optional: A function to extract details for the audit log.
  // This could involve comparing pre and post states for updates.
  getDetails?: (context: ExecutionContext, result?: any, error?: any) => Record<string, any> | undefined;
}

export const LOG_ACTION_KEY = 'log_action_metadata'; // Key to store/retrieve metadata

// The decorator itself
export const LogAction = (metadata: LogActionMetadata) => SetMetadata(LOG_ACTION_KEY, metadata);
