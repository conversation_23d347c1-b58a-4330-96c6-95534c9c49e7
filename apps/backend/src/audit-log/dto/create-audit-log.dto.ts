import { IsString, IsOptional, IsNotEmpty, IsObject } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'; // Assuming Swagger is used

export class CreateAuditLogDto {
  @ApiProperty({ description: 'ID of the user performing the action', required: false })
  @IsString()
  @IsOptional()
  userId?: string;

  @ApiProperty({ description: 'Email of the user performing the action', required: false })
  @IsString()
  @IsOptional()
  userEmail?: string;

  @ApiProperty({ description: 'Type of action performed (e.g., CREATE_PALLET)' })
  @IsString()
  @IsNotEmpty()
  action: string;

  @ApiProperty({ description: 'Type of entity affected (e.g., Pallet)' })
  @IsString()
  @IsNotEmpty()
  entity: string;

  @ApiProperty({ description: 'ID of the entity affected' })
  @IsString()
  @IsNotEmpty()
  entityId: string;

  @ApiPropertyOptional({ description: 'Additional details about the action (e.g., before/after values)', type: Object, additionalProperties: true })
  @IsObject()
  @IsOptional()
  details?: Record<string, any>; // Prisma's Json type

  @ApiProperty({ description: 'ID of the tenant this log belongs to' })
  @IsString()
  @IsNotEmpty()
  tenantId: string;
}
