import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AuditLog } from '@prisma/client';

/**
 * User information included in audit log responses
 */
export class AuditLogUserDto {
  @ApiProperty({ description: 'User ID' })
  id: string;

  @ApiProperty({ description: 'User name' })
  name: string;

  @ApiProperty({ description: 'User email' })
  email: string;
}

/**
 * Warehouse context information for audit logs
 */
export class AuditLogWarehouseContextDto {
  @ApiProperty({ description: 'Warehouse ID where the action occurred' })
  warehouseId: string;

  @ApiProperty({ description: 'Warehouse name' })
  warehouseName: string;

  @ApiPropertyOptional({ description: 'Location ID if action was location-specific' })
  locationId?: string;

  @ApiPropertyOptional({ description: 'Location name if action was location-specific' })
  locationName?: string;
}

/**
 * Response DTO for audit log entries with warehouse context
 */
export class AuditLogResponseDto {
  @ApiProperty({ description: 'Audit log entry ID' })
  id: string;

  @ApiProperty({ description: 'Type of action performed' })
  action: string;

  @ApiProperty({ description: 'Type of entity affected' })
  entity: string;

  @ApiProperty({ description: 'ID of the entity affected' })
  entityId: string;

  @ApiProperty({ description: 'Timestamp when the action occurred' })
  timestamp: Date;

  @ApiPropertyOptional({ 
    description: 'Additional details about the action',
    type: Object,
    additionalProperties: true 
  })
  details?: Record<string, any>;

  @ApiPropertyOptional({ 
    description: 'User who performed the action',
    type: AuditLogUserDto 
  })
  user?: AuditLogUserDto;

  @ApiPropertyOptional({ 
    description: 'Warehouse context information',
    type: AuditLogWarehouseContextDto 
  })
  warehouseContext?: AuditLogWarehouseContextDto;

  @ApiProperty({ description: 'Tenant ID' })
  tenantId: string;
}

/**
 * Paginated response for audit log queries
 */
export class AuditLogListResponseDto {
  @ApiProperty({ 
    description: 'Array of audit log entries',
    type: [AuditLogResponseDto] 
  })
  data: AuditLogResponseDto[];

  @ApiProperty({ description: 'Total number of audit log entries' })
  count: number;

  @ApiPropertyOptional({ description: 'Current page number' })
  page?: number;

  @ApiPropertyOptional({ description: 'Number of items per page' })
  limit?: number;

  @ApiPropertyOptional({ description: 'Total number of pages' })
  totalPages?: number;
}
