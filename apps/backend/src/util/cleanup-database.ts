import { PrismaClient } from "@prisma/client";

/**
 * Cleans all data from the test database except for Prisma migration history.
 * Uses Prisma interactive transactions for efficiency.
 * @param prisma - An instance of PrismaClient.
 */
export async function cleanupDatabase(prisma: PrismaClient): Promise<void> {
  const tablenames = await prisma.$queryRaw<
    Array<{ tablename: string }>
  >`SELECT tablename FROM pg_tables WHERE schemaname='public'`;

  const tables = tablenames
    .map(({ tablename }) => tablename)
    .filter((name) => name !== "_prisma_migrations") // Don't delete migration history
    .map((name) => `"${name}"`); // Wrap table names in quotes

  if (tables.length === 0) {
    console.log("No tables found to clean.");
    return;
  }

  try {
    // Use TRUNCATE for faster deletion, resetting sequences
    const truncateQuery = `TRUNCATE TABLE ${tables.join(
      ", "
    )} RESTART IDENTITY CASCADE;`;
    await prisma.$executeRawUnsafe(truncateQuery);
    // console.log(`Cleaned tables: ${tables.join(', ')}`);
  } catch (error) {
    console.error("Error cleaning database:", error);
    throw new Error("Could not clean database");
  }
}
