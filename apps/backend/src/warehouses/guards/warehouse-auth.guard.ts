import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  SetMetadata,
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { WarehouseValidationUtils } from "../utils/warehouse-validation.utils";
import { AuthenticatedUser } from "../../auth/types/authenticated-user.interface";
import { Role } from "@prisma/client";

// Metadata key for warehouse requirements
export const WAREHOUSE_REQUIREMENTS_KEY = "warehouse_requirements";

// Interface for warehouse access requirements
export interface WarehouseRequirements {
  // Minimum role required for the warehouse operation
  minimumRole?: Role;
  // Whether warehouse ID is required in the request
  requireWarehouseId?: boolean;
  // Custom validation function
  customValidation?: (
    warehouseId: string | undefined,
    user: AuthenticatedUser
  ) => Promise<boolean>;
}

// Decorator to set warehouse requirements
export const RequireWarehouseAccess = (requirements: WarehouseRequirements) =>
  SetMetadata(WAREHOUSE_REQUIREMENTS_KEY, requirements);

@Injectable()
export class WarehouseAuthGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly warehouseValidationUtils: WarehouseValidationUtils
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Get warehouse requirements from decorator metadata
    const requirements = this.reflector.get<WarehouseRequirements>(
      WAREHOUSE_REQUIREMENTS_KEY,
      context.getHandler()
    );

    // If no requirements specified, allow access
    if (!requirements) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user: AuthenticatedUser = request.user;

    if (!user) {
      throw new ForbiddenException("User not authenticated");
    }

    // Extract warehouse ID from various sources
    const warehouseId = this.extractWarehouseId(request);

    // Check if warehouse ID is required
    if (requirements.requireWarehouseId && !warehouseId) {
      throw new ForbiddenException(
        "Warehouse ID is required for this operation"
      );
    }

    // Validate warehouse access if warehouse ID is provided
    if (warehouseId) {
      try {
        if (requirements.minimumRole) {
          await this.warehouseValidationUtils.validateUserWarehouseRole(
            warehouseId,
            user,
            requirements.minimumRole
          );
        } else {
          await this.warehouseValidationUtils.validateUserWarehouseAccess(
            warehouseId,
            user
          );
        }
      } catch (error) {
        throw new ForbiddenException(error.message);
      }
    }

    // Run custom validation if provided
    if (requirements.customValidation) {
      const isValid = await requirements.customValidation(warehouseId, user);
      if (!isValid) {
        throw new ForbiddenException("Custom warehouse validation failed");
      }
    }

    return true;
  }

  /**
   * Extract warehouse ID from request parameters, query, headers, or body
   */
  private extractWarehouseId(request: any): string | undefined {
    // Check URL parameters first
    if (request.params?.warehouseId) {
      return request.params.warehouseId;
    }

    // Check query parameters
    if (request.query?.warehouseId) {
      return request.query.warehouseId;
    }

    // Check headers
    if (request.headers?.["x-warehouse-id"]) {
      return request.headers["x-warehouse-id"];
    }

    // Check request body
    if (request.body?.warehouseId) {
      return request.body.warehouseId;
    }

    return undefined;
  }
}

// Convenience decorators for common warehouse access patterns

/**
 * Requires user to have access to the warehouse specified in the request
 */
export const RequireWarehouseAccess_Basic = () =>
  RequireWarehouseAccess({
    requireWarehouseId: true,
  });

/**
 * Requires user to have WAREHOUSE_MANAGER role or higher in the specified warehouse
 */
export const RequireWarehouseManager = () =>
  RequireWarehouseAccess({
    requireWarehouseId: true,
    minimumRole: Role.WAREHOUSE_MANAGER,
  });

/**
 * Requires user to have TENANT_ADMIN role (warehouse-agnostic admin operations)
 */
export const RequireTenantAdmin = () =>
  RequireWarehouseAccess({
    minimumRole: Role.TENANT_ADMIN,
  });

/**
 * Allows warehouse filtering but doesn't require it (for list operations)
 */
export const AllowWarehouseFiltering = () =>
  RequireWarehouseAccess({
    requireWarehouseId: false,
  });
