import {
  Injectable,
  ForbiddenException,
  NotFoundException,
} from "@nestjs/common";
import { PrismaService } from "../../prisma/prisma.service";
import { AuthenticatedUser } from "../../auth/types/authenticated-user.interface";
import { Role } from "@prisma/client";

@Injectable()
export class WarehouseValidationUtils {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Validate that a user has access to a specific warehouse
   * @param warehouseId - The warehouse ID to validate access for
   * @param currentUser - The authenticated user
   * @throws ForbiddenException if user doesn't have access
   * @throws NotFoundException if warehouse doesn't exist
   */
  async validateUserWarehouseAccess(
    warehouseId: string,
    currentUser: AuthenticatedUser
  ): Promise<void> {
    // First verify the warehouse exists and belongs to the user's tenant
    const warehouse = await this.prisma.warehouse.findFirst({
      where: {
        id: warehouseId,
        tenantId: currentUser.tenantId,
      },
    });

    if (!warehouse) {
      throw new NotFoundException(
        `Warehouse with ID "${warehouseId}" not found or not accessible.`
      );
    }

    // Tenant admins have access to all warehouses in their tenant
    if (currentUser.role === Role.TENANT_ADMIN) {
      return;
    }

    // For other roles, check warehouse assignments
    const userWarehouseIds =
      currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];

    if (!userWarehouseIds.includes(warehouseId)) {
      throw new ForbiddenException(
        "Access denied: You do not have permission to access this warehouse."
      );
    }
  }

  /**
   * Validate that a user has a specific role in a warehouse
   * @param warehouseId - The warehouse ID
   * @param currentUser - The authenticated user
   * @param requiredRole - The minimum role required
   * @throws ForbiddenException if user doesn't have the required role
   */
  async validateUserWarehouseRole(
    warehouseId: string,
    currentUser: AuthenticatedUser,
    requiredRole: Role
  ): Promise<void> {
    // First validate basic access
    await this.validateUserWarehouseAccess(warehouseId, currentUser);

    // Tenant admins always have the highest permissions
    if (currentUser.role === Role.TENANT_ADMIN) {
      return;
    }

    // Check the user's role in this specific warehouse
    const warehouseUser = currentUser.warehouseUsers?.find(
      (wu) => wu.warehouseId === warehouseId
    );

    if (!warehouseUser) {
      throw new ForbiddenException(
        "Access denied: You are not assigned to this warehouse."
      );
    }

    // Check if user has sufficient role
    if (!this.hasRequiredRole(warehouseUser.role, requiredRole)) {
      throw new ForbiddenException(
        `Access denied: This action requires ${requiredRole} role or higher.`
      );
    }
  }

  /**
   * Get all warehouse IDs that a user has access to
   * @param currentUser - The authenticated user
   * @returns Array of warehouse IDs the user can access
   */
  async getUserAccessibleWarehouseIds(
    currentUser: AuthenticatedUser
  ): Promise<string[]> {
    if (currentUser.role === Role.TENANT_ADMIN) {
      // Tenant admins have access to all warehouses in their tenant
      const warehouses = await this.prisma.warehouse.findMany({
        where: { tenantId: currentUser.tenantId },
        select: { id: true },
      });
      return warehouses.map((w) => w.id);
    }

    // For other roles, return assigned warehouses
    return currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
  }

  /**
   * Validate that a warehouse belongs to the user's tenant
   * @param warehouseId - The warehouse ID to validate
   * @param tenantId - The tenant ID to validate against
   * @throws NotFoundException if warehouse doesn't exist or belong to tenant
   */
  async validateWarehouseTenantRelationship(
    warehouseId: string,
    tenantId: string
  ): Promise<void> {
    const warehouse = await this.prisma.warehouse.findFirst({
      where: {
        id: warehouseId,
        tenantId: tenantId,
      },
    });

    if (!warehouse) {
      throw new NotFoundException(
        `Warehouse with ID "${warehouseId}" not found in tenant "${tenantId}".`
      );
    }
  }

  /**
   * Validate that an entity belongs to a warehouse the user can access
   * @param entityId - The entity ID
   * @param entityType - The type of entity
   * @param currentUser - The authenticated user
   * @returns The warehouse ID that contains the entity
   * @throws NotFoundException if entity doesn't exist
   * @throws ForbiddenException if user doesn't have access to the warehouse
   */
  async validateEntityWarehouseAccess(
    entityId: string,
    entityType: "pallet" | "location" | "purchaseOrder" | "shipment",
    currentUser: AuthenticatedUser
  ): Promise<string> {
    let warehouseId: string | null = null;

    switch (entityType) {
      case "pallet":
        const pallet = await this.prisma.pallet.findUnique({
          where: { id: entityId },
          include: { location: true },
        });
        if (!pallet || !pallet.location) {
          throw new NotFoundException(
            `Pallet with ID "${entityId}" not found.`
          );
        }
        warehouseId = pallet.location.warehouseId;
        break;

      case "location":
        const location = await this.prisma.location.findUnique({
          where: { id: entityId },
        });
        if (!location) {
          throw new NotFoundException(
            `Location with ID "${entityId}" not found.`
          );
        }
        warehouseId = location.warehouseId;
        break;

      case "purchaseOrder":
        const purchaseOrder = await this.prisma.purchaseOrder.findUnique({
          where: { id: entityId },
        });
        if (!purchaseOrder) {
          throw new NotFoundException(
            `Purchase Order with ID "${entityId}" not found.`
          );
        }
        warehouseId = purchaseOrder.warehouseId;
        break;

      case "shipment":
        const shipment = await this.prisma.shipment.findUnique({
          where: { id: entityId },
          include: { purchaseOrder: true },
        });
        if (!shipment || !shipment.purchaseOrder) {
          throw new NotFoundException(
            `Shipment with ID "${entityId}" not found.`
          );
        }
        warehouseId = shipment.purchaseOrder.warehouseId;
        break;

      default:
        throw new Error(`Unsupported entity type: ${entityType}`);
    }

    // Validate user has access to this warehouse
    await this.validateUserWarehouseAccess(warehouseId, currentUser);

    return warehouseId;
  }

  /**
   * Check if a user role meets the minimum required role
   * @param userRole - The user's current role
   * @param requiredRole - The minimum required role
   * @returns true if user has sufficient permissions
   */
  private hasRequiredRole(userRole: Role, requiredRole: Role): boolean {
    const roleHierarchy = {
      [Role.WAREHOUSE_MEMBER]: 1,
      [Role.WAREHOUSE_MANAGER]: 2,
      [Role.TENANT_ADMIN]: 3,
    };

    return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
  }

  /**
   * Build warehouse-scoped where clause for Prisma queries
   * @param currentUser - The authenticated user
   * @param warehouseId - Optional specific warehouse ID to filter by
   * @returns Prisma where clause for warehouse filtering
   */
  async buildWarehouseWhereClause(
    currentUser: AuthenticatedUser,
    warehouseId?: string
  ): Promise<{ warehouseId?: string | { in: string[] } }> {
    const whereClause: { warehouseId?: string | { in: string[] } } = {};

    if (warehouseId) {
      // Validate access to specific warehouse
      await this.validateUserWarehouseAccess(warehouseId, currentUser);
      whereClause.warehouseId = warehouseId;
    } else if (currentUser.role !== Role.TENANT_ADMIN) {
      // For non-admin users, filter by accessible warehouses
      const accessibleWarehouseIds = await this.getUserAccessibleWarehouseIds(
        currentUser
      );
      if (accessibleWarehouseIds.length === 0) {
        // User has no warehouse access, return impossible condition
        whereClause.warehouseId = "impossible-warehouse-id";
      } else {
        whereClause.warehouseId = { in: accessibleWarehouseIds };
      }
    }
    // For tenant admins without specific warehouse filter, no additional filtering needed

    return whereClause;
  }

  /**
   * Build location-scoped where clause for entities that belong to locations
   * @param currentUser - The authenticated user
   * @param warehouseId - Optional specific warehouse ID to filter by
   * @returns Prisma where clause for location filtering through warehouse
   */
  async buildLocationWarehouseWhereClause(
    currentUser: AuthenticatedUser,
    warehouseId?: string
  ): Promise<{
    location: {
      warehouse: { tenantId: string; id?: string | { in: string[] } };
    };
  }> {
    const warehouseFilter = await this.buildWarehouseWhereClause(
      currentUser,
      warehouseId
    );

    return {
      location: {
        warehouse: {
          tenantId: currentUser.tenantId,
          ...warehouseFilter,
        },
      },
    };
  }
}
