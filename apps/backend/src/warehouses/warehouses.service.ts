import { Injectable, NotFoundException } from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { Warehouse, Prisma } from "@prisma/client";
import { CreateWarehouseDto } from "./dto/create-warehouse.dto";
import { UpdateWarehouseDto } from "./dto/update-warehouse.dto";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";

@Injectable()
export class WarehousesService {
  constructor(private prisma: PrismaService) {}

  async create(
    createWarehouseDto: CreateWarehouseDto,
    currentUser: AuthenticatedUser
  ): Promise<Warehouse> {
    const newWarehouse = await this.prisma.warehouse.create({
      data: {
        ...createWarehouseDto,
        tenantId: currentUser.tenantId,
      },
    });

    // Automatically associate the creating admin/user with this new warehouse
    // This assumes the current user (regardless of role, if they have create permission)
    // should be associated with the warehouse they create.
    if (currentUser && currentUser.id && newWarehouse) {
      await this.prisma.warehouseUser.create({
        data: {
          userId: currentUser.id,
          warehouseId: newWarehouse.id,
          role: currentUser.role, // Use the user's current role
        },
      });
    }
    return newWarehouse;
  }

  async findAll(currentUser: AuthenticatedUser): Promise<Warehouse[]> {
    const userWarehouseIds =
      currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];

    if (currentUser.role !== "TENANT_ADMIN" && userWarehouseIds.length === 0) {
      // Non-admins with no specific warehouse associations see an empty list.
      return [];
    }

    const whereConditions: Prisma.WarehouseWhereInput = {
      tenantId: currentUser.tenantId,
    };

    if (currentUser.role !== "TENANT_ADMIN" && userWarehouseIds.length > 0) {
      // Non-admins with specific associations are restricted to those.
      whereConditions.id = { in: userWarehouseIds };
    }
    // TENANT_ADMIN sees all warehouses in their tenant, no id filter needed based on warehouseUsers.

    return this.prisma.warehouse.findMany({
      where: whereConditions,
      orderBy: { name: "asc" },
    });
  }

  async findOne(
    id: string,
    currentUser: AuthenticatedUser
  ): Promise<Warehouse | null> {
    const warehouse = await this.prisma.warehouse.findUnique({
      where: {
        id,
        tenantId: currentUser.tenantId,
      },
    });

    if (!warehouse) {
      throw new NotFoundException(
        `Warehouse with ID \"${id}\" not found or not accessible within your tenant.`
      );
    }

    const userWarehouseIds =
      currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];

    // Additional check: if user has specific warehouse assignments, they must include this one.
    // TENANT_ADMIN might bypass this, or this check applies to all.
    if (
      currentUser.role !== "TENANT_ADMIN" &&
      !userWarehouseIds.includes(warehouse.id)
    ) {
      throw new NotFoundException(
        `Warehouse with ID \"${id}\" not found or not accessible.`
      );
    }

    return warehouse;
  }

  async update(
    id: string,
    updateWarehouseDto: UpdateWarehouseDto,
    currentUser: AuthenticatedUser
  ): Promise<Warehouse> {
    // Ensure warehouse exists within the current user's tenant before update
    const existingWarehouse = await this.prisma.warehouse.findUnique({
      where: { id, tenantId: currentUser.tenantId },
    });

    if (!existingWarehouse) {
      throw new NotFoundException(
        `Warehouse with ID \"${id}\" not found within your tenant.`
      );
    }

    // Further authorization for update can be role-based (e.g. only TENANT_ADMIN or specific WAREHOUSE_MANAGER)
    // Here, we assume if they found it via findOne logic (implicit) and have @Roles, they can update.
    // Or check if they are associated with this warehouse directly via warehouseUsers if that's a requirement.

    return this.prisma.warehouse.update({
      where: {
        id,
        tenantId: currentUser.tenantId, // Add tenantId for extra safety
      },
      data: {
        name: updateWarehouseDto.name, // Explicitly map fields to prevent tenantId update
        address: updateWarehouseDto.address,
      },
    });
  }

  async remove(id: string, currentUser: AuthenticatedUser): Promise<Warehouse> {
    // Ensure warehouse exists within the current user's tenant before delete
    const existingWarehouse = await this.prisma.warehouse.findUnique({
      where: { id, tenantId: currentUser.tenantId },
    });

    if (!existingWarehouse) {
      throw new NotFoundException(
        `Warehouse with ID \"${id}\" not found within your tenant.`
      );
    }

    // Further authorization for delete can be role-based.
    // Assume if they found it and have @Roles, they can delete.

    // Before deleting warehouse, remove WarehouseUser associations
    await this.prisma.warehouseUser.deleteMany({ where: { warehouseId: id } });

    return this.prisma.warehouse.delete({
      where: {
        id,
        tenantId: currentUser.tenantId, // Add tenantId for extra safety
      },
    });
  }
}
