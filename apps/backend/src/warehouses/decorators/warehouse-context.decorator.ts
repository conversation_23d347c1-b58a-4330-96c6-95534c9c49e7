import { createParamDecorator, ExecutionContext } from "@nestjs/common";

/**
 * Interface for warehouse context information
 */
export interface WarehouseContext {
  warehouseId?: string;
  isWarehouseSpecific: boolean;
  source: "params" | "query" | "headers" | "body" | "none";
}

/**
 * Decorator to extract warehouse context from the request
 * This provides a standardized way to get warehouse information across controllers
 */
export const GetWarehouseContext = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): WarehouseContext => {
    const request = ctx.switchToHttp().getRequest();

    // Check URL parameters first (highest priority)
    if (request.params?.warehouseId) {
      return {
        warehouseId: request.params.warehouseId,
        isWarehouseSpecific: true,
        source: "params",
      };
    }

    // Check query parameters
    if (request.query?.warehouseId) {
      return {
        warehouseId: request.query.warehouseId,
        isWarehouseSpecific: true,
        source: "query",
      };
    }

    // Check headers
    if (request.headers?.["x-warehouse-id"]) {
      return {
        warehouseId: request.headers["x-warehouse-id"],
        isWarehouseSpecific: true,
        source: "headers",
      };
    }

    // Check request body
    if (request.body?.warehouseId) {
      return {
        warehouseId: request.body.warehouseId,
        isWarehouseSpecific: true,
        source: "body",
      };
    }

    // No warehouse context found
    return {
      warehouseId: undefined,
      isWarehouseSpecific: false,
      source: "none",
    };
  }
);

/**
 * Simple decorator to extract just the warehouse ID
 */
export const GetWarehouseId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string | undefined => {
    const request = ctx.switchToHttp().getRequest();

    // Check URL parameters first (highest priority)
    if (request.params?.warehouseId) {
      return request.params.warehouseId;
    }

    // Check query parameters
    if (request.query?.warehouseId) {
      return request.query.warehouseId;
    }

    // Check headers
    if (request.headers?.["x-warehouse-id"]) {
      return request.headers["x-warehouse-id"];
    }

    // Check request body
    if (request.body?.warehouseId) {
      return request.body.warehouseId;
    }

    return undefined;
  }
);

/**
 * Decorator to extract warehouse ID with validation that it exists
 * Throws an error if warehouse ID is not provided
 */
export const GetRequiredWarehouseId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();

    // Check URL parameters first (highest priority)
    if (request.params?.warehouseId) {
      return request.params.warehouseId;
    }

    // Check query parameters
    if (request.query?.warehouseId) {
      return request.query.warehouseId;
    }

    // Check headers
    if (request.headers?.["x-warehouse-id"]) {
      return request.headers["x-warehouse-id"];
    }

    // Check request body
    if (request.body?.warehouseId) {
      return request.body.warehouseId;
    }

    throw new Error("Warehouse ID is required but not provided in request");
  }
);
