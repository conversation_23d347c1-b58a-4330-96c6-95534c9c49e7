import { Module } from "@nestjs/common";
import { WarehousesService } from "./warehouses.service";
import { WarehousesController } from "./warehouses.controller";
import { WarehouseValidationUtils } from "./utils/warehouse-validation.utils";
import { WarehouseAuthGuard } from "./guards/warehouse-auth.guard";

@Module({
  providers: [WarehousesService, WarehouseValidationUtils, WarehouseAuthGuard],
  controllers: [WarehousesController],
  exports: [WarehousesService, WarehouseValidationUtils, WarehouseAuthGuard],
})
export class WarehousesModule {}
