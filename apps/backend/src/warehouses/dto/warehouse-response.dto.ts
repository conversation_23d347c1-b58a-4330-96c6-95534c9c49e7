import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Role } from '@prisma/client';

/**
 * User access information for warehouse responses
 */
export class WarehouseUserAccessDto {
  @ApiProperty({ description: 'User ID' })
  userId: string;

  @ApiProperty({ description: 'User name' })
  userName: string;

  @ApiProperty({ description: 'User email' })
  userEmail: string;

  @ApiProperty({ 
    description: 'User role in this warehouse',
    enum: Role 
  })
  role: Role;
}

/**
 * Location summary for warehouse responses
 */
export class WarehouseLocationSummaryDto {
  @ApiProperty({ description: 'Total number of locations' })
  totalLocations: number;

  @ApiProperty({ description: 'Number of active locations' })
  activeLocations: number;

  @ApiProperty({ description: 'Number of locations with pallets' })
  occupiedLocations: number;
}

/**
 * Pallet summary for warehouse responses
 */
export class WarehousePalletSummaryDto {
  @ApiProperty({ description: 'Total number of pallets' })
  totalPallets: number;

  @ApiProperty({ description: 'Number of pallets in storage' })
  storedPallets: number;

  @ApiProperty({ description: 'Number of pallets being received' })
  receivingPallets: number;

  @ApiProperty({ description: 'Number of pallets being picked' })
  pickingPallets: number;

  @ApiProperty({ description: 'Number of pallets ready for shipping' })
  shippingPallets: number;
}

/**
 * Response DTO for warehouse with context information
 */
export class WarehouseResponseDto {
  @ApiProperty({ description: 'Warehouse ID' })
  id: string;

  @ApiProperty({ description: 'Warehouse name' })
  name: string;

  @ApiPropertyOptional({ description: 'Warehouse address' })
  address?: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;

  @ApiProperty({ description: 'Tenant ID' })
  tenantId: string;

  @ApiPropertyOptional({ 
    description: 'Location summary statistics',
    type: WarehouseLocationSummaryDto 
  })
  locationSummary?: WarehouseLocationSummaryDto;

  @ApiPropertyOptional({ 
    description: 'Pallet summary statistics',
    type: WarehousePalletSummaryDto 
  })
  palletSummary?: WarehousePalletSummaryDto;

  @ApiPropertyOptional({ 
    description: 'Users with access to this warehouse',
    type: [WarehouseUserAccessDto] 
  })
  userAccess?: WarehouseUserAccessDto[];

  @ApiPropertyOptional({ description: 'Current user role in this warehouse' })
  currentUserRole?: Role;
}

/**
 * Paginated response for warehouse queries
 */
export class WarehouseListResponseDto {
  @ApiProperty({ 
    description: 'Array of warehouses',
    type: [WarehouseResponseDto] 
  })
  data: WarehouseResponseDto[];

  @ApiProperty({ description: 'Total number of warehouses' })
  count: number;

  @ApiPropertyOptional({ description: 'Current page number' })
  page?: number;

  @ApiPropertyOptional({ description: 'Number of items per page' })
  limit?: number;

  @ApiPropertyOptional({ description: 'Total number of pages' })
  totalPages?: number;
}
