import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  UsePipes,
  ValidationPipe,
  Req,
} from "@nestjs/common";
import { WarehousesService } from "./warehouses.service";
import { CreateWarehouseDto } from "./dto/create-warehouse.dto";
import { UpdateWarehouseDto } from "./dto/update-warehouse.dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { WarehouseAuthGuard } from "../auth/guards/warehouse-auth.guard";
import { EnhancedUserPayload } from "../auth/types";

@Controller("warehouses")
@UseGuards(JwtAuthGuard, WarehouseAuthGuard)
export class WarehousesController {
  constructor(private readonly warehousesService: WarehousesService) {}

  @Post()
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  create(
    @Body() createWarehouseDto: CreateWarehouseDto,
    @Req() req: { user: EnhancedUserPayload }
  ) {
    return this.warehousesService.create(createWarehouseDto, req.user);
  }

  @Get()
  findAll(@Req() req: { user: EnhancedUserPayload }) {
    return this.warehousesService.findAll(req.user);
  }

  @Get(":id")
  findOne(@Param("id") id: string, @Req() req: { user: EnhancedUserPayload }) {
    return this.warehousesService.findOne(id, req.user);
  }

  @Patch(":id")
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  update(
    @Param("id") id: string,
    @Body() updateWarehouseDto: UpdateWarehouseDto,
    @Req() req: { user: EnhancedUserPayload }
  ) {
    return this.warehousesService.update(id, updateWarehouseDto, req.user);
  }

  @Delete(":id")
  remove(@Param("id") id: string, @Req() req: { user: EnhancedUserPayload }) {
    return this.warehousesService.remove(id, req.user);
  }
}
