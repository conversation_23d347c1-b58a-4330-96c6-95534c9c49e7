import {
  IsString,
  IsNotEmpty,
  IsArray,
  <PERSON>idate<PERSON>ested,
  <PERSON><PERSON><PERSON>al,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "class-validator";
import { Type } from "class-transformer";
import { ReceiveItemDetailDto } from "./receive-item-detail.dto";

export class ReceiveItemsDto {
  @IsString()
  @IsNotEmpty()
  poNumber: string;

  @IsString()
  @IsNotEmpty()
  receivingLocationId: string;

  @IsString()
  @IsNotEmpty()
  barcode: string;

  @IsString()
  @IsOptional()
  shipToDestination?: string;

  @IsString()
  @IsOptional()
  @Matches(/^\d+$/, { message: "Destination code must contain only numbers" })
  @MaxLength(50)
  destinationCode?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  warehouseId?: string; // Add warehouse ID for warehouse-scoped access control

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ReceiveItemDetailDto)
  items: ReceiveItemDetailDto[];
}
