import {
  <PERSON>,
  Get,
  Param,
  Query,
  UseGuards,
  Req,
  ParseIntPipe,
  DefaultValuePipe,
} from "@nestjs/common";
import { PurchaseOrdersService } from "./purchase-orders.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { WarehousePermissionGuard } from "../auth/guards/warehouse-permission.guard";
import {
  AllowWarehouseFiltering,
  RequirePurchaseOrderAccess,
  RequestWithWarehouseContext,
} from "../auth/decorators/warehouse-permission.decorator";
import { EnhancedUserPayload } from "../auth/types";
import { QueryPurchaseOrderDto, PurchaseOrderResponse } from "./dto";
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiParam,
} from "@nestjs/swagger";

@ApiTags("Purchase Orders")
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, WarehousePermissionGuard)
@Controller("purchase-orders")
export class PurchaseOrdersController {
  constructor(private readonly purchaseOrdersService: PurchaseOrdersService) {}

  @Get()
  @AllowWarehouseFiltering()
  @ApiOperation({ summary: "Get all purchase orders for the tenant" })
  @ApiQuery({
    name: "page",
    required: false,
    type: Number,
    description: "Page number for pagination",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Number of items per page",
  })
  @ApiQuery({
    name: "status",
    required: false,
    type: String,
    description: "Filter by purchase order status",
  })
  @ApiQuery({
    name: "sortBy",
    required: false,
    type: String,
    description: "Field to sort by (e.g., createdAt, poNumber)",
  })
  @ApiQuery({
    name: "sortOrder",
    required: false,
    enum: ["asc", "desc"],
    description: "Sort order (asc or desc)",
  })
  @ApiQuery({
    name: "warehouseId",
    required: false,
    type: String,
    description: "Filter by warehouse ID",
  })
  async findAll(
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>,
    @Query() queryDto: QueryPurchaseOrderDto,
    @Query("warehouseId") warehouseId?: string
  ): Promise<{ data: PurchaseOrderResponse[]; count: number }> {
    return this.purchaseOrdersService.findAll(req.user, queryDto, warehouseId);
  }

  @Get(":poNumber")
  @RequirePurchaseOrderAccess()
  @ApiOperation({ summary: "Get a specific purchase order by PO Number" })
  @ApiParam({
    name: "poNumber",
    type: String,
    description: "The Purchase Order Number",
  })
  @ApiQuery({
    name: "warehouseId",
    required: false,
    type: String,
    description: "Filter by warehouse ID",
  })
  async findOne(
    @Param("poNumber") poNumber: string,
    @Req() req: RequestWithWarehouseContext<{ user: EnhancedUserPayload }>,
    @Query("warehouseId") warehouseId?: string
  ): Promise<PurchaseOrderResponse> {
    return this.purchaseOrdersService.findOne(poNumber, req.user, warehouseId);
  }

  // Placeholder for POST, PUT, PATCH endpoints
  // @Post()
  // @ApiOperation({ summary: 'Create a new purchase order' })
  // async create(@Req() req: { user: AuthenticatedUser }, /* @Body() createDto: CreatePurchaseOrderDto */) { /* ... */ }

  // @Patch(':poNumber}/finalize-receipt')
  // @ApiOperation({ summary: 'Finalize the receipt of a purchase order' })
  // async finalizeReceipt(@Param('poNumber') poNumber: string, @Req() req: { user: AuthenticatedUser }) { /* ... */ }
}
