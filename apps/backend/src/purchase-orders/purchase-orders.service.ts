import { Injectable, NotFoundException } from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { Prisma, Role } from "@prisma/client"; // Ensure Prisma namespace is imported
import {
  QueryPurchaseOrderDto,
  PurchaseOrderResponse,
  PurchaseOrderItemResponse,
} from "./dto";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";

// Define Prisma query arguments and payload type at the module level
const purchaseOrderFindUniqueArgs =
  Prisma.validator<Prisma.PurchaseOrderDefaultArgs>()({
    include: {
      items: {
        include: {
          item: true,
        },
      },
      warehouse: true,
    },
  });

type PrismaPurchaseOrderWithDetails = Prisma.PurchaseOrderGetPayload<
  typeof purchaseOrderFindUniqueArgs
>;

@Injectable()
export class PurchaseOrdersService {
  constructor(private prisma: PrismaService) {}

  async findAll(
    currentUser: AuthenticatedUser,
    queryDto: QueryPurchaseOrderDto,
    warehouseId?: string
  ): Promise<{ data: PurchaseOrderResponse[]; count: number }> {
    const {
      page = 1,
      limit = 10,
      status,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = queryDto;
    const skip = (page - 1) * limit;

    // Build warehouse-scoped where clause with security validation
    const whereClause: Prisma.PurchaseOrderWhereInput = {
      warehouse: {
        tenantId: currentUser.tenantId, // Security: ensure tenant access
      },
    };

    // Apply warehouse filtering for non-admin users
    if (currentUser.role !== Role.TENANT_ADMIN) {
      const userWarehouseIds =
        currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
      if (userWarehouseIds.length === 0) {
        return { data: [], count: 0 }; // No warehouse access
      }
      whereClause.warehouseId = { in: userWarehouseIds };
    }

    // Apply specific warehouse filter if provided
    if (warehouseId) {
      whereClause.warehouseId = warehouseId;
    }

    if (status) {
      whereClause.status = status;
    }

    const purchaseOrders = await this.prisma.purchaseOrder.findMany({
      where: whereClause,
      include: {
        // Using a simpler include here as full details might not be needed for list view
        items: { include: { item: true } },
        warehouse: true,
      },
      orderBy: {
        [sortBy]: sortOrder,
      },
      skip: skip,
      take: limit,
    });

    const totalCount = await this.prisma.purchaseOrder.count({
      where: whereClause,
    });

    const formattedResponse = purchaseOrders.map((po) => {
      const poAsDetailed = po as unknown as PrismaPurchaseOrderWithDetails;
      return {
        id: poAsDetailed.id,
        poNumber: poAsDetailed.poNumber,
        status: poAsDetailed.status,
        supplier: poAsDetailed.supplier,
        notes: poAsDetailed.notes,
        orderDate: poAsDetailed.orderDate,
        expectedDeliveryDate: poAsDetailed.expectedDeliveryDate,
        createdAt: poAsDetailed.createdAt,
        updatedAt: poAsDetailed.updatedAt,
        tenantId: poAsDetailed.tenantId,
        warehouseId: poAsDetailed.warehouseId,
        warehouse: poAsDetailed.warehouse
          ? {
              id: poAsDetailed.warehouse.id,
              name: poAsDetailed.warehouse.name,
              address: poAsDetailed.warehouse.address,
              status: poAsDetailed.warehouse.status,
              tenantId: poAsDetailed.warehouse.tenantId,
              createdAt: poAsDetailed.warehouse.createdAt,
              updatedAt: poAsDetailed.warehouse.updatedAt,
            }
          : undefined,
        items: poAsDetailed.items.map(
          (item) =>
            ({
              id: item.id,
              quantity: item.quantity,
              unitCost: item.unitCost,
              receivedQuantity: item.receivedQuantity,
              purchaseOrderId: item.purchaseOrderId,
              itemId: item.itemId,
              item: item.item
                ? {
                    id: item.item.id,
                    sku: item.item.sku,
                    name: item.item.name,
                    description: item.item.description,
                    unitOfMeasure: item.item.unitOfMeasure,
                    status: item.item.status,
                    createdAt: item.item.createdAt,
                    updatedAt: item.item.updatedAt,
                    tenantId: item.item.tenantId,
                    defaultCost: item.item.defaultCost,
                    lowStockThreshold: item.item.lowStockThreshold,
                  }
                : undefined,
            } as PurchaseOrderItemResponse)
        ),
      } as PurchaseOrderResponse;
    });

    return { data: formattedResponse, count: totalCount };
  }

  async findOne(
    poNumber: string,
    currentUser: AuthenticatedUser,
    warehouseId?: string
  ): Promise<PurchaseOrderResponse> {
    // Build warehouse-scoped where clause with security validation
    const whereClause: Prisma.PurchaseOrderWhereInput = {
      poNumber: poNumber,
      warehouse: {
        tenantId: currentUser.tenantId, // Security: ensure tenant access
      },
    };

    // Apply warehouse filtering for non-admin users
    if (currentUser.role !== Role.TENANT_ADMIN) {
      const userWarehouseIds =
        currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
      if (userWarehouseIds.length === 0) {
        throw new NotFoundException(
          `Purchase Order with PO Number '${poNumber}' not found.`
        );
      }
      whereClause.warehouseId = { in: userWarehouseIds };
    }

    // Apply specific warehouse filter if provided
    if (warehouseId) {
      whereClause.warehouseId = warehouseId;
    }

    const purchaseOrder: PrismaPurchaseOrderWithDetails | null =
      await this.prisma.purchaseOrder.findFirst({
        where: whereClause,
        include: purchaseOrderFindUniqueArgs.include, // Use module-level args
      });

    if (!purchaseOrder) {
      throw new NotFoundException(
        `Purchase Order with PO Number '${poNumber}' not found.`
      );
    }

    const formattedPo: PurchaseOrderResponse = {
      id: purchaseOrder.id,
      poNumber: purchaseOrder.poNumber,
      status: purchaseOrder.status,
      supplier: purchaseOrder.supplier,
      notes: purchaseOrder.notes,
      orderDate: purchaseOrder.orderDate,
      expectedDeliveryDate: purchaseOrder.expectedDeliveryDate,
      createdAt: purchaseOrder.createdAt,
      updatedAt: purchaseOrder.updatedAt,
      tenantId: purchaseOrder.tenantId,
      warehouseId: purchaseOrder.warehouseId,
      warehouse: purchaseOrder.warehouse
        ? {
            id: purchaseOrder.warehouse.id,
            name: purchaseOrder.warehouse.name,
            address: purchaseOrder.warehouse.address,
            status: purchaseOrder.warehouse.status,
            tenantId: purchaseOrder.warehouse.tenantId,
            createdAt: purchaseOrder.warehouse.createdAt,
            updatedAt: purchaseOrder.warehouse.updatedAt,
          }
        : undefined,
      items: purchaseOrder.items.map((poItem) => ({
        id: poItem.id,
        quantity: poItem.quantity,
        unitCost: poItem.unitCost,
        receivedQuantity: poItem.receivedQuantity,
        purchaseOrderId: poItem.purchaseOrderId,
        itemId: poItem.itemId,
        item: poItem.item
          ? {
              id: poItem.item.id,
              sku: poItem.item.sku,
              name: poItem.item.name,
              description: poItem.item.description,
              unitOfMeasure: poItem.item.unitOfMeasure,
              status: poItem.item.status,
              createdAt: poItem.item.createdAt,
              updatedAt: poItem.item.updatedAt,
              tenantId: poItem.item.tenantId,
              defaultCost: poItem.item.defaultCost,
              lowStockThreshold: poItem.item.lowStockThreshold,
            }
          : undefined,
      })),
    };
    return formattedPo;
  }

  // Placeholder for create, update, finalize methods to be added later
  // async create(createDto: any, tenantId: string) { /* ... */ }
  // async update(poNumber: string, updateDto: any, tenantId: string) { /* ... */ }
  // async finalizeReceipt(poNumber: string, tenantId: string) { /* ... */ }
}
