import { Module } from '@nestjs/common';
import { PurchaseOrdersService } from './purchase-orders.service';
import { PurchaseOrdersController } from './purchase-orders.controller';
import { PrismaModule } from '../prisma/prisma.module'; // Assuming you have a PrismaModule
import { AuthModule } from '../auth/auth.module'; // For JWT guard dependency
import { WarehousesModule } from '@/warehouses/warehouses.module';

@Module({
  imports: [
    PrismaModule, 
    AuthModule, // If guards/decorators from AuthModule are used directly or if service needs auth context
    WarehousesModule,
  ],
  controllers: [PurchaseOrdersController],
  providers: [PurchaseOrdersService],
  exports: [PurchaseOrdersService], // Export if other modules need this service
})
export class PurchaseOrdersModule {}
