import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { LocationCategory } from "@prisma/client";

class WarehouseSummaryDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;
}

class LocationSummaryDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty({ enum: LocationCategory })
  category: LocationCategory;

  @ApiPropertyOptional({ type: () => WarehouseSummaryDto })
  warehouse?: WarehouseSummaryDto;
}

class PalletSummaryDto {
  @ApiProperty()
  palletId: string;

  @ApiProperty()
  barcode: string;

  @ApiProperty()
  contentsSummary: string;

  @ApiProperty({ type: () => LocationSummaryDto, nullable: true })
  location: LocationSummaryDto | null;
}

class GroupedPalletsDto {
  @ApiProperty()
  shipToDestination: string;

  @ApiProperty({ type: () => [PalletSummaryDto] })
  pallets: PalletSummaryDto[];
}

export class ShipmentSummaryResponseDto {
  @ApiProperty()
  shipmentId: string;

  @ApiProperty()
  referenceNumber: string;

  @ApiProperty({ type: () => [GroupedPalletsDto] })
  groupedPallets: GroupedPalletsDto[];
}
