import { IsOptional, IsString, <PERSON>I<PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

export class QueryPurchaseOrderDto {
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @IsOptional()
  @IsString()
  status?: string; // e.g., Pending, Receiving, Received

  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt'; // Default sort field

  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc'; // Default sort order
}
