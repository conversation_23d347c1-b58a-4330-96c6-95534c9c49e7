import { Item, PurchaseOrderItem as PrismaPurchaseOrderItem, PurchaseOrder as PrismaPurchaseOrder, Warehouse } from '@prisma/client';

// Define a type for PurchaseOrderItem that includes the nested Item details
export interface PurchaseOrderItemResponse extends PrismaPurchaseOrderItem {
  item: Item;
}

// Define a type for PurchaseOrder that includes the nested PurchaseOrderItemResponse
export interface PurchaseOrderResponse extends PrismaPurchaseOrder {
  items: PurchaseOrderItemResponse[];
  warehouse?: Warehouse | null;
}
