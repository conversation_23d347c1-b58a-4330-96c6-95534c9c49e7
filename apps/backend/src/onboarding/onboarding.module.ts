import { Modu<PERSON> } from "@nestjs/common";
import { OnboardingController } from "./onboarding.controller";
import { OnboardingService } from "./onboarding.service";
import { SessionService } from "./session.service";
import { InvitationsController } from "./invitations.controller";
import { InvitationsService } from "./invitations.service";
import { CleanupService } from "./cleanup.service";
import { PrismaModule } from "../prisma/prisma.module";
import { AuthModule } from "../auth/auth.module";
import { WarehousesModule } from "../warehouses/warehouses.module";

@Module({
  imports: [PrismaModule, AuthModule, WarehousesModule],
  controllers: [OnboardingController, InvitationsController],
  providers: [
    OnboardingService,
    SessionService,
    InvitationsService,
    CleanupService,
  ],
  exports: [OnboardingService, SessionService, InvitationsService],
})
export class OnboardingModule {}
