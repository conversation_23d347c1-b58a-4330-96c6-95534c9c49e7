import { Injectable, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { OnboardingState, OnboardingStep, ONBOARDING_SESSION_DURATION } from '@quildora/types';

export interface OnboardingSession {
  id: string;
  userId?: string;
  tenantId?: string;
  currentStep: OnboardingStep;
  data: OnboardingState;
  completedAt?: Date;
  expiresAt: Date;
  createdAt: Date;
  updatedAt: Date;
  warehouseId?: string; // Additional field for warehouse tracking
}

@Injectable()
export class SessionService {
  constructor(private readonly prisma: PrismaService) {}

  async createSession(data: Partial<OnboardingState>): Promise<OnboardingSession> {
    try {
      const expiresAt = new Date(Date.now() + ONBOARDING_SESSION_DURATION);
      
      const session = await this.prisma.onboardingSession.create({
        data: {
          currentStep: data.step || 'business_info',
          data: data as any, // Prisma <PERSON> type
          expiresAt,
        },
      });

      return this.mapPrismaSessionToSession(session);
    } catch (error) {
      console.error('Error creating onboarding session:', error);
      throw new InternalServerErrorException('Failed to create onboarding session');
    }
  }

  async getSession(sessionId: string): Promise<OnboardingSession | null> {
    try {
      const session = await this.prisma.onboardingSession.findUnique({
        where: { id: sessionId },
      });

      if (!session) {
        return null;
      }

      // Check if session is expired
      if (session.expiresAt < new Date()) {
        await this.deleteSession(sessionId);
        return null;
      }

      return this.mapPrismaSessionToSession(session);
    } catch (error) {
      console.error('Error getting onboarding session:', error);
      throw new InternalServerErrorException('Failed to get onboarding session');
    }
  }

  async updateSession(
    sessionId: string, 
    updates: Partial<OnboardingState & { userId?: string; tenantId?: string; warehouseId?: string }>
  ): Promise<OnboardingSession> {
    try {
      const existingSession = await this.getSession(sessionId);
      if (!existingSession) {
        throw new NotFoundException('Onboarding session not found');
      }

      // Merge existing data with updates
      const updatedData = {
        ...existingSession.data,
        ...updates,
      };

      // Remove non-data fields from the data object
      const { userId, tenantId, warehouseId, ...dataUpdates } = updates;
      const mergedData = {
        ...existingSession.data,
        ...dataUpdates,
      };

      const session = await this.prisma.onboardingSession.update({
        where: { id: sessionId },
        data: {
          currentStep: updates.step || existingSession.currentStep,
          data: mergedData as any,
          userId: userId || existingSession.userId,
          tenantId: tenantId || existingSession.tenantId,
          updatedAt: new Date(),
        },
      });

      const mappedSession = this.mapPrismaSessionToSession(session);
      
      // Add warehouseId if provided (not stored in Prisma model but tracked in service)
      if (warehouseId) {
        (mappedSession as any).warehouseId = warehouseId;
      }

      return mappedSession;
    } catch (error) {
      console.error('Error updating onboarding session:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update onboarding session');
    }
  }

  async completeSession(sessionId: string): Promise<void> {
    try {
      const session = await this.getSession(sessionId);
      if (!session) {
        throw new NotFoundException('Onboarding session not found');
      }

      await this.prisma.onboardingSession.update({
        where: { id: sessionId },
        data: {
          completedAt: new Date(),
          currentStep: 'completion',
        },
      });
    } catch (error) {
      console.error('Error completing onboarding session:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to complete onboarding session');
    }
  }

  async deleteSession(sessionId: string): Promise<void> {
    try {
      await this.prisma.onboardingSession.delete({
        where: { id: sessionId },
      });
    } catch (error) {
      // Don't throw error if session doesn't exist
      if (error.code !== 'P2025') {
        console.error('Error deleting onboarding session:', error);
        throw new InternalServerErrorException('Failed to delete onboarding session');
      }
    }
  }

  async cleanupExpiredSessions(): Promise<number> {
    try {
      const result = await this.prisma.onboardingSession.deleteMany({
        where: {
          expiresAt: {
            lt: new Date(),
          },
          completedAt: null, // Only delete incomplete sessions
        },
      });

      console.log(`Cleaned up ${result.count} expired onboarding sessions`);
      return result.count;
    } catch (error) {
      console.error('Error cleaning up expired sessions:', error);
      throw new InternalServerErrorException('Failed to cleanup expired sessions');
    }
  }

  async getSessionsByUser(userId: string): Promise<OnboardingSession[]> {
    try {
      const sessions = await this.prisma.onboardingSession.findMany({
        where: { 
          userId,
          expiresAt: {
            gt: new Date(), // Only return non-expired sessions
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      return sessions.map(session => this.mapPrismaSessionToSession(session));
    } catch (error) {
      console.error('Error getting sessions by user:', error);
      throw new InternalServerErrorException('Failed to get user sessions');
    }
  }

  async getSessionsByTenant(tenantId: string): Promise<OnboardingSession[]> {
    try {
      const sessions = await this.prisma.onboardingSession.findMany({
        where: { 
          tenantId,
          expiresAt: {
            gt: new Date(), // Only return non-expired sessions
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      return sessions.map(session => this.mapPrismaSessionToSession(session));
    } catch (error) {
      console.error('Error getting sessions by tenant:', error);
      throw new InternalServerErrorException('Failed to get tenant sessions');
    }
  }

  async extendSession(sessionId: string, additionalHours: number = 24): Promise<OnboardingSession> {
    try {
      const session = await this.getSession(sessionId);
      if (!session) {
        throw new NotFoundException('Onboarding session not found');
      }

      const newExpiresAt = new Date(session.expiresAt.getTime() + (additionalHours * 60 * 60 * 1000));

      const updatedSession = await this.prisma.onboardingSession.update({
        where: { id: sessionId },
        data: { expiresAt: newExpiresAt },
      });

      return this.mapPrismaSessionToSession(updatedSession);
    } catch (error) {
      console.error('Error extending onboarding session:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to extend onboarding session');
    }
  }

  async getSessionStats(): Promise<{
    total: number;
    active: number;
    completed: number;
    expired: number;
  }> {
    try {
      const now = new Date();
      
      const [total, active, completed, expired] = await Promise.all([
        this.prisma.onboardingSession.count(),
        this.prisma.onboardingSession.count({
          where: {
            expiresAt: { gt: now },
            completedAt: null,
          },
        }),
        this.prisma.onboardingSession.count({
          where: { completedAt: { not: null } },
        }),
        this.prisma.onboardingSession.count({
          where: {
            expiresAt: { lt: now },
            completedAt: null,
          },
        }),
      ]);

      return { total, active, completed, expired };
    } catch (error) {
      console.error('Error getting session stats:', error);
      throw new InternalServerErrorException('Failed to get session statistics');
    }
  }

  private mapPrismaSessionToSession(prismaSession: any): OnboardingSession {
    return {
      id: prismaSession.id,
      userId: prismaSession.userId,
      tenantId: prismaSession.tenantId,
      currentStep: prismaSession.currentStep as OnboardingStep,
      data: prismaSession.data as OnboardingState,
      completedAt: prismaSession.completedAt,
      expiresAt: prismaSession.expiresAt,
      createdAt: prismaSession.createdAt,
      updatedAt: prismaSession.updatedAt,
    };
  }
}
