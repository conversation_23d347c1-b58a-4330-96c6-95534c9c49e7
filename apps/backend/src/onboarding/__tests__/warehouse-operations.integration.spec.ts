import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import request from "supertest";
import { PrismaService } from "../../prisma/prisma.service";
import { OnboardingService } from "../onboarding.service";
import { AuthService } from "../../auth/auth.service";
import { Role } from "@quildora/types";

describe("Warehouse Operations Integration Tests", () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let onboardingService: OnboardingService;
  let authService: AuthService;
  let adminToken: string;
  let memberToken: string;
  let managerToken: string;
  let testTenant: any;
  let testWarehouse: any;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        // Import your actual modules here
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    prisma = moduleFixture.get<PrismaService>(PrismaService);
    onboardingService = moduleFixture.get<OnboardingService>(OnboardingService);
    authService = moduleFixture.get<AuthService>(AuthService);

    // Setup test data
    await setupTestData();
  });

  afterAll(async () => {
    await cleanupTestData();
    await app.close();
  });

  async function setupTestData() {
    // Create test tenant
    testTenant = await prisma.tenant.create({
      data: {
        name: "Integration Test Tenant",
      },
    });

    // Create test warehouse
    testWarehouse = await prisma.warehouse.create({
      data: {
        name: "Integration Test Warehouse",
        tenantId: testTenant.id,
        status: "Active",
      },
    });

    // Create test users with different roles
    const adminUser = await prisma.user.create({
      data: {
        email: "<EMAIL>",
        name: "Test Admin",
        role: Role.TENANT_ADMIN,
        tenantId: testTenant.id,
        status: "Active",
      },
    });

    const managerUser = await prisma.user.create({
      data: {
        email: "<EMAIL>",
        name: "Test Manager",
        role: Role.WAREHOUSE_MANAGER,
        tenantId: testTenant.id,
        status: "Active",
      },
    });

    const memberUser = await prisma.user.create({
      data: {
        email: "<EMAIL>",
        name: "Test Member",
        role: Role.WAREHOUSE_MEMBER,
        tenantId: testTenant.id,
        status: "Active",
      },
    });

    // Create warehouse user assignments
    await prisma.warehouseUser.createMany({
      data: [
        {
          userId: adminUser.id,
          warehouseId: testWarehouse.id,
          role: Role.TENANT_ADMIN,
        },
        {
          userId: managerUser.id,
          warehouseId: testWarehouse.id,
          role: Role.WAREHOUSE_MANAGER,
        },
        {
          userId: memberUser.id,
          warehouseId: testWarehouse.id,
          role: Role.WAREHOUSE_MEMBER,
        },
      ],
    });

    // Generate auth tokens (mock implementation)
    adminToken = "mock-admin-token";
    managerToken = "mock-manager-token";
    memberToken = "mock-member-token";
  }

  async function cleanupTestData() {
    await prisma.warehouseUser.deleteMany({
      where: { warehouseId: testWarehouse.id },
    });
    await prisma.user.deleteMany({
      where: { tenantId: testTenant.id },
    });
    await prisma.warehouse.deleteMany({
      where: { tenantId: testTenant.id },
    });
    await prisma.tenant.delete({
      where: { id: testTenant.id },
    });
  }

  describe("Pallet Management Operations", () => {
    it("should allow all roles to view pallets", async () => {
      // Test admin access
      const adminResponse = await request(app.getHttpServer())
        .get("/api/pallets")
        .set("Authorization", `Bearer ${adminToken}`)
        .set("X-Warehouse-ID", testWarehouse.id)
        .expect(200);

      // Test manager access
      const managerResponse = await request(app.getHttpServer())
        .get("/api/pallets")
        .set("Authorization", `Bearer ${managerToken}`)
        .set("X-Warehouse-ID", testWarehouse.id)
        .expect(200);

      // Test member access
      const memberResponse = await request(app.getHttpServer())
        .get("/api/pallets")
        .set("Authorization", `Bearer ${memberToken}`)
        .set("X-Warehouse-ID", testWarehouse.id)
        .expect(200);

      expect(adminResponse.body).toBeDefined();
      expect(managerResponse.body).toBeDefined();
      expect(memberResponse.body).toBeDefined();
    });

    it("should allow all roles to create pallets", async () => {
      const palletData = {
        barcode: "TEST-PALLET-001",
        locationId: "test-location-id",
        description: "Test pallet for integration",
      };

      // Test admin can create pallets
      await request(app.getHttpServer())
        .post("/api/pallets")
        .set("Authorization", `Bearer ${adminToken}`)
        .set("X-Warehouse-ID", testWarehouse.id)
        .send(palletData)
        .expect(201);

      // Test manager can create pallets
      await request(app.getHttpServer())
        .post("/api/pallets")
        .set("Authorization", `Bearer ${managerToken}`)
        .set("X-Warehouse-ID", testWarehouse.id)
        .send({ ...palletData, barcode: "TEST-PALLET-002" })
        .expect(201);

      // Test member can create pallets
      await request(app.getHttpServer())
        .post("/api/pallets")
        .set("Authorization", `Bearer ${memberToken}`)
        .set("X-Warehouse-ID", testWarehouse.id)
        .send({ ...palletData, barcode: "TEST-PALLET-003" })
        .expect(201);
    });

    it("should enforce warehouse context for pallet operations", async () => {
      // Test without warehouse header should fail
      await request(app.getHttpServer())
        .get("/api/pallets")
        .set("Authorization", `Bearer ${adminToken}`)
        .expect(400); // Should require warehouse context

      // Test with invalid warehouse should fail
      await request(app.getHttpServer())
        .get("/api/pallets")
        .set("Authorization", `Bearer ${adminToken}`)
        .set("X-Warehouse-ID", "invalid-warehouse-id")
        .expect(403); // Should deny access to invalid warehouse
    });
  });

  describe("Receiving Operations", () => {
    it("should allow receiving operations for all roles", async () => {
      // Create a purchase order first
      const purchaseOrder = await prisma.purchaseOrder.create({
        data: {
          poNumber: "PO-TEST-001",
          supplier: "Test Supplier",
          warehouseId: testWarehouse.id,
          status: "PROCESSING",
          expectedDeliveryDate: new Date(),
          tenantId: testTenant.id,
        },
      });

      const shipmentData = {
        referenceNumber: "SHIP-TEST-001",
        status: "PROCESSING",
        purchaseOrderId: purchaseOrder.id,
      };

      // Test admin can create shipments
      const adminShipment = await request(app.getHttpServer())
        .post("/api/shipments")
        .set("Authorization", `Bearer ${adminToken}`)
        .set("X-Warehouse-ID", testWarehouse.id)
        .send(shipmentData)
        .expect(201);

      // Test manager can view shipments
      await request(app.getHttpServer())
        .get("/api/shipments")
        .set("Authorization", `Bearer ${managerToken}`)
        .set("X-Warehouse-ID", testWarehouse.id)
        .expect(200);

      // Test member can view shipments
      await request(app.getHttpServer())
        .get("/api/shipments")
        .set("Authorization", `Bearer ${memberToken}`)
        .set("X-Warehouse-ID", testWarehouse.id)
        .expect(200);

      expect(adminShipment.body.id).toBeDefined();
    });

    it("should allow pallet receiving for all roles", async () => {
      // Create a test purchase order and shipment first
      const purchaseOrder = await prisma.purchaseOrder.create({
        data: {
          poNumber: "PO-RECEIVE-001",
          supplier: "Test Supplier",
          warehouseId: testWarehouse.id,
          status: "PROCESSING",
          expectedDeliveryDate: new Date(),
          tenantId: testTenant.id,
        },
      });

      const shipment = await prisma.shipment.create({
        data: {
          referenceNumber: "SHIP-001",
          status: "PROCESSING",
          purchaseOrderId: purchaseOrder.id,
          tenantId: testTenant.id,
        },
      });

      const receivingData = {
        shipmentId: shipment.id,
        pallets: [
          {
            barcode: "RECEIVE-PALLET-001",
            locationId: "test-location-id",
            description: "Received pallet",
          },
        ],
      };

      // Test all roles can receive pallets
      await request(app.getHttpServer())
        .post("/api/receiving/pallets")
        .set("Authorization", `Bearer ${memberToken}`)
        .set("X-Warehouse-ID", testWarehouse.id)
        .send(receivingData)
        .expect(201);
    });
  });

  describe("Location Management", () => {
    it("should allow location access based on role permissions", async () => {
      // Test all roles can view locations
      await request(app.getHttpServer())
        .get("/api/locations")
        .set("Authorization", `Bearer ${adminToken}`)
        .set("X-Warehouse-ID", testWarehouse.id)
        .expect(200);

      await request(app.getHttpServer())
        .get("/api/locations")
        .set("Authorization", `Bearer ${managerToken}`)
        .set("X-Warehouse-ID", testWarehouse.id)
        .expect(200);

      await request(app.getHttpServer())
        .get("/api/locations")
        .set("Authorization", `Bearer ${memberToken}`)
        .set("X-Warehouse-ID", testWarehouse.id)
        .expect(200);
    });

    it("should restrict location creation to managers and admins", async () => {
      const locationData = {
        name: "Test Location",
        code: "TEST-LOC-001",
        type: "STORAGE",
      };

      // Admin should be able to create locations
      await request(app.getHttpServer())
        .post("/api/locations")
        .set("Authorization", `Bearer ${adminToken}`)
        .set("X-Warehouse-ID", testWarehouse.id)
        .send(locationData)
        .expect(201);

      // Manager should be able to create locations
      await request(app.getHttpServer())
        .post("/api/locations")
        .set("Authorization", `Bearer ${managerToken}`)
        .set("X-Warehouse-ID", testWarehouse.id)
        .send({ ...locationData, code: "TEST-LOC-002" })
        .expect(201);

      // Member should NOT be able to create locations
      await request(app.getHttpServer())
        .post("/api/locations")
        .set("Authorization", `Bearer ${memberToken}`)
        .set("X-Warehouse-ID", testWarehouse.id)
        .send({ ...locationData, code: "TEST-LOC-003" })
        .expect(403);
    });
  });

  describe("User Management Operations", () => {
    it("should restrict user management to tenant admins", async () => {
      // Only tenant admin should access user management
      await request(app.getHttpServer())
        .get("/api/users")
        .set("Authorization", `Bearer ${adminToken}`)
        .expect(200);

      // Manager should NOT access user management
      await request(app.getHttpServer())
        .get("/api/users")
        .set("Authorization", `Bearer ${managerToken}`)
        .expect(403);

      // Member should NOT access user management
      await request(app.getHttpServer())
        .get("/api/users")
        .set("Authorization", `Bearer ${memberToken}`)
        .expect(403);
    });

    it("should allow invitation management for tenant admins only", async () => {
      const invitationData = {
        email: "<EMAIL>",
        role: Role.WAREHOUSE_MEMBER,
        warehouseIds: [testWarehouse.id],
      };

      // Admin should be able to create invitations
      await request(app.getHttpServer())
        .post("/api/invitations")
        .set("Authorization", `Bearer ${adminToken}`)
        .send(invitationData)
        .expect(201);

      // Manager should NOT be able to create invitations
      await request(app.getHttpServer())
        .post("/api/invitations")
        .set("Authorization", `Bearer ${managerToken}`)
        .send(invitationData)
        .expect(403);

      // Member should NOT be able to create invitations
      await request(app.getHttpServer())
        .post("/api/invitations")
        .set("Authorization", `Bearer ${memberToken}`)
        .send(invitationData)
        .expect(403);
    });
  });

  describe("Cross-Warehouse Access Control", () => {
    it("should prevent access to unauthorized warehouses", async () => {
      // Create another warehouse in different tenant
      const otherTenant = await prisma.tenant.create({
        data: {
          name: "Other Tenant",
        },
      });

      const otherWarehouse = await prisma.warehouse.create({
        data: {
          name: "Other Warehouse",
          tenantId: otherTenant.id,
          status: "Active",
        },
      });

      // Test that users cannot access other tenant's warehouse
      await request(app.getHttpServer())
        .get("/api/pallets")
        .set("Authorization", `Bearer ${adminToken}`)
        .set("X-Warehouse-ID", otherWarehouse.id)
        .expect(403);

      // Cleanup
      await prisma.warehouse.delete({ where: { id: otherWarehouse.id } });
      await prisma.tenant.delete({ where: { id: otherTenant.id } });
    });
  });

  describe("Audit Trail Integration", () => {
    it("should create audit logs for warehouse operations", async () => {
      const palletData = {
        barcode: "AUDIT-PALLET-001",
        locationId: "test-location-id",
        description: "Audit test pallet",
      };

      // Create a pallet
      const response = await request(app.getHttpServer())
        .post("/api/pallets")
        .set("Authorization", `Bearer ${adminToken}`)
        .set("X-Warehouse-ID", testWarehouse.id)
        .send(palletData)
        .expect(201);

      // Check that audit log was created
      const auditLogs = await prisma.auditLog.findMany({
        where: {
          entityId: response.body.id,
        },
      });

      expect(auditLogs.length).toBeGreaterThan(0);
      expect(auditLogs[0].action).toBe("CREATE_PALLET");
      expect(auditLogs[0].userId).toBeDefined();
    });
  });
});
