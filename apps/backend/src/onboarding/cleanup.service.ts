import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from '../prisma/prisma.service';
import { SessionService } from './session.service';

@Injectable()
export class CleanupService {
  private readonly logger = new Logger(CleanupService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly sessionService: SessionService,
  ) {}

  // Run cleanup every hour
  @Cron(CronExpression.EVERY_HOUR)
  async handleExpiredSessionsCleanup() {
    this.logger.log('Starting expired onboarding sessions cleanup...');
    
    try {
      const deletedCount = await this.sessionService.cleanupExpiredSessions();
      this.logger.log(`Cleaned up ${deletedCount} expired onboarding sessions`);
    } catch (error) {
      this.logger.error('Failed to cleanup expired onboarding sessions:', error);
    }
  }

  // Run cleanup every day at 2 AM
  @Cron('0 2 * * *')
  async handleExpiredInvitationsCleanup() {
    this.logger.log('Starting expired invitations cleanup...');
    
    try {
      const deletedCount = await this.cleanupExpiredInvitations();
      this.logger.log(`Cleaned up ${deletedCount} expired invitations`);
    } catch (error) {
      this.logger.error('Failed to cleanup expired invitations:', error);
    }
  }

  // Manual cleanup methods for testing or admin use
  async cleanupExpiredInvitations(): Promise<number> {
    try {
      const result = await this.prisma.tenantInvitation.deleteMany({
        where: {
          expiresAt: {
            lt: new Date(),
          },
          acceptedAt: null, // Only delete unaccepted invitations
        },
      });

      this.logger.log(`Cleaned up ${result.count} expired invitations`);
      return result.count;
    } catch (error) {
      this.logger.error('Error cleaning up expired invitations:', error);
      throw error;
    }
  }

  async cleanupOldCompletedSessions(olderThanDays: number = 30): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const result = await this.prisma.onboardingSession.deleteMany({
        where: {
          completedAt: {
            not: null,
            lt: cutoffDate,
          },
        },
      });

      this.logger.log(`Cleaned up ${result.count} old completed onboarding sessions`);
      return result.count;
    } catch (error) {
      this.logger.error('Error cleaning up old completed sessions:', error);
      throw error;
    }
  }

  async getCleanupStats(): Promise<{
    expiredSessions: number;
    expiredInvitations: number;
    oldCompletedSessions: number;
  }> {
    try {
      const now = new Date();
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const [expiredSessions, expiredInvitations, oldCompletedSessions] = await Promise.all([
        this.prisma.onboardingSession.count({
          where: {
            expiresAt: { lt: now },
            completedAt: null,
          },
        }),
        this.prisma.tenantInvitation.count({
          where: {
            expiresAt: { lt: now },
            acceptedAt: null,
          },
        }),
        this.prisma.onboardingSession.count({
          where: {
            completedAt: {
              not: null,
              lt: thirtyDaysAgo,
            },
          },
        }),
      ]);

      return {
        expiredSessions,
        expiredInvitations,
        oldCompletedSessions,
      };
    } catch (error) {
      this.logger.error('Error getting cleanup stats:', error);
      throw error;
    }
  }

  // Run comprehensive cleanup (for admin use)
  async runFullCleanup(): Promise<{
    expiredSessions: number;
    expiredInvitations: number;
    oldCompletedSessions: number;
  }> {
    this.logger.log('Starting full cleanup process...');

    try {
      const [expiredSessions, expiredInvitations, oldCompletedSessions] = await Promise.all([
        this.sessionService.cleanupExpiredSessions(),
        this.cleanupExpiredInvitations(),
        this.cleanupOldCompletedSessions(),
      ]);

      const results = {
        expiredSessions,
        expiredInvitations,
        oldCompletedSessions,
      };

      this.logger.log('Full cleanup completed:', results);
      return results;
    } catch (error) {
      this.logger.error('Error during full cleanup:', error);
      throw error;
    }
  }
}
