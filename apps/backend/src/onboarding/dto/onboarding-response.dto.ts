import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { OnboardingStep } from "@quildora/types";

export class StartBusinessOnboardingResponseDto {
  @ApiProperty({
    description: "Unique session ID for the onboarding process",
    example: "clp123abc456def789",
  })
  sessionId: string;

  @ApiProperty({
    description: "Next step in the onboarding process",
    enum: [
      "business_info",
      "admin_account",
      "warehouse_setup",
      "team_setup",
      "completion",
    ],
    example: "admin_account",
  })
  nextStep: OnboardingStep;
}

export class CreateAdminAccountResponseDto {
  @ApiProperty({
    description: "Created user ID",
    example: "clp456def789ghi012",
  })
  userId: string;

  @ApiProperty({
    description: "Supabase user object",
    type: Object,
  })
  supabaseUser: any;

  @ApiProperty({
    description: "Next step in the onboarding process",
    enum: [
      "business_info",
      "admin_account",
      "warehouse_setup",
      "team_setup",
      "completion",
    ],
    example: "warehouse_setup",
  })
  nextStep: OnboardingStep;
}

export class SetupWarehouseResponseDto {
  @ApiProperty({
    description: "Created warehouse ID",
    example: "clp789ghi012jkl345",
  })
  warehouseId: string;

  @ApiProperty({
    description: "Created tenant ID",
    example: "clp012jkl345mno678",
  })
  tenantId: string;

  @ApiProperty({
    description: "Next step in the onboarding process",
    enum: [
      "business_info",
      "admin_account",
      "warehouse_setup",
      "team_setup",
      "completion",
    ],
    example: "team_setup",
  })
  nextStep: OnboardingStep;
}

export class CompleteOnboardingResponseDto {
  @ApiProperty({
    description: "JWT access token for the authenticated user",
    example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  })
  accessToken: string;

  @ApiProperty({
    description: "Complete user object",
    type: Object,
  })
  user: any;

  @ApiProperty({
    description: "Created warehouse object",
    type: Object,
  })
  warehouse: any;

  @ApiProperty({
    description: "Created tenant object",
    type: Object,
  })
  tenant: any;

  @ApiPropertyOptional({
    description: "Sent invitations (if team setup was included)",
    type: "array",
    items: { type: "object" },
  })
  invitations?: any[];
}
