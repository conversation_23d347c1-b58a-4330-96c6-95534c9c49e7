import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON>ot<PERSON><PERSON>y,
  <PERSON><PERSON><PERSON>al,
  IsArray,
  IsEmail,
  IsEnum,
  ValidateNested,
} from "class-validator";
import { Type } from "class-transformer";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Role } from "@quildora/types";

export class TeamSetupDto {
  @ApiProperty({
    description: "Email addresses to invite to the team",
    example: ["<EMAIL>", "<EMAIL>"],
    type: [String],
  })
  @IsArray()
  @IsEmail({}, { each: true, message: "Please enter valid email addresses" })
  inviteEmails: string[];

  @ApiProperty({
    description: "Default role for invited team members",
    enum: Role,
    example: Role.WAREHOUSE_MEMBER,
  })
  @IsEnum(Role, { message: "Invalid role specified" })
  defaultRole: Role.WAREHOUSE_MEMBER | Role.WAREHOUSE_MANAGER;
}

export class CompleteOnboardingDto {
  @ApiProperty({
    description: "Onboarding session ID",
    example: "clp123abc456def789",
  })
  @IsString()
  @IsNotEmpty()
  sessionId: string;

  @ApiPropertyOptional({
    description: "Team setup information (optional)",
    type: TeamSetupDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => TeamSetupDto)
  teamSetup?: TeamSetupDto;
}
