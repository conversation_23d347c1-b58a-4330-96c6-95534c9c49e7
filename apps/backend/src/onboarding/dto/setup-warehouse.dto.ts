import { IsString, <PERSON>NotEmpty, <PERSON><PERSON>ptional, MaxLength, Min<PERSON>ength } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SetupWarehouseDto {
  @ApiProperty({
    description: 'Onboarding session ID',
    example: 'clp123abc456def789',
  })
  @IsString()
  @IsNotEmpty()
  sessionId: string;

  @ApiProperty({
    description: 'Warehouse name',
    example: 'Main Distribution Center',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(2, { message: 'Warehouse name must be at least 2 characters' })
  @MaxLength(100, { message: 'Warehouse name must be less than 100 characters' })
  warehouseName: string;

  @ApiPropertyOptional({
    description: 'Warehouse address',
    example: '123 Industrial Blvd, Manufacturing City, ST 12345',
    maxLength: 500,
  })
  @IsString()
  @IsOptional()
  @MaxLength(500, { message: 'Address must be less than 500 characters' })
  address?: string;

  @ApiPropertyOptional({
    description: 'Type of warehouse',
    example: 'Distribution Center',
  })
  @IsString()
  @IsOptional()
  warehouseType?: string;

  @ApiPropertyOptional({
    description: 'Expected volume/throughput',
    example: 'Medium (1000-5000 pallets/month)',
  })
  @IsString()
  @IsOptional()
  expectedVolume?: string;
}
