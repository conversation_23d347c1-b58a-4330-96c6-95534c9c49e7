import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "class-validator";
import { ApiProperty } from "@nestjs/swagger";
import { Role } from "@prisma/client";

export class CreateInvitationDto {
  @ApiProperty({
    description: "Email address to invite",
    example: "<EMAIL>",
  })
  @IsEmail({}, { message: "Please enter a valid email address" })
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: "Role for the invited user",
    enum: ["WAREHOUSE_MEMBER", "WAREHOUSE_MANAGER"],
    example: "WAREHOUSE_MEMBER",
  })
  @IsEnum(["WAREHOUSE_MEMBER", "WAREHOUSE_MANAGER"], {
    message: "Role must be WAREHOUSE_MEMBER or WAREHOUSE_MANAGER",
  })
  role: "WAREHOUSE_MEMBER" | "WAREHOUSE_MANAGER";

  @ApiProperty({
    description: "Array of warehouse IDs the user should have access to",
    example: ["clp123abc456def789"],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  warehouseIds: string[];
}

export class AcceptInvitationDto {
  @ApiProperty({
    description: "Full name of the user",
    example: "Jane Smith",
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(2, { message: "Full name must be at least 2 characters" })
  @MaxLength(100, { message: "Full name must be less than 100 characters" })
  fullName: string;

  @ApiProperty({
    description: "Password for the new account",
    example: "SecurePassword123!",
    minLength: 8,
    maxLength: 128,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(8, { message: "Password must be at least 8 characters" })
  @MaxLength(128, { message: "Password must be less than 128 characters" })
  @Matches(/[A-Z]/, {
    message: "Password must contain at least one uppercase letter",
  })
  @Matches(/[a-z]/, {
    message: "Password must contain at least one lowercase letter",
  })
  @Matches(/[0-9]/, { message: "Password must contain at least one number" })
  password: string;
}

export class CreateInvitationResponseDto {
  @ApiProperty({
    description: "Unique invitation ID",
    example: "clp123abc456def789",
  })
  invitationId: string;

  @ApiProperty({
    description: "Invitation code for sharing",
    example: "INV-ABC123DEF456",
  })
  invitationCode: string;

  @ApiProperty({
    description: "Invitation expiration date",
    example: "2024-12-22T10:30:00Z",
  })
  expiresAt: string;
}

export class ValidateInvitationResponseDto {
  @ApiProperty({
    description: "Whether the invitation is valid",
    example: true,
  })
  valid: boolean;

  @ApiProperty({
    description: "Invitation ID",
    required: false,
    example: "clp123abc456def789",
  })
  id?: string;

  @ApiProperty({
    description: "Email address that was invited",
    required: false,
    example: "<EMAIL>",
  })
  email?: string;

  @ApiProperty({
    description: "Role that will be assigned",
    enum: Role,
    required: false,
    example: Role.WAREHOUSE_MEMBER,
  })
  role?: string;

  @ApiProperty({
    description: "Invitation status",
    required: false,
    example: "pending",
  })
  status?: string;

  @ApiProperty({
    description: "Invitation expiration date",
    required: false,
    example: "2024-12-22T10:30:00Z",
  })
  expiresAt?: string;

  @ApiProperty({
    description: "Tenant information",
    required: false,
    type: Object,
  })
  tenant?: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: "Warehouse information",
    required: false,
    type: Object,
  })
  warehouse?: {
    id: string;
    name: string;
    address?: string;
  };

  @ApiProperty({
    description: "Information about who sent the invitation",
    required: false,
    type: Object,
  })
  invitedBy?: {
    name: string;
    email: string;
  };
}

export class AcceptInvitationResponseDto {
  @ApiProperty({
    description: "JWT access token for the new user",
    example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  })
  accessToken: string;

  @ApiProperty({
    description: "Created user object",
    type: Object,
  })
  user: any;

  @ApiProperty({
    description: "Warehouses the user has access to",
    type: [Object],
  })
  warehouses: any[];

  @ApiProperty({
    description: "Tenant information",
    type: Object,
  })
  tenant: any;
}
