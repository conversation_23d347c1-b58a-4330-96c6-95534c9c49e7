import { IsS<PERSON>, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class StartBusinessOnboardingDto {
  @ApiProperty({
    description: 'Company name',
    example: 'Acme Corporation',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(2, { message: 'Company name must be at least 2 characters' })
  @MaxLength(100, { message: 'Company name must be less than 100 characters' })
  companyName: string;

  @ApiPropertyOptional({
    description: 'Industry sector',
    example: 'Manufacturing',
  })
  @IsString()
  @IsOptional()
  industry?: string;

  @ApiPropertyOptional({
    description: 'Company size',
    example: 'Small (1-50 employees)',
  })
  @IsString()
  @IsOptional()
  companySize?: string;

  @ApiPropertyOptional({
    description: 'Primary use case for the system',
    example: 'Inventory tracking and management',
  })
  @IsString()
  @IsOptional()
  primaryUseCase?: string;
}
