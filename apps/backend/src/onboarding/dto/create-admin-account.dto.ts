import { 
  Is<PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>Empty, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>,
  Matches,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateAdminAccountDto {
  @ApiProperty({
    description: 'Onboarding session ID',
    example: 'clp123abc456def789',
  })
  @IsString()
  @IsNotEmpty()
  sessionId: string;

  @ApiProperty({
    description: 'Full name of the admin user',
    example: '<PERSON>',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(2, { message: 'Full name must be at least 2 characters' })
  @MaxLength(100, { message: 'Full name must be less than 100 characters' })
  fullName: string;

  @ApiProperty({
    description: 'Email address for the admin account',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Please enter a valid email address' })
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Password for the admin account',
    example: 'SecurePassword123!',
    minLength: 8,
    maxLength: 128,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(8, { message: 'Password must be at least 8 characters' })
  @MaxLength(128, { message: 'Password must be less than 128 characters' })
  @Matches(/[A-Z]/, { message: 'Password must contain at least one uppercase letter' })
  @Matches(/[a-z]/, { message: 'Password must contain at least one lowercase letter' })
  @Matches(/[0-9]/, { message: 'Password must contain at least one number' })
  password: string;

  @ApiPropertyOptional({
    description: 'Phone number (optional)',
    example: '******-123-4567',
  })
  @IsString()
  @IsOptional()
  @Matches(/^\+?[\d\s\-\(\)]+$/, { 
    message: 'Please enter a valid phone number' 
  })
  phoneNumber?: string;
}
