import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { Role } from "@prisma/client";
import { SessionService } from "./session.service";
import { WarehousesService } from "../warehouses/warehouses.service";
import { AuthService } from "../auth/auth.service";
import { InvitationsService } from "./invitations.service";
import {
  StartBusinessOnboardingDto,
  CreateAdminAccountDto,
  SetupWarehouseDto,
  CompleteOnboardingDto,
  StartBusinessOnboardingResponseDto,
  CreateAdminAccountResponseDto,
  SetupWarehouseResponseDto,
  CompleteOnboardingResponseDto,
  CompleteProfileDto,
} from "./dto";

@Injectable()
export class OnboardingService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly sessionService: SessionService,
    private readonly warehousesService: WarehousesService,
    private readonly authService: AuthService,
    private readonly invitationsService: InvitationsService
  ) {}

  async startBusinessOnboarding(
    dto: StartBusinessOnboardingDto
  ): Promise<StartBusinessOnboardingResponseDto> {
    try {
      // Create onboarding session
      const session = await this.sessionService.createSession({
        step: "business_info",
        businessInfo: {
          companyName: dto.companyName,
          industry: dto.industry,
          companySize: dto.companySize,
          primaryUseCase: dto.primaryUseCase,
        },
      });

      return {
        sessionId: session.id,
        nextStep: "admin_account",
      };
    } catch (error) {
      console.error("Error starting business onboarding:", error);
      throw new InternalServerErrorException(
        "Failed to start business onboarding process"
      );
    }
  }

  async createAdminAccount(
    dto: CreateAdminAccountDto
  ): Promise<CreateAdminAccountResponseDto> {
    try {
      // Get and validate session
      const session = await this.sessionService.getSession(dto.sessionId);
      if (!session) {
        throw new NotFoundException("Onboarding session not found");
      }

      if (session.currentStep !== "business_info") {
        throw new BadRequestException(
          "Invalid step: admin account creation not allowed at this stage"
        );
      }

      // Create Supabase user
      const supabaseUser = await this.authService.createSupabaseUser(
        dto.email,
        dto.password
      );

      // Create app user
      const appUser = await this.prisma.user.create({
        data: {
          email: dto.email,
          name: dto.fullName,
          authUserId: supabaseUser.id,
          role: Role.TENANT_ADMIN,
          status: "ACTIVE",
        },
      });

      // Update session
      await this.sessionService.updateSession(dto.sessionId, {
        step: "admin_account",
        adminAccount: {
          fullName: dto.fullName,
          email: dto.email,
          password: dto.password,
          phoneNumber: dto.phoneNumber,
        },
        userId: appUser.id,
      });

      return {
        userId: appUser.id,
        supabaseUser,
        nextStep: "warehouse_setup",
      };
    } catch (error) {
      console.error("Error creating admin account:", error);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException("Failed to create admin account");
    }
  }

  async setupWarehouse(
    dto: SetupWarehouseDto
  ): Promise<SetupWarehouseResponseDto> {
    try {
      // Get and validate session
      const session = await this.sessionService.getSession(dto.sessionId);
      if (!session) {
        throw new NotFoundException("Onboarding session not found");
      }

      if (session.currentStep !== "admin_account" || !session.userId) {
        throw new BadRequestException(
          `Invalid step: expected 'admin_account', got '${session.currentStep}'. Warehouse setup not allowed at this stage.`
        );
      }

      // Check if warehouse already exists for this session
      const sessionData = session.data as any;
      if (sessionData?.warehouseId) {
        return {
          warehouseId: sessionData.warehouseId,
          tenantId: sessionData.tenantId,
          nextStep: "team_setup" as const,
        };
      }

      // Create tenant first
      const tenant = await this.prisma.tenant.create({
        data: {
          name:
            (session.data as any).businessInfo?.companyName || "New Company",
        },
      });

      // Update user with tenant
      await this.prisma.user.update({
        where: { id: session.userId },
        data: { tenantId: tenant.id },
      });

      // Create warehouse
      const currentUser = {
        id: session.userId,
        email: (session.data as any).adminAccount?.email || "",
        name: (session.data as any).adminAccount?.fullName || "",
        role: Role.TENANT_ADMIN,
        tenantId: tenant.id,
        authUserId: null, // Will be set after Supabase user creation
      };

      const warehouse = await this.warehousesService.create(
        {
          name: dto.warehouseName,
          address: dto.address,
          tenantId: tenant.id,
        },
        currentUser
      );

      // Assign user to warehouse (use upsert to handle duplicates)
      await this.prisma.warehouseUser.upsert({
        where: {
          userId_warehouseId: {
            userId: session.userId,
            warehouseId: warehouse.id,
          },
        },
        update: {
          role: Role.TENANT_ADMIN,
        },
        create: {
          userId: session.userId,
          warehouseId: warehouse.id,
          role: Role.TENANT_ADMIN,
        },
      });

      // Update session
      await this.sessionService.updateSession(dto.sessionId, {
        step: "warehouse_setup",
        warehouseSetup: {
          warehouseName: dto.warehouseName,
          address: dto.address,
          warehouseType: dto.warehouseType,
          expectedVolume: dto.expectedVolume,
        },
        tenantId: tenant.id,
        warehouseId: warehouse.id,
      });

      return {
        warehouseId: warehouse.id,
        tenantId: tenant.id,
        nextStep: "team_setup" as const,
      };
    } catch (error) {
      console.error("Error setting up warehouse:", error);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException("Failed to setup warehouse");
    }
  }

  async completeOnboarding(
    dto: CompleteOnboardingDto
  ): Promise<CompleteOnboardingResponseDto> {
    try {
      // Get and validate session
      const session = await this.sessionService.getSession(dto.sessionId);
      if (!session) {
        throw new NotFoundException("Onboarding session not found");
      }

      if (
        session.currentStep !== "warehouse_setup" ||
        !session.userId ||
        !session.tenantId
      ) {
        throw new BadRequestException(
          "Invalid step: onboarding completion not allowed at this stage"
        );
      }

      // Get user, tenant, and warehouse
      const [user, tenant, warehouse] = await Promise.all([
        this.prisma.user.findUnique({
          where: { id: session.userId },
          include: { warehouseUsers: true },
        }),
        this.prisma.tenant.findUnique({
          where: { id: session.tenantId },
        }),
        this.prisma.warehouse.findFirst({
          where: { tenantId: session.tenantId },
        }),
      ]);

      if (!user || !tenant || !warehouse) {
        throw new InternalServerErrorException(
          "Missing required data for completion"
        );
      }

      // Generate access token
      const accessToken = await this.authService.generateAccessToken(user);

      // Handle team invitations if provided
      let invitations = [];
      if (dto.teamSetup?.inviteEmails?.length > 0) {
        try {
          // Create invitations for each email
          for (const email of dto.teamSetup.inviteEmails) {
            const invitation = await this.invitationsService.createInvitation(
              user.id,
              {
                email,
                role:
                  dto.teamSetup.defaultRole === "WAREHOUSE_MANAGER"
                    ? Role.WAREHOUSE_MANAGER
                    : Role.WAREHOUSE_MEMBER,
                warehouseIds: [warehouse.id],
              }
            );
            invitations.push(invitation);
          }
        } catch (error) {
          console.error("Error creating invitations:", error);
          // Don't fail the entire onboarding if invitations fail
          // Just log the error and continue
        }
      }

      // Mark session as complete
      await this.sessionService.completeSession(dto.sessionId);

      return {
        accessToken,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          tenantId: user.tenantId,
        },
        warehouse: {
          id: warehouse.id,
          name: warehouse.name,
          address: warehouse.address,
        },
        tenant: {
          id: tenant.id,
          name: tenant.name,
        },
        invitations: invitations.length > 0 ? invitations : undefined,
      };
    } catch (error) {
      console.error("Error completing onboarding:", error);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException("Failed to complete onboarding");
    }
  }

  // Legacy method for backward compatibility
  async completeProfile(
    userId: string,
    completeProfileDto: CompleteProfileDto
  ) {
    const { companyName } = completeProfileDto;

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException("User not found.");
    }

    if (user.tenantId) {
      throw new ForbiddenException(
        "User already has a tenant. Onboarding already completed or not applicable."
      );
    }

    try {
      // Create the new tenant
      const newTenant = await this.prisma.tenant.create({
        data: {
          name: companyName,
          // createdByUserId: userId, // Optional: if you want to track who created the tenant
        },
      });

      // Update the user to link to the new tenant and set role to TENANT_ADMIN
      const updatedUser = await this.prisma.user.update({
        where: { id: userId },
        data: {
          tenantId: newTenant.id,
          role: Role.TENANT_ADMIN, // Ensure Role.TENANT_ADMIN is defined in your Prisma schema enum
          name: user.name || companyName, // Optionally update user's name if not set
        },
      });

      // Exclude password from the returned user object
      const { password, ...userWithoutPassword } = updatedUser;
      return userWithoutPassword;
    } catch (error) {
      console.error("Error during completeProfile:", error);
      // TODO: Add more specific error handling, e.g., if tenant name already exists (if it should be unique)
      throw new InternalServerErrorException(
        "Failed to complete profile and create tenant."
      );
    }
  }
}
