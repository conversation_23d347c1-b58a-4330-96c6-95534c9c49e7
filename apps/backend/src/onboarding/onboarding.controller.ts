import {
  Controller,
  Post,
  Body,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
  ValidationPipe,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { OnboardingService } from "./onboarding.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { EnhancedUserPayload } from "../auth/types";
import {
  StartBusinessOnboardingDto,
  CreateAdminAccountDto,
  SetupWarehouseDto,
  CompleteOnboardingDto,
  StartBusinessOnboardingResponseDto,
  CreateAdminAccountResponseDto,
  SetupWarehouseResponseDto,
  CompleteOnboardingResponseDto,
} from "./dto";

@ApiTags("onboarding")
@Controller("onboarding")
export class OnboardingController {
  constructor(private readonly onboardingService: OnboardingService) {}

  @Post("business/start")
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: "Start business onboarding process" })
  @ApiResponse({
    status: 201,
    description: "Onboarding session created successfully",
    type: StartBusinessOnboardingResponseDto,
  })
  @ApiResponse({ status: 400, description: "Invalid business information" })
  async startBusinessOnboarding(
    @Body(ValidationPipe) dto: StartBusinessOnboardingDto
  ): Promise<StartBusinessOnboardingResponseDto> {
    return this.onboardingService.startBusinessOnboarding(dto);
  }

  @Post("business/admin-account")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Create admin account during onboarding" })
  @ApiResponse({
    status: 200,
    description: "Admin account created successfully",
    type: CreateAdminAccountResponseDto,
  })
  @ApiResponse({ status: 400, description: "Invalid account information" })
  @ApiResponse({ status: 404, description: "Onboarding session not found" })
  async createAdminAccount(
    @Body(ValidationPipe) dto: CreateAdminAccountDto
  ): Promise<CreateAdminAccountResponseDto> {
    return this.onboardingService.createAdminAccount(dto);
  }

  @Post("business/warehouse")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Setup warehouse during onboarding" })
  @ApiResponse({
    status: 200,
    description: "Warehouse setup completed successfully",
    type: SetupWarehouseResponseDto,
  })
  @ApiResponse({ status: 400, description: "Invalid warehouse information" })
  @ApiResponse({ status: 404, description: "Onboarding session not found" })
  async setupWarehouse(
    @Body(ValidationPipe) dto: SetupWarehouseDto
  ): Promise<SetupWarehouseResponseDto> {
    return this.onboardingService.setupWarehouse(dto);
  }

  @Post("business/complete")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Complete business onboarding process" })
  @ApiResponse({
    status: 200,
    description: "Onboarding completed successfully",
    type: CompleteOnboardingResponseDto,
  })
  @ApiResponse({ status: 400, description: "Invalid completion data" })
  @ApiResponse({ status: 404, description: "Onboarding session not found" })
  async completeOnboarding(
    @Body(ValidationPipe) dto: CompleteOnboardingDto
  ): Promise<CompleteOnboardingResponseDto> {
    return this.onboardingService.completeOnboarding(dto);
  }

  // Legacy endpoint for backward compatibility
  @UseGuards(JwtAuthGuard)
  @Post("complete-profile")
  @ApiBearerAuth()
  @ApiOperation({ summary: "Complete user profile (legacy)" })
  @ApiResponse({ status: 200, description: "Profile completed successfully" })
  async completeProfile(
    @Req() req: { user: EnhancedUserPayload },
    @Body() completeProfileDto: any // Keep existing DTO for now
  ) {
    const userId = req.user.id;
    return this.onboardingService.completeProfile(userId, completeProfileDto);
  }
}
