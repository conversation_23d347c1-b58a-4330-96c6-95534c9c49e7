import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
  ForbiddenException,
  InternalServerErrorException,
} from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { AuthService } from "../auth/auth.service";
import { Role } from "@prisma/client";
import { INVITATION_DURATION } from "@quildora/types";
import {
  CreateInvitationDto,
  AcceptInvitationDto,
  CreateInvitationResponseDto,
  ValidateInvitationResponseDto,
  AcceptInvitationResponseDto,
} from "./dto/invitations.dto";

@Injectable()
export class InvitationsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly authService: AuthService
  ) {}

  async createInvitation(
    invitedByUserId: string,
    dto: CreateInvitationDto
  ): Promise<CreateInvitationResponseDto> {
    try {
      // Get the inviting user and validate permissions
      const invitingUser = await this.prisma.user.findUnique({
        where: { id: invitedByUserId },
        include: { warehouseUsers: true },
      });

      if (!invitingUser || !invitingUser.tenantId) {
        throw new ForbiddenException("User not authorized to send invitations");
      }

      // Check if user has permission to invite to the specified warehouses
      const userWarehouseIds = invitingUser.warehouseUsers.map(
        (wu) => wu.warehouseId
      );
      const hasAccessToAllWarehouses = dto.warehouseIds.every((id) =>
        userWarehouseIds.includes(id)
      );

      if (
        !hasAccessToAllWarehouses &&
        invitingUser.role !== Role.TENANT_ADMIN
      ) {
        throw new ForbiddenException(
          "Insufficient permissions for specified warehouses"
        );
      }

      // Check if email is already invited or exists
      const [existingUser, existingInvitation] = await Promise.all([
        this.prisma.user.findUnique({ where: { email: dto.email } }),
        this.prisma.tenantInvitation.findFirst({
          where: {
            email: dto.email,
            tenantId: invitingUser.tenantId,
            acceptedAt: null,
            expiresAt: { gt: new Date() },
          },
        }),
      ]);

      if (existingUser) {
        throw new ConflictException("User with this email already exists");
      }

      if (existingInvitation) {
        throw new ConflictException(
          "Active invitation already exists for this email"
        );
      }

      // Generate unique invitation code
      const invitationCode = this.generateInvitationCode();
      const expiresAt = new Date(Date.now() + INVITATION_DURATION);

      // Create invitation
      const invitation = await this.prisma.tenantInvitation.create({
        data: {
          tenantId: invitingUser.tenantId,
          invitedByUserId,
          email: dto.email,
          role: dto.role,
          warehouseIds: dto.warehouseIds,
          invitationCode,
          expiresAt,
        },
      });

      return {
        invitationId: invitation.id,
        invitationCode: invitation.invitationCode,
        expiresAt: invitation.expiresAt.toISOString(),
      };
    } catch (error) {
      console.error("Error creating invitation:", error);
      if (
        error instanceof ForbiddenException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException("Failed to create invitation");
    }
  }

  async validateInvitation(
    code: string
  ): Promise<ValidateInvitationResponseDto> {
    try {
      const invitation = await this.prisma.tenantInvitation.findUnique({
        where: { invitationCode: code },
        include: {
          tenant: true,
          invitedBy: true,
        },
      });

      if (!invitation) {
        return { valid: false };
      }

      // Check if invitation is expired
      if (invitation.expiresAt < new Date()) {
        return { valid: false };
      }

      // Check if already accepted
      if (invitation.acceptedAt) {
        return { valid: false };
      }

      // Get warehouse information (assuming first warehouse for now)
      let warehouse = null;
      if (invitation.warehouseIds && invitation.warehouseIds.length > 0) {
        const warehouseData = await this.prisma.warehouse.findFirst({
          where: { id: { in: invitation.warehouseIds } },
        });
        if (warehouseData) {
          warehouse = {
            id: warehouseData.id,
            name: warehouseData.name,
            address: warehouseData.address,
          };
        }
      }

      return {
        valid: true,
        id: invitation.id,
        email: invitation.email,
        role: invitation.role,
        status: this.getInvitationStatus(invitation),
        expiresAt: invitation.expiresAt.toISOString(),
        tenant: {
          id: invitation.tenant.id,
          name: invitation.tenant.name,
        },
        warehouse,
        invitedBy: {
          name: invitation.invitedBy.name,
          email: invitation.invitedBy.email,
        },
      };
    } catch (error) {
      console.error("Error validating invitation:", error);
      throw new InternalServerErrorException("Failed to validate invitation");
    }
  }

  async acceptInvitation(
    code: string,
    dto: AcceptInvitationDto
  ): Promise<AcceptInvitationResponseDto> {
    try {
      // Validate invitation
      const invitation = await this.prisma.tenantInvitation.findUnique({
        where: { invitationCode: code },
        include: { tenant: true },
      });

      if (!invitation) {
        throw new NotFoundException("Invitation not found");
      }

      if (invitation.expiresAt < new Date()) {
        throw new BadRequestException("Invitation has expired");
      }

      if (invitation.acceptedAt) {
        throw new BadRequestException("Invitation has already been accepted");
      }

      // Check if user already exists
      const existingUser = await this.prisma.user.findUnique({
        where: { email: invitation.email },
      });

      if (existingUser) {
        throw new ConflictException("User with this email already exists");
      }

      // Create Supabase user
      const supabaseUser = await this.authService.createSupabaseUser(
        invitation.email,
        dto.password
      );

      // Create app user
      const user = await this.prisma.user.create({
        data: {
          email: invitation.email,
          name: dto.fullName,
          authUserId: supabaseUser.id,
          role: invitation.role,
          tenantId: invitation.tenantId,
          status: "ACTIVE",
        },
      });

      // Create warehouse assignments
      const warehouseAssignments = invitation.warehouseIds.map(
        (warehouseId) => ({
          userId: user.id,
          warehouseId,
          role: invitation.role,
        })
      );

      await this.prisma.warehouseUser.createMany({
        data: warehouseAssignments,
      });

      // Mark invitation as accepted
      await this.prisma.tenantInvitation.update({
        where: { id: invitation.id },
        data: {
          acceptedAt: new Date(),
          acceptedByUserId: user.id,
        },
      });

      // Get warehouses for response
      const warehouses = await this.prisma.warehouse.findMany({
        where: { id: { in: invitation.warehouseIds } },
        select: { id: true, name: true, address: true },
      });

      // Generate access token
      const accessToken = await this.authService.generateAccessToken(user);

      return {
        accessToken,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          tenantId: user.tenantId,
        },
        warehouses,
        tenant: {
          id: invitation.tenant.id,
          name: invitation.tenant.name,
        },
      };
    } catch (error) {
      console.error("Error accepting invitation:", error);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new InternalServerErrorException("Failed to accept invitation");
    }
  }

  async listInvitations(tenantId: string) {
    try {
      const invitations = await this.prisma.tenantInvitation.findMany({
        where: { tenantId },
        include: {
          invitedBy: { select: { id: true, name: true, email: true } },
          acceptedByUser: { select: { id: true, name: true, email: true } },
        },
        orderBy: { createdAt: "desc" },
      });

      return invitations.map((invitation) => ({
        id: invitation.id,
        email: invitation.email,
        role: invitation.role,
        warehouseIds: invitation.warehouseIds,
        invitationCode: invitation.invitationCode,
        expiresAt: invitation.expiresAt,
        acceptedAt: invitation.acceptedAt,
        createdAt: invitation.createdAt,
        invitedBy: invitation.invitedBy,
        acceptedByUser: invitation.acceptedByUser,
        status: this.getInvitationStatus(invitation),
      }));
    } catch (error) {
      console.error("Error listing invitations:", error);
      throw new InternalServerErrorException("Failed to list invitations");
    }
  }

  private generateInvitationCode(): string {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let result = "INV-";
    for (let i = 0; i < 12; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  private getInvitationStatus(invitation: any): string {
    if (invitation.acceptedAt) {
      return "accepted";
    }
    if (invitation.expiresAt < new Date()) {
      return "expired";
    }
    return "pending";
  }
}
