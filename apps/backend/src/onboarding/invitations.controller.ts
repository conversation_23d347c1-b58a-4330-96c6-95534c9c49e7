import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { InvitationsService } from './invitations.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { EnhancedUserPayload } from '../auth/types';
import {
  CreateInvitationDto,
  AcceptInvitationDto,
  CreateInvitationResponseDto,
  ValidateInvitationResponseDto,
  AcceptInvitationResponseDto,
} from './dto/invitations.dto';

@ApiTags('invitations')
@Controller('invitations')
export class InvitationsController {
  constructor(private readonly invitationsService: InvitationsService) {}

  @UseGuards(JwtAuthGuard)
  @Post('create')
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create invitation for new team member' })
  @ApiResponse({
    status: 201,
    description: 'Invitation created successfully',
    type: CreateInvitationResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid invitation data' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async createInvitation(
    @Req() req: { user: EnhancedUserPayload },
    @Body(ValidationPipe) dto: CreateInvitationDto,
  ): Promise<CreateInvitationResponseDto> {
    return this.invitationsService.createInvitation(req.user.id, dto);
  }

  @Get('validate/:code')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Validate invitation code' })
  @ApiResponse({
    status: 200,
    description: 'Invitation validation result',
    type: ValidateInvitationResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Invitation not found or expired' })
  async validateInvitation(
    @Param('code') code: string,
  ): Promise<ValidateInvitationResponseDto> {
    return this.invitationsService.validateInvitation(code);
  }

  @Post('accept/:code')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Accept invitation and create account' })
  @ApiResponse({
    status: 200,
    description: 'Invitation accepted successfully',
    type: AcceptInvitationResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid account data' })
  @ApiResponse({ status: 404, description: 'Invitation not found or expired' })
  @ApiResponse({ status: 409, description: 'Email already exists' })
  async acceptInvitation(
    @Param('code') code: string,
    @Body(ValidationPipe) dto: AcceptInvitationDto,
  ): Promise<AcceptInvitationResponseDto> {
    return this.invitationsService.acceptInvitation(code, dto);
  }

  @UseGuards(JwtAuthGuard)
  @Get('list')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'List invitations for current tenant' })
  @ApiResponse({
    status: 200,
    description: 'List of invitations',
    type: [Object], // TODO: Create proper response DTO
  })
  async listInvitations(@Req() req: { user: EnhancedUserPayload }) {
    return this.invitationsService.listInvitations(req.user.tenantId);
  }
}
