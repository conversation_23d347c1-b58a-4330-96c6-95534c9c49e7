import { Controller, Post, Body, HttpCode, HttpStatus } from "@nestjs/common";
import { AuthService } from "./auth.service";
import { LoginDto } from "./dto/login.dto";
import { CreateTenantWithAdminDto } from './dto/create-tenant-with-admin.dto'; // Import the new DTO

@Controller("auth")
export class AuthController {
  constructor(private authService: AuthService) {}

  @HttpCode(HttpStatus.OK) // Return 200 OK on successful login
  @Post("login")
  signIn(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }

  @HttpCode(HttpStatus.CREATED) // Return 201 Created on successful sign-up
  @Post("signup")
  async tenantSignUp(@Body() createTenantDto: CreateTenantWithAdminDto) {
    // We will define this service method in the next step
    return this.authService.createTenantWithAdmin(createTenantDto);
  }

  // Add other endpoints later if needed (e.g., refresh token, register?)
}
