import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  BadRequestException,
  Logger,
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { Role } from "@prisma/client";
import {
  WAREHOUSE_PERMISSION_KEY,
  WarehousePermissionOptions,
} from "../decorators/warehouse-permission.decorator";
import { EnhancedUserPayload } from "../types";
import { WarehouseValidationUtils } from "../../warehouses/utils/warehouse-validation.utils";

@Injectable()
export class WarehousePermissionGuard implements CanActivate {
  private readonly logger = new Logger(WarehousePermissionGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly warehouseValidationUtils: WarehouseValidationUtils
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Get warehouse permission options from decorator metadata
    const permissionOptions =
      this.reflector.getAllAndOverride<WarehousePermissionOptions>(
        WAREHOUSE_PERMISSION_KEY,
        [context.getHandler(), context.getClass()]
      );

    // If no warehouse permission requirements specified, allow access
    if (!permissionOptions) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user: EnhancedUserPayload = request.user;

    if (!user) {
      throw new ForbiddenException("User not authenticated");
    }

    this.logger.debug(
      `Validating warehouse permissions for user ${user.email} on ${request.method} ${request.path}`
    );

    try {
      // Skip validation for tenant admins if configured
      if (
        permissionOptions.skipForTenantAdmin &&
        user.role === Role.TENANT_ADMIN
      ) {
        this.logger.debug(
          `Skipping warehouse validation for tenant admin: ${user.email}`
        );
        this.setWarehouseContext(request, undefined, user.role, true);
        return true;
      }

      // Extract warehouse ID from various sources
      const warehouseId = await this.extractWarehouseId(
        request,
        permissionOptions
      );

      // Check if warehouse ID is required
      if (permissionOptions.requireWarehouseId && !warehouseId) {
        const errorMessage =
          permissionOptions.errorMessage ||
          "Warehouse ID is required for this operation";
        throw new BadRequestException(errorMessage);
      }

      // Validate warehouse access if warehouse ID is provided
      if (warehouseId) {
        await this.validateWarehouseAccess(
          warehouseId,
          user,
          permissionOptions
        );

        // Get user's role in this warehouse
        const userRole = user.getWarehouseRole?.(warehouseId) || user.role;

        this.setWarehouseContext(request, warehouseId, userRole, true);

        this.logger.debug(
          `Warehouse access validated for user ${user.email} in warehouse ${warehouseId} with role ${userRole}`
        );
      } else {
        // No specific warehouse, but user is authenticated
        this.setWarehouseContext(request, undefined, user.role, true);
      }

      return true;
    } catch (error) {
      this.logger.error(
        `Warehouse permission validation failed for ${request.method} ${request.path}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Extract warehouse ID from request parameters, query, headers, or entity relationships
   */
  private async extractWarehouseId(
    request: any,
    options: WarehousePermissionOptions
  ): Promise<string | undefined> {
    // Try to get warehouse ID from various sources in order of priority

    // 1. URL parameters
    let warehouseId = request.params.warehouseId || request.params.warehouse_id;

    // 2. Query parameters
    if (!warehouseId) {
      warehouseId = request.query.warehouseId || request.query.warehouse_id;
    }

    // 3. Request headers
    if (!warehouseId) {
      warehouseId = request.headers["x-warehouse-id"];
    }

    // 4. Request body
    if (!warehouseId && request.body?.warehouseId) {
      warehouseId = request.body.warehouseId;
    }

    // 5. Extract from entity relationships if configured
    if (!warehouseId && options.extractFromEntity) {
      warehouseId = await this.extractWarehouseFromEntity(
        request,
        options.extractFromEntity
      );
    }

    return warehouseId;
  }

  /**
   * Extract warehouse ID from entity relationships
   */
  private async extractWarehouseFromEntity(
    request: any,
    entityConfig: NonNullable<WarehousePermissionOptions["extractFromEntity"]>
  ): Promise<string | undefined> {
    const entityId = request.params[entityConfig.paramName];

    if (!entityId) {
      return undefined;
    }

    try {
      // Handle all supported entity types
      if (
        entityConfig.entityType === "pallet" ||
        entityConfig.entityType === "location" ||
        entityConfig.entityType === "purchaseOrder" ||
        entityConfig.entityType === "shipment"
      ) {
        const warehouseId =
          await this.warehouseValidationUtils.validateEntityWarehouseAccess(
            entityId,
            entityConfig.entityType,
            request.user
          );
        return warehouseId;
      }

      // For other entity types, we'd need to implement additional logic
      this.logger.warn(
        `Entity type ${entityConfig.entityType} not yet supported for warehouse extraction`
      );
      return undefined;
    } catch (error) {
      this.logger.warn(
        `Failed to extract warehouse from ${entityConfig.entityType} ${entityId}: ${error.message}`
      );
      throw error; // Re-throw to maintain security - if we can't validate, we should deny
    }
  }

  /**
   * Validate user's access to a specific warehouse
   */
  private async validateWarehouseAccess(
    warehouseId: string,
    user: EnhancedUserPayload,
    options: WarehousePermissionOptions
  ): Promise<void> {
    // Use enhanced user payload utility methods if available
    if (user.canAccessWarehouse && !user.canAccessWarehouse(warehouseId)) {
      const errorMessage =
        options.errorMessage ||
        `Access denied: You do not have permission to access warehouse ${warehouseId}`;
      throw new ForbiddenException(errorMessage);
    }

    // Validate minimum role if specified
    if (options.minimumRole && user.hasWarehousePermission) {
      if (!user.hasWarehousePermission(warehouseId, options.minimumRole)) {
        const errorMessage =
          options.errorMessage ||
          `Access denied: This operation requires ${options.minimumRole} role or higher in warehouse ${warehouseId}`;
        throw new ForbiddenException(errorMessage);
      }
    }

    // Fallback to warehouse validation utils for additional checks
    await this.warehouseValidationUtils.validateUserWarehouseAccess(
      warehouseId,
      user
    );

    if (options.minimumRole) {
      await this.warehouseValidationUtils.validateUserWarehouseRole(
        warehouseId,
        user,
        options.minimumRole
      );
    }
  }

  /**
   * Set warehouse context on the request for downstream handlers
   */
  private setWarehouseContext(
    request: any,
    warehouseId: string | undefined,
    userRole: Role,
    hasAccess: boolean
  ): void {
    request.warehouseContext = {
      warehouseId: warehouseId || "",
      userRole,
      hasAccess,
      isManager:
        userRole === Role.WAREHOUSE_MANAGER || userRole === Role.TENANT_ADMIN,
      isAdmin: userRole === Role.TENANT_ADMIN,
    };
  }
}

/**
 * Helper function to get warehouse context from request
 * Use this in controllers to access warehouse context set by the guard
 */
export function getWarehouseContext(request: any):
  | {
      warehouseId: string;
      userRole: Role;
      hasAccess: boolean;
      isManager: boolean;
      isAdmin: boolean;
    }
  | undefined {
  return request.warehouseContext;
}

/**
 * Helper function to get warehouse ID from request context
 */
export function getWarehouseId(request: any): string | undefined {
  return request.warehouseContext?.warehouseId;
}

/**
 * Helper function to check if user is warehouse manager in current context
 */
export function isWarehouseManager(request: any): boolean {
  return request.warehouseContext?.isManager || false;
}

/**
 * Helper function to check if user is tenant admin
 */
export function isTenantAdmin(request: any): boolean {
  return request.warehouseContext?.isAdmin || false;
}
