import { Test, TestingModule } from "@nestjs/testing";
import {
  ExecutionContext,
  ForbiddenException,
  BadRequestException,
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { Role } from "@prisma/client";
import { WarehousePermissionGuard } from "./warehouse-permission.guard";
import { WarehouseValidationUtils } from "../../warehouses/utils/warehouse-validation.utils";
import { WAREHOUSE_PERMISSION_KEY } from "../decorators/warehouse-permission.decorator";
import { EnhancedUserPayload, WarehouseContext } from "../types";

interface MockRequest {
  user?: EnhancedUserPayload;
  params?: Record<string, string>;
  query?: Record<string, string>;
  headers?: Record<string, string>;
  body?: Record<string, any>;
  warehouseContext?: WarehouseContext;
}

describe("WarehousePermissionGuard", () => {
  let guard: WarehousePermissionGuard;
  let reflector: Reflector;
  let warehouseValidationUtils: jest.Mocked<WarehouseValidationUtils>;

  const mockExecutionContext = (request: any) =>
    ({
      switchToHttp: () => ({
        getRequest: () => request,
        getResponse: jest.fn(),
        getNext: jest.fn(),
      }),
      getHandler: jest.fn(),
      getClass: jest.fn(),
      getArgs: jest.fn(),
      getArgByIndex: jest.fn(),
      switchToRpc: jest.fn(),
      switchToWs: jest.fn(),
      getType: jest.fn(),
    } as ExecutionContext);

  const createMockUser = (
    overrides: Partial<EnhancedUserPayload> = {}
  ): EnhancedUserPayload => ({
    id: "user-1",
    email: "<EMAIL>",
    name: "Test User",
    password: "password",
    authUserId: "auth-1",
    role: Role.WAREHOUSE_MEMBER,
    status: "ACTIVE",
    createdAt: new Date(),
    updatedAt: new Date(),
    tenantId: "tenant-1",
    warehouseUsers: [],
    accessibleWarehouses: ["warehouse-1", "warehouse-2"],
    warehouseRoles: new Map([
      ["warehouse-1", Role.WAREHOUSE_MANAGER],
      ["warehouse-2", Role.WAREHOUSE_MEMBER],
    ]),
    canAccessWarehouse: jest.fn((warehouseId: string) =>
      ["warehouse-1", "warehouse-2"].includes(warehouseId)
    ),
    getWarehouseRole: jest.fn((warehouseId: string) =>
      warehouseId === "warehouse-1"
        ? Role.WAREHOUSE_MANAGER
        : Role.WAREHOUSE_MEMBER
    ),
    hasWarehousePermission: jest.fn(
      (warehouseId: string, requiredRole: Role) => {
        const userRole =
          warehouseId === "warehouse-1"
            ? Role.WAREHOUSE_MANAGER
            : Role.WAREHOUSE_MEMBER;
        const roleHierarchy = {
          [Role.WAREHOUSE_MEMBER]: 1,
          [Role.WAREHOUSE_MANAGER]: 2,
          [Role.TENANT_ADMIN]: 3,
        };
        return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
      }
    ),
    isTenantAdmin: jest.fn().mockReturnValue(false),
    ...overrides,
  });

  beforeEach(async () => {
    const mockWarehouseValidationUtils = {
      validateUserWarehouseAccess: jest.fn(),
      validateUserWarehouseRole: jest.fn(),
      validateEntityWarehouseAccess: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WarehousePermissionGuard,
        {
          provide: Reflector,
          useValue: {
            getAllAndOverride: jest.fn(),
          },
        },
        {
          provide: WarehouseValidationUtils,
          useValue: mockWarehouseValidationUtils,
        },
      ],
    }).compile();

    guard = module.get<WarehousePermissionGuard>(WarehousePermissionGuard);
    reflector = module.get<Reflector>(Reflector);
    warehouseValidationUtils = module.get(WarehouseValidationUtils);
  });

  describe("canActivate", () => {
    it("should allow access when no warehouse permission requirements specified", async () => {
      const request: MockRequest = { user: createMockUser() };
      const context = mockExecutionContext(request);

      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(undefined);

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
    });

    it("should deny access when user is not authenticated", async () => {
      const request: MockRequest = { user: null };
      const context = mockExecutionContext(request);

      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
        requireWarehouseId: true,
      });

      await expect(guard.canActivate(context)).rejects.toThrow(
        ForbiddenException
      );
    });

    it("should skip validation for tenant admin when configured", async () => {
      const user = createMockUser({ role: Role.TENANT_ADMIN });
      const request: MockRequest = { user };
      const context = mockExecutionContext(request);

      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
        requireWarehouseId: true,
        skipForTenantAdmin: true,
      });

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
      expect(request.warehouseContext).toBeDefined();
      expect(request.warehouseContext.isAdmin).toBe(true);
    });

    it("should require warehouse ID when configured", async () => {
      const request: MockRequest = {
        user: createMockUser(),
        params: {},
        query: {},
        headers: {},
        body: {},
      };
      const context = mockExecutionContext(request);

      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
        requireWarehouseId: true,
      });

      await expect(guard.canActivate(context)).rejects.toThrow(
        BadRequestException
      );
    });

    it("should extract warehouse ID from URL parameters", async () => {
      const user = createMockUser();
      const request: MockRequest = {
        user,
        params: { warehouseId: "warehouse-1" },
        query: {},
        headers: {},
        body: {},
      };
      const context = mockExecutionContext(request);

      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
        requireWarehouseId: true,
      });

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
      expect(request.warehouseContext).toBeDefined();
      expect(request.warehouseContext.warehouseId).toBe("warehouse-1");
      expect(request.warehouseContext.userRole).toBe(Role.WAREHOUSE_MANAGER);
    });

    it("should extract warehouse ID from query parameters", async () => {
      const user = createMockUser();
      const request: MockRequest = {
        user,
        params: {},
        query: { warehouseId: "warehouse-2" },
        headers: {},
        body: {},
      };
      const context = mockExecutionContext(request);

      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
        requireWarehouseId: true,
      });

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
      expect(request.warehouseContext.warehouseId).toBe("warehouse-2");
      expect(request.warehouseContext.userRole).toBe(Role.WAREHOUSE_MEMBER);
    });

    it("should extract warehouse ID from headers", async () => {
      const user = createMockUser();
      const request: MockRequest = {
        user,
        params: {},
        query: {},
        headers: { "x-warehouse-id": "warehouse-1" },
        body: {},
      };
      const context = mockExecutionContext(request);

      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
        requireWarehouseId: true,
      });

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
      expect(request.warehouseContext.warehouseId).toBe("warehouse-1");
    });

    it("should extract warehouse ID from entity relationships", async () => {
      const user = createMockUser();
      const request: MockRequest = {
        user,
        params: { palletId: "pallet-1" },
        query: {},
        headers: {},
        body: {},
      };
      const context = mockExecutionContext(request);

      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
        extractFromEntity: {
          entityType: "pallet",
          paramName: "palletId",
        },
      });

      warehouseValidationUtils.validateEntityWarehouseAccess.mockResolvedValue(
        "warehouse-1"
      );

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
      expect(
        warehouseValidationUtils.validateEntityWarehouseAccess
      ).toHaveBeenCalledWith("pallet-1", "pallet", user);
      expect(request.warehouseContext.warehouseId).toBe("warehouse-1");
    });

    it("should validate minimum role requirements", async () => {
      const user = createMockUser();
      const request: MockRequest = {
        user,
        params: { warehouseId: "warehouse-2" }, // User is WAREHOUSE_MEMBER in warehouse-2
        query: {},
        headers: {},
        body: {},
      };
      const context = mockExecutionContext(request);

      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
        requireWarehouseId: true,
        minimumRole: Role.WAREHOUSE_MANAGER, // Requires WAREHOUSE_MANAGER
      });

      await expect(guard.canActivate(context)).rejects.toThrow(
        ForbiddenException
      );
    });

    it("should allow access when user has sufficient role", async () => {
      const user = createMockUser();
      const request: MockRequest = {
        user,
        params: { warehouseId: "warehouse-1" }, // User is WAREHOUSE_MANAGER in warehouse-1
        query: {},
        headers: {},
        body: {},
      };
      const context = mockExecutionContext(request);

      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
        requireWarehouseId: true,
        minimumRole: Role.WAREHOUSE_MANAGER,
      });

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
      expect(request.warehouseContext.isManager).toBe(true);
    });

    it("should deny access to inaccessible warehouse", async () => {
      const user = createMockUser();
      const request: MockRequest = {
        user,
        params: { warehouseId: "warehouse-3" }, // User doesn't have access to warehouse-3
        query: {},
        headers: {},
        body: {},
      };
      const context = mockExecutionContext(request);

      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
        requireWarehouseId: true,
      });

      await expect(guard.canActivate(context)).rejects.toThrow(
        ForbiddenException
      );
    });

    it("should set warehouse context correctly", async () => {
      const user = createMockUser();
      const request: MockRequest = {
        user,
        params: { warehouseId: "warehouse-1" },
        query: {},
        headers: {},
        body: {},
      };
      const context = mockExecutionContext(request);

      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
        requireWarehouseId: true,
      });

      await guard.canActivate(context);

      expect(request.warehouseContext).toEqual({
        warehouseId: "warehouse-1",
        userRole: Role.WAREHOUSE_MANAGER,
        hasAccess: true,
        isManager: true,
        isAdmin: false,
      });
    });
  });
});
