import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { Role } from "@prisma/client";
import { EnhancedUserPayload } from "../types";

/**
 * Role-based authorization guard for warehouse-scoped access control.
 * This guard validates that users have the required role within the appropriate warehouse context.
 */
@Injectable()
export class RoleBasedGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<Role[]>("roles", [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) {
      return true; // No role requirement specified
    }

    const request = context.switchToHttp().getRequest();
    const user: EnhancedUserPayload = request.user;

    if (!user) {
      throw new ForbiddenException("User not authenticated");
    }

    // Check if user has any of the required roles
    const hasRequiredRole = requiredRoles.some((role) => {
      switch (role) {
        case Role.TENANT_ADMIN:
          return user.isTenantAdmin();
        case Role.WAREHOUSE_MANAGER:
          // Check if user is warehouse manager in any warehouse
          return user.warehouseUsers.some(
            (wu) => wu.role === Role.WAREHOUSE_MANAGER
          );
        case Role.WAREHOUSE_MEMBER:
          // Check if user has any warehouse access
          return user.warehouseUsers.length > 0;
        default:
          return false;
      }
    });

    if (!hasRequiredRole) {
      throw new ForbiddenException(
        `Access denied. Required roles: ${requiredRoles.join(", ")}`
      );
    }

    return true;
  }
}

/**
 * Tenant Admin Guard - Ensures user has TENANT_ADMIN role
 */
@Injectable()
export class TenantAdminGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user: EnhancedUserPayload = request.user;

    if (!user) {
      throw new ForbiddenException("User not authenticated");
    }

    if (!user.isTenantAdmin()) {
      throw new ForbiddenException(
        "Access denied. Tenant admin role required."
      );
    }

    return true;
  }
}

/**
 * Warehouse Manager Guard - Ensures user has WAREHOUSE_MANAGER role in at least one warehouse
 */
@Injectable()
export class WarehouseManagerGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user: EnhancedUserPayload = request.user;

    if (!user) {
      throw new ForbiddenException("User not authenticated");
    }

    // Tenant admins have implicit warehouse manager access
    if (user.isTenantAdmin()) {
      return true;
    }

    const hasManagerRole = user.warehouseUsers.some(
      (wu) => wu.role === Role.WAREHOUSE_MANAGER
    );
    if (!hasManagerRole) {
      throw new ForbiddenException(
        "Access denied. Warehouse manager role required."
      );
    }

    return true;
  }
}

/**
 * Warehouse Member Guard - Ensures user has access to at least one warehouse
 */
@Injectable()
export class WarehouseMemberGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user: EnhancedUserPayload = request.user;

    if (!user) {
      throw new ForbiddenException("User not authenticated");
    }

    // Tenant admins have implicit warehouse access
    if (user.isTenantAdmin()) {
      return true;
    }

    if (user.warehouseUsers.length === 0) {
      throw new ForbiddenException("Access denied. Warehouse access required.");
    }

    return true;
  }
}

/**
 * Warehouse-Specific Role Guard - Validates role within a specific warehouse context
 */
@Injectable()
export class WarehouseSpecificRoleGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRole = this.reflector.getAllAndOverride<Role>(
      "warehouseRole",
      [context.getHandler(), context.getClass()]
    );

    if (!requiredRole) {
      return true; // No specific warehouse role requirement
    }

    const request = context.switchToHttp().getRequest();
    const user: EnhancedUserPayload = request.user;
    const warehouseId = request.warehouseContext?.warehouseId;

    if (!user) {
      throw new ForbiddenException("User not authenticated");
    }

    if (!warehouseId) {
      throw new ForbiddenException(
        "Warehouse context required for role validation"
      );
    }

    // Tenant admins have access to all warehouses
    if (user.isTenantAdmin()) {
      return true;
    }

    const userRole = user.getWarehouseRole(warehouseId);
    if (!userRole) {
      throw new ForbiddenException(
        `Access denied. No access to warehouse ${warehouseId}`
      );
    }

    const hasPermission = user.hasWarehousePermission(
      warehouseId,
      requiredRole
    );
    if (!hasPermission) {
      throw new ForbiddenException(
        `Access denied. Required role: ${requiredRole} in warehouse ${warehouseId}`
      );
    }

    return true;
  }
}
