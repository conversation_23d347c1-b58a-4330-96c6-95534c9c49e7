import { Injectable, CanActivate, ExecutionContext } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { Role } from "../entities/role.enum";
import { ROLES_KEY } from "../decorators/roles.decorator";
import { User } from "@prisma/client"; // Import User type from Prisma Client

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<Role[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (!requiredRoles || requiredRoles.length === 0) {
      // If no roles are required, allow access (or deny if default should be deny)
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user: User = request.user; // User object attached by JwtAuthGuard (via JwtStrategy)

    if (!user || !user.role) {
      // No user attached or user has no role
      return false;
    }

    // Check if the user's role is included in the required roles
    return requiredRoles.some((role) => user.role === role);
  }
}
