import { Test, TestingModule } from "@nestjs/testing";
import { Reflector } from "@nestjs/core";
import { ExecutionContext, ForbiddenException } from "@nestjs/common";
import { Role } from "@prisma/client";
import {
  WarehouseAuthGuard,
  WarehouseAccessOptions,
} from "./warehouse-auth.guard";
import { WAREHOUSE_ACCESS_KEY } from "../decorators/warehouse-access.decorator";
import { AuthenticatedUser } from "../types/authenticated-user.interface";

describe("WarehouseAuthGuard", () => {
  let guard: WarehouseAuthGuard;
  let reflector: Reflector;

  const mockUser: AuthenticatedUser = {
    id: "user-1",
    email: "<EMAIL>",
    role: Role.WAREHOUSE_MEMBER,
    tenantId: "tenant-1",
    name: "Test User",
    authUserId: "auth-1",
    warehouseUsers: [
      { warehouseId: "warehouse-1", role: Role.WAREHOUSE_MEMBER },
      { warehouseId: "warehouse-2", role: Role.WAREHOUSE_MEMBER },
    ],
  };

  const mockTenantAdmin: AuthenticatedUser = {
    ...mockUser,
    role: Role.TENANT_ADMIN,
  };

  const createMockExecutionContext = (
    user: AuthenticatedUser | null,
    params: any = {},
    query: any = {},
    headers: any = {},
    body: any = {}
  ): ExecutionContext => {
    return {
      switchToHttp: () => ({
        getRequest: () => ({
          user,
          params,
          query,
          headers,
          body,
        }),
      }),
      getHandler: jest.fn(),
      getClass: jest.fn(),
    } as any;
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WarehouseAuthGuard,
        {
          provide: Reflector,
          useValue: {
            getAllAndOverride: jest.fn(),
          },
        },
      ],
    }).compile();

    guard = module.get<WarehouseAuthGuard>(WarehouseAuthGuard);
    reflector = module.get<Reflector>(Reflector);
  });

  describe("canActivate", () => {
    it("should allow access when no warehouse access options are defined", () => {
      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(undefined);
      const context = createMockExecutionContext(mockUser);

      expect(guard.canActivate(context)).toBe(true);
    });

    it("should throw ForbiddenException when user is not authenticated", () => {
      const options: WarehouseAccessOptions = { requireWarehouseId: true };
      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
      const context = createMockExecutionContext(null);

      expect(() => guard.canActivate(context)).toThrow(ForbiddenException);
    });

    it("should allow TENANT_ADMIN access to any warehouse", () => {
      const options: WarehouseAccessOptions = { requireWarehouseId: true };
      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
      const context = createMockExecutionContext(mockTenantAdmin, {
        warehouseId: "any-warehouse",
      });

      expect(guard.canActivate(context)).toBe(true);
    });

    it("should check role-based access when allowedRoles is specified", () => {
      const options: WarehouseAccessOptions = {
        allowedRoles: [Role.WAREHOUSE_MANAGER],
        requireWarehouseId: true,
      };
      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
      const context = createMockExecutionContext(mockUser);

      expect(() => guard.canActivate(context)).toThrow(ForbiddenException);
    });

    it("should allow access when user has required role", () => {
      const options: WarehouseAccessOptions = {
        allowedRoles: [Role.WAREHOUSE_MEMBER],
        requireWarehouseId: false,
      };
      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
      const context = createMockExecutionContext(mockUser);

      expect(guard.canActivate(context)).toBe(true);
    });

    it("should throw ForbiddenException when warehouseId is required but not provided", () => {
      const options: WarehouseAccessOptions = { requireWarehouseId: true };
      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
      const context = createMockExecutionContext(mockUser);

      expect(() => guard.canActivate(context)).toThrow(ForbiddenException);
    });

    it("should allow access when warehouseId is not required and not provided", () => {
      const options: WarehouseAccessOptions = { requireWarehouseId: false };
      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
      const context = createMockExecutionContext(mockUser);

      expect(guard.canActivate(context)).toBe(true);
    });

    it("should allow access when user has access to the specified warehouse", () => {
      const options: WarehouseAccessOptions = { requireWarehouseId: true };
      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
      const context = createMockExecutionContext(mockUser, {
        warehouseId: "warehouse-1",
      });

      expect(guard.canActivate(context)).toBe(true);
    });

    it("should throw ForbiddenException when user does not have access to warehouse", () => {
      const options: WarehouseAccessOptions = { requireWarehouseId: true };
      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
      const context = createMockExecutionContext(mockUser, {
        warehouseId: "warehouse-3",
      });

      expect(() => guard.canActivate(context)).toThrow(ForbiddenException);
    });

    it("should extract warehouseId from query parameters", () => {
      const options: WarehouseAccessOptions = { requireWarehouseId: true };
      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
      const context = createMockExecutionContext(
        mockUser,
        {},
        { warehouseId: "warehouse-1" }
      );

      expect(guard.canActivate(context)).toBe(true);
    });

    it("should extract warehouseId from headers", () => {
      const options: WarehouseAccessOptions = { requireWarehouseId: true };
      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
      const context = createMockExecutionContext(
        mockUser,
        {},
        {},
        { "x-warehouse-id": "warehouse-1" }
      );

      expect(guard.canActivate(context)).toBe(true);
    });

    it("should extract warehouseId from request body", () => {
      const options: WarehouseAccessOptions = { requireWarehouseId: true };
      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
      const context = createMockExecutionContext(
        mockUser,
        {},
        {},
        {},
        { warehouseId: "warehouse-1" }
      );

      expect(guard.canActivate(context)).toBe(true);
    });

    it("should use custom parameter names", () => {
      const options: WarehouseAccessOptions = {
        requireWarehouseId: true,
        paramName: "id",
        queryParam: "warehouse",
        headerName: "x-custom-warehouse",
      };
      jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
      const context = createMockExecutionContext(mockUser, {
        id: "warehouse-1",
      });

      expect(guard.canActivate(context)).toBe(true);
    });
  });
});
