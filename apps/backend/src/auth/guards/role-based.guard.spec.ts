import { ExecutionContext, ForbiddenException } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { Role } from "@prisma/client";
import {
  RoleBasedGuard,
  TenantAdminGuard,
  WarehouseManagerGuard,
  WarehouseMemberGuard,
  WarehouseSpecificRoleGuard,
} from "./role-based.guard";
import { EnhancedUserPayload } from "../types";

describe("Role-Based Guards", () => {
  let mockReflector: jest.Mocked<Reflector>;
  let mockExecutionContext: jest.Mocked<ExecutionContext>;
  let mockRequest: any;

  beforeEach(() => {
    mockReflector = {
      getAllAndOverride: jest.fn(),
    } as any;

    mockRequest = {
      user: null,
      warehouseContext: null,
    };

    mockExecutionContext = {
      switchToHttp: jest.fn().mockReturnValue({
        getRequest: jest.fn().mockReturnValue(mockRequest),
      }),
      getHandler: jest.fn(),
      getClass: jest.fn(),
    } as any;
  });

  const createMockUser = (
    overrides: Partial<EnhancedUserPayload> = {}
  ): EnhancedUserPayload => ({
    id: "user-1",
    email: "<EMAIL>",
    name: "Test User",
    password: "password",
    authUserId: "auth-1",
    role: Role.WAREHOUSE_MEMBER,
    status: "ACTIVE",
    createdAt: new Date(),
    updatedAt: new Date(),
    tenantId: "tenant-1",
    warehouseUsers: [],
    accessibleWarehouses: [],
    warehouseRoles: new Map(),
    isTenantAdmin: jest.fn().mockReturnValue(false),
    canAccessWarehouse: jest.fn(),
    getWarehouseRole: jest.fn(),
    hasWarehousePermission: jest.fn(),
    ...overrides,
  });

  describe("RoleBasedGuard", () => {
    let guard: RoleBasedGuard;

    beforeEach(() => {
      guard = new RoleBasedGuard(mockReflector);
    });

    it("should allow access when no roles are required", () => {
      mockReflector.getAllAndOverride.mockReturnValue(undefined);
      mockRequest.user = createMockUser();

      const result = guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
    });

    it("should throw ForbiddenException when user is not authenticated", () => {
      mockReflector.getAllAndOverride.mockReturnValue([Role.WAREHOUSE_MEMBER]);
      mockRequest.user = null;

      expect(() => guard.canActivate(mockExecutionContext)).toThrow(
        ForbiddenException
      );
    });

    it("should allow access for tenant admin", () => {
      mockReflector.getAllAndOverride.mockReturnValue([Role.TENANT_ADMIN]);
      const mockUser = createMockUser();
      (mockUser.isTenantAdmin as jest.Mock).mockReturnValue(true);
      mockRequest.user = mockUser;

      const result = guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
    });

    it("should allow access for warehouse manager", () => {
      mockReflector.getAllAndOverride.mockReturnValue([Role.WAREHOUSE_MANAGER]);
      mockRequest.user = createMockUser({
        warehouseUsers: [{ warehouseId: "wh-1", role: Role.WAREHOUSE_MANAGER }],
      });

      const result = guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
    });

    it("should allow access for warehouse member", () => {
      mockReflector.getAllAndOverride.mockReturnValue([Role.WAREHOUSE_MEMBER]);
      mockRequest.user = createMockUser({
        warehouseUsers: [{ warehouseId: "wh-1", role: Role.WAREHOUSE_MEMBER }],
      });

      const result = guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
    });

    it("should deny access when user lacks required role", () => {
      mockReflector.getAllAndOverride.mockReturnValue([Role.WAREHOUSE_MANAGER]);
      mockRequest.user = createMockUser({
        warehouseUsers: [{ warehouseId: "wh-1", role: Role.WAREHOUSE_MEMBER }],
      });

      expect(() => guard.canActivate(mockExecutionContext)).toThrow(
        ForbiddenException
      );
    });
  });

  describe("TenantAdminGuard", () => {
    let guard: TenantAdminGuard;

    beforeEach(() => {
      guard = new TenantAdminGuard();
    });

    it("should allow access for tenant admin", () => {
      const mockUser = createMockUser();
      (mockUser.isTenantAdmin as jest.Mock).mockReturnValue(true);
      mockRequest.user = mockUser;

      const result = guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
    });

    it("should deny access for non-tenant admin", () => {
      const mockUser = createMockUser();
      (mockUser.isTenantAdmin as jest.Mock).mockReturnValue(false);
      mockRequest.user = mockUser;

      expect(() => guard.canActivate(mockExecutionContext)).toThrow(
        ForbiddenException
      );
    });

    it("should throw ForbiddenException when user is not authenticated", () => {
      mockRequest.user = null;

      expect(() => guard.canActivate(mockExecutionContext)).toThrow(
        ForbiddenException
      );
    });
  });

  describe("WarehouseManagerGuard", () => {
    let guard: WarehouseManagerGuard;

    beforeEach(() => {
      guard = new WarehouseManagerGuard();
    });

    it("should allow access for tenant admin", () => {
      const mockUser = createMockUser();
      (mockUser.isTenantAdmin as jest.Mock).mockReturnValue(true);
      mockRequest.user = mockUser;

      const result = guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
    });

    it("should allow access for warehouse manager", () => {
      mockRequest.user = createMockUser({
        warehouseUsers: [{ warehouseId: "wh-1", role: Role.WAREHOUSE_MANAGER }],
      });

      const result = guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
    });

    it("should deny access for warehouse member only", () => {
      mockRequest.user = createMockUser({
        warehouseUsers: [{ warehouseId: "wh-1", role: Role.WAREHOUSE_MEMBER }],
      });

      expect(() => guard.canActivate(mockExecutionContext)).toThrow(
        ForbiddenException
      );
    });
  });

  describe("WarehouseMemberGuard", () => {
    let guard: WarehouseMemberGuard;

    beforeEach(() => {
      guard = new WarehouseMemberGuard();
    });

    it("should allow access for tenant admin", () => {
      const mockUser = createMockUser();
      (mockUser.isTenantAdmin as jest.Mock).mockReturnValue(true);
      mockRequest.user = mockUser;

      const result = guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
    });

    it("should allow access for any warehouse role", () => {
      mockRequest.user = createMockUser({
        warehouseUsers: [{ warehouseId: "wh-1", role: Role.WAREHOUSE_MEMBER }],
      });

      const result = guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
    });

    it("should deny access for users with no warehouse access", () => {
      mockRequest.user = createMockUser({
        warehouseUsers: [],
      });

      expect(() => guard.canActivate(mockExecutionContext)).toThrow(
        ForbiddenException
      );
    });
  });

  describe("WarehouseSpecificRoleGuard", () => {
    let guard: WarehouseSpecificRoleGuard;

    beforeEach(() => {
      guard = new WarehouseSpecificRoleGuard(mockReflector);
    });

    it("should allow access when no warehouse role is required", () => {
      mockReflector.getAllAndOverride.mockReturnValue(undefined);
      mockRequest.user = createMockUser();

      const result = guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
    });

    it("should allow access for tenant admin", () => {
      mockReflector.getAllAndOverride.mockReturnValue(Role.WAREHOUSE_MANAGER);
      const mockUser = createMockUser();
      (mockUser.isTenantAdmin as jest.Mock).mockReturnValue(true);
      mockRequest.user = mockUser;
      mockRequest.warehouseContext = { warehouseId: "wh-1" };

      const result = guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
    });

    it("should allow access when user has required role in warehouse", () => {
      mockReflector.getAllAndOverride.mockReturnValue(Role.WAREHOUSE_MANAGER);
      const mockUser = createMockUser();
      (mockUser.getWarehouseRole as jest.Mock).mockReturnValue(
        Role.WAREHOUSE_MANAGER
      );
      (mockUser.hasWarehousePermission as jest.Mock).mockReturnValue(true);
      mockRequest.user = mockUser;
      mockRequest.warehouseContext = { warehouseId: "wh-1" };

      const result = guard.canActivate(mockExecutionContext);

      expect(result).toBe(true);
    });

    it("should deny access when warehouse context is missing", () => {
      mockReflector.getAllAndOverride.mockReturnValue(Role.WAREHOUSE_MANAGER);
      mockRequest.user = createMockUser();
      mockRequest.warehouseContext = null;

      expect(() => guard.canActivate(mockExecutionContext)).toThrow(
        ForbiddenException
      );
    });

    it("should deny access when user has no access to warehouse", () => {
      mockReflector.getAllAndOverride.mockReturnValue(Role.WAREHOUSE_MANAGER);
      const mockUser = createMockUser();
      (mockUser.getWarehouseRole as jest.Mock).mockReturnValue(null);
      mockRequest.user = mockUser;
      mockRequest.warehouseContext = { warehouseId: "wh-1" };

      expect(() => guard.canActivate(mockExecutionContext)).toThrow(
        ForbiddenException
      );
    });

    it("should deny access when user lacks required role in warehouse", () => {
      mockReflector.getAllAndOverride.mockReturnValue(Role.WAREHOUSE_MANAGER);
      const mockUser = createMockUser();
      (mockUser.getWarehouseRole as jest.Mock).mockReturnValue(
        Role.WAREHOUSE_MEMBER
      );
      (mockUser.hasWarehousePermission as jest.Mock).mockReturnValue(false);
      mockRequest.user = mockUser;
      mockRequest.warehouseContext = { warehouseId: "wh-1" };

      expect(() => guard.canActivate(mockExecutionContext)).toThrow(
        ForbiddenException
      );
    });
  });
});
