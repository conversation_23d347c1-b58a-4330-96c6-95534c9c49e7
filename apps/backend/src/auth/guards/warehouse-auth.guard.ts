import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { Role } from "@prisma/client";
import { WAREHOUSE_ACCESS_KEY } from "../decorators/warehouse-access.decorator";
import { AuthenticatedUser } from "../types/authenticated-user.interface";

export interface WarehouseAccessOptions {
  requireWarehouseId?: boolean; // Whether warehouseId parameter is required
  allowedRoles?: Role[]; // Roles that can access this endpoint
  paramName?: string; // Name of the parameter containing warehouseId (default: 'warehouseId')
  queryParam?: string; // Name of the query parameter containing warehouseId
  headerName?: string; // Name of the header containing warehouseId
}

@Injectable()
export class WarehouseAuthGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const warehouseAccessOptions = this.reflector.getAllAndOverride<WarehouseAccessOptions>(
      WAREHOUSE_ACCESS_KEY,
      [context.getHandler(), context.getClass()]
    );

    // If no warehouse access options are defined, allow access
    if (!warehouseAccessOptions) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user: AuthenticatedUser = request.user;

    if (!user) {
      throw new ForbiddenException("User not authenticated");
    }

    // Check role-based access if specified
    if (warehouseAccessOptions.allowedRoles && warehouseAccessOptions.allowedRoles.length > 0) {
      if (!warehouseAccessOptions.allowedRoles.includes(user.role)) {
        throw new ForbiddenException(
          `Access denied. Required roles: ${warehouseAccessOptions.allowedRoles.join(", ")}`
        );
      }
    }

    // TENANT_ADMIN has access to all warehouses in their tenant
    if (user.role === Role.TENANT_ADMIN) {
      return true;
    }

    // Extract warehouseId from request
    const warehouseId = this.extractWarehouseId(request, warehouseAccessOptions);

    // If warehouseId is required but not provided
    if (warehouseAccessOptions.requireWarehouseId && !warehouseId) {
      throw new ForbiddenException("Warehouse ID is required for this operation");
    }

    // If no warehouseId is provided and it's not required, allow access
    if (!warehouseId) {
      return true;
    }

    // Check if user has access to the specified warehouse
    const userWarehouseIds = user.warehouseUsers?.map((wu) => wu.warehouseId) || [];
    
    if (!userWarehouseIds.includes(warehouseId)) {
      throw new ForbiddenException(
        `Access denied. You do not have permission to access warehouse: ${warehouseId}`
      );
    }

    return true;
  }

  private extractWarehouseId(request: any, options: WarehouseAccessOptions): string | undefined {
    const paramName = options.paramName || 'warehouseId';
    const queryParam = options.queryParam || 'warehouseId';
    const headerName = options.headerName || 'x-warehouse-id';

    // Try to extract from route parameters first
    if (request.params && request.params[paramName]) {
      return request.params[paramName];
    }

    // Try to extract from query parameters
    if (request.query && request.query[queryParam]) {
      return request.query[queryParam];
    }

    // Try to extract from headers
    if (request.headers && request.headers[headerName.toLowerCase()]) {
      return request.headers[headerName.toLowerCase()];
    }

    // Try to extract from request body
    if (request.body && request.body.warehouseId) {
      return request.body.warehouseId;
    }

    return undefined;
  }
}
