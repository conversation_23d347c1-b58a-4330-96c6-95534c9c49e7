import { Role } from "@prisma/client";

// This interface should closely match the object returned by JwtStrategy.validate()
// and provide the necessary fields for services and guards.
export interface AuthenticatedUser {
  id: string; // The user's primary ID from the database (Prisma User ID)
  email: string;
  role: Role; // Prisma Role enum
  tenantId: string;
  name: string | null;
  authUserId: string | null; // Supabase auth user ID, if available
  warehouseUsers?: { warehouseId: string; role: Role }[]; // Optional, included by JwtStrategy

  // Enhanced warehouse access properties (added by JwtStrategy)
  accessibleWarehouses?: string[]; // Array of warehouse IDs user can access
  warehouseRoles?: Map<string, Role>; // Map of warehouseId -> role for quick lookup

  // Utility methods for warehouse access (added by JwtStrategy)
  canAccessWarehouse?: (warehouseId: string) => boolean;
  getWarehouseRole?: (warehouseId: string) => Role | null;
  hasWarehousePermission?: (warehouseId: string, requiredRole: Role) => boolean;
}
