import { Test, TestingModule } from "@nestjs/testing";
import { JwtService } from "@nestjs/jwt";
import { ConfigService } from "@nestjs/config";
import { UnauthorizedException } from "@nestjs/common";
import { PrismaClient, Role, User, Tenant } from "@prisma/client"; // Import Prisma types

import { AuthService } from "./auth.service";
import { AuthModule } from "./auth.module";
import { PrismaModule } from "../prisma/prisma.module";
import { PrismaService } from "../prisma/prisma.service";
import { cleanupDatabase } from "../util/cleanup-database"; // Utility to clean DB

const TEST_JWT_SECRET = process.env.TEST_JWT_SECRET;
if (!TEST_JWT_SECRET) {
  throw new Error('TEST_JWT_SECRET must be set in the environment for integration tests');
}
describe("AuthService Integration", () => {
  let authService: AuthService;
  let prisma: PrismaService;
  let jwtService: JwtService; // Get the real JwtService instance
  let moduleRef: TestingModule;
  let testUser: User;
  let testTenant: Tenant; // Added to store the test tenant
  const testUserSupabaseId = "supabase|12345";

  beforeAll(async () => {
    moduleRef = await Test.createTestingModule({
      imports: [AuthModule, PrismaModule], // Import AuthModule which sets up JwtService
    }).compile();

    authService = moduleRef.get<AuthService>(AuthService);
    prisma = moduleRef.get<PrismaService>(PrismaService);
    jwtService = moduleRef.get<JwtService>(JwtService); // Get configured JwtService

    // Clean the DB before tests
    await cleanupDatabase(prisma);

    // Seed a test tenant
    testTenant = await prisma.tenant.create({
      data: {
        name: "Auth Test Tenant",
      },
    });

    // Seed a test user linked to a Supabase ID and the test tenant
    testUser = await prisma.user.create({
      data: {
        email: "<EMAIL>",
        authUserId: testUserSupabaseId,
        role: Role.WAREHOUSE_MEMBER, // Example role
        name: "Auth Test User",
        tenantId: testTenant.id, // Link user to the created tenant
      },
    });
  });

  afterAll(async () => {
    // Clean up DB
    await cleanupDatabase(prisma);
    await moduleRef.close();
  });

  // Helper to generate a mock Supabase token
  const generateMockSupabaseToken = async (
    payload: object,
    secret: string = TEST_JWT_SECRET!,
    expiresIn: string = "15m"
  ): Promise<string> => {
    return jwtService.signAsync(payload, { secret, expiresIn });
  };

  it("should successfully login with a valid Supabase token and return an app access token", async () => {
    const validSupabaseToken = await generateMockSupabaseToken({
      sub: testUserSupabaseId,
      email: testUser.email,
      // Add other claims Supabase might include if needed for validation later
    });

    const result = await authService.login({
      supabaseToken: validSupabaseToken,
    });

    expect(result).toHaveProperty("accessToken");
    expect(result.accessToken).toBeDefined();

    // Verify the generated app token
    const appTokenPayload = await jwtService.verifyAsync(result.accessToken, {
      secret: TEST_JWT_SECRET!, // Our app token is also signed with this secret in the test setup
    });

    expect(appTokenPayload.userId).toEqual(testUser.id);
    expect(appTokenPayload.email).toEqual(testUser.email);
    expect(appTokenPayload.role).toEqual(testUser.role);
    expect(appTokenPayload.tenantId).toEqual(testTenant.id); // Added tenantId assertion for app token
  });

  it("should throw UnauthorizedException for an invalid Supabase token (bad signature)", async () => {
    const tokenWithWrongSecret = await generateMockSupabaseToken(
      { sub: testUserSupabaseId, email: testUser.email },
      "wrong-secret" // Sign with a different secret
    );

    await expect(
      authService.login({ supabaseToken: tokenWithWrongSecret })
    ).rejects.toThrow(UnauthorizedException);
  });

  it("should throw UnauthorizedException for an expired Supabase token", async () => {
    const expiredToken = await generateMockSupabaseToken(
      { sub: testUserSupabaseId, email: testUser.email },
      TEST_JWT_SECRET!,
      "-1s" // Expired 1 second ago
    );

    await expect(
      authService.login({ supabaseToken: expiredToken })
    ).rejects.toThrow(UnauthorizedException);
  });

  it("should create a new user and log them in if the user does not exist locally", async () => {
    const unknownUserEmail = "<EMAIL>";
    const unknownUserSupabaseId = "supabase|new-user";
    const tokenForUnknownUser = await generateMockSupabaseToken({
      sub: unknownUserSupabaseId,
      email: unknownUserEmail,
    });

    const result = await authService.login({
      supabaseToken: tokenForUnknownUser,
    });

    expect(result).toHaveProperty("accessToken");
    expect(result.onboardingStatus).toBe("pending_company_details");
    expect(result.user).toBeDefined();
    expect(result.user.email).toBe(unknownUserEmail);
    expect(result.user.authUserId).toBe(unknownUserSupabaseId);

    // Verify the user was created in the database
    const dbUser = await prisma.user.findUnique({
      where: { authUserId: unknownUserSupabaseId },
    });
    expect(dbUser).not.toBeNull();
    expect(dbUser?.email).toBe(unknownUserEmail);
  });

  it("should throw UnauthorizedException if Supabase token lacks required claims (sub)", async () => {
    const tokenMissingSub = await generateMockSupabaseToken({
      email: testUser.email, // Missing 'sub'
    });

    await expect(
      authService.login({ supabaseToken: tokenMissingSub })
    ).rejects.toThrow(UnauthorizedException);
  });

  it("should throw UnauthorizedException if Supabase token lacks required claims (email)", async () => {
    // Note: Supabase usually includes email, but testing robustness
    const tokenMissingEmail = await generateMockSupabaseToken({
      sub: testUserSupabaseId, // Missing 'email'
    });

    await expect(
      authService.login({ supabaseToken: tokenMissingEmail })
    ).rejects.toThrow(UnauthorizedException);
  });
});
