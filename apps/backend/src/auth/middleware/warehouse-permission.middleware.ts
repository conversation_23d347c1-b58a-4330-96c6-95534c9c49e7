import {
  Injectable,
  NestMiddleware,
  ForbiddenException,
  BadRequestException,
  Logger,
} from "@nestjs/common";
import { Request, Response, NextFunction } from "express";
import { Role } from "@prisma/client";
import { EnhancedUserPayload } from "../types";
import { WarehouseValidationUtils } from "../../warehouses/utils/warehouse-validation.utils";
import { SecurityAuditService } from "../../audit-log/services/security-audit.service";

// Extended request interface to include warehouse context
export interface WarehouseRequest extends Request {
  user: EnhancedUserPayload;
  warehouseContext?: {
    warehouseId: string;
    userRole: Role;
    hasAccess: boolean;
    isManager: boolean;
    isAdmin: boolean;
  };
}

// Configuration for warehouse permission middleware
export interface WarehousePermissionConfig {
  // Whether warehouse ID is required in the request
  requireWarehouseId?: boolean;
  // Minimum role required for the operation
  minimumRole?: Role;
  // Whether to extract warehouse ID from entity relationships (e.g., pallet -> location -> warehouse)
  extractFromEntity?: {
    entityType: "pallet" | "location" | "purchaseOrder" | "shipment";
    paramName: string; // e.g., "palletId", "locationId"
  };
  // Custom validation function
  customValidation?: (
    warehouseId: string | undefined,
    user: EnhancedUserPayload,
    req: WarehouseRequest
  ) => Promise<boolean>;
  // Whether to skip validation for tenant admins
  skipForTenantAdmin?: boolean;
}

@Injectable()
export class WarehousePermissionMiddleware implements NestMiddleware {
  public readonly logger = new Logger(WarehousePermissionMiddleware.name);

  constructor(
    public readonly warehouseValidationUtils: WarehouseValidationUtils,
    public readonly securityAuditService: SecurityAuditService
  ) {}

  /**
   * Create a configured middleware instance
   */
  static configure(config: WarehousePermissionConfig = {}) {
    class ConfiguredWarehousePermissionMiddleware extends WarehousePermissionMiddleware {
      async use(req: WarehouseRequest, res: Response, next: NextFunction) {
        await this.validateWarehousePermissions(req, res, next, config);
      }
    }
    return ConfiguredWarehousePermissionMiddleware;
  }

  /**
   * Default middleware implementation (basic warehouse access validation)
   */
  async use(req: WarehouseRequest, res: Response, next: NextFunction) {
    await this.validateWarehousePermissions(req, res, next, {
      requireWarehouseId: false,
      skipForTenantAdmin: true,
    });
  }

  /**
   * Core warehouse permission validation logic
   */
  public async validateWarehousePermissions(
    req: WarehouseRequest,
    res: Response,
    next: NextFunction,
    config: WarehousePermissionConfig
  ) {
    try {
      const user = req.user;

      if (!user) {
        throw new ForbiddenException("User not authenticated");
      }

      this.logger.debug(
        `Validating warehouse permissions for user ${user.email} on ${req.method} ${req.path}`
      );

      // Skip validation for tenant admins if configured
      if (config.skipForTenantAdmin && user.role === Role.TENANT_ADMIN) {
        this.logger.debug(
          `Skipping warehouse validation for tenant admin: ${user.email}`
        );
        this.setWarehouseContext(req, undefined, user.role, true);
        return next();
      }

      // Extract warehouse ID from various sources
      const warehouseId = await this.extractWarehouseId(req, config);

      // Check if warehouse ID is required
      if (config.requireWarehouseId && !warehouseId) {
        throw new BadRequestException(
          "Warehouse ID is required for this operation"
        );
      }

      // Validate warehouse access if warehouse ID is provided
      if (warehouseId) {
        await this.validateWarehouseAccess(warehouseId, user, config);

        // Get user's role in this warehouse
        const userRole = user.getWarehouseRole?.(warehouseId) || user.role;

        this.setWarehouseContext(req, warehouseId, userRole, true);

        this.logger.debug(
          `Warehouse access validated for user ${user.email} in warehouse ${warehouseId} with role ${userRole}`
        );

        // Log successful warehouse access
        await this.securityAuditService.logWarehouseAccessGranted(
          warehouseId,
          user,
          req,
          {
            operation: `${req.method}_${req.path}`,
            userRole,
          }
        );
      } else {
        // No specific warehouse, but user is authenticated
        this.setWarehouseContext(req, undefined, user.role, true);
      }

      // Run custom validation if provided
      if (config.customValidation) {
        const customResult = await config.customValidation(
          warehouseId,
          user,
          req
        );
        if (!customResult) {
          throw new ForbiddenException("Custom validation failed");
        }
      }

      next();
    } catch (error) {
      this.logger.error(
        `Warehouse permission validation failed for ${req.method} ${req.path}: ${error.message}`,
        error.stack
      );

      // Log failed warehouse access attempt
      const warehouseId = await this.extractWarehouseId(req, config);
      await this.securityAuditService.logWarehouseAccessDenied(
        warehouseId,
        req.user,
        req,
        error.message,
        {
          operation: `${req.method}_${req.path}`,
          userRole: req.user?.role,
        }
      );

      next(error);
    }
  }

  /**
   * Extract warehouse ID from request parameters, query, headers, or entity relationships
   */
  public async extractWarehouseId(
    req: WarehouseRequest,
    config: WarehousePermissionConfig
  ): Promise<string | undefined> {
    // Try to get warehouse ID from various sources in order of priority

    // 1. URL parameters
    let warehouseId = req.params.warehouseId || req.params.warehouse_id;

    // 2. Query parameters
    if (!warehouseId) {
      warehouseId =
        (req.query.warehouseId as string) || (req.query.warehouse_id as string);
    }

    // 3. Request headers
    if (!warehouseId) {
      warehouseId = req.headers["x-warehouse-id"] as string;
    }

    // 4. Request body
    if (!warehouseId && req.body?.warehouseId) {
      warehouseId = req.body.warehouseId;
    }

    // 5. Extract from entity relationships if configured
    if (!warehouseId && config.extractFromEntity) {
      warehouseId = await this.extractWarehouseFromEntity(
        req,
        config.extractFromEntity
      );
    }

    return warehouseId;
  }

  /**
   * Extract warehouse ID from entity relationships
   */
  public async extractWarehouseFromEntity(
    req: WarehouseRequest,
    entityConfig: NonNullable<WarehousePermissionConfig["extractFromEntity"]>
  ): Promise<string | undefined> {
    const entityId = req.params[entityConfig.paramName];

    if (!entityId) {
      return undefined;
    }

    try {
      const warehouseId =
        await this.warehouseValidationUtils.validateEntityWarehouseAccess(
          entityId,
          entityConfig.entityType as "pallet" | "location",
          req.user
        );
      return warehouseId;
    } catch (error) {
      this.logger.warn(
        `Failed to extract warehouse from ${entityConfig.entityType} ${entityId}: ${error.message}`
      );
      return undefined;
    }
  }

  /**
   * Validate user's access to a specific warehouse
   */
  public async validateWarehouseAccess(
    warehouseId: string,
    user: EnhancedUserPayload,
    config: WarehousePermissionConfig
  ): Promise<void> {
    // Use enhanced user payload utility methods if available
    if (user.canAccessWarehouse && !user.canAccessWarehouse(warehouseId)) {
      throw new ForbiddenException(
        `Access denied: You do not have permission to access warehouse ${warehouseId}`
      );
    }

    // Validate minimum role if specified
    if (config.minimumRole && user.hasWarehousePermission) {
      if (!user.hasWarehousePermission(warehouseId, config.minimumRole)) {
        throw new ForbiddenException(
          `Access denied: This operation requires ${config.minimumRole} role or higher in warehouse ${warehouseId}`
        );
      }
    }

    // Fallback to warehouse validation utils for additional checks
    await this.warehouseValidationUtils.validateUserWarehouseAccess(
      warehouseId,
      user
    );

    if (config.minimumRole) {
      await this.warehouseValidationUtils.validateUserWarehouseRole(
        warehouseId,
        user,
        config.minimumRole
      );
    }
  }

  /**
   * Set warehouse context on the request for downstream handlers
   */
  public setWarehouseContext(
    req: WarehouseRequest,
    warehouseId: string | undefined,
    userRole: Role,
    hasAccess: boolean
  ): void {
    req.warehouseContext = {
      warehouseId: warehouseId || "",
      userRole,
      hasAccess,
      isManager:
        userRole === Role.WAREHOUSE_MANAGER || userRole === Role.TENANT_ADMIN,
      isAdmin: userRole === Role.TENANT_ADMIN,
    };
  }
}

// Convenience factory functions for common middleware configurations

/**
 * Middleware that requires warehouse ID and basic access
 */
export const RequireWarehouseAccess: typeof WarehousePermissionMiddleware =
  WarehousePermissionMiddleware.configure({
    requireWarehouseId: true,
    skipForTenantAdmin: false,
  });

/**
 * Middleware that requires warehouse manager role or higher
 */
export const RequireWarehouseManager: typeof WarehousePermissionMiddleware =
  WarehousePermissionMiddleware.configure({
    requireWarehouseId: true,
    minimumRole: Role.WAREHOUSE_MANAGER,
    skipForTenantAdmin: true,
  });

/**
 * Middleware that extracts warehouse from pallet ID parameter
 */
export const RequirePalletWarehouseAccess: typeof WarehousePermissionMiddleware =
  WarehousePermissionMiddleware.configure({
    extractFromEntity: {
      entityType: "pallet",
      paramName: "palletId",
    },
    skipForTenantAdmin: true,
  });

/**
 * Middleware that extracts warehouse from location ID parameter
 */
export const RequireLocationWarehouseAccess: typeof WarehousePermissionMiddleware =
  WarehousePermissionMiddleware.configure({
    extractFromEntity: {
      entityType: "location",
      paramName: "locationId",
    },
    skipForTenantAdmin: true,
  });
