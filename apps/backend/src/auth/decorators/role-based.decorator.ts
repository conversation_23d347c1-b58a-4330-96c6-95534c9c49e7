import { SetMetadata, UseGuards, applyDecorators } from '@nestjs/common';
import { Role } from '@prisma/client';
import { 
  RoleBasedGuard, 
  TenantAdminGuard, 
  WarehouseManagerGuard, 
  WarehouseMemberGuard,
  WarehouseSpecificRoleGuard 
} from '../guards/role-based.guard';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';

/**
 * Decorator to specify required roles for an endpoint
 */
export const Roles = (...roles: Role[]) => SetMetadata('roles', roles);

/**
 * Decorator to specify required warehouse-specific role
 */
export const WarehouseRole = (role: Role) => SetMetadata('warehouseRole', role);

/**
 * Composite decorator for tenant admin only access
 */
export const TenantAdminOnly = () => 
  applyDecorators(
    UseGuards(JwtAuthGuard, TenantAdminGuard)
  );

/**
 * Composite decorator for warehouse manager access
 * Allows tenant admins and warehouse managers
 */
export const WarehouseManagerOnly = () => 
  applyDecorators(
    UseGuards(JwtAuthGuard, WarehouseManagerGuard)
  );

/**
 * Composite decorator for warehouse member access
 * Allows tenant admins, warehouse managers, and warehouse members
 */
export const WarehouseMemberOnly = () => 
  applyDecorators(
    UseGuards(JwtAuthGuard, WarehouseMemberGuard)
  );

/**
 * Composite decorator for role-based access with multiple roles
 */
export const RequireRoles = (...roles: Role[]) => 
  applyDecorators(
    Roles(...roles),
    UseGuards(JwtAuthGuard, RoleBasedGuard)
  );

/**
 * Composite decorator for warehouse-specific role requirements
 * Requires warehouse context to be available in the request
 */
export const RequireWarehouseRole = (role: Role) => 
  applyDecorators(
    WarehouseRole(role),
    UseGuards(JwtAuthGuard, WarehouseSpecificRoleGuard)
  );

/**
 * Convenience decorators for common role combinations
 */

/**
 * Allows tenant admins and warehouse managers
 */
export const AdminOrManager = () => 
  RequireRoles(Role.TENANT_ADMIN, Role.WAREHOUSE_MANAGER);

/**
 * Allows all warehouse roles (admin, manager, member)
 */
export const AnyWarehouseRole = () => 
  RequireRoles(Role.TENANT_ADMIN, Role.WAREHOUSE_MANAGER, Role.WAREHOUSE_MEMBER);

/**
 * Requires tenant admin role specifically
 */
export const TenantAdminRole = () => 
  RequireRoles(Role.TENANT_ADMIN);

/**
 * Requires warehouse manager role in the current warehouse context
 */
export const WarehouseManagerRole = () => 
  RequireWarehouseRole(Role.WAREHOUSE_MANAGER);

/**
 * Requires warehouse member role in the current warehouse context
 */
export const WarehouseMemberRole = () => 
  RequireWarehouseRole(Role.WAREHOUSE_MEMBER);

/**
 * Combined decorator for endpoints that need both authentication and role validation
 * This is the most commonly used pattern for protected endpoints
 */
export const ProtectedEndpoint = (...roles: Role[]) => 
  applyDecorators(
    Roles(...roles),
    UseGuards(JwtAuthGuard, RoleBasedGuard)
  );

/**
 * Combined decorator for warehouse-scoped endpoints with role validation
 * Requires both warehouse context and specific role within that warehouse
 */
export const ProtectedWarehouseEndpoint = (role: Role) => 
  applyDecorators(
    WarehouseRole(role),
    UseGuards(JwtAuthGuard, WarehouseSpecificRoleGuard)
  );
