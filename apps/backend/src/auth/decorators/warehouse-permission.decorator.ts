import { SetMetadata, applyDecorators, UseGuards } from "@nestjs/common";
import { Role } from "@prisma/client";

// Metadata key for warehouse permission configuration
export const WAREHOUSE_PERMISSION_KEY = "warehouse_permission";

// Configuration interface for warehouse permissions
export interface WarehousePermissionOptions {
  // Whether warehouse ID is required in the request
  requireWarehouseId?: boolean;
  // Minimum role required for the operation
  minimumRole?: Role;
  // Whether to extract warehouse ID from entity relationships
  extractFromEntity?: {
    entityType: "pallet" | "location" | "purchaseOrder" | "shipment";
    paramName: string;
  };
  // Whether to skip validation for tenant admins
  skipForTenantAdmin?: boolean;
  // Custom error message for access denied
  errorMessage?: string;
}

/**
 * Decorator to set warehouse permission requirements on routes
 */
export const WarehousePermission = (options: WarehousePermissionOptions = {}) =>
  SetMetadata(WAREHOUSE_PERMISSION_KEY, options);

/**
 * Convenience decorators for common warehouse permission patterns
 */

/**
 * Requires user to have access to the warehouse specified in the request
 * Warehouse ID must be provided via parameter, query, or header
 */
export const RequireWarehouseAccess = (options: Partial<WarehousePermissionOptions> = {}) =>
  WarehousePermission({
    requireWarehouseId: true,
    skipForTenantAdmin: false,
    ...options,
  });

/**
 * Requires user to have WAREHOUSE_MANAGER role or higher in the specified warehouse
 */
export const RequireWarehouseManager = (options: Partial<WarehousePermissionOptions> = {}) =>
  WarehousePermission({
    requireWarehouseId: true,
    minimumRole: Role.WAREHOUSE_MANAGER,
    skipForTenantAdmin: true,
    ...options,
  });

/**
 * Requires user to have WAREHOUSE_MEMBER role or higher in the specified warehouse
 */
export const RequireWarehouseMember = (options: Partial<WarehousePermissionOptions> = {}) =>
  WarehousePermission({
    requireWarehouseId: true,
    minimumRole: Role.WAREHOUSE_MEMBER,
    skipForTenantAdmin: true,
    ...options,
  });

/**
 * Requires user to have TENANT_ADMIN role (warehouse-agnostic admin operations)
 */
export const RequireTenantAdmin = () =>
  WarehousePermission({
    minimumRole: Role.TENANT_ADMIN,
    requireWarehouseId: false,
    skipForTenantAdmin: false,
  });

/**
 * Extracts warehouse context from pallet ID parameter and validates access
 */
export const RequirePalletAccess = (options: Partial<WarehousePermissionOptions> = {}) =>
  WarehousePermission({
    extractFromEntity: {
      entityType: "pallet",
      paramName: "palletId",
    },
    skipForTenantAdmin: true,
    ...options,
  });

/**
 * Extracts warehouse context from location ID parameter and validates access
 */
export const RequireLocationAccess = (options: Partial<WarehousePermissionOptions> = {}) =>
  WarehousePermission({
    extractFromEntity: {
      entityType: "location",
      paramName: "locationId",
    },
    skipForTenantAdmin: true,
    ...options,
  });

/**
 * Extracts warehouse context from purchase order ID parameter and validates access
 */
export const RequirePurchaseOrderAccess = (options: Partial<WarehousePermissionOptions> = {}) =>
  WarehousePermission({
    extractFromEntity: {
      entityType: "purchaseOrder",
      paramName: "purchaseOrderId",
    },
    skipForTenantAdmin: true,
    ...options,
  });

/**
 * Extracts warehouse context from shipment ID parameter and validates access
 */
export const RequireShipmentAccess = (options: Partial<WarehousePermissionOptions> = {}) =>
  WarehousePermission({
    extractFromEntity: {
      entityType: "shipment",
      paramName: "shipmentId",
    },
    skipForTenantAdmin: true,
    ...options,
  });

/**
 * Allows warehouse filtering but doesn't require specific warehouse access
 * Useful for list endpoints that can be filtered by warehouse
 */
export const AllowWarehouseFiltering = () =>
  WarehousePermission({
    requireWarehouseId: false,
    skipForTenantAdmin: true,
  });

/**
 * Combined decorator that applies JWT authentication and warehouse permission validation
 * This is the recommended way to protect warehouse-scoped endpoints
 */
export const ProtectedWarehouseRoute = (options: WarehousePermissionOptions = {}) =>
  applyDecorators(
    WarehousePermission(options)
  );

/**
 * Role-based warehouse access decorators with authentication
 */

/**
 * Protects route requiring warehouse manager access with JWT auth
 */
export const WarehouseManagerRoute = (options: Partial<WarehousePermissionOptions> = {}) =>
  ProtectedWarehouseRoute({
    requireWarehouseId: true,
    minimumRole: Role.WAREHOUSE_MANAGER,
    skipForTenantAdmin: true,
    ...options,
  });

/**
 * Protects route requiring warehouse member access with JWT auth
 */
export const WarehouseMemberRoute = (options: Partial<WarehousePermissionOptions> = {}) =>
  ProtectedWarehouseRoute({
    requireWarehouseId: true,
    minimumRole: Role.WAREHOUSE_MEMBER,
    skipForTenantAdmin: true,
    ...options,
  });

/**
 * Protects route requiring tenant admin access with JWT auth
 */
export const TenantAdminRoute = () =>
  ProtectedWarehouseRoute({
    minimumRole: Role.TENANT_ADMIN,
    requireWarehouseId: false,
    skipForTenantAdmin: false,
  });

/**
 * Protects pallet-related routes with automatic warehouse extraction
 */
export const PalletRoute = (minimumRole: Role = Role.WAREHOUSE_MEMBER) =>
  ProtectedWarehouseRoute({
    extractFromEntity: {
      entityType: "pallet",
      paramName: "palletId",
    },
    minimumRole,
    skipForTenantAdmin: true,
  });

/**
 * Protects location-related routes with automatic warehouse extraction
 */
export const LocationRoute = (minimumRole: Role = Role.WAREHOUSE_MEMBER) =>
  ProtectedWarehouseRoute({
    extractFromEntity: {
      entityType: "location",
      paramName: "locationId",
    },
    minimumRole,
    skipForTenantAdmin: true,
  });

// Type definitions for request objects with warehouse context

export interface WarehouseContextRequest {
  warehouseContext?: {
    warehouseId: string;
    userRole: Role;
    hasAccess: boolean;
    isManager: boolean;
    isAdmin: boolean;
  };
}

// Helper type for controllers that need warehouse context
export type RequestWithWarehouseContext<T = any> = T & WarehouseContextRequest;
