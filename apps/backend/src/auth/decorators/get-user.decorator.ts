import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { EnhancedUserPayload } from '../types';

/**
 * Decorator to extract the authenticated user from the request
 * 
 * This decorator extracts the user object that was attached to the request
 * by the JWT authentication guard. The user object contains all the user
 * information including warehouse access and roles.
 * 
 * @example
 * ```typescript
 * @Get('profile')
 * async getProfile(@GetUser() user: EnhancedUserPayload) {
 *   return { userId: user.id, email: user.email };
 * }
 * ```
 * 
 * @example
 * ```typescript
 * // Extract specific user property
 * @Get('tenant')
 * async getTenant(@GetUser('tenantId') tenantId: string) {
 *   return { tenantId };
 * }
 * ```
 */
export const GetUser = createParamDecorator(
  (data: keyof EnhancedUserPayload | undefined, ctx: ExecutionContext): EnhancedUserPayload | any => {
    const request = ctx.switchToHttp().getRequest();
    const user: EnhancedUserPayload = request.user;

    // If no specific property requested, return the entire user object
    if (!data) {
      return user;
    }

    // Return the specific property requested
    return user?.[data];
  },
);
