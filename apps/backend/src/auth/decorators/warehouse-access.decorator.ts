import { SetMetadata } from "@nestjs/common";
import { Role } from "@prisma/client";

export const WAREHOUSE_ACCESS_KEY = "warehouse_access";

export interface WarehouseAccessOptions {
  requireWarehouseId?: boolean; // Whether warehouseId parameter is required
  allowedRoles?: Role[]; // Roles that can access this endpoint
  paramName?: string; // Name of the parameter containing warehouseId (default: 'warehouseId')
  queryParam?: string; // Name of the query parameter containing warehouseId
  headerName?: string; // Name of the header containing warehouseId
}

/**
 * Decorator to enforce warehouse-based access control
 * 
 * @param options Configuration options for warehouse access control
 * 
 * @example
 * // Require warehouse access with warehouseId in route params
 * @WarehouseAccess({ requireWarehouseId: true })
 * 
 * @example
 * // Allow only WAREHOUSE_MANAGER and TENANT_ADMIN roles
 * @WarehouseAccess({ 
 *   allowedRoles: [Role.WAREHOUSE_MANAGER, Role.TENANT_ADMIN],
 *   requireWarehouseId: true 
 * })
 * 
 * @example
 * // Custom parameter name for warehouseId
 * @WarehouseAccess({ 
 *   requireWarehouseId: true,
 *   paramName: 'id',
 *   queryParam: 'warehouse'
 * })
 */
export const WarehouseAccess = (options: WarehouseAccessOptions = {}) =>
  SetMetadata(WAREHOUSE_ACCESS_KEY, options);

/**
 * Shorthand decorator for requiring warehouse access with default settings
 */
export const RequireWarehouseAccess = () =>
  WarehouseAccess({ requireWarehouseId: true });

/**
 * Shorthand decorator for warehouse manager access
 */
export const WarehouseManagerAccess = (requireWarehouseId: boolean = true) =>
  WarehouseAccess({
    allowedRoles: [Role.WAREHOUSE_MANAGER, Role.TENANT_ADMIN],
    requireWarehouseId,
  });

/**
 * Shorthand decorator for warehouse member access (all warehouse roles)
 */
export const WarehouseMemberAccess = (requireWarehouseId: boolean = true) =>
  WarehouseAccess({
    allowedRoles: [Role.WAREHOUSE_MEMBER, Role.WAREHOUSE_MANAGER, Role.TENANT_ADMIN],
    requireWarehouseId,
  });
