# Security Testing Suite

This directory contains comprehensive security tests for the Quildora warehouse access control system. The test suite validates security controls, identifies vulnerabilities, and ensures proper implementation of security measures.

## Overview

The security testing suite covers multiple attack vectors and security concerns:

- **Authentication Security**: JWT manipulation, token expiration, brute force protection
- **Authorization Security**: Role escalation, warehouse access control, cross-tenant isolation
- **Data Isolation**: IDOR prevention, data leakage, bulk operation security
- **Injection Attacks**: SQL injection, NoSQL injection, XSS prevention
- **Audit Logging**: Security event logging, audit integrity, access control
- **Performance Security**: Rate limiting, DoS protection, resource exhaustion

## Test Files

### Core Test Suites

1. **`security-testing.spec.ts`**
   - Main security test suite for warehouse access control
   - Tests unauthorized access prevention
   - Validates role escalation prevention
   - Ensures cross-warehouse data isolation
   - Verifies security audit logging

2. **`attack-vector-tests.spec.ts`**
   - Specific attack pattern tests
   - JWT manipulation attacks
   - Parameter pollution
   - IDOR (Insecure Direct Object Reference) prevention
   - Session security tests

### Utilities and Infrastructure

3. **`security-test-utils.ts`**
   - Helper functions for security testing
   - Test data creation and cleanup
   - Token generation utilities
   - Injection payload generators

4. **`security-test-runner.ts`**
   - Orchestrates comprehensive security testing
   - Generates security reports
   - Calculates security scores
   - Provides recommendations

## Running Security Tests

### Individual Test Suites

```bash
# Run main security tests
npm test -- security-testing.spec.ts

# Run attack vector tests
npm test -- attack-vector-tests.spec.ts

# Run all security tests
npm test -- --testPathPattern=security
```

### Comprehensive Security Testing

```typescript
import { SecurityTestRunner } from './security-test-runner';

// Run complete security test suite
const runner = new SecurityTestRunner(moduleFixture);
const report = await runner.runAllTests();

console.log(`Security Score: ${report.summary.securityScore}%`);
console.log(`Pass Rate: ${report.summary.passRate}%`);
```

## Test Categories

### 1. Authentication Security Tests

**JWT Manipulation Prevention**
- Modified payload detection
- Signature validation
- Algorithm confusion attacks
- Token structure validation

**Token Security**
- Expiration enforcement
- Logout token invalidation
- Concurrent session handling
- Token fixation prevention

**Brute Force Protection**
- Login attempt rate limiting
- Account lockout mechanisms
- Progressive delays
- IP-based restrictions

### 2. Authorization Security Tests

**Role Escalation Prevention**
- Warehouse member → manager escalation
- Manager → tenant admin escalation
- Role modification attempts
- Permission boundary enforcement

**Warehouse Access Control**
- Unauthorized warehouse access
- Cross-warehouse data access
- Warehouse-specific operations
- Entity-based access validation

**Cross-Tenant Isolation**
- Tenant data separation
- Cross-tenant resource access
- Tenant context validation
- Multi-tenant security boundaries

### 3. Data Isolation Tests

**IDOR Prevention**
- Direct object reference attacks
- ID enumeration attempts
- Predictable ID exploitation
- Bulk operation IDOR

**Data Leakage Prevention**
- Information disclosure
- Error message leakage
- Timing attack prevention
- Side-channel information

**Bulk Operation Security**
- Cross-warehouse bulk operations
- Unauthorized bulk access
- Bulk permission validation
- Resource scope enforcement

### 4. Injection Attack Tests

**SQL Injection Prevention**
- Query parameter injection
- Search field injection
- Filter injection
- Stored procedure injection

**NoSQL Injection Prevention**
- MongoDB operator injection
- JSON injection attacks
- Query manipulation
- Aggregation pipeline injection

**XSS Prevention**
- Stored XSS
- Reflected XSS
- DOM-based XSS
- Content Security Policy validation

### 5. Security Audit Tests

**Event Logging Verification**
- Authentication events
- Authorization failures
- Permission violations
- Security configuration changes

**Audit Log Integrity**
- Log tampering prevention
- Log completeness
- Timestamp accuracy
- Event correlation

**Audit Access Control**
- Log access restrictions
- Audit viewer permissions
- Log export controls
- Retention policy enforcement

### 6. Performance Security Tests

**Rate Limiting**
- API request limits
- User-specific limits
- Endpoint-specific limits
- Burst protection

**DoS Protection**
- Request flooding
- Resource exhaustion
- Memory consumption
- CPU utilization

**Resource Management**
- Connection limits
- Query complexity limits
- File upload limits
- Concurrent operation limits

## Security Test Data

### Test Users

The security tests create users with different roles and access levels:

```typescript
// Tenant Admin - Full access
const tenantAdmin = {
  role: Role.TENANT_ADMIN,
  tenantId: 'test-tenant',
  warehouseAccess: 'ALL'
};

// Warehouse Manager - Warehouse-specific management
const warehouseManager = {
  role: Role.WAREHOUSE_MANAGER,
  tenantId: 'test-tenant',
  warehouseAccess: ['warehouse-1', 'warehouse-2']
};

// Warehouse Member - Limited operational access
const warehouseMember = {
  role: Role.WAREHOUSE_MEMBER,
  tenantId: 'test-tenant',
  warehouseAccess: ['warehouse-1']
};

// Unauthorized User - Different tenant
const unauthorizedUser = {
  role: Role.WAREHOUSE_MEMBER,
  tenantId: 'different-tenant',
  warehouseAccess: []
};
```

### Test Warehouses

```typescript
const testWarehouses = {
  warehouse1: { tenantId: 'test-tenant' },
  warehouse2: { tenantId: 'test-tenant' },
  crossTenantWarehouse: { tenantId: 'different-tenant' }
};
```

## Security Assertions

### Common Security Checks

```typescript
// Verify unauthorized access is denied
expect(response.status).toBe(403);
expect(response.body.message).toContain('Access denied');

// Verify security audit log was created
const auditLog = await verifySecurityAuditLog(
  'WAREHOUSE_ACCESS_DENIED',
  userId,
  warehouseId
);
expect(auditLog).toBeTruthy();

// Verify data isolation
pallets.forEach(pallet => {
  expect(pallet.warehouseId).toBe(authorizedWarehouseId);
});
```

### Security Score Calculation

The security test runner calculates a comprehensive security score:

- **Critical Tests (70% weight)**: JWT, Authorization, Cross-Tenant
- **General Tests (30% weight)**: Other security measures
- **Score Range**: 0-100%
- **Recommendations**: Generated based on failed tests

## Best Practices

### Test Organization

1. **Isolation**: Each test should be independent
2. **Cleanup**: Always clean up test data
3. **Realistic**: Use realistic attack scenarios
4. **Comprehensive**: Cover all security boundaries

### Security Validation

1. **Positive Tests**: Verify authorized access works
2. **Negative Tests**: Verify unauthorized access fails
3. **Edge Cases**: Test boundary conditions
4. **Error Handling**: Verify secure error responses

### Continuous Security Testing

1. **CI/CD Integration**: Run security tests in pipeline
2. **Regular Updates**: Update tests for new features
3. **Threat Modeling**: Base tests on threat models
4. **Security Reviews**: Regular security test reviews

## Troubleshooting

### Common Issues

**Test Data Conflicts**
```bash
# Clean up test data manually
npm run test:security:cleanup
```

**Authentication Failures**
```bash
# Verify JWT configuration
npm run test:auth:verify
```

**Database Connection Issues**
```bash
# Reset test database
npm run test:db:reset
```

### Debug Mode

Enable detailed security test logging:

```typescript
process.env.SECURITY_TEST_DEBUG = 'true';
```

## Security Compliance

This test suite helps ensure compliance with:

- **OWASP Top 10** security risks
- **ISO 27001** security controls
- **SOC 2** security requirements
- **GDPR** data protection requirements

## Contributing

When adding new security tests:

1. Follow existing test patterns
2. Include both positive and negative cases
3. Add appropriate documentation
4. Update this README if needed
5. Ensure tests are deterministic and reliable

## Security Contact

For security-related questions or concerns about the test suite:

- Review security documentation
- Check existing test patterns
- Follow secure coding guidelines
- Report security issues through proper channels
