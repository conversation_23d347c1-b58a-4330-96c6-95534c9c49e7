import { JwtService } from "@nestjs/jwt";
import { PrismaService } from "../../prisma/prisma.service";
import { Role, User, Warehouse, WarehouseUser } from "@prisma/client";
import { EnhancedUserPayload } from "../types";

/**
 * Security Testing Utilities
 *
 * Provides helper functions and test data setup for security testing
 */
export class SecurityTestUtils {
  constructor(
    private readonly prisma: PrismaService,
    private readonly jwtService: JwtService
  ) {}

  /**
   * Create test users with different roles and warehouse access
   */
  async createTestUsers() {
    const testTenantId = "test-tenant-security";
    const altTenantId = "alt-tenant-security";

    // Create tenant admin
    const tenantAdmin = await this.prisma.user.create({
      data: {
        email: "<EMAIL>",
        name: "Security Test Admin",
        password: "hashed-password",
        authUserId: "auth-admin-123",
        tenantId: testTenantId,
        role: Role.TENANT_ADMIN,
        status: "ACTIVE",
      },
    });

    // Create warehouse manager
    const warehouseManager = await this.prisma.user.create({
      data: {
        email: "<EMAIL>",
        name: "Security Test Manager",
        password: "hashed-password",
        authUserId: "auth-manager-123",
        tenantId: testTenantId,
        role: Role.WAREHOUSE_MANAGER,
        status: "ACTIVE",
      },
    });

    // Create warehouse member
    const warehouseMember = await this.prisma.user.create({
      data: {
        email: "<EMAIL>",
        name: "Security Test Member",
        password: "hashed-password",
        authUserId: "auth-member-123",
        tenantId: testTenantId,
        role: Role.WAREHOUSE_MEMBER,
        status: "ACTIVE",
      },
    });

    // Create unauthorized user (different tenant)
    const unauthorizedUser = await this.prisma.user.create({
      data: {
        email: "<EMAIL>",
        name: "Unauthorized User",
        password: "hashed-password",
        authUserId: "auth-unauthorized-123",
        tenantId: altTenantId,
        role: Role.WAREHOUSE_MEMBER,
        status: "ACTIVE",
      },
    });

    return {
      tenantAdmin,
      warehouseManager,
      warehouseMember,
      unauthorizedUser,
      testTenantId,
      altTenantId,
    };
  }

  /**
   * Create test warehouses for security testing
   */
  async createTestWarehouses(testTenantId: string, altTenantId: string) {
    const warehouse1 = await this.prisma.warehouse.create({
      data: {
        name: "Security Test Warehouse 1",
        address: "123 Security Test St",
        tenantId: testTenantId,
      },
    });

    const warehouse2 = await this.prisma.warehouse.create({
      data: {
        name: "Security Test Warehouse 2",
        address: "456 Security Test Ave",
        tenantId: testTenantId,
      },
    });

    const crossTenantWarehouse = await this.prisma.warehouse.create({
      data: {
        name: "Cross Tenant Warehouse",
        address: "789 Cross Tenant Blvd",
        tenantId: altTenantId,
      },
    });

    return {
      warehouse1,
      warehouse2,
      crossTenantWarehouse,
    };
  }

  /**
   * Setup warehouse user relationships
   */
  async setupWarehouseAccess(
    users: { warehouseManager: User; warehouseMember: User },
    warehouses: { warehouse1: Warehouse; warehouse2: Warehouse }
  ) {
    // Manager has access to warehouse1
    await this.prisma.warehouseUser.create({
      data: {
        userId: users.warehouseManager.id,
        warehouseId: warehouses.warehouse1.id,
        role: Role.WAREHOUSE_MANAGER,
      },
    });

    // Member has access to warehouse1 only
    await this.prisma.warehouseUser.create({
      data: {
        userId: users.warehouseMember.id,
        warehouseId: warehouses.warehouse1.id,
        role: Role.WAREHOUSE_MEMBER,
      },
    });

    // Manager also has access to warehouse2
    await this.prisma.warehouseUser.create({
      data: {
        userId: users.warehouseManager.id,
        warehouseId: warehouses.warehouse2.id,
        role: Role.WAREHOUSE_MANAGER,
      },
    });
  }

  /**
   * Create test pallets for security testing
   */
  async createTestPallets(
    warehouses: {
      warehouse1: Warehouse;
      warehouse2: Warehouse;
      crossTenantWarehouse: Warehouse;
    },
    users: { warehouseMember: User }
  ) {
    // First create locations in each warehouse
    const location1 = await this.prisma.location.create({
      data: {
        name: "Security Test Location 1",
        category: "Storage",
        locationType: "RACK",
        warehouseId: warehouses.warehouse1.id,
      },
    });

    const location2 = await this.prisma.location.create({
      data: {
        name: "Security Test Location 2",
        category: "Storage",
        locationType: "RACK",
        warehouseId: warehouses.warehouse2.id,
      },
    });

    const crossTenantLocation = await this.prisma.location.create({
      data: {
        name: "Cross Tenant Location",
        category: "Storage",
        locationType: "RACK",
        warehouseId: warehouses.crossTenantWarehouse.id,
      },
    });

    // Create pallets in warehouse1
    const pallet1 = await this.prisma.pallet.create({
      data: {
        label: "SEC-TEST-001",
        barcode: "SEC-TEST-001",
        description: "Security Test Pallet 1",
        locationId: location1.id,
      },
    });

    // Create pallets in warehouse2
    const pallet2 = await this.prisma.pallet.create({
      data: {
        label: "SEC-TEST-002",
        barcode: "SEC-TEST-002",
        description: "Security Test Pallet 2",
        locationId: location2.id,
      },
    });

    // Create pallet in cross-tenant warehouse
    const crossTenantPallet = await this.prisma.pallet.create({
      data: {
        label: "CROSS-TENANT-001",
        barcode: "CROSS-TENANT-001",
        description: "Cross Tenant Pallet",
        locationId: crossTenantLocation.id,
      },
    });

    return {
      pallet1,
      pallet2,
      crossTenantPallet,
      location1,
      location2,
      crossTenantLocation,
    };
  }

  /**
   * Generate JWT token for a user
   */
  generateTokenForUser(user: User, warehouseAccess?: WarehouseUser[]): string {
    const payload = {
      userId: user.id,
      tenantId: user.tenantId,
      email: user.email,
      role: user.role,
      warehouseAccess:
        warehouseAccess?.map((wa) => ({
          warehouseId: wa.warehouseId,
          role: wa.role,
        })) || [],
    };

    return this.jwtService.sign(payload);
  }

  /**
   * Generate malicious JWT tokens for testing
   */
  generateMaliciousTokens() {
    return {
      // Token with escalated role
      escalatedRole: this.jwtService.sign({
        userId: "user-123",
        tenantId: "tenant-123",
        role: Role.TENANT_ADMIN, // Escalated from member
      }),

      // Token with different tenant
      crossTenant: this.jwtService.sign({
        userId: "user-123",
        tenantId: "different-tenant",
        role: Role.WAREHOUSE_MEMBER,
      }),

      // Expired token
      expired: this.jwtService.sign(
        {
          userId: "user-123",
          tenantId: "tenant-123",
          role: Role.WAREHOUSE_MEMBER,
        },
        { expiresIn: "-1h" }
      ),

      // Token with invalid structure
      malformed: "invalid.jwt.token",

      // Empty token
      empty: "",
    };
  }

  /**
   * Generate test payloads for injection attacks
   */
  generateInjectionPayloads() {
    return {
      sql: [
        "'; DROP TABLE users; --",
        "' OR '1'='1",
        "'; UPDATE users SET role='TENANT_ADMIN' WHERE id='user-123'; --",
        "' UNION SELECT * FROM users WHERE role='TENANT_ADMIN' --",
      ],
      nosql: [
        "{ $ne: null }",
        "{ $where: 'this.role == \"TENANT_ADMIN\"' }",
        "'; return true; //",
      ],
      xss: [
        "<script>alert('xss')</script>",
        "javascript:alert('xss')",
        "<img src=x onerror=alert('xss')>",
      ],
      pathTraversal: [
        "../../../etc/passwd",
        "..\\..\\..\\windows\\system32\\config\\sam",
        "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
      ],
    };
  }

  /**
   * Clean up all test data
   */
  async cleanupTestData() {
    // Delete in reverse order of dependencies
    await this.prisma.pallet.deleteMany({
      where: {
        barcode: {
          startsWith: "SEC-TEST-",
        },
      },
    });

    await this.prisma.pallet.deleteMany({
      where: {
        barcode: {
          startsWith: "CROSS-TENANT-",
        },
      },
    });

    await this.prisma.warehouseUser.deleteMany({
      where: {
        user: {
          email: {
            endsWith: "@security-test.com",
          },
        },
      },
    });

    await this.prisma.warehouse.deleteMany({
      where: {
        name: {
          startsWith: "Security Test",
        },
      },
    });

    await this.prisma.warehouse.deleteMany({
      where: {
        name: "Cross Tenant Warehouse",
      },
    });

    await this.prisma.user.deleteMany({
      where: {
        email: {
          endsWith: "@security-test.com",
        },
      },
    });

    await this.prisma.user.deleteMany({
      where: {
        email: "<EMAIL>",
      },
    });
  }

  /**
   * Verify security audit logs were created
   */
  async verifySecurityAuditLog(
    action: string,
    userId?: string,
    warehouseId?: string
  ): Promise<boolean> {
    const auditLog = await this.prisma.auditLog.findFirst({
      where: {
        action,
        userId,
        details: warehouseId
          ? {
              path: ["warehouseId"],
              equals: warehouseId,
            }
          : undefined,
      },
      orderBy: {
        timestamp: "desc",
      },
    });

    return !!auditLog;
  }

  /**
   * Create enhanced user payload for testing
   */
  createEnhancedUserPayload(
    user: User,
    warehouseAccess: WarehouseUser[] = []
  ): EnhancedUserPayload {
    const accessibleWarehouses = warehouseAccess.map((wa) => wa.warehouseId);
    const warehouseRoles = new Map(
      warehouseAccess.map((wa) => [wa.warehouseId, wa.role])
    );

    return {
      ...user,
      accessibleWarehouses,
      warehouseRoles,
      warehouseUsers: warehouseAccess,
      isTenantAdmin: () => user.role === Role.TENANT_ADMIN,
      canAccessWarehouse: (warehouseId: string) =>
        user.role === Role.TENANT_ADMIN ||
        accessibleWarehouses.includes(warehouseId),
      getWarehouseRole: (warehouseId: string) =>
        warehouseRoles.get(warehouseId) || null,
      hasWarehousePermission: (warehouseId: string, requiredRole: Role) => {
        if (user.role === Role.TENANT_ADMIN) return true;
        const userRole = warehouseRoles.get(warehouseId);
        if (!userRole) return false;

        const roleHierarchy = [
          Role.WAREHOUSE_MEMBER,
          Role.WAREHOUSE_MANAGER,
          Role.TENANT_ADMIN,
        ];
        const userRoleIndex = roleHierarchy.indexOf(userRole);
        const requiredRoleIndex = roleHierarchy.indexOf(requiredRole);

        return userRoleIndex >= requiredRoleIndex;
      },
    } as EnhancedUserPayload;
  }
}
