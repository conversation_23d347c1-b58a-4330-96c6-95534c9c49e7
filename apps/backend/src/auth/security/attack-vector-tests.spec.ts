import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { PrismaService } from "../../prisma/prisma.service";
import { Role } from "@prisma/client";
import request from "supertest";

/**
 * Security Tests for Common Attack Vectors
 *
 * Tests specific attack patterns and security vulnerabilities:
 * - JWT manipulation attacks
 * - Parameter pollution
 * - SQL injection attempts
 * - Authorization bypass attempts
 * - Session fixation
 * - IDOR (Insecure Direct Object Reference)
 */
describe("Attack Vector Security Tests", () => {
  let app: INestApplication;
  let jwtService: JwtService;
  let prisma: PrismaService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      // Import your actual app module here
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    jwtService = moduleFixture.get<JwtService>(JwtService);
    prisma = moduleFixture.get<PrismaService>(PrismaService);
  });

  afterAll(async () => {
    await app.close();
  });

  describe("JWT Manipulation Attacks", () => {
    it("should reject JWT with modified payload", async () => {
      // Create a valid token
      const validToken = jwtService.sign({
        userId: "user-123",
        tenantId: "tenant-123",
        role: Role.WAREHOUSE_MEMBER,
      });

      // Manually modify the payload (this would normally break the signature)
      const [header, payload, signature] = validToken.split(".");
      const decodedPayload = JSON.parse(
        Buffer.from(payload, "base64").toString()
      );
      decodedPayload.role = Role.TENANT_ADMIN; // Escalate role
      const modifiedPayload = Buffer.from(
        JSON.stringify(decodedPayload)
      ).toString("base64");
      const modifiedToken = `${header}.${modifiedPayload}.${signature}`;

      await request(app.getHttpServer())
        .get("/api/warehouses")
        .set("Authorization", `Bearer ${modifiedToken}`)
        .expect(401); // Should reject due to invalid signature
    });

    it("should reject JWT with modified tenant ID", async () => {
      const validToken = jwtService.sign({
        userId: "user-123",
        tenantId: "tenant-123",
        role: Role.WAREHOUSE_MEMBER,
      });

      const [header, payload, signature] = validToken.split(".");
      const decodedPayload = JSON.parse(
        Buffer.from(payload, "base64").toString()
      );
      decodedPayload.tenantId = "different-tenant"; // Change tenant
      const modifiedPayload = Buffer.from(
        JSON.stringify(decodedPayload)
      ).toString("base64");
      const modifiedToken = `${header}.${modifiedPayload}.${signature}`;

      await request(app.getHttpServer())
        .get("/api/warehouses")
        .set("Authorization", `Bearer ${modifiedToken}`)
        .expect(401);
    });

    it("should reject JWT with none algorithm", async () => {
      // Create a token with "none" algorithm (security vulnerability)
      const noneHeader = Buffer.from(
        JSON.stringify({ alg: "none", typ: "JWT" })
      ).toString("base64");
      const payload = Buffer.from(
        JSON.stringify({
          userId: "user-123",
          tenantId: "tenant-123",
          role: Role.TENANT_ADMIN,
        })
      ).toString("base64");
      const noneToken = `${noneHeader}.${payload}.`;

      await request(app.getHttpServer())
        .get("/api/warehouses")
        .set("Authorization", `Bearer ${noneToken}`)
        .expect(401);
    });
  });

  describe("Parameter Pollution Attacks", () => {
    it("should handle duplicate warehouse ID parameters safely", async () => {
      const token = generateValidToken();

      // Send multiple warehouseId parameters
      await request(app.getHttpServer())
        .get("/api/pallets")
        .set("Authorization", `Bearer ${token}`)
        .query("warehouseId=warehouse-1&warehouseId=warehouse-2")
        .expect(400); // Should reject ambiguous parameters
    });

    it("should sanitize array parameters", async () => {
      const token = generateValidToken();

      await request(app.getHttpServer())
        .post("/api/pallets/bulk-update")
        .set("Authorization", `Bearer ${token}`)
        .send({
          palletIds: ["valid-id", null, undefined, "", "another-valid-id"],
          updates: { status: "SHIPPED" },
        })
        .expect(400); // Should reject invalid array elements
    });

    it("should prevent SQL injection in query parameters", async () => {
      const token = generateValidToken();

      // Attempt SQL injection through search parameter
      await request(app.getHttpServer())
        .get("/api/pallets")
        .set("Authorization", `Bearer ${token}`)
        .query({ search: "'; DROP TABLE pallets; --" })
        .expect(400); // Should sanitize or reject malicious input
    });
  });

  describe("Authorization Bypass Attempts", () => {
    it("should prevent header injection attacks", async () => {
      const token = generateValidToken();

      await request(app.getHttpServer())
        .get("/api/warehouses/warehouse-123/pallets")
        .set("Authorization", `Bearer ${token}`)
        .set("X-Warehouse-Id", "different-warehouse") // Attempt to override warehouse context
        .expect(403); // Should use proper authorization, not headers
    });

    it("should prevent method override attacks", async () => {
      const token = generateValidToken();

      // Attempt to override HTTP method to bypass restrictions
      await request(app.getHttpServer())
        .post("/api/warehouses/warehouse-123/pallets")
        .set("Authorization", `Bearer ${token}`)
        .set("X-HTTP-Method-Override", "DELETE")
        .expect(405); // Should not allow method override for security operations
    });

    it("should prevent path traversal in warehouse ID", async () => {
      const token = generateValidToken();

      await request(app.getHttpServer())
        .get("/api/warehouses/../admin/users")
        .set("Authorization", `Bearer ${token}`)
        .expect(404); // Should normalize path and reject traversal
    });
  });

  describe("IDOR (Insecure Direct Object Reference) Prevention", () => {
    it("should prevent access to other tenant resources by ID guessing", async () => {
      const token = generateValidToken();

      // Try to access resources with sequential IDs
      const suspiciousIds = [
        "warehouse-000001",
        "warehouse-000002",
        "pallet-123456",
        "user-admin-001",
      ];

      for (const id of suspiciousIds) {
        await request(app.getHttpServer())
          .get(`/api/warehouses/${id}`)
          .set("Authorization", `Bearer ${token}`)
          .expect(403); // Should deny access to unauthorized resources
      }
    });

    it("should prevent UUID enumeration attacks", async () => {
      const token = generateValidToken();

      // Generate predictable UUIDs and try to access them
      const predictableUuids = [
        "00000000-0000-0000-0000-000000000001",
        "11111111-1111-1111-1111-111111111111",
        "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa",
      ];

      for (const uuid of predictableUuids) {
        await request(app.getHttpServer())
          .get(`/api/pallets/${uuid}`)
          .set("Authorization", `Bearer ${token}`)
          .expect(404); // Should return 404 for non-existent resources
      }
    });

    it("should prevent bulk operation IDOR attacks", async () => {
      const token = generateValidToken();

      await request(app.getHttpServer())
        .delete("/api/pallets/bulk")
        .set("Authorization", `Bearer ${token}`)
        .send({
          palletIds: [
            "user-owned-pallet-1",
            "other-tenant-pallet-1", // Attempt to delete other tenant's pallet
            "admin-pallet-1", // Attempt to delete admin pallet
          ],
        })
        .expect(403); // Should reject unauthorized bulk operations
    });
  });

  describe("Session and Token Security", () => {
    it("should prevent token reuse after logout", async () => {
      const token = generateValidToken();

      // First, logout
      await request(app.getHttpServer())
        .post("/api/auth/logout")
        .set("Authorization", `Bearer ${token}`)
        .expect(200);

      // Then try to use the same token
      await request(app.getHttpServer())
        .get("/api/warehouses")
        .set("Authorization", `Bearer ${token}`)
        .expect(401); // Should reject the token after logout
    });

    it("should prevent concurrent session attacks", async () => {
      const userCredentials = {
        email: "<EMAIL>",
        password: "password123",
      };

      // Login from first "device"
      const response1 = await request(app.getHttpServer())
        .post("/api/auth/login")
        .send(userCredentials)
        .expect(200);

      const token1 = response1.body.token;

      // Login from second "device"
      const response2 = await request(app.getHttpServer())
        .post("/api/auth/login")
        .send(userCredentials)
        .expect(200);

      const token2 = response2.body.token;

      // Both tokens should be valid (or implement session limit if required)
      await request(app.getHttpServer())
        .get("/api/warehouses")
        .set("Authorization", `Bearer ${token1}`)
        .expect(200);

      await request(app.getHttpServer())
        .get("/api/warehouses")
        .set("Authorization", `Bearer ${token2}`)
        .expect(200);
    });

    it("should prevent token fixation attacks", async () => {
      // Attempt to use a pre-generated token for login
      const fixedToken = "fixed-token-value";

      await request(app.getHttpServer())
        .post("/api/auth/login")
        .set("Authorization", `Bearer ${fixedToken}`)
        .send({
          email: "<EMAIL>",
          password: "password123",
        })
        .expect(400); // Should not accept pre-existing tokens for login
    });
  });

  describe("Rate Limiting and Brute Force Protection", () => {
    it("should rate limit login attempts", async () => {
      const invalidCredentials = {
        email: "<EMAIL>",
        password: "wrongpassword",
      };

      // Make multiple failed login attempts
      const attempts = Array(10).fill(null);
      const responses = await Promise.all(
        attempts.map(() =>
          request(app.getHttpServer())
            .post("/api/auth/login")
            .send(invalidCredentials)
        )
      );

      // Later attempts should be rate limited
      const rateLimitedResponses = responses.slice(-3);
      rateLimitedResponses.forEach((response) => {
        expect([401, 429]).toContain(response.status); // 429 = Too Many Requests
      });
    });

    it("should rate limit API requests per user", async () => {
      const token = generateValidToken();

      // Make many requests rapidly
      const requests = Array(50).fill(null);
      const responses = await Promise.all(
        requests.map(() =>
          request(app.getHttpServer())
            .get("/api/warehouses")
            .set("Authorization", `Bearer ${token}`)
        )
      );

      // Some requests should be rate limited
      const rateLimitedCount = responses.filter((r) => r.status === 429).length;
      expect(rateLimitedCount).toBeGreaterThan(0);
    });
  });

  // Helper function
  function generateValidToken(): string {
    return jwtService.sign({
      userId: "test-user-123",
      tenantId: "test-tenant-123",
      email: "<EMAIL>",
      role: Role.WAREHOUSE_MEMBER,
    });
  }
});
