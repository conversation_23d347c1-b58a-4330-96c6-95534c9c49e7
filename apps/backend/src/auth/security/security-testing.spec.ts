import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { PrismaService } from "../../prisma/prisma.service";
import { AuthService } from "../auth.service";
import { WarehousePermissionGuard } from "../guards/warehouse-permission.guard";
import { RoleBasedGuard } from "../guards/role-based.guard";
import { SecurityAuditService } from "../../audit-log/services/security-audit.service";
import { Role, User, Warehouse, WarehouseUser } from "@prisma/client";
import { EnhancedUserPayload } from "../types";
import request from "supertest";

/**
 * Comprehensive Security Testing Suite for Warehouse Access Control
 *
 * Tests cover:
 * - Unauthorized access attempts
 * - Role escalation prevention
 * - Cross-warehouse data leakage prevention
 * - Permission boundary enforcement
 * - Security audit logging verification
 */
describe("Security Testing Suite - Warehouse Access Control", () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let jwtService: JwtService;
  let authService: AuthService;
  let securityAuditService: SecurityAuditService;

  // Test data
  let tenantAdmin: User;
  let warehouseManager: User;
  let warehouseMember: User;
  let unauthorizedUser: User;
  let warehouse1: Warehouse;
  let warehouse2: Warehouse;
  let crossTenantWarehouse: Warehouse;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      // Import your actual app module here
      // imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    prisma = moduleFixture.get<PrismaService>(PrismaService);
    jwtService = moduleFixture.get<JwtService>(JwtService);
    authService = moduleFixture.get<AuthService>(AuthService);
    securityAuditService =
      moduleFixture.get<SecurityAuditService>(SecurityAuditService);

    // Setup test data
    await setupTestData();
  });

  afterAll(async () => {
    await cleanupTestData();
    await app.close();
  });

  describe("Unauthorized Access Prevention", () => {
    it("should deny access to users without warehouse permissions", async () => {
      const token = generateTokenForUser(unauthorizedUser);

      const response = await request(app.getHttpServer())
        .get(`/api/warehouses/${warehouse1.id}/pallets`)
        .set("Authorization", `Bearer ${token}`)
        .expect(403);

      expect(response.body.message).toContain("Access denied");

      // Verify security audit log
      // Note: In real implementation, you'd verify the audit log was created
    });

    it("should deny access to non-existent warehouse", async () => {
      const token = generateTokenForUser(warehouseMember);
      const nonExistentWarehouseId = "non-existent-warehouse-id";

      await request(app.getHttpServer())
        .get(`/api/warehouses/${nonExistentWarehouseId}/pallets`)
        .set("Authorization", `Bearer ${token}`)
        .expect(404);
    });

    it("should deny access without authentication token", async () => {
      await request(app.getHttpServer())
        .get(`/api/warehouses/${warehouse1.id}/pallets`)
        .expect(401);
    });

    it("should deny access with invalid token", async () => {
      await request(app.getHttpServer())
        .get(`/api/warehouses/${warehouse1.id}/pallets`)
        .set("Authorization", "Bearer invalid-token")
        .expect(401);
    });

    it("should deny access with expired token", async () => {
      const expiredToken = jwtService.sign(
        { userId: warehouseMember.id, tenantId: warehouseMember.tenantId },
        { expiresIn: "-1h" } // Expired 1 hour ago
      );

      await request(app.getHttpServer())
        .get(`/api/warehouses/${warehouse1.id}/pallets`)
        .set("Authorization", `Bearer ${expiredToken}`)
        .expect(401);
    });
  });

  describe("Role Escalation Prevention", () => {
    it("should prevent warehouse member from performing manager operations", async () => {
      const token = generateTokenForUser(warehouseMember);

      // Try to delete a warehouse (manager-only operation)
      await request(app.getHttpServer())
        .delete(`/api/warehouses/${warehouse1.id}`)
        .set("Authorization", `Bearer ${token}`)
        .expect(403);
    });

    it("should prevent warehouse member from modifying user roles", async () => {
      const token = generateTokenForUser(warehouseMember);

      await request(app.getHttpServer())
        .put(
          `/api/warehouses/${warehouse1.id}/users/${warehouseMember.id}/role`
        )
        .set("Authorization", `Bearer ${token}`)
        .send({ role: Role.WAREHOUSE_MANAGER })
        .expect(403);
    });

    it("should prevent warehouse manager from accessing tenant-admin operations", async () => {
      const token = generateTokenForUser(warehouseManager);

      // Try to create a new warehouse (tenant-admin only)
      await request(app.getHttpServer())
        .post("/api/warehouses")
        .set("Authorization", `Bearer ${token}`)
        .send({
          name: "Unauthorized Warehouse",
          address: "123 Test St",
        })
        .expect(403);
    });

    it("should allow tenant admin to access all operations", async () => {
      const token = generateTokenForUser(tenantAdmin);

      // Tenant admin should be able to access warehouse operations
      await request(app.getHttpServer())
        .get(`/api/warehouses/${warehouse1.id}/pallets`)
        .set("Authorization", `Bearer ${token}`)
        .expect(200);
    });
  });

  describe("Cross-Warehouse Data Leakage Prevention", () => {
    it("should prevent access to pallets from unauthorized warehouse", async () => {
      const token = generateTokenForUser(warehouseMember); // Has access to warehouse1 only

      await request(app.getHttpServer())
        .get(`/api/warehouses/${warehouse2.id}/pallets`)
        .set("Authorization", `Bearer ${token}`)
        .expect(403);
    });

    it("should prevent moving pallets between unauthorized warehouses", async () => {
      const token = generateTokenForUser(warehouseMember);

      // Create a pallet in warehouse1
      const palletResponse = await request(app.getHttpServer())
        .post(`/api/warehouses/${warehouse1.id}/pallets`)
        .set("Authorization", `Bearer ${token}`)
        .send({
          barcode: "TEST-PALLET-001",
          description: "Test Pallet",
        })
        .expect(201);

      const palletId = palletResponse.body.id;

      // Try to move it to warehouse2 (unauthorized)
      await request(app.getHttpServer())
        .put(`/api/pallets/${palletId}/move`)
        .set("Authorization", `Bearer ${token}`)
        .send({
          warehouseId: warehouse2.id,
          locationId: "some-location-in-warehouse2",
        })
        .expect(403);
    });

    it("should prevent cross-tenant data access", async () => {
      const token = generateTokenForUser(warehouseMember);

      await request(app.getHttpServer())
        .get(`/api/warehouses/${crossTenantWarehouse.id}/pallets`)
        .set("Authorization", `Bearer ${token}`)
        .expect(403);
    });

    it("should isolate warehouse data in list operations", async () => {
      const token = generateTokenForUser(warehouseMember);

      const response = await request(app.getHttpServer())
        .get("/api/pallets") // List all pallets user has access to
        .set("Authorization", `Bearer ${token}`)
        .expect(200);

      // Verify only warehouse1 pallets are returned
      const pallets = response.body;
      pallets.forEach((pallet: any) => {
        expect(pallet.warehouseId).toBe(warehouse1.id);
      });
    });
  });

  describe("Permission Boundary Enforcement", () => {
    it("should enforce warehouse-specific permissions for bulk operations", async () => {
      const token = generateTokenForUser(warehouseMember);

      // Try to perform bulk operation across multiple warehouses
      await request(app.getHttpServer())
        .post("/api/pallets/bulk-update")
        .set("Authorization", `Bearer ${token}`)
        .send({
          palletIds: ["pallet-in-warehouse1", "pallet-in-warehouse2"],
          updates: { status: "SHIPPED" },
        })
        .expect(403);
    });

    it("should validate warehouse context in nested resource access", async () => {
      const token = generateTokenForUser(warehouseMember);

      // Try to access location in unauthorized warehouse
      await request(app.getHttpServer())
        .get(`/api/warehouses/${warehouse2.id}/locations/some-location-id`)
        .set("Authorization", `Bearer ${token}`)
        .expect(403);
    });

    it("should prevent privilege escalation through parameter manipulation", async () => {
      const token = generateTokenForUser(warehouseMember);

      // Try to manipulate tenantId in request
      await request(app.getHttpServer())
        .get(`/api/warehouses/${warehouse1.id}/pallets`)
        .set("Authorization", `Bearer ${token}`)
        .query({ tenantId: "different-tenant-id" })
        .expect(403);
    });
  });

  describe("Security Audit Logging Verification", () => {
    it("should log failed authentication attempts", async () => {
      const logSpy = jest.spyOn(securityAuditService, "logAuthenticationEvent");

      await request(app.getHttpServer())
        .post("/api/auth/login")
        .send({
          email: "<EMAIL>",
          password: "wrongpassword",
        })
        .expect(401);

      expect(logSpy).toHaveBeenCalledWith(
        "LOGIN_FAILURE",
        expect.any(Object),
        expect.any(Object),
        expect.objectContaining({
          reason: expect.stringContaining("Invalid credentials"),
        })
      );
    });

    it("should log permission violations", async () => {
      const logSpy = jest.spyOn(securityAuditService, "logPermissionViolation");
      const token = generateTokenForUser(warehouseMember);

      await request(app.getHttpServer())
        .delete(`/api/warehouses/${warehouse1.id}`)
        .set("Authorization", `Bearer ${token}`)
        .expect(403);

      expect(logSpy).toHaveBeenCalledWith(
        "INSUFFICIENT_ROLE",
        expect.any(Object),
        expect.any(Object),
        expect.objectContaining({
          requiredRole: Role.WAREHOUSE_MANAGER,
          userRole: Role.WAREHOUSE_MEMBER,
        })
      );
    });

    it("should log cross-warehouse access attempts", async () => {
      const logSpy = jest.spyOn(
        securityAuditService,
        "logWarehouseAccessDenied"
      );
      const token = generateTokenForUser(warehouseMember);

      await request(app.getHttpServer())
        .get(`/api/warehouses/${warehouse2.id}/pallets`)
        .set("Authorization", `Bearer ${token}`)
        .expect(403);

      expect(logSpy).toHaveBeenCalledWith(
        warehouse2.id,
        expect.any(Object),
        expect.any(Object),
        expect.stringContaining("Access denied"),
        expect.any(Object)
      );
    });
  });

  // Helper functions
  async function setupTestData() {
    // Create test tenant and users
    // Implementation would create actual test data in database
    // This is a simplified version for demonstration
  }

  async function cleanupTestData() {
    // Clean up test data
    // Implementation would remove test data from database
  }

  function generateTokenForUser(user: User): string {
    return jwtService.sign({
      userId: user.id,
      tenantId: user.tenantId,
      email: user.email,
      role: user.role,
    });
  }
});
