import { Request } from "express";
import { User as <PERSON>rismaUser, WarehouseUser, Role } from "@prisma/client";

// Define the structure of the user object attached to the request
// This includes the fields selected in the JwtStrategy's prisma query
export interface UserPayload extends PrismaUser {
  warehouseUsers: { warehouseId: string; role: WarehouseUser["role"] }[];
}

// Extend the Express Request type to include the custom user payload
export interface AuthenticatedRequest extends Request {
  user: UserPayload;
}

// JWT Payload interface for warehouse-scoped access control
export interface JwtPayload {
  userId: string; // Internal user ID from our database
  sub: string; // JWT subject (same as userId for consistency)
  email: string;
  role: Role; // User's primary role
  tenantId: string | null; // Tenant ID for backward compatibility
  warehouseAccess?: WarehouseAccessInfo[]; // User's warehouse assignments
  iat?: number; // Issued at timestamp
  exp?: number; // Expiration timestamp
}

// Warehouse access information included in JWT payload
export interface WarehouseAccessInfo {
  warehouseId: string;
  role: Role; // User's role in this specific warehouse
  warehouseName?: string; // Optional warehouse name for convenience
}

// Enhanced user payload with warehouse context for request processing
export interface EnhancedUserPayload extends UserPayload {
  // Additional computed properties for warehouse context
  accessibleWarehouses: string[]; // Array of warehouse IDs user can access
  warehouseRoles: Map<string, Role>; // Map of warehouseId -> role for quick lookup
  canAccessWarehouse: (warehouseId: string) => boolean;
  getWarehouseRole: (warehouseId: string) => Role | null;
  hasWarehousePermission: (warehouseId: string, requiredRole: Role) => boolean;
  isTenantAdmin: () => boolean;
}

// Warehouse context information attached to requests
export interface WarehouseContext {
  warehouseId: string;
  userRole: Role;
  isAdmin: boolean;
  isManager: boolean;
  isMember: boolean;
}

// Request with warehouse context for warehouse-scoped operations
export interface RequestWithWarehouseContext<T = any> extends Request {
  user: EnhancedUserPayload;
  warehouseContext?: WarehouseContext;
}
