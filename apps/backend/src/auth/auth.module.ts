import { Module } from "@nestjs/common";
import { JwtModule } from "@nestjs/jwt";
import { PassportModule } from "@nestjs/passport";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { AuthService } from "./auth.service";
import { AuthController } from "./auth.controller";
import { JwtStrategy } from "./jwt.strategy";
import { jwtConstants } from "./constants";
import { PrismaModule } from "../prisma/prisma.module";
import { WarehousePermissionGuard } from "./guards/warehouse-permission.guard";
import {
  RoleBasedGuard,
  TenantAdminGuard,
  WarehouseManagerGuard,
  WarehouseMemberGuard,
  WarehouseSpecificRoleGuard,
} from "./guards/role-based.guard";
import { WarehousesModule } from "../warehouses/warehouses.module";
import { AuditLogModule } from "../audit-log/audit-log.module";

@Module({
  imports: [
    PrismaModule, // Needed by JwtStrategy
    PassportModule,
    ConfigModule, // Needed to read JWT_SECRET from .env
    WarehousesModule, // Needed for WarehouseValidationUtils
    AuditLogModule, // Needed for security audit services
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        // Use a dedicated test secret if running in test environment, otherwise use config
        secret:
          process.env.NODE_ENV === "test"
            ? process.env.TEST_JWT_SECRET // Expect this to be set for tests
            : configService.get<string>(jwtConstants.secretKeyName),
        signOptions: { expiresIn: "1d" }, // Example: Token expires in 1 day
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    JwtStrategy,
    WarehousePermissionGuard,
    RoleBasedGuard,
    TenantAdminGuard,
    WarehouseManagerGuard,
    WarehouseMemberGuard,
    WarehouseSpecificRoleGuard,
  ],
  exports: [
    AuthService,
    JwtModule,
    WarehousePermissionGuard,
    RoleBasedGuard,
    TenantAdminGuard,
    WarehouseManagerGuard,
    WarehouseMemberGuard,
    WarehouseSpecificRoleGuard,
  ],
})
export class AuthModule {}
