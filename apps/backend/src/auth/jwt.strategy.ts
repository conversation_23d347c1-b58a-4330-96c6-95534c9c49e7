import { ExtractJwt, Strategy } from "passport-jwt";
import { PassportStrategy } from "@nestjs/passport";
import { Injectable, UnauthorizedException, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { jwtConstants } from "./constants";
import { PrismaService } from "../prisma/prisma.service";
import { JwtPayload, EnhancedUserPayload } from "./types";
import { Role } from "@prisma/client";
import { AuthAuditService } from "../audit-log/services/auth-audit.service";

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor(
    private configService: ConfigService,
    private prisma: PrismaService,
    private authAuditService: AuthAuditService
  ) {
    // Determine the secret for strategy based on environment
    const strategySecret =
      process.env.NODE_ENV === "test"
        ? process.env.TEST_JWT_SECRET
        : configService.get<string>(jwtConstants.secretKeyName);

    if (!strategySecret) {
      // Log an error if the secret is missing
      const errorMsg =
        process.env.NODE_ENV === "test"
          ? "TEST_JWT_SECRET environment variable is not set for JwtStrategy in test mode."
          : `JWT secret key (${jwtConstants.secretKeyName}) is not set in environment variables.`;
      new Logger(JwtStrategy.name).error(errorMsg);
      throw new Error("JWT secret configuration error"); // Throw to prevent startup
    }

    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: strategySecret, // Use the determined secret
    });
    // Log the secret being used (optional, remove in production)
    this.logger.debug(
      `JwtStrategy initialized with secret source: ${
        process.env.NODE_ENV === "test" ? "TEST_ENV_VAR" : "ConfigService"
      }`
    );
  }

  async validate(payload: JwtPayload): Promise<EnhancedUserPayload> {
    // Extract user information from JWT payload
    const { userId, tenantId: jwtTenantId, warehouseAccess } = payload;

    if (!userId) {
      this.logger.warn("JWT payload missing userId.");
      await this.authAuditService.logTokenValidationFailure(
        "Invalid token: User identifier missing"
      );
      throw new UnauthorizedException(
        "Invalid token: User identifier missing."
      );
    }

    this.logger.debug(
      `Validating JWT payload for userId: ${userId}, tenantId: ${jwtTenantId}, warehouses: ${
        warehouseAccess?.length || 0
      }`
    );

    // Fetch user from database with current warehouse assignments
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        warehouseUsers: {
          select: {
            warehouseId: true,
            role: true,
          },
        },
      },
    });

    if (!user) {
      this.logger.warn(`User not found during JWT validation: ${userId}`);
      await this.authAuditService.logTokenValidationFailure(
        "User not found",
        userId,
        undefined,
        jwtTenantId
      );
      throw new UnauthorizedException("User not found.");
    }

    // Verify tenant ID matches (backward compatibility check)
    if (user.tenantId !== jwtTenantId) {
      this.logger.error(
        `Tenant ID mismatch for user ${userId}. JWT tenantId: ${jwtTenantId}, DB tenantId: ${user.tenantId}`
      );
      await this.authAuditService.logTokenValidationFailure(
        "Tenant ID mismatch",
        userId,
        user.email,
        jwtTenantId
      );
      throw new UnauthorizedException("Tenant ID mismatch.");
    }

    // Validate warehouse access from JWT against current database state
    // This ensures JWT warehouse access is still valid
    const currentWarehouseIds = user.warehouseUsers.map((wu) => wu.warehouseId);
    const jwtWarehouseIds = warehouseAccess?.map((wa) => wa.warehouseId) || [];

    // Check if JWT warehouse access is outdated
    const hasOutdatedAccess = jwtWarehouseIds.some(
      (id) => !currentWarehouseIds.includes(id)
    );
    if (hasOutdatedAccess) {
      this.logger.warn(
        `Outdated warehouse access in JWT for user ${userId}. JWT warehouses: [${jwtWarehouseIds.join(
          ", "
        )}], Current warehouses: [${currentWarehouseIds.join(", ")}]`
      );
      // Note: In production, you might want to force token refresh here
      // For now, we'll use the current database state
    }

    // Create enhanced user payload with warehouse access utilities
    const enhancedUser = this.createEnhancedUserPayload(user);

    this.logger.debug(
      `JWT validation successful for ${user.email}. Accessible warehouses: ${enhancedUser.accessibleWarehouses.length}`
    );

    // Log successful token validation
    await this.authAuditService.logTokenValidationSuccess(enhancedUser);

    return enhancedUser;
  }

  /**
   * Creates an enhanced user payload with warehouse access utility methods
   */
  private createEnhancedUserPayload(user: any): EnhancedUserPayload {
    const accessibleWarehouses = user.warehouseUsers.map(
      (wu: any) => wu.warehouseId
    );
    const warehouseRoles = new Map<string, Role>();

    // Build warehouse roles map for quick lookup
    user.warehouseUsers.forEach((wu: any) => {
      warehouseRoles.set(wu.warehouseId, wu.role);
    });

    return {
      ...user,
      accessibleWarehouses,
      warehouseRoles,

      // Utility method to check if user can access a warehouse
      canAccessWarehouse: (warehouseId: string): boolean => {
        return accessibleWarehouses.includes(warehouseId);
      },

      // Utility method to get user's role in a specific warehouse
      getWarehouseRole: (warehouseId: string): Role | null => {
        return warehouseRoles.get(warehouseId) || null;
      },

      // Utility method to check if user has required permission in warehouse
      hasWarehousePermission: (
        warehouseId: string,
        requiredRole: Role
      ): boolean => {
        const userRole = warehouseRoles.get(warehouseId);
        if (!userRole) return false;

        // Define role hierarchy: TENANT_ADMIN > WAREHOUSE_MANAGER > WAREHOUSE_MEMBER
        const roleHierarchy = {
          [Role.TENANT_ADMIN]: 3,
          [Role.WAREHOUSE_MANAGER]: 2,
          [Role.WAREHOUSE_MEMBER]: 1,
        };

        return (
          (roleHierarchy[userRole] || 0) >= (roleHierarchy[requiredRole] || 0)
        );
      },

      // Utility method to check if user is a tenant admin
      isTenantAdmin: (): boolean => {
        return user.role === Role.TENANT_ADMIN;
      },
    };
  }
}
