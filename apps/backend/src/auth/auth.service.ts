import { Injectable, UnauthorizedException, Logger } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { PrismaService } from "../prisma/prisma.service";
import { ConfigService } from "@nestjs/config";
import { jwtConstants } from "./constants";
import { LoginDto } from "./dto/login.dto";
import { CreateTenantWithAdminDto } from "./dto";
import * as bcrypt from "bcryptjs"; // For password hashing
import {
  ConflictException,
  InternalServerErrorException,
} from "@nestjs/common";
import { Role } from "@prisma/client";
import { JwtPayload, WarehouseAccessInfo } from "./types";
import { createClient } from "@supabase/supabase-js";

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private configService: ConfigService
  ) {}

  async login(loginDto: LoginDto): Promise<{
    accessToken: string;
    onboardingStatus: string;
    user: Omit<import("@prisma/client").User, "password">;
  }> {
    let supabasePayload: any;
    try {
      // Determine the secret to use for verification
      const verificationSecret =
        process.env.NODE_ENV === "test"
          ? process.env.TEST_JWT_SECRET // Use test secret if NODE_ENV is test
          : this.configService.get<string>(jwtConstants.secretKeyName);

      this.logger.log(
        `Attempting JWT verification. NODE_ENV: ${process.env.NODE_ENV}`
      );
      this.logger.log(
        `Using secret key name from constants: ${jwtConstants.secretKeyName}`
      );
      this.logger.log(
        `Retrieved verification secret: ${
          verificationSecret ? "******** (present)" : "MISSING OR EMPTY"
        }`
      );

      if (!verificationSecret) {
        this.logger.error(
          `JWT verification secret is missing or empty for key name: ${jwtConstants.secretKeyName}. Ensure it's set in environment variables.`
        );
        // Throwing an explicit InternalServerErrorException here to ensure it's a JSON response
        throw new InternalServerErrorException(
          "Internal configuration error: JWT secret missing."
        );
      }

      // Verify the Supabase token using the explicitly determined secret
      supabasePayload = await this.jwtService.verifyAsync(
        loginDto.supabaseToken,
        { secret: verificationSecret }
      );
      this.logger.debug(
        `Supabase token verified for sub: ${supabasePayload.sub}`
      );
    } catch (error) {
      this.logger.error(
        `Supabase token validation failed: ${error.message}`,
        error.stack
      );
      // Explicitly handle JWT errors to ensure a 401 is returned.
      // The jwtService throws errors like TokenExpiredError or JsonWebTokenError.
      if (error.name === "TokenExpiredError") {
        throw new UnauthorizedException("Token has expired");
      }
      if (error.name === "JsonWebTokenError") {
        throw new UnauthorizedException("Invalid token");
      }
      // Fallback for any other unexpected errors during verification.
      throw new UnauthorizedException("Supabase token validation failed");
    }

    if (!supabasePayload.sub || !supabasePayload.email) {
      this.logger.error(
        "Supabase token missing required claims (sub or email)"
      );
      throw new UnauthorizedException("Invalid Supabase token claims");
    }

    // Try to find the user in our database linked to the Supabase user ID
    let user = await this.prisma.user.findUnique({
      where: { authUserId: supabasePayload.sub },
      include: {
        warehouseUsers: {
          select: {
            warehouseId: true,
            role: true,
          },
        },
      },
    });

    // If user is not found by authUserId, it could be an existing local user
    // logging in via Supabase for the first time, or a genuinely new user.
    if (!user) {
      this.logger.log(
        `User with Supabase ID ${supabasePayload.sub} not found locally. Checking for existing email ${supabasePayload.email}.`
      );

      // Check if a user already exists with this email (e.g., from manual sign-up)
      const existingUserWithEmail = await this.prisma.user.findUnique({
        where: { email: supabasePayload.email },
        include: {
          warehouseUsers: {
            select: {
              warehouseId: true,
              role: true,
            },
          },
        },
      });

      if (existingUserWithEmail) {
        this.logger.log(
          `Found existing user with email ${supabasePayload.email} (ID: ${existingUserWithEmail.id}). Linking Supabase authUserId.`
        );
        // Link Supabase authUserId to the existing local user
        try {
          user = await this.prisma.user.update({
            where: { id: existingUserWithEmail.id },
            data: { authUserId: supabasePayload.sub },
            include: { warehouseUsers: true },
          });
          this.logger.log(
            `Successfully linked Supabase authUserId ${supabasePayload.sub} to user ID ${user.id}.`
          );
        } catch (linkError) {
          this.logger.error(
            `Failed to link Supabase authUserId ${supabasePayload.sub} to existing user ${existingUserWithEmail.id}: ${linkError.message}`,
            linkError.stack
          );
          throw new InternalServerErrorException(
            "Failed to link social account to existing user."
          );
        }
      } else {
        // No user by authUserId and no user by email - genuinely new Supabase user, create in Quildora DB
        this.logger.log(
          `No existing Quildora user found for email ${supabasePayload.email}. Creating new Quildora user linked to Supabase ID ${supabasePayload.sub}.`
        );
        try {
          // Create the user without automatically creating or linking a tenant.
          // tenantId will be null by default.
          user = await this.prisma.user.create({
            data: {
              authUserId: supabasePayload.sub,
              email: supabasePayload.email,
              role: Role.WAREHOUSE_MEMBER, // Default role, tenantId being null signifies pending onboarding
              name:
                supabasePayload.user_metadata?.full_name ||
                supabasePayload.email.split("@")[0],
            },
            include: {
              warehouseUsers: {
                select: {
                  warehouseId: true,
                  role: true,
                },
              },
            },
          });
          this.logger.log(
            `New Quildora user created: ${user.email} (ID: ${user.id}). Needs company onboarding.`
          );
        } catch (creationError) {
          this.logger.error(
            `Failed to create new user for Supabase ID ${supabasePayload.sub} with email ${supabasePayload.email}: ${creationError.message}`,
            creationError.stack
          );
          // More specific error for frontend if possible, otherwise generic
          if (
            creationError.code === "P2002" &&
            creationError.meta?.target?.includes("email")
          ) {
            throw new ConflictException(
              "An account with this email already exists but could not be linked. Please contact support."
            );
          } else {
            throw new InternalServerErrorException(
              "Failed to provision new user account."
            );
          }
        }
      }
    }

    // Determine onboarding status
    const onboardingStatus = user.tenantId
      ? "complete"
      : "pending_company_details";

    // Prepare user object for response (excluding password)
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...userWithoutPassword } = user;

    // Prepare warehouse access information for JWT payload
    const warehouseAccess: WarehouseAccessInfo[] =
      user.warehouseUsers?.map((wu) => ({
        warehouseId: wu.warehouseId,
        role: wu.role,
        // Note: warehouseName could be added here if needed, but would require additional query
      })) || [];

    const jwtPayload: JwtPayload = {
      userId: user.id, // Add internal user ID as userId for JwtStrategy
      sub: user.id, // Use our internal user ID for JWT subject
      email: user.email,
      role: user.role,
      tenantId: user.tenantId, // Include tenantId in the JWT payload for backward compatibility
      warehouseAccess, // Include warehouse access information
    };

    this.logger.log(
      `Generating JWT for user: ${user.email} (ID: ${user.id}, Role: ${user.role}, Tenant: ${user.tenantId}), Warehouses: ${warehouseAccess.length}, Onboarding: ${onboardingStatus}`
    );

    // Log warehouse access for debugging (remove in production)
    if (warehouseAccess.length > 0) {
      this.logger.debug(
        `Warehouse access for ${user.email}: ${warehouseAccess
          .map((wa) => `${wa.warehouseId}(${wa.role})`)
          .join(", ")}`
      );
    }

    return {
      accessToken: await this.jwtService.signAsync(jwtPayload),
      onboardingStatus,
      user: userWithoutPassword,
    };
  }

  async createTenantWithAdmin(
    createTenantDto: CreateTenantWithAdminDto
  ): Promise<Omit<import("@prisma/client").User, "password">> {
    const { companyName, adminEmail, adminPassword } = createTenantDto;

    this.logger.log(
      `Attempting to create tenant '${companyName}' with admin '${adminEmail}'`
    );

    // Check if user with this email already exists
    const existingUser = await this.prisma.user.findUnique({
      where: { email: adminEmail },
    });

    if (existingUser) {
      this.logger.warn(
        `Signup attempt failed: Email '${adminEmail}' already exists.`
      );
      throw new ConflictException("Email already exists");
    }

    // Hash the password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(adminPassword, saltRounds);
    this.logger.debug(`Password hashed for user '${adminEmail}'`);

    try {
      const result = await this.prisma.$transaction(async (tx) => {
        // Create the tenant
        const newTenant = await tx.tenant.create({
          data: {
            name: companyName,
          },
        });
        this.logger.log(
          `Tenant created: ${newTenant.name} (ID: ${newTenant.id})`
        );

        // Create the TENANT_ADMIN user for this tenant
        const userData = {
          email: adminEmail,
          password: hashedPassword, // Store the hashed password
          role: Role.TENANT_ADMIN,
          name: adminEmail.split("@")[0], // Derive name from email
          tenantId: newTenant.id, // Link to the newly created tenant
          // authUserId will be null as this user is not from Supabase JIT
        };

        const newUser = await tx.user.create({
          data: userData,
        });
        this.logger.log(
          `TENANT_ADMIN user created: ${newUser.email} (ID: ${newUser.id}) for tenant ${newTenant.id}`
        );

        // The type error on the original destructuring suggests newUser from Prisma create
        // does not include 'password'. If so, newUser already matches Omit<User, 'password'>.
        return newUser;
      });
      return result;
    } catch (error) {
      this.logger.error(
        `Error during tenant/admin creation for ${adminEmail}: ${error.message}`,
        error.stack
      );
      // Check for specific Prisma errors if needed, e.g., unique constraint violation if not caught by pre-check
      if (error.code === "P2002" && error.meta?.target?.includes("email")) {
        throw new ConflictException("Email already exists");
      }
      throw new InternalServerErrorException(
        "Could not create tenant and admin user."
      );
    }
  }

  // New methods for onboarding support
  async createSupabaseUser(email: string, password: string): Promise<any> {
    try {
      const supabaseUrl = this.configService.get<string>("SUPABASE_URL");
      const supabaseServiceKey = this.configService.get<string>(
        "SUPABASE_SERVICE_ROLE_KEY"
      );

      if (!supabaseUrl || !supabaseServiceKey) {
        throw new InternalServerErrorException(
          "Supabase configuration missing"
        );
      }

      const supabase = createClient(supabaseUrl, supabaseServiceKey);

      const { data, error } = await supabase.auth.admin.createUser({
        email,
        password,
        email_confirm: true, // Auto-confirm email for onboarding
      });

      if (error) {
        this.logger.error("Failed to create Supabase user:", error);
        throw new InternalServerErrorException(
          `Failed to create user: ${error.message}`
        );
      }

      return data.user;
    } catch (error) {
      this.logger.error("Error creating Supabase user:", error);
      throw error;
    }
  }

  async generateAccessToken(user: any): Promise<string> {
    try {
      // Get user's warehouse access
      const warehouseAccess = await this.prisma.warehouseUser.findMany({
        where: { userId: user.id },
        include: { warehouse: true },
      });

      const warehouseAccessInfo: WarehouseAccessInfo[] = warehouseAccess.map(
        (wa) => ({
          warehouseId: wa.warehouseId,
          warehouseName: wa.warehouse.name,
          role: wa.role,
        })
      );

      const jwtPayload: JwtPayload = {
        userId: user.id,
        sub: user.id,
        email: user.email,
        role: user.role,
        tenantId: user.tenantId,
        warehouseAccess: warehouseAccessInfo,
      };

      return await this.jwtService.signAsync(jwtPayload);
    } catch (error) {
      this.logger.error("Error generating access token:", error);
      throw new InternalServerErrorException("Failed to generate access token");
    }
  }
}
