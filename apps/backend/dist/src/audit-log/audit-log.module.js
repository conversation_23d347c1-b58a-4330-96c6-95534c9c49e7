"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditLogModule = void 0;
const common_1 = require("@nestjs/common");
const audit_log_service_1 = require("./audit-log.service");
const audit_log_interceptor_1 = require("./interceptors/audit-log.interceptor");
const security_audit_service_1 = require("./services/security-audit.service");
const auth_audit_service_1 = require("./services/auth-audit.service");
const security_audit_interceptor_1 = require("./interceptors/security-audit.interceptor");
const prisma_module_1 = require("../prisma/prisma.module");
let AuditLogModule = class AuditLogModule {
};
exports.AuditLogModule = AuditLogModule;
exports.AuditLogModule = AuditLogModule = __decorate([
    (0, common_1.Module)({
        imports: [prisma_module_1.PrismaModule],
        providers: [
            audit_log_service_1.AuditLogService,
            audit_log_interceptor_1.AuditLogInterceptor,
            security_audit_service_1.SecurityAuditService,
            auth_audit_service_1.AuthAuditService,
            security_audit_interceptor_1.SecurityAuditInterceptor,
        ],
        exports: [
            audit_log_service_1.AuditLogService,
            audit_log_interceptor_1.AuditLogInterceptor,
            security_audit_service_1.SecurityAuditService,
            auth_audit_service_1.AuthAuditService,
            security_audit_interceptor_1.SecurityAuditInterceptor,
        ],
    })
], AuditLogModule);
//# sourceMappingURL=audit-log.module.js.map