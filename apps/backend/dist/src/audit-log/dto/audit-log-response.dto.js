"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditLogListResponseDto = exports.AuditLogResponseDto = exports.AuditLogWarehouseContextDto = exports.AuditLogUserDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class AuditLogUserDto {
}
exports.AuditLogUserDto = AuditLogUserDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User ID' }),
    __metadata("design:type", String)
], AuditLogUserDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User name' }),
    __metadata("design:type", String)
], AuditLogUserDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User email' }),
    __metadata("design:type", String)
], AuditLogUserDto.prototype, "email", void 0);
class AuditLogWarehouseContextDto {
}
exports.AuditLogWarehouseContextDto = AuditLogWarehouseContextDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Warehouse ID where the action occurred' }),
    __metadata("design:type", String)
], AuditLogWarehouseContextDto.prototype, "warehouseId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Warehouse name' }),
    __metadata("design:type", String)
], AuditLogWarehouseContextDto.prototype, "warehouseName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Location ID if action was location-specific' }),
    __metadata("design:type", String)
], AuditLogWarehouseContextDto.prototype, "locationId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Location name if action was location-specific' }),
    __metadata("design:type", String)
], AuditLogWarehouseContextDto.prototype, "locationName", void 0);
class AuditLogResponseDto {
}
exports.AuditLogResponseDto = AuditLogResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Audit log entry ID' }),
    __metadata("design:type", String)
], AuditLogResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Type of action performed' }),
    __metadata("design:type", String)
], AuditLogResponseDto.prototype, "action", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Type of entity affected' }),
    __metadata("design:type", String)
], AuditLogResponseDto.prototype, "entity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID of the entity affected' }),
    __metadata("design:type", String)
], AuditLogResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp when the action occurred' }),
    __metadata("design:type", Date)
], AuditLogResponseDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional details about the action',
        type: Object,
        additionalProperties: true
    }),
    __metadata("design:type", Object)
], AuditLogResponseDto.prototype, "details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'User who performed the action',
        type: AuditLogUserDto
    }),
    __metadata("design:type", AuditLogUserDto)
], AuditLogResponseDto.prototype, "user", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Warehouse context information',
        type: AuditLogWarehouseContextDto
    }),
    __metadata("design:type", AuditLogWarehouseContextDto)
], AuditLogResponseDto.prototype, "warehouseContext", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tenant ID' }),
    __metadata("design:type", String)
], AuditLogResponseDto.prototype, "tenantId", void 0);
class AuditLogListResponseDto {
}
exports.AuditLogListResponseDto = AuditLogListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Array of audit log entries',
        type: [AuditLogResponseDto]
    }),
    __metadata("design:type", Array)
], AuditLogListResponseDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of audit log entries' }),
    __metadata("design:type", Number)
], AuditLogListResponseDto.prototype, "count", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Current page number' }),
    __metadata("design:type", Number)
], AuditLogListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of items per page' }),
    __metadata("design:type", Number)
], AuditLogListResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Total number of pages' }),
    __metadata("design:type", Number)
], AuditLogListResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=audit-log-response.dto.js.map