export declare class AuditLogUserDto {
    id: string;
    name: string;
    email: string;
}
export declare class AuditLogWarehouseContextDto {
    warehouseId: string;
    warehouseName: string;
    locationId?: string;
    locationName?: string;
}
export declare class AuditLogResponseDto {
    id: string;
    action: string;
    entity: string;
    entityId: string;
    timestamp: Date;
    details?: Record<string, any>;
    user?: AuditLogUserDto;
    warehouseContext?: AuditLogWarehouseContextDto;
    tenantId: string;
}
export declare class AuditLogListResponseDto {
    data: AuditLogResponseDto[];
    count: number;
    page?: number;
    limit?: number;
    totalPages?: number;
}
