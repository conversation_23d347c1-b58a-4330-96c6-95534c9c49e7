"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditLogService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const role_enum_1 = require("../auth/entities/role.enum");
let AuditLogService = class AuditLogService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createAuditLogDto, prismaClient = this.prisma) {
        await prismaClient.auditLog.create({
            data: {
                userId: createAuditLogDto.userId,
                userEmail: createAuditLogDto.userEmail,
                action: createAuditLogDto.action,
                entity: createAuditLogDto.entity,
                entityId: createAuditLogDto.entityId,
                details: createAuditLogDto.details || undefined,
                tenantId: createAuditLogDto.tenantId,
            },
        });
    }
    async findByEntityId(entityId, tenantId, entity) {
        const where = {
            entityId,
            tenantId,
        };
        if (entity) {
            where.entity = entity;
        }
        return this.prisma.auditLog.findMany({
            where,
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
            },
            orderBy: {
                timestamp: "desc",
            },
        });
    }
    async findByEntityIdWithWarehouseContext(entityId, currentUser, entity) {
        const where = {
            entityId,
            tenantId: currentUser.tenantId,
        };
        if (entity) {
            where.entity = entity;
        }
        if (entity && this.isWarehouseScopedEntity(entity)) {
            await this.validateWarehouseAccess(entityId, entity, currentUser);
        }
        return this.prisma.auditLog.findMany({
            where,
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
            },
            orderBy: {
                timestamp: "desc",
            },
        });
    }
    async findByWarehouse(warehouseId, currentUser, options) {
        await this.validateUserWarehouseAccess(warehouseId, currentUser);
        const where = {
            tenantId: currentUser.tenantId,
            OR: [
                {
                    entity: "Warehouse",
                    entityId: warehouseId,
                },
                {
                    entity: "Location",
                    entityId: {
                        in: await this.getLocationIdsInWarehouse(warehouseId),
                    },
                },
                {
                    entity: "Pallet",
                    entityId: {
                        in: await this.getPalletIdsInWarehouse(warehouseId),
                    },
                },
            ],
        };
        if (options?.entity) {
            delete where.OR;
            where.entity = options.entity;
            if (options.entity === "Location") {
                where.entityId = {
                    in: await this.getLocationIdsInWarehouse(warehouseId),
                };
            }
            else if (options.entity === "Pallet") {
                where.entityId = {
                    in: await this.getPalletIdsInWarehouse(warehouseId),
                };
            }
            else if (options.entity === "Warehouse") {
                where.entityId = warehouseId;
            }
        }
        if (options?.action) {
            where.action = options.action;
        }
        const [data, count] = await Promise.all([
            this.prisma.auditLog.findMany({
                where,
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                        },
                    },
                },
                orderBy: {
                    timestamp: "desc",
                },
                take: options?.limit || 50,
                skip: options?.offset || 0,
            }),
            this.prisma.auditLog.count({ where }),
        ]);
        return { data, count };
    }
    isWarehouseScopedEntity(entity) {
        const warehouseScopedEntities = ["Pallet", "Location", "PalletItem"];
        return warehouseScopedEntities.includes(entity);
    }
    async validateWarehouseAccess(entityId, entity, currentUser) {
        if (currentUser.role === role_enum_1.Role.TENANT_ADMIN) {
            return;
        }
        let warehouseId = null;
        switch (entity) {
            case "Pallet":
                const pallet = await this.prisma.pallet.findUnique({
                    where: { id: entityId },
                    include: { location: true },
                });
                warehouseId = pallet?.location?.warehouseId || null;
                break;
            case "Location":
                const location = await this.prisma.location.findUnique({
                    where: { id: entityId },
                });
                warehouseId = location?.warehouseId || null;
                break;
            case "PalletItem":
                const palletItem = await this.prisma.palletItem.findUnique({
                    where: { id: entityId },
                    include: { pallet: { include: { location: true } } },
                });
                warehouseId = palletItem?.pallet?.location?.warehouseId || null;
                break;
        }
        if (warehouseId) {
            await this.validateUserWarehouseAccess(warehouseId, currentUser);
        }
    }
    async validateUserWarehouseAccess(warehouseId, currentUser) {
        if (currentUser.role === role_enum_1.Role.TENANT_ADMIN) {
            return;
        }
        const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
        if (!userWarehouseIds.includes(warehouseId)) {
            throw new Error("Access denied: User does not have access to this warehouse");
        }
    }
    async getLocationIdsInWarehouse(warehouseId) {
        const locations = await this.prisma.location.findMany({
            where: { warehouseId },
            select: { id: true },
        });
        return locations.map((l) => l.id);
    }
    async getPalletIdsInWarehouse(warehouseId) {
        const pallets = await this.prisma.pallet.findMany({
            where: {
                location: {
                    warehouseId,
                },
            },
            select: { id: true },
        });
        return pallets.map((p) => p.id);
    }
};
exports.AuditLogService = AuditLogService;
exports.AuditLogService = AuditLogService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], AuditLogService);
//# sourceMappingURL=audit-log.service.js.map