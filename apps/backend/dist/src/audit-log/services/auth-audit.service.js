"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AuthAuditService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthAuditService = void 0;
const common_1 = require("@nestjs/common");
const security_audit_service_1 = require("./security-audit.service");
let AuthAuditService = AuthAuditService_1 = class AuthAuditService {
    constructor(securityAuditService) {
        this.securityAuditService = securityAuditService;
        this.logger = new common_1.Logger(AuthAuditService_1.name);
    }
    async logTokenValidationSuccess(user, request) {
        try {
            const req = request || this.createMinimalRequest();
            await this.securityAuditService.logAuthenticationEvent('TOKEN_VALIDATION_SUCCESS', user, req, {
                reason: 'JWT token successfully validated',
            });
            this.logger.debug(`Token validation success logged for user ${user.email}`);
        }
        catch (error) {
            this.logger.error(`Failed to log token validation success: ${error.message}`, error.stack);
        }
    }
    async logTokenValidationFailure(reason, userId, userEmail, tenantId, request) {
        try {
            const req = request || this.createMinimalRequest();
            const partialUser = userId ? {
                id: userId,
                email: userEmail || 'unknown',
                tenantId: tenantId || 'unknown',
            } : undefined;
            await this.securityAuditService.logAuthenticationEvent('TOKEN_VALIDATION_FAILURE', partialUser, req, {
                reason,
                tokenExpired: reason.toLowerCase().includes('expired'),
                invalidCredentials: reason.toLowerCase().includes('invalid'),
            });
            this.logger.warn(`Token validation failure logged: ${reason} for user ${userEmail || userId || 'unknown'}`);
        }
        catch (error) {
            this.logger.error(`Failed to log token validation failure: ${error.message}`, error.stack);
        }
    }
    async logLoginSuccess(user, request) {
        try {
            await this.securityAuditService.logAuthenticationEvent('LOGIN_SUCCESS', user, request);
            this.logger.debug(`Login success logged for user ${user.email}`);
        }
        catch (error) {
            this.logger.error(`Failed to log login success: ${error.message}`, error.stack);
        }
    }
    async logLoginFailure(email, reason, request, tenantId) {
        try {
            const partialUser = {
                id: 'unknown',
                email,
                tenantId: tenantId || 'unknown',
            };
            await this.securityAuditService.logAuthenticationEvent('LOGIN_FAILURE', partialUser, request, {
                reason,
                invalidCredentials: reason.toLowerCase().includes('credential') || reason.toLowerCase().includes('password'),
            });
            this.logger.warn(`Login failure logged for email ${email}: ${reason}`);
        }
        catch (error) {
            this.logger.error(`Failed to log login failure: ${error.message}`, error.stack);
        }
    }
    async logLogout(user, request) {
        try {
            await this.securityAuditService.logAuthenticationEvent('LOGOUT', user, request);
            this.logger.debug(`Logout logged for user ${user.email}`);
        }
        catch (error) {
            this.logger.error(`Failed to log logout: ${error.message}`, error.stack);
        }
    }
    createMinimalRequest() {
        return {
            path: '/auth/validate',
            method: 'POST',
            headers: {
                'user-agent': 'JWT-Strategy',
            },
            connection: {
                remoteAddress: 'unknown',
            },
            socket: {
                remoteAddress: 'unknown',
            },
        };
    }
};
exports.AuthAuditService = AuthAuditService;
exports.AuthAuditService = AuthAuditService = AuthAuditService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [security_audit_service_1.SecurityAuditService])
], AuthAuditService);
//# sourceMappingURL=auth-audit.service.js.map