import { AuditLogService } from '../audit-log.service';
import { EnhancedUserPayload } from '../../auth/types';
import { Role } from '@prisma/client';
import { Request } from 'express';
export declare class SecurityAuditService {
    private readonly auditLogService;
    private readonly logger;
    constructor(auditLogService: AuditLogService);
    logWarehouseAccessGranted(warehouseId: string, user: EnhancedUserPayload, request: Request, context?: {
        operation?: string;
        resource?: string;
        userRole?: Role;
    }): Promise<void>;
    logWarehouseAccessDenied(warehouseId: string | undefined, user: EnhancedUserPayload | undefined, request: Request, reason: string, context?: {
        operation?: string;
        resource?: string;
        requiredRole?: Role;
        userRole?: Role;
    }): Promise<void>;
    logAuthenticationEvent(eventType: 'LOGIN_SUCCESS' | 'LOGIN_FAILURE' | 'TOKEN_VALIDATION_SUCCESS' | 'TOKEN_VALIDATION_FAILURE' | 'LOGOUT', user: EnhancedUserPayload | undefined, request: Request, details?: {
        reason?: string;
        tokenExpired?: boolean;
        invalidCredentials?: boolean;
    }): Promise<void>;
    logPermissionViolation(violationType: 'INSUFFICIENT_ROLE' | 'UNAUTHORIZED_WAREHOUSE_ACCESS' | 'INVALID_OPERATION' | 'CROSS_TENANT_ACCESS', user: EnhancedUserPayload | undefined, request: Request, context: {
        warehouseId?: string;
        requiredRole?: Role;
        userRole?: Role;
        operation?: string;
        resource?: string;
        targetTenantId?: string;
    }): Promise<void>;
    logSecurityConfigChange(changeType: 'USER_ROLE_CHANGED' | 'WAREHOUSE_ACCESS_MODIFIED' | 'PERMISSION_UPDATED', user: EnhancedUserPayload, request: Request, context: {
        targetUserId?: string;
        targetUserEmail?: string;
        warehouseId?: string;
        oldRole?: Role;
        newRole?: Role;
        operation?: string;
    }): Promise<void>;
    private extractIpAddress;
}
