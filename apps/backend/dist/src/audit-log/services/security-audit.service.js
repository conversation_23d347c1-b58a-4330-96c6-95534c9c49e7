"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SecurityAuditService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityAuditService = void 0;
const common_1 = require("@nestjs/common");
const audit_log_service_1 = require("../audit-log.service");
let SecurityAuditService = SecurityAuditService_1 = class SecurityAuditService {
    constructor(auditLogService) {
        this.auditLogService = auditLogService;
        this.logger = new common_1.Logger(SecurityAuditService_1.name);
    }
    async logWarehouseAccessGranted(warehouseId, user, request, context) {
        try {
            await this.auditLogService.create({
                action: 'WAREHOUSE_ACCESS_GRANTED',
                entity: 'Security',
                entityId: warehouseId,
                userId: user.id,
                userEmail: user.email,
                tenantId: user.tenantId,
                details: {
                    warehouseId,
                    userRole: context?.userRole || user.role,
                    operation: context?.operation,
                    resource: context?.resource,
                    requestPath: request.path,
                    requestMethod: request.method,
                    userAgent: request.headers['user-agent'],
                    ipAddress: this.extractIpAddress(request),
                    timestamp: new Date().toISOString(),
                },
            });
            this.logger.debug(`Warehouse access granted logged for user ${user.email} to warehouse ${warehouseId}`);
        }
        catch (error) {
            this.logger.error(`Failed to log warehouse access granted: ${error.message}`, error.stack);
        }
    }
    async logWarehouseAccessDenied(warehouseId, user, request, reason, context) {
        try {
            await this.auditLogService.create({
                action: 'WAREHOUSE_ACCESS_DENIED',
                entity: 'Security',
                entityId: warehouseId || 'unknown',
                userId: user?.id,
                userEmail: user?.email,
                tenantId: user?.tenantId || 'unknown',
                details: {
                    warehouseId,
                    reason,
                    userRole: context?.userRole || user?.role,
                    requiredRole: context?.requiredRole,
                    operation: context?.operation,
                    resource: context?.resource,
                    requestPath: request.path,
                    requestMethod: request.method,
                    userAgent: request.headers['user-agent'],
                    ipAddress: this.extractIpAddress(request),
                    timestamp: new Date().toISOString(),
                },
            });
            this.logger.warn(`Warehouse access denied logged for user ${user?.email || 'unknown'} to warehouse ${warehouseId || 'unknown'}: ${reason}`);
        }
        catch (error) {
            this.logger.error(`Failed to log warehouse access denied: ${error.message}`, error.stack);
        }
    }
    async logAuthenticationEvent(eventType, user, request, details) {
        try {
            await this.auditLogService.create({
                action: eventType,
                entity: 'Authentication',
                entityId: user?.id || 'unknown',
                userId: user?.id,
                userEmail: user?.email,
                tenantId: user?.tenantId || 'unknown',
                details: {
                    eventType,
                    reason: details?.reason,
                    tokenExpired: details?.tokenExpired,
                    invalidCredentials: details?.invalidCredentials,
                    requestPath: request.path,
                    requestMethod: request.method,
                    userAgent: request.headers['user-agent'],
                    ipAddress: this.extractIpAddress(request),
                    timestamp: new Date().toISOString(),
                },
            });
            this.logger.debug(`Authentication event logged: ${eventType} for user ${user?.email || 'unknown'}`);
        }
        catch (error) {
            this.logger.error(`Failed to log authentication event: ${error.message}`, error.stack);
        }
    }
    async logPermissionViolation(violationType, user, request, context) {
        try {
            await this.auditLogService.create({
                action: 'PERMISSION_VIOLATION',
                entity: 'Security',
                entityId: context.warehouseId || context.targetTenantId || 'unknown',
                userId: user?.id,
                userEmail: user?.email,
                tenantId: user?.tenantId || 'unknown',
                details: {
                    violationType,
                    warehouseId: context.warehouseId,
                    requiredRole: context.requiredRole,
                    userRole: context.userRole || user?.role,
                    operation: context.operation,
                    resource: context.resource,
                    targetTenantId: context.targetTenantId,
                    requestPath: request.path,
                    requestMethod: request.method,
                    userAgent: request.headers['user-agent'],
                    ipAddress: this.extractIpAddress(request),
                    timestamp: new Date().toISOString(),
                },
            });
            this.logger.warn(`Permission violation logged: ${violationType} for user ${user?.email || 'unknown'}`);
        }
        catch (error) {
            this.logger.error(`Failed to log permission violation: ${error.message}`, error.stack);
        }
    }
    async logSecurityConfigChange(changeType, user, request, context) {
        try {
            await this.auditLogService.create({
                action: changeType,
                entity: 'SecurityConfig',
                entityId: context.targetUserId || context.warehouseId || 'unknown',
                userId: user.id,
                userEmail: user.email,
                tenantId: user.tenantId,
                details: {
                    changeType,
                    targetUserId: context.targetUserId,
                    targetUserEmail: context.targetUserEmail,
                    warehouseId: context.warehouseId,
                    oldRole: context.oldRole,
                    newRole: context.newRole,
                    operation: context.operation,
                    performedBy: {
                        userId: user.id,
                        userEmail: user.email,
                        userRole: user.role,
                    },
                    requestPath: request.path,
                    requestMethod: request.method,
                    userAgent: request.headers['user-agent'],
                    ipAddress: this.extractIpAddress(request),
                    timestamp: new Date().toISOString(),
                },
            });
            this.logger.debug(`Security config change logged: ${changeType} by user ${user.email}`);
        }
        catch (error) {
            this.logger.error(`Failed to log security config change: ${error.message}`, error.stack);
        }
    }
    extractIpAddress(request) {
        const forwarded = request.headers['x-forwarded-for'];
        if (forwarded) {
            return Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0];
        }
        return request.connection.remoteAddress || request.socket.remoteAddress || 'unknown';
    }
};
exports.SecurityAuditService = SecurityAuditService;
exports.SecurityAuditService = SecurityAuditService = SecurityAuditService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [audit_log_service_1.AuditLogService])
], SecurityAuditService);
//# sourceMappingURL=security-audit.service.js.map