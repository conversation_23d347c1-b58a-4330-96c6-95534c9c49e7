{"version": 3, "file": "security-audit.service.spec.js", "sourceRoot": "", "sources": ["../../../../src/audit-log/services/security-audit.service.spec.ts"], "names": [], "mappings": ";;AAAA,6CAAsD;AACtD,qEAAgE;AAChE,4DAAuD;AAEvD,2CAAsC;AAGtC,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,IAAI,OAA6B,CAAC;IAClC,IAAI,eAA6C,CAAC;IAElD,MAAM,QAAQ,GAAwB;QACpC,EAAE,EAAE,UAAU;QACd,KAAK,EAAE,kBAAkB;QACzB,IAAI,EAAE,WAAW;QACjB,QAAQ,EAAE,iBAAiB;QAC3B,UAAU,EAAE,eAAe;QAC3B,QAAQ,EAAE,YAAY;QACtB,IAAI,EAAE,aAAI,CAAC,gBAAgB;QAC3B,MAAM,EAAE,QAAQ;QAChB,SAAS,EAAE,IAAI,IAAI,EAAE;QACrB,SAAS,EAAE,IAAI,IAAI,EAAE;QACrB,oBAAoB,EAAE,CAAC,eAAe,CAAC;QACvC,cAAc,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,eAAe,EAAE,aAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;QACnE,cAAc,EAAE,EAAE;QAClB,aAAa,EAAE,GAAG,EAAE,CAAC,KAAK;QAC1B,kBAAkB,EAAE,CAAC,WAAmB,EAAE,EAAE,CAC1C,WAAW,KAAK,eAAe;QACjC,gBAAgB,EAAE,CAAC,WAAmB,EAAE,EAAE,CAAC,aAAI,CAAC,gBAAgB;QAChE,sBAAsB,EAAE,CAAC,WAAmB,EAAE,IAAU,EAAE,EAAE,CAAC,IAAI;KAC3C,CAAC;IAEzB,MAAM,WAAW,GAAqB;QACpC,IAAI,EAAE,cAAc;QACpB,MAAM,EAAE,KAAK;QACb,OAAO,EAAE;YACP,YAAY,EAAE,aAAa;SAC5B;QACD,UAAU,EAAE;YACV,aAAa,EAAE,eAAe;SAC/B;KACS,CAAC;IAEb,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,mBAAmB,GAAG;YAC1B,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;SAC/C,CAAC;QAEF,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,6CAAoB;gBACpB;oBACE,OAAO,EAAE,mCAAe;oBACxB,QAAQ,EAAE,mBAAmB;iBAC9B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAuB,6CAAoB,CAAC,CAAC;QACjE,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,mCAAe,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;QAC3B,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,OAAO,CAAC,yBAAyB,CACrC,eAAe,EACf,QAAQ,EACR,WAAsB,EACtB;gBACE,SAAS,EAAE,cAAc;gBACzB,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,aAAI,CAAC,gBAAgB;aAChC,CACF,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBAClD,MAAM,EAAE,0BAA0B;gBAClC,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,eAAe;gBACzB,MAAM,EAAE,UAAU;gBAClB,SAAS,EAAE,kBAAkB;gBAC7B,QAAQ,EAAE,YAAY;gBACtB,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC/B,WAAW,EAAE,eAAe;oBAC5B,QAAQ,EAAE,aAAI,CAAC,gBAAgB;oBAC/B,SAAS,EAAE,cAAc;oBACzB,QAAQ,EAAE,QAAQ;oBAClB,WAAW,EAAE,cAAc;oBAC3B,aAAa,EAAE,KAAK;oBACpB,SAAS,EAAE,aAAa;oBACxB,SAAS,EAAE,eAAe;oBAC1B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC9B,CAAC;aACH,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,eAAe,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAEtE,MAAM,MAAM,CACV,OAAO,CAAC,yBAAyB,CAC/B,eAAe,EACf,QAAQ,EACR,WAAsB,CACvB,CACF,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,OAAO,CAAC,wBAAwB,CACpC,eAAe,EACf,QAAQ,EACR,WAAsB,EACtB,yCAAyC,EACzC;gBACE,SAAS,EAAE,eAAe;gBAC1B,QAAQ,EAAE,QAAQ;gBAClB,YAAY,EAAE,aAAI,CAAC,iBAAiB;gBACpC,QAAQ,EAAE,aAAI,CAAC,gBAAgB;aAChC,CACF,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBAClD,MAAM,EAAE,yBAAyB;gBACjC,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,eAAe;gBACzB,MAAM,EAAE,UAAU;gBAClB,SAAS,EAAE,kBAAkB;gBAC7B,QAAQ,EAAE,YAAY;gBACtB,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC/B,WAAW,EAAE,eAAe;oBAC5B,MAAM,EAAE,yCAAyC;oBACjD,QAAQ,EAAE,aAAI,CAAC,gBAAgB;oBAC/B,YAAY,EAAE,aAAI,CAAC,iBAAiB;oBACpC,SAAS,EAAE,eAAe;oBAC1B,QAAQ,EAAE,QAAQ;oBAClB,WAAW,EAAE,cAAc;oBAC3B,aAAa,EAAE,KAAK;oBACpB,SAAS,EAAE,aAAa;oBACxB,SAAS,EAAE,eAAe;oBAC1B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC9B,CAAC;aACH,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,MAAM,OAAO,CAAC,wBAAwB,CACpC,eAAe,EACf,SAAS,EACT,WAAsB,EACtB,wBAAwB,CACzB,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBAClD,MAAM,EAAE,yBAAyB;gBACjC,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,eAAe;gBACzB,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,SAAS;gBACpB,QAAQ,EAAE,SAAS;gBACnB,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC/B,WAAW,EAAE,eAAe;oBAC5B,MAAM,EAAE,wBAAwB;oBAChC,QAAQ,EAAE,SAAS;iBACpB,CAAC;aACH,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;YACxC,MAAM,OAAO,CAAC,sBAAsB,CAClC,eAAe,EACf,QAAQ,EACR,WAAsB,CACvB,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBAClD,MAAM,EAAE,eAAe;gBACvB,MAAM,EAAE,gBAAgB;gBACxB,QAAQ,EAAE,UAAU;gBACpB,MAAM,EAAE,UAAU;gBAClB,SAAS,EAAE,kBAAkB;gBAC7B,QAAQ,EAAE,YAAY;gBACtB,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC/B,SAAS,EAAE,eAAe;oBAC1B,WAAW,EAAE,cAAc;oBAC3B,aAAa,EAAE,KAAK;oBACpB,SAAS,EAAE,aAAa;oBACxB,SAAS,EAAE,eAAe;oBAC1B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC9B,CAAC;aACH,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,OAAO,CAAC,sBAAsB,CAClC,0BAA0B,EAC1B,SAAS,EACT,WAAsB,EACtB;gBACE,MAAM,EAAE,eAAe;gBACvB,YAAY,EAAE,IAAI;aACnB,CACF,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBAClD,MAAM,EAAE,0BAA0B;gBAClC,MAAM,EAAE,gBAAgB;gBACxB,QAAQ,EAAE,SAAS;gBACnB,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,SAAS;gBACpB,QAAQ,EAAE,SAAS;gBACnB,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC/B,SAAS,EAAE,0BAA0B;oBACrC,MAAM,EAAE,eAAe;oBACvB,YAAY,EAAE,IAAI;iBACnB,CAAC;aACH,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,OAAO,CAAC,sBAAsB,CAClC,mBAAmB,EACnB,QAAQ,EACR,WAAsB,EACtB;gBACE,WAAW,EAAE,eAAe;gBAC5B,YAAY,EAAE,aAAI,CAAC,iBAAiB;gBACpC,QAAQ,EAAE,aAAI,CAAC,gBAAgB;gBAC/B,SAAS,EAAE,kBAAkB;gBAC7B,QAAQ,EAAE,WAAW;aACtB,CACF,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBAClD,MAAM,EAAE,sBAAsB;gBAC9B,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,eAAe;gBACzB,MAAM,EAAE,UAAU;gBAClB,SAAS,EAAE,kBAAkB;gBAC7B,QAAQ,EAAE,YAAY;gBACtB,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC/B,aAAa,EAAE,mBAAmB;oBAClC,WAAW,EAAE,eAAe;oBAC5B,YAAY,EAAE,aAAI,CAAC,iBAAiB;oBACpC,QAAQ,EAAE,aAAI,CAAC,gBAAgB;oBAC/B,SAAS,EAAE,kBAAkB;oBAC7B,QAAQ,EAAE,WAAW;iBACtB,CAAC;aACH,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,MAAM,OAAO,CAAC,uBAAuB,CACnC,mBAAmB,EACnB,QAAQ,EACR,WAAsB,EACtB;gBACE,YAAY,EAAE,iBAAiB;gBAC/B,eAAe,EAAE,oBAAoB;gBACrC,WAAW,EAAE,eAAe;gBAC5B,OAAO,EAAE,aAAI,CAAC,gBAAgB;gBAC9B,OAAO,EAAE,aAAI,CAAC,iBAAiB;gBAC/B,SAAS,EAAE,kBAAkB;aAC9B,CACF,CAAC;YAEF,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;gBAClD,MAAM,EAAE,mBAAmB;gBAC3B,MAAM,EAAE,gBAAgB;gBACxB,QAAQ,EAAE,iBAAiB;gBAC3B,MAAM,EAAE,UAAU;gBAClB,SAAS,EAAE,kBAAkB;gBAC7B,QAAQ,EAAE,YAAY;gBACtB,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC/B,UAAU,EAAE,mBAAmB;oBAC/B,YAAY,EAAE,iBAAiB;oBAC/B,eAAe,EAAE,oBAAoB;oBACrC,WAAW,EAAE,eAAe;oBAC5B,OAAO,EAAE,aAAI,CAAC,gBAAgB;oBAC9B,OAAO,EAAE,aAAI,CAAC,iBAAiB;oBAC/B,SAAS,EAAE,kBAAkB;oBAC7B,WAAW,EAAE;wBACX,MAAM,EAAE,UAAU;wBAClB,SAAS,EAAE,kBAAkB;wBAC7B,QAAQ,EAAE,aAAI,CAAC,gBAAgB;qBAChC;iBACF,CAAC;aACH,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}