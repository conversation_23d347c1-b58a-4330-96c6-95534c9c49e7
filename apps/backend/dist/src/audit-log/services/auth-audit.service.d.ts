import { SecurityAuditService } from './security-audit.service';
import { EnhancedUserPayload } from '../../auth/types';
import { Request } from 'express';
export declare class AuthAuditService {
    private readonly securityAuditService;
    private readonly logger;
    constructor(securityAuditService: SecurityAuditService);
    logTokenValidationSuccess(user: EnhancedUserPayload, request?: Request): Promise<void>;
    logTokenValidationFailure(reason: string, userId?: string, userEmail?: string, tenantId?: string, request?: Request): Promise<void>;
    logLoginSuccess(user: EnhancedUserPayload, request: Request): Promise<void>;
    logLoginFailure(email: string, reason: string, request: Request, tenantId?: string): Promise<void>;
    logLogout(user: EnhancedUserPayload, request: Request): Promise<void>;
    private createMinimalRequest;
}
