"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const security_audit_service_1 = require("./security-audit.service");
const audit_log_service_1 = require("../audit-log.service");
const client_1 = require("@prisma/client");
describe("SecurityAuditService", () => {
    let service;
    let auditLogService;
    const mockUser = {
        id: "user-123",
        email: "<EMAIL>",
        name: "Test User",
        password: "hashed-password",
        authUserId: "auth-user-123",
        tenantId: "tenant-123",
        role: client_1.Role.WAREHOUSE_MEMBER,
        status: "ACTIVE",
        createdAt: new Date(),
        updatedAt: new Date(),
        accessibleWarehouses: ["warehouse-123"],
        warehouseRoles: new Map([["warehouse-123", client_1.Role.WAREHOUSE_MEMBER]]),
        warehouseUsers: [],
        isTenantAdmin: () => false,
        canAccessWarehouse: (warehouseId) => warehouseId === "warehouse-123",
        getWarehouseRole: (warehouseId) => client_1.Role.WAREHOUSE_MEMBER,
        hasWarehousePermission: (warehouseId, role) => true,
    };
    const mockRequest = {
        path: "/api/pallets",
        method: "GET",
        headers: {
            "user-agent": "Mozilla/5.0",
        },
        connection: {
            remoteAddress: "*************",
        },
    };
    beforeEach(async () => {
        const mockAuditLogService = {
            create: jest.fn().mockResolvedValue(undefined),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                security_audit_service_1.SecurityAuditService,
                {
                    provide: audit_log_service_1.AuditLogService,
                    useValue: mockAuditLogService,
                },
            ],
        }).compile();
        service = module.get(security_audit_service_1.SecurityAuditService);
        auditLogService = module.get(audit_log_service_1.AuditLogService);
    });
    it("should be defined", () => {
        expect(service).toBeDefined();
    });
    describe("logWarehouseAccessGranted", () => {
        it("should log successful warehouse access", async () => {
            await service.logWarehouseAccessGranted("warehouse-123", mockUser, mockRequest, {
                operation: "VIEW_PALLETS",
                resource: "Pallet",
                userRole: client_1.Role.WAREHOUSE_MEMBER,
            });
            expect(auditLogService.create).toHaveBeenCalledWith({
                action: "WAREHOUSE_ACCESS_GRANTED",
                entity: "Security",
                entityId: "warehouse-123",
                userId: "user-123",
                userEmail: "<EMAIL>",
                tenantId: "tenant-123",
                details: expect.objectContaining({
                    warehouseId: "warehouse-123",
                    userRole: client_1.Role.WAREHOUSE_MEMBER,
                    operation: "VIEW_PALLETS",
                    resource: "Pallet",
                    requestPath: "/api/pallets",
                    requestMethod: "GET",
                    userAgent: "Mozilla/5.0",
                    ipAddress: "*************",
                    timestamp: expect.any(String),
                }),
            });
        });
        it("should handle errors gracefully", async () => {
            auditLogService.create.mockRejectedValue(new Error("Database error"));
            await expect(service.logWarehouseAccessGranted("warehouse-123", mockUser, mockRequest)).resolves.not.toThrow();
        });
    });
    describe("logWarehouseAccessDenied", () => {
        it("should log failed warehouse access", async () => {
            await service.logWarehouseAccessDenied("warehouse-456", mockUser, mockRequest, "Access denied: Insufficient permissions", {
                operation: "DELETE_PALLET",
                resource: "Pallet",
                requiredRole: client_1.Role.WAREHOUSE_MANAGER,
                userRole: client_1.Role.WAREHOUSE_MEMBER,
            });
            expect(auditLogService.create).toHaveBeenCalledWith({
                action: "WAREHOUSE_ACCESS_DENIED",
                entity: "Security",
                entityId: "warehouse-456",
                userId: "user-123",
                userEmail: "<EMAIL>",
                tenantId: "tenant-123",
                details: expect.objectContaining({
                    warehouseId: "warehouse-456",
                    reason: "Access denied: Insufficient permissions",
                    userRole: client_1.Role.WAREHOUSE_MEMBER,
                    requiredRole: client_1.Role.WAREHOUSE_MANAGER,
                    operation: "DELETE_PALLET",
                    resource: "Pallet",
                    requestPath: "/api/pallets",
                    requestMethod: "GET",
                    userAgent: "Mozilla/5.0",
                    ipAddress: "*************",
                    timestamp: expect.any(String),
                }),
            });
        });
        it("should handle undefined user", async () => {
            await service.logWarehouseAccessDenied("warehouse-456", undefined, mockRequest, "User not authenticated");
            expect(auditLogService.create).toHaveBeenCalledWith({
                action: "WAREHOUSE_ACCESS_DENIED",
                entity: "Security",
                entityId: "warehouse-456",
                userId: undefined,
                userEmail: undefined,
                tenantId: "unknown",
                details: expect.objectContaining({
                    warehouseId: "warehouse-456",
                    reason: "User not authenticated",
                    userRole: undefined,
                }),
            });
        });
    });
    describe("logAuthenticationEvent", () => {
        it("should log login success", async () => {
            await service.logAuthenticationEvent("LOGIN_SUCCESS", mockUser, mockRequest);
            expect(auditLogService.create).toHaveBeenCalledWith({
                action: "LOGIN_SUCCESS",
                entity: "Authentication",
                entityId: "user-123",
                userId: "user-123",
                userEmail: "<EMAIL>",
                tenantId: "tenant-123",
                details: expect.objectContaining({
                    eventType: "LOGIN_SUCCESS",
                    requestPath: "/api/pallets",
                    requestMethod: "GET",
                    userAgent: "Mozilla/5.0",
                    ipAddress: "*************",
                    timestamp: expect.any(String),
                }),
            });
        });
        it("should log token validation failure", async () => {
            await service.logAuthenticationEvent("TOKEN_VALIDATION_FAILURE", undefined, mockRequest, {
                reason: "Token expired",
                tokenExpired: true,
            });
            expect(auditLogService.create).toHaveBeenCalledWith({
                action: "TOKEN_VALIDATION_FAILURE",
                entity: "Authentication",
                entityId: "unknown",
                userId: undefined,
                userEmail: undefined,
                tenantId: "unknown",
                details: expect.objectContaining({
                    eventType: "TOKEN_VALIDATION_FAILURE",
                    reason: "Token expired",
                    tokenExpired: true,
                }),
            });
        });
    });
    describe("logPermissionViolation", () => {
        it("should log insufficient role violation", async () => {
            await service.logPermissionViolation("INSUFFICIENT_ROLE", mockUser, mockRequest, {
                warehouseId: "warehouse-123",
                requiredRole: client_1.Role.WAREHOUSE_MANAGER,
                userRole: client_1.Role.WAREHOUSE_MEMBER,
                operation: "DELETE_WAREHOUSE",
                resource: "Warehouse",
            });
            expect(auditLogService.create).toHaveBeenCalledWith({
                action: "PERMISSION_VIOLATION",
                entity: "Security",
                entityId: "warehouse-123",
                userId: "user-123",
                userEmail: "<EMAIL>",
                tenantId: "tenant-123",
                details: expect.objectContaining({
                    violationType: "INSUFFICIENT_ROLE",
                    warehouseId: "warehouse-123",
                    requiredRole: client_1.Role.WAREHOUSE_MANAGER,
                    userRole: client_1.Role.WAREHOUSE_MEMBER,
                    operation: "DELETE_WAREHOUSE",
                    resource: "Warehouse",
                }),
            });
        });
    });
    describe("logSecurityConfigChange", () => {
        it("should log user role change", async () => {
            await service.logSecurityConfigChange("USER_ROLE_CHANGED", mockUser, mockRequest, {
                targetUserId: "target-user-123",
                targetUserEmail: "<EMAIL>",
                warehouseId: "warehouse-123",
                oldRole: client_1.Role.WAREHOUSE_MEMBER,
                newRole: client_1.Role.WAREHOUSE_MANAGER,
                operation: "UPDATE_USER_ROLE",
            });
            expect(auditLogService.create).toHaveBeenCalledWith({
                action: "USER_ROLE_CHANGED",
                entity: "SecurityConfig",
                entityId: "target-user-123",
                userId: "user-123",
                userEmail: "<EMAIL>",
                tenantId: "tenant-123",
                details: expect.objectContaining({
                    changeType: "USER_ROLE_CHANGED",
                    targetUserId: "target-user-123",
                    targetUserEmail: "<EMAIL>",
                    warehouseId: "warehouse-123",
                    oldRole: client_1.Role.WAREHOUSE_MEMBER,
                    newRole: client_1.Role.WAREHOUSE_MANAGER,
                    operation: "UPDATE_USER_ROLE",
                    performedBy: {
                        userId: "user-123",
                        userEmail: "<EMAIL>",
                        userRole: client_1.Role.WAREHOUSE_MEMBER,
                    },
                }),
            });
        });
    });
});
//# sourceMappingURL=security-audit.service.spec.js.map