{"version": 3, "file": "auth-audit.service.js", "sourceRoot": "", "sources": ["../../../../src/audit-log/services/auth-audit.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,qEAAgE;AASzD,IAAM,gBAAgB,wBAAtB,MAAM,gBAAgB;IAG3B,YAA6B,oBAA0C;QAA1C,yBAAoB,GAApB,oBAAoB,CAAsB;QAFtD,WAAM,GAAG,IAAI,eAAM,CAAC,kBAAgB,CAAC,IAAI,CAAC,CAAC;IAEc,CAAC;IAK3E,KAAK,CAAC,yBAAyB,CAC7B,IAAyB,EACzB,OAAiB;QAEjB,IAAI,CAAC;YAEH,MAAM,GAAG,GAAG,OAAO,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAEnD,MAAM,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,CACpD,0BAA0B,EAC1B,IAAI,EACJ,GAAG,EACH;gBACE,MAAM,EAAE,kCAAkC;aAC3C,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,4CAA4C,IAAI,CAAC,KAAK,EAAE,CACzD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2CAA2C,KAAK,CAAC,OAAO,EAAE,EAC1D,KAAK,CAAC,KAAK,CACZ,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,yBAAyB,CAC7B,MAAc,EACd,MAAe,EACf,SAAkB,EAClB,QAAiB,EACjB,OAAiB;QAEjB,IAAI,CAAC;YAEH,MAAM,GAAG,GAAG,OAAO,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAGnD,MAAM,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC;gBAC3B,EAAE,EAAE,MAAM;gBACV,KAAK,EAAE,SAAS,IAAI,SAAS;gBAC7B,QAAQ,EAAE,QAAQ,IAAI,SAAS;aACT,CAAC,CAAC,CAAC,SAAS,CAAC;YAErC,MAAM,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,CACpD,0BAA0B,EAC1B,WAAW,EACX,GAAG,EACH;gBACE,MAAM;gBACN,YAAY,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;gBACtD,kBAAkB,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;aAC7D,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,oCAAoC,MAAM,aAAa,SAAS,IAAI,MAAM,IAAI,SAAS,EAAE,CAC1F,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2CAA2C,KAAK,CAAC,OAAO,EAAE,EAC1D,KAAK,CAAC,KAAK,CACZ,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CACnB,IAAyB,EACzB,OAAgB;QAEhB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,CACpD,eAAe,EACf,IAAI,EACJ,OAAO,CACR,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,iCAAiC,IAAI,CAAC,KAAK,EAAE,CAC9C,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,gCAAgC,KAAK,CAAC,OAAO,EAAE,EAC/C,KAAK,CAAC,KAAK,CACZ,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CACnB,KAAa,EACb,MAAc,EACd,OAAgB,EAChB,QAAiB;QAEjB,IAAI,CAAC;YAEH,MAAM,WAAW,GAAG;gBAClB,EAAE,EAAE,SAAS;gBACb,KAAK;gBACL,QAAQ,EAAE,QAAQ,IAAI,SAAS;aACT,CAAC;YAEzB,MAAM,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,CACpD,eAAe,EACf,WAAW,EACX,OAAO,EACP;gBACE,MAAM;gBACN,kBAAkB,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;aAC7G,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,kCAAkC,KAAK,KAAK,MAAM,EAAE,CACrD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,gCAAgC,KAAK,CAAC,OAAO,EAAE,EAC/C,KAAK,CAAC,KAAK,CACZ,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,SAAS,CACb,IAAyB,EACzB,OAAgB;QAEhB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,CACpD,QAAQ,EACR,IAAI,EACJ,OAAO,CACR,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,0BAA0B,IAAI,CAAC,KAAK,EAAE,CACvC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,yBAAyB,KAAK,CAAC,OAAO,EAAE,EACxC,KAAK,CAAC,KAAK,CACZ,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,oBAAoB;QAC1B,OAAO;YACL,IAAI,EAAE,gBAAgB;YACtB,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,YAAY,EAAE,cAAc;aAC7B;YACD,UAAU,EAAE;gBACV,aAAa,EAAE,SAAS;aACzB;YACD,MAAM,EAAE;gBACN,aAAa,EAAE,SAAS;aACzB;SACS,CAAC;IACf,CAAC;CACF,CAAA;AAzLY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;qCAIwC,6CAAoB;GAH5D,gBAAgB,CAyL5B"}