{"version": 3, "file": "security-audit.service.js", "sourceRoot": "", "sources": ["../../../../src/audit-log/services/security-audit.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,4DAAuD;AAUhD,IAAM,oBAAoB,4BAA1B,MAAM,oBAAoB;IAG/B,YAA6B,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;QAF5C,WAAM,GAAG,IAAI,eAAM,CAAC,sBAAoB,CAAC,IAAI,CAAC,CAAC;IAEA,CAAC;IAKjE,KAAK,CAAC,yBAAyB,CAC7B,WAAmB,EACnB,IAAyB,EACzB,OAAgB,EAChB,OAIC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;gBAChC,MAAM,EAAE,0BAA0B;gBAClC,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,WAAW;gBACrB,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,SAAS,EAAE,IAAI,CAAC,KAAK;gBACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,OAAO,EAAE;oBACP,WAAW;oBACX,QAAQ,EAAE,OAAO,EAAE,QAAQ,IAAI,IAAI,CAAC,IAAI;oBACxC,SAAS,EAAE,OAAO,EAAE,SAAS;oBAC7B,QAAQ,EAAE,OAAO,EAAE,QAAQ;oBAC3B,WAAW,EAAE,OAAO,CAAC,IAAI;oBACzB,aAAa,EAAE,OAAO,CAAC,MAAM;oBAC7B,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;oBACxC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;oBACzC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,4CAA4C,IAAI,CAAC,KAAK,iBAAiB,WAAW,EAAE,CACrF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2CAA2C,KAAK,CAAC,OAAO,EAAE,EAC1D,KAAK,CAAC,KAAK,CACZ,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAC5B,WAA+B,EAC/B,IAAqC,EACrC,OAAgB,EAChB,MAAc,EACd,OAKC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;gBAChC,MAAM,EAAE,yBAAyB;gBACjC,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,WAAW,IAAI,SAAS;gBAClC,MAAM,EAAE,IAAI,EAAE,EAAE;gBAChB,SAAS,EAAE,IAAI,EAAE,KAAK;gBACtB,QAAQ,EAAE,IAAI,EAAE,QAAQ,IAAI,SAAS;gBACrC,OAAO,EAAE;oBACP,WAAW;oBACX,MAAM;oBACN,QAAQ,EAAE,OAAO,EAAE,QAAQ,IAAI,IAAI,EAAE,IAAI;oBACzC,YAAY,EAAE,OAAO,EAAE,YAAY;oBACnC,SAAS,EAAE,OAAO,EAAE,SAAS;oBAC7B,QAAQ,EAAE,OAAO,EAAE,QAAQ;oBAC3B,WAAW,EAAE,OAAO,CAAC,IAAI;oBACzB,aAAa,EAAE,OAAO,CAAC,MAAM;oBAC7B,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;oBACxC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;oBACzC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,2CAA2C,IAAI,EAAE,KAAK,IAAI,SAAS,iBAAiB,WAAW,IAAI,SAAS,KAAK,MAAM,EAAE,CAC1H,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,0CAA0C,KAAK,CAAC,OAAO,EAAE,EACzD,KAAK,CAAC,KAAK,CACZ,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAC1B,SAAiH,EACjH,IAAqC,EACrC,OAAgB,EAChB,OAIC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;gBAChC,MAAM,EAAE,SAAS;gBACjB,MAAM,EAAE,gBAAgB;gBACxB,QAAQ,EAAE,IAAI,EAAE,EAAE,IAAI,SAAS;gBAC/B,MAAM,EAAE,IAAI,EAAE,EAAE;gBAChB,SAAS,EAAE,IAAI,EAAE,KAAK;gBACtB,QAAQ,EAAE,IAAI,EAAE,QAAQ,IAAI,SAAS;gBACrC,OAAO,EAAE;oBACP,SAAS;oBACT,MAAM,EAAE,OAAO,EAAE,MAAM;oBACvB,YAAY,EAAE,OAAO,EAAE,YAAY;oBACnC,kBAAkB,EAAE,OAAO,EAAE,kBAAkB;oBAC/C,WAAW,EAAE,OAAO,CAAC,IAAI;oBACzB,aAAa,EAAE,OAAO,CAAC,MAAM;oBAC7B,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;oBACxC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;oBACzC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,gCAAgC,SAAS,aAAa,IAAI,EAAE,KAAK,IAAI,SAAS,EAAE,CACjF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,uCAAuC,KAAK,CAAC,OAAO,EAAE,EACtD,KAAK,CAAC,KAAK,CACZ,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAC1B,aAAkH,EAClH,IAAqC,EACrC,OAAgB,EAChB,OAOC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;gBAChC,MAAM,EAAE,sBAAsB;gBAC9B,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,cAAc,IAAI,SAAS;gBACpE,MAAM,EAAE,IAAI,EAAE,EAAE;gBAChB,SAAS,EAAE,IAAI,EAAE,KAAK;gBACtB,QAAQ,EAAE,IAAI,EAAE,QAAQ,IAAI,SAAS;gBACrC,OAAO,EAAE;oBACP,aAAa;oBACb,WAAW,EAAE,OAAO,CAAC,WAAW;oBAChC,YAAY,EAAE,OAAO,CAAC,YAAY;oBAClC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE,IAAI;oBACxC,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,QAAQ,EAAE,OAAO,CAAC,QAAQ;oBAC1B,cAAc,EAAE,OAAO,CAAC,cAAc;oBACtC,WAAW,EAAE,OAAO,CAAC,IAAI;oBACzB,aAAa,EAAE,OAAO,CAAC,MAAM;oBAC7B,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;oBACxC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;oBACzC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,gCAAgC,aAAa,aAAa,IAAI,EAAE,KAAK,IAAI,SAAS,EAAE,CACrF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,uCAAuC,KAAK,CAAC,OAAO,EAAE,EACtD,KAAK,CAAC,KAAK,CACZ,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAC3B,UAAoF,EACpF,IAAyB,EACzB,OAAgB,EAChB,OAOC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;gBAChC,MAAM,EAAE,UAAU;gBAClB,MAAM,EAAE,gBAAgB;gBACxB,QAAQ,EAAE,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,WAAW,IAAI,SAAS;gBAClE,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,SAAS,EAAE,IAAI,CAAC,KAAK;gBACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,OAAO,EAAE;oBACP,UAAU;oBACV,YAAY,EAAE,OAAO,CAAC,YAAY;oBAClC,eAAe,EAAE,OAAO,CAAC,eAAe;oBACxC,WAAW,EAAE,OAAO,CAAC,WAAW;oBAChC,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,WAAW,EAAE;wBACX,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,SAAS,EAAE,IAAI,CAAC,KAAK;wBACrB,QAAQ,EAAE,IAAI,CAAC,IAAI;qBACpB;oBACD,WAAW,EAAE,OAAO,CAAC,IAAI;oBACzB,aAAa,EAAE,OAAO,CAAC,MAAM;oBAC7B,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;oBACxC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;oBACzC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kCAAkC,UAAU,YAAY,IAAI,CAAC,KAAK,EAAE,CACrE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,yCAAyC,KAAK,CAAC,OAAO,EAAE,EACxD,KAAK,CAAC,KAAK,CACZ,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,gBAAgB,CAAC,OAAgB;QACvC,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACrD,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3E,CAAC;QACD,OAAO,OAAO,CAAC,UAAU,CAAC,aAAa,IAAI,OAAO,CAAC,MAAM,CAAC,aAAa,IAAI,SAAS,CAAC;IACvF,CAAC;CACF,CAAA;AArQY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;qCAImC,mCAAe;GAHlD,oBAAoB,CAqQhC"}