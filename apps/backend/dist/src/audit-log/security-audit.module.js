"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityAuditModule = void 0;
const common_1 = require("@nestjs/common");
const security_audit_service_1 = require("./services/security-audit.service");
const auth_audit_service_1 = require("./services/auth-audit.service");
const security_audit_interceptor_1 = require("./interceptors/security-audit.interceptor");
const audit_log_module_1 = require("./audit-log.module");
let SecurityAuditModule = class SecurityAuditModule {
};
exports.SecurityAuditModule = SecurityAuditModule;
exports.SecurityAuditModule = SecurityAuditModule = __decorate([
    (0, common_1.Module)({
        imports: [audit_log_module_1.AuditLogModule],
        providers: [
            security_audit_service_1.SecurityAuditService,
            auth_audit_service_1.AuthAuditService,
            security_audit_interceptor_1.SecurityAuditInterceptor,
        ],
        exports: [
            security_audit_service_1.SecurityAuditService,
            auth_audit_service_1.AuthAuditService,
            security_audit_interceptor_1.SecurityAuditInterceptor,
        ],
    })
], SecurityAuditModule);
//# sourceMappingURL=security-audit.module.js.map