import { PrismaService } from "../prisma/prisma.service";
import { Prisma, AuditLog } from "@prisma/client";
import { CreateAuditLogDto } from "./dto/create-audit-log.dto";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";
export declare class AuditLogService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    create(createAuditLogDto: CreateAuditLogDto, prismaClient?: Prisma.TransactionClient | PrismaService): Promise<void>;
    findByEntityId(entityId: string, tenantId: string, entity?: string): Promise<AuditLog[]>;
    findByEntityIdWithWarehouseContext(entityId: string, currentUser: AuthenticatedUser, entity?: string): Promise<AuditLog[]>;
    findByWarehouse(warehouseId: string, currentUser: AuthenticatedUser, options?: {
        entity?: string;
        action?: string;
        limit?: number;
        offset?: number;
    }): Promise<{
        data: AuditLog[];
        count: number;
    }>;
    private isWarehouseScopedEntity;
    private validateWarehouseAccess;
    private validateUserWarehouseAccess;
    private getLocationIdsInWarehouse;
    private getPalletIdsInWarehouse;
}
