import { ExecutionContext } from '@nestjs/common';
export interface LogActionMetadata {
    action: string;
    entity: string;
    getEntityId?: (context: ExecutionContext, result?: any) => string | undefined;
    getDetails?: (context: ExecutionContext, result?: any, error?: any) => Record<string, any> | undefined;
}
export declare const LOG_ACTION_KEY = "log_action_metadata";
export declare const LogAction: (metadata: LogActionMetadata) => import("@nestjs/common").CustomDecorator<string>;
