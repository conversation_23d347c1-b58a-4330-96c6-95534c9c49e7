"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditBulkOperation = exports.AuditCrossTenant = exports.AuditDataModification = exports.AuditPermissions = exports.AuditAuthentication = exports.AuditHighRisk = exports.AuditWarehouseAccess = exports.SecurityAudit = exports.SECURITY_AUDIT_KEY = void 0;
const common_1 = require("@nestjs/common");
exports.SECURITY_AUDIT_KEY = 'security_audit_options';
const SecurityAudit = (options = {}) => (0, common_1.SetMetadata)(exports.SECURITY_AUDIT_KEY, options);
exports.SecurityAudit = SecurityAudit;
const AuditWarehouseAccess = (operation, resource) => (0, exports.SecurityAudit)({
    operation: operation || 'WAREHOUSE_ACCESS',
    resource: resource || 'Warehouse',
    logSuccess: true,
    logFailure: true,
});
exports.AuditWarehouseAccess = AuditWarehouseAccess;
const AuditHighRisk = (operation, resource) => (0, exports.SecurityAudit)({
    operation,
    resource,
    logSuccess: true,
    logFailure: true,
    highRisk: true,
});
exports.AuditHighRisk = AuditHighRisk;
const AuditAuthentication = (operation) => (0, exports.SecurityAudit)({
    operation: operation || 'AUTHENTICATION',
    resource: 'Authentication',
    logSuccess: true,
    logFailure: true,
});
exports.AuditAuthentication = AuditAuthentication;
const AuditPermissions = (operation, resource) => (0, exports.SecurityAudit)({
    operation,
    resource,
    logSuccess: true,
    logFailure: true,
    getContext: (context) => {
        const request = context.switchToHttp().getRequest();
        return {
            userRole: request.user?.role,
            warehouseContext: request.warehouseContext,
            requestedResource: resource,
        };
    },
});
exports.AuditPermissions = AuditPermissions;
const AuditDataModification = (operation, resource) => (0, exports.SecurityAudit)({
    operation,
    resource,
    logSuccess: true,
    logFailure: true,
    getContext: (context, result, error) => {
        const request = context.switchToHttp().getRequest();
        const auditContext = {
            operation,
            resource,
            userId: request.user?.id,
            userEmail: request.user?.email,
        };
        if (operation.includes('CREATE') || operation.includes('UPDATE')) {
            auditContext.requestData = request.body;
        }
        if (result && !error) {
            auditContext.resultData = result;
        }
        if (error) {
            auditContext.errorDetails = {
                message: error.message,
                status: error.status,
                name: error.name,
            };
        }
        return auditContext;
    },
});
exports.AuditDataModification = AuditDataModification;
const AuditCrossTenant = (operation, resource) => (0, exports.SecurityAudit)({
    operation,
    resource,
    logSuccess: true,
    logFailure: true,
    highRisk: true,
    getContext: (context) => {
        const request = context.switchToHttp().getRequest();
        return {
            userTenantId: request.user?.tenantId,
            targetTenantId: request.params?.tenantId || request.body?.tenantId,
            crossTenantOperation: true,
        };
    },
});
exports.AuditCrossTenant = AuditCrossTenant;
const AuditBulkOperation = (operation, resource) => (0, exports.SecurityAudit)({
    operation,
    resource,
    logSuccess: true,
    logFailure: true,
    getContext: (context, result) => {
        const request = context.switchToHttp().getRequest();
        const bulkData = request.body;
        return {
            bulkOperation: true,
            itemCount: Array.isArray(bulkData) ? bulkData.length : 1,
            affectedItems: Array.isArray(bulkData)
                ? bulkData.map(item => item.id || item.barcode || 'unknown')
                : [bulkData.id || bulkData.barcode || 'unknown'],
            resultCount: result ? (Array.isArray(result) ? result.length : 1) : 0,
        };
    },
});
exports.AuditBulkOperation = AuditBulkOperation;
//# sourceMappingURL=security-audit.decorator.js.map