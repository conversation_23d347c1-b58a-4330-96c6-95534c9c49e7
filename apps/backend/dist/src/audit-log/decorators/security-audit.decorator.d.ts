import { ExecutionContext } from '@nestjs/common';
export interface SecurityAuditOptions {
    operation?: string;
    resource?: string;
    logSuccess?: boolean;
    logFailure?: boolean;
    getContext?: (context: ExecutionContext, result?: any, error?: any) => Record<string, any>;
    highRisk?: boolean;
    metadata?: Record<string, any>;
}
export declare const SECURITY_AUDIT_KEY = "security_audit_options";
export declare const SecurityAudit: (options?: SecurityAuditOptions) => import("@nestjs/common").CustomDecorator<string>;
export declare const AuditWarehouseAccess: (operation?: string, resource?: string) => import("@nestjs/common").CustomDecorator<string>;
export declare const AuditHighRisk: (operation: string, resource: string) => import("@nestjs/common").CustomDecorator<string>;
export declare const AuditAuthentication: (operation?: string) => import("@nestjs/common").CustomDecorator<string>;
export declare const AuditPermissions: (operation: string, resource: string) => import("@nestjs/common").CustomDecorator<string>;
export declare const AuditDataModification: (operation: string, resource: string) => import("@nestjs/common").CustomDecorator<string>;
export declare const AuditCrossTenant: (operation: string, resource: string) => import("@nestjs/common").CustomDecorator<string>;
export declare const AuditBulkOperation: (operation: string, resource: string) => import("@nestjs/common").CustomDecorator<string>;
