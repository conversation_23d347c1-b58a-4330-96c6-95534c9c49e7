{"version": 3, "file": "security-audit.decorator.js", "sourceRoot": "", "sources": ["../../../../src/audit-log/decorators/security-audit.decorator.ts"], "names": [], "mappings": ";;;AAAA,2CAA6C;AAuBhC,QAAA,kBAAkB,GAAG,wBAAwB,CAAC;AAsBpD,MAAM,aAAa,GAAG,CAAC,UAAgC,EAAE,EAAE,EAAE,CAClE,IAAA,oBAAW,EAAC,0BAAkB,EAAE,OAAO,CAAC,CAAC;AAD9B,QAAA,aAAa,iBACiB;AASpC,MAAM,oBAAoB,GAAG,CAAC,SAAkB,EAAE,QAAiB,EAAE,EAAE,CAC5E,IAAA,qBAAa,EAAC;IACZ,SAAS,EAAE,SAAS,IAAI,kBAAkB;IAC1C,QAAQ,EAAE,QAAQ,IAAI,WAAW;IACjC,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,IAAI;CACjB,CAAC,CAAC;AANQ,QAAA,oBAAoB,wBAM5B;AAKE,MAAM,aAAa,GAAG,CAAC,SAAiB,EAAE,QAAgB,EAAE,EAAE,CACnE,IAAA,qBAAa,EAAC;IACZ,SAAS;IACT,QAAQ;IACR,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,IAAI;IAChB,QAAQ,EAAE,IAAI;CACf,CAAC,CAAC;AAPQ,QAAA,aAAa,iBAOrB;AAKE,MAAM,mBAAmB,GAAG,CAAC,SAAkB,EAAE,EAAE,CACxD,IAAA,qBAAa,EAAC;IACZ,SAAS,EAAE,SAAS,IAAI,gBAAgB;IACxC,QAAQ,EAAE,gBAAgB;IAC1B,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,IAAI;CACjB,CAAC,CAAC;AANQ,QAAA,mBAAmB,uBAM3B;AAKE,MAAM,gBAAgB,GAAG,CAAC,SAAiB,EAAE,QAAgB,EAAE,EAAE,CACtE,IAAA,qBAAa,EAAC;IACZ,SAAS;IACT,QAAQ;IACR,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,CAAC,OAAO,EAAE,EAAE;QACtB,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,OAAO;YACL,QAAQ,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI;YAC5B,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;YAC1C,iBAAiB,EAAE,QAAQ;SAC5B,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAdQ,QAAA,gBAAgB,oBAcxB;AAKE,MAAM,qBAAqB,GAAG,CAAC,SAAiB,EAAE,QAAgB,EAAE,EAAE,CAC3E,IAAA,qBAAa,EAAC;IACZ,SAAS;IACT,QAAQ;IACR,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE;QACrC,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,YAAY,GAAwB;YACxC,SAAS;YACT,QAAQ;YACR,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE;YACxB,SAAS,EAAE,OAAO,CAAC,IAAI,EAAE,KAAK;SAC/B,CAAC;QAGF,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjE,YAAY,CAAC,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC;QAC1C,CAAC;QAGD,IAAI,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;YACrB,YAAY,CAAC,UAAU,GAAG,MAAM,CAAC;QACnC,CAAC;QAGD,IAAI,KAAK,EAAE,CAAC;YACV,YAAY,CAAC,YAAY,GAAG;gBAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,IAAI,EAAE,KAAK,CAAC,IAAI;aACjB,CAAC;QACJ,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;CACF,CAAC,CAAC;AApCQ,QAAA,qBAAqB,yBAoC7B;AAKE,MAAM,gBAAgB,GAAG,CAAC,SAAiB,EAAE,QAAgB,EAAE,EAAE,CACtE,IAAA,qBAAa,EAAC;IACZ,SAAS;IACT,QAAQ;IACR,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,IAAI;IAChB,QAAQ,EAAE,IAAI;IACd,UAAU,EAAE,CAAC,OAAO,EAAE,EAAE;QACtB,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,OAAO;YACL,YAAY,EAAE,OAAO,CAAC,IAAI,EAAE,QAAQ;YACpC,cAAc,EAAE,OAAO,CAAC,MAAM,EAAE,QAAQ,IAAI,OAAO,CAAC,IAAI,EAAE,QAAQ;YAClE,oBAAoB,EAAE,IAAI;SAC3B,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAfQ,QAAA,gBAAgB,oBAexB;AAKE,MAAM,kBAAkB,GAAG,CAAC,SAAiB,EAAE,QAAgB,EAAE,EAAE,CACxE,IAAA,qBAAa,EAAC;IACZ,SAAS;IACT,QAAQ;IACR,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC9B,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;QAE9B,OAAO;YACL,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACxD,aAAa,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;gBACpC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,OAAO,IAAI,SAAS,CAAC;gBAC5D,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,IAAI,QAAQ,CAAC,OAAO,IAAI,SAAS,CAAC;YAClD,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACtE,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAnBQ,QAAA,kBAAkB,sBAmB1B"}