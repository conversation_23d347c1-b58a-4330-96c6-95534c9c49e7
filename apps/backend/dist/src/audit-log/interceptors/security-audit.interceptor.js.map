{"version": 3, "file": "security-audit.interceptor.js", "sourceRoot": "", "sources": ["../../../../src/audit-log/interceptors/security-audit.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAQwB;AACxB,uCAAyC;AACzC,+BAA8C;AAC9C,8CAAiD;AACjD,+EAA0E;AAqB7D,QAAA,kBAAkB,GAAG,yBAAyB,CAAC;AAKrD,MAAM,aAAa,GAAG,CAAC,WAAkC,EAAE,EAAE,EAAE,CACpE,OAAO,CAAC,cAAc,CAAC,0BAAkB,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AADnD,QAAA,aAAa,iBACsC;AAMzD,IAAM,wBAAwB,gCAA9B,MAAM,wBAAwB;IAGnC,YACmB,SAAoB,EACpB,oBAA0C;QAD1C,cAAS,GAAT,SAAS,CAAW;QACpB,yBAAoB,GAApB,oBAAoB,CAAsB;QAJ5C,WAAM,GAAG,IAAI,eAAM,CAAC,0BAAwB,CAAC,IAAI,CAAC,CAAC;IAKjE,CAAC;IAEJ,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,qBAAqB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAC9C,0BAAkB,EAClB,OAAO,CAAC,UAAU,EAAE,CACrB,CAAC;QAGF,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC;QAED,MAAM,OAAO,GAAY,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QAC7D,MAAM,IAAI,GACR,OAAO,CAAC,IAA2B,CAAC;QACtC,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAGrD,MAAM,EACJ,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,EACjD,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,EAC/C,UAAU,GAAG,IAAI,EACjB,UAAU,GAAG,IAAI,EACjB,UAAU,GACX,GAAG,qBAAqB,CAAC;QAE1B,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,KAAK,EAAE,MAAM,EAAE,EAAE;YACnB,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;gBACvB,IAAI,CAAC;oBACH,MAAM,aAAa,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAEpE,MAAM,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CACvD,WAAW,IAAI,QAAQ,EACvB,IAAI,EACJ,OAAO,EACP;wBACE,SAAS;wBACT,QAAQ;wBACR,QAAQ,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,WAAW,CAAC;wBACtD,GAAG,aAAa;qBACjB,CACF,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,4CAA4C,KAAK,CAAC,OAAO,EAAE,EAC3D,KAAK,CAAC,KAAK,CACZ,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC,CAAC,EACF,IAAA,sBAAU,EAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YACzB,IAAI,UAAU,EAAE,CAAC;gBACf,IAAI,CAAC;oBACH,MAAM,aAAa,GAAG,UAAU;wBAC9B,CAAC,CAAC,UAAU,CAAC,OAAO,EAAE,SAAS,CAAC;wBAChC,CAAC,CAAC,EAAE,CAAC;oBAEP,IACE,KAAK,YAAY,2BAAkB;wBACnC,KAAK,YAAY,8BAAqB,EACtC,CAAC;wBAED,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;4BACxB,MAAM,IAAI,CAAC,oBAAoB,CAAC,wBAAwB,CACtD,WAAW,EACX,IAAI,EACJ,OAAO,EACP,KAAK,CAAC,OAAO,EACb;gCACE,SAAS;gCACT,QAAQ;gCACR,QAAQ,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,WAAW,CAAC;gCACtD,GAAG,aAAa;6BACjB,CACF,CAAC;wBACJ,CAAC;6BAAM,IAAI,KAAK,YAAY,8BAAqB,EAAE,CAAC;4BAClD,MAAM,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,CACpD,0BAA0B,EAC1B,IAAI,EACJ,OAAO,EACP;gCACE,MAAM,EAAE,KAAK,CAAC,OAAO;gCACrB,GAAG,aAAa;6BACjB,CACF,CAAC;wBACJ,CAAC;6BAAM,CAAC;4BACN,MAAM,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,CACpD,+BAA+B,EAC/B,IAAI,EACJ,OAAO,EACP;gCACE,WAAW;gCACX,SAAS;gCACT,QAAQ;gCACR,QAAQ,EAAE,IAAI,EAAE,IAAI;gCACpB,GAAG,aAAa;6BACjB,CACF,CAAC;wBACJ,CAAC;oBACH,CAAC;gBACH,CAAC;gBAAC,OAAO,UAAU,EAAE,CAAC;oBACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2CAA2C,UAAU,CAAC,OAAO,EAAE,EAC/D,UAAU,CAAC,KAAK,CACjB,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAKO,kBAAkB,CAAC,OAAgB;QAEzC,IAAI,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,CAAC;YAChC,OAAO,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC;QACpC,CAAC;QAGD,IAAI,OAAO,CAAC,KAAK,EAAE,WAAW,EAAE,CAAC;YAC/B,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;gBAC7C,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;gBACnC,CAAC,CAAE,OAAO,CAAC,KAAK,CAAC,WAAsB,CAAC;QAC5C,CAAC;QAGD,IAAI,OAAO,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;YAC9B,OAAO,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;QAClC,CAAC;QAGD,MAAM,gBAAgB,GAAG,OAAc,CAAC;QACxC,IAAI,gBAAgB,CAAC,gBAAgB,EAAE,WAAW,EAAE,CAAC;YACnD,OAAO,gBAAgB,CAAC,gBAAgB,CAAC,WAAW,CAAC;QACvD,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAKO,uBAAuB,CAAC,OAAgB;QAC9C,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAC5C,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAG1B,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9B,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,KAAK;oBACR,OAAO,cAAc,CAAC;gBACxB,KAAK,MAAM;oBACT,OAAO,eAAe,CAAC;gBACzB,KAAK,KAAK;oBACR,OAAO,eAAe,CAAC;gBACzB,KAAK,QAAQ;oBACX,OAAO,eAAe,CAAC;gBACzB;oBACE,OAAO,GAAG,MAAM,UAAU,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAChC,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,KAAK;oBACR,OAAO,gBAAgB,CAAC;gBAC1B,KAAK,MAAM;oBACT,OAAO,iBAAiB,CAAC;gBAC3B,KAAK,KAAK;oBACR,OAAO,iBAAiB,CAAC;gBAC3B,KAAK,QAAQ;oBACX,OAAO,iBAAiB,CAAC;gBAC3B;oBACE,OAAO,GAAG,MAAM,YAAY,CAAC;YACjC,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YACjC,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,KAAK;oBACR,OAAO,iBAAiB,CAAC;gBAC3B,KAAK,MAAM;oBACT,OAAO,kBAAkB,CAAC;gBAC5B,KAAK,KAAK;oBACR,OAAO,kBAAkB,CAAC;gBAC5B,KAAK,QAAQ;oBACX,OAAO,kBAAkB,CAAC;gBAC5B;oBACE,OAAO,GAAG,MAAM,aAAa,CAAC;YAClC,CAAC;QACH,CAAC;QAED,OAAO,GAAG,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,IAAI,SAAS,EAAE,CAAC;IACvE,CAAC;IAKO,sBAAsB,CAAC,OAAgB;QAC7C,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAE1B,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,QAAQ,CAAC;QAC/C,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;YAAE,OAAO,UAAU,CAAC;QACnD,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;YAAE,OAAO,WAAW,CAAC;QACrD,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,MAAM,CAAC;QAC3C,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,MAAM,CAAC;QAC3C,IAAI,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC;YAAE,OAAO,eAAe,CAAC;QAC9D,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;YAAE,OAAO,UAAU,CAAC;QAEnD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC;IACzC,CAAC;IAKO,oBAAoB,CAC1B,IAAyB,EACzB,WAAoB;QAEpB,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB,CAAC;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC;IACzD,CAAC;CACF,CAAA;AA7OY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;qCAKmB,gBAAS;QACE,6CAAoB;GALlD,wBAAwB,CA6OpC"}