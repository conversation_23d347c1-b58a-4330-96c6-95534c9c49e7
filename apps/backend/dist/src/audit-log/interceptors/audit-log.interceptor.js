"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditLogInterceptor = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const operators_1 = require("rxjs/operators");
const audit_log_service_1 = require("../audit-log.service");
const log_action_decorator_1 = require("../decorators/log-action.decorator");
let AuditLogInterceptor = class AuditLogInterceptor {
    constructor(reflector, auditLogService) {
        this.reflector = reflector;
        this.auditLogService = auditLogService;
    }
    intercept(context, next) {
        const logActionMetadata = this.reflector.get(log_action_decorator_1.LOG_ACTION_KEY, context.getHandler());
        if (!logActionMetadata || !logActionMetadata.action || !logActionMetadata.entity) {
            return next.handle();
        }
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        const processLog = async (data, error) => {
            try {
                const { action, entity, getEntityId, getDetails } = logActionMetadata;
                let entityId;
                if (getEntityId) {
                    entityId = getEntityId(context, data);
                }
                else if (request.params?.id) {
                    entityId = request.params.id;
                }
                else if (data?.id) {
                    entityId = data.id;
                }
                if (!entityId) {
                    console.warn(`AuditLogInterceptor: Could not determine entityId for action ${action} on ${entity}. Skipping log.`);
                    return;
                }
                let details;
                if (getDetails) {
                    details = getDetails(context, data, error);
                }
                else if (error) {
                    details = { error: { message: error.message, status: error.status, name: error.name } };
                }
                else if (action.toUpperCase().includes('CREATE') || action.toUpperCase().includes('UPDATE')) {
                    details = { newValues: request.body };
                }
                await this.auditLogService.create({
                    action,
                    entity,
                    entityId,
                    userId: user?.id,
                    userEmail: user?.email,
                    tenantId: user?.tenantId,
                    details,
                });
            }
            catch (logError) {
                console.error('Failed to write audit log:', logError);
            }
        };
        return next.handle().pipe((0, operators_1.tap)((data) => {
            if (user?.tenantId) {
                processLog(data, undefined);
            }
            else {
                console.warn('AuditLogInterceptor: tenantId not found on user. Skipping log.');
            }
        }), (0, operators_1.catchError)((error) => {
            if (user?.tenantId) {
                processLog(undefined, error);
            }
            else {
                console.warn('AuditLogInterceptor: tenantId not found on user during error. Skipping log.');
            }
            throw error;
        }));
    }
};
exports.AuditLogInterceptor = AuditLogInterceptor;
exports.AuditLogInterceptor = AuditLogInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector,
        audit_log_service_1.AuditLogService])
], AuditLogInterceptor);
//# sourceMappingURL=audit-log.interceptor.js.map