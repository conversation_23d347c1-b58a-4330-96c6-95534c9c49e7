"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SecurityAuditInterceptor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityAuditInterceptor = exports.SecurityAudit = exports.SECURITY_AUDIT_KEY = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const security_audit_service_1 = require("../services/security-audit.service");
exports.SECURITY_AUDIT_KEY = "security_audit_metadata";
const SecurityAudit = (metadata = {}) => Reflect.defineMetadata(exports.SECURITY_AUDIT_KEY, metadata, Reflect);
exports.SecurityAudit = SecurityAudit;
let SecurityAuditInterceptor = SecurityAuditInterceptor_1 = class SecurityAuditInterceptor {
    constructor(reflector, securityAuditService) {
        this.reflector = reflector;
        this.securityAuditService = securityAuditService;
        this.logger = new common_1.Logger(SecurityAuditInterceptor_1.name);
    }
    intercept(context, next) {
        const securityAuditMetadata = this.reflector.get(exports.SECURITY_AUDIT_KEY, context.getHandler());
        if (!securityAuditMetadata) {
            return next.handle();
        }
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        const warehouseId = this.extractWarehouseId(request);
        const { operation = this.getOperationFromRequest(request), resource = this.getResourceFromRequest(request), logSuccess = true, logFailure = true, getContext, } = securityAuditMetadata;
        return next.handle().pipe((0, operators_1.tap)(async (result) => {
            if (logSuccess && user) {
                try {
                    const customContext = getContext ? getContext(context, result) : {};
                    await this.securityAuditService.logWarehouseAccessGranted(warehouseId || "global", user, request, {
                        operation,
                        resource,
                        userRole: this.getUserWarehouseRole(user, warehouseId),
                        ...customContext,
                    });
                }
                catch (error) {
                    this.logger.error(`Failed to log successful security audit: ${error.message}`, error.stack);
                }
            }
        }), (0, operators_1.catchError)(async (error) => {
            if (logFailure) {
                try {
                    const customContext = getContext
                        ? getContext(context, undefined)
                        : {};
                    if (error instanceof common_1.ForbiddenException ||
                        error instanceof common_1.UnauthorizedException) {
                        if (warehouseId && user) {
                            await this.securityAuditService.logWarehouseAccessDenied(warehouseId, user, request, error.message, {
                                operation,
                                resource,
                                userRole: this.getUserWarehouseRole(user, warehouseId),
                                ...customContext,
                            });
                        }
                        else if (error instanceof common_1.UnauthorizedException) {
                            await this.securityAuditService.logAuthenticationEvent("TOKEN_VALIDATION_FAILURE", user, request, {
                                reason: error.message,
                                ...customContext,
                            });
                        }
                        else {
                            await this.securityAuditService.logPermissionViolation("UNAUTHORIZED_WAREHOUSE_ACCESS", user, request, {
                                warehouseId,
                                operation,
                                resource,
                                userRole: user?.role,
                                ...customContext,
                            });
                        }
                    }
                }
                catch (auditError) {
                    this.logger.error(`Failed to log security audit for error: ${auditError.message}`, auditError.stack);
                }
            }
            return (0, rxjs_1.throwError)(() => error);
        }));
    }
    extractWarehouseId(request) {
        if (request.params?.warehouseId) {
            return request.params.warehouseId;
        }
        if (request.query?.warehouseId) {
            return Array.isArray(request.query.warehouseId)
                ? String(request.query.warehouseId)
                : request.query.warehouseId;
        }
        if (request.body?.warehouseId) {
            return request.body.warehouseId;
        }
        const warehouseRequest = request;
        if (warehouseRequest.warehouseContext?.warehouseId) {
            return warehouseRequest.warehouseContext.warehouseId;
        }
        return undefined;
    }
    getOperationFromRequest(request) {
        const method = request.method.toUpperCase();
        const path = request.path;
        if (path.includes("/pallets")) {
            switch (method) {
                case "GET":
                    return "VIEW_PALLETS";
                case "POST":
                    return "CREATE_PALLET";
                case "PUT":
                    return "UPDATE_PALLET";
                case "DELETE":
                    return "DELETE_PALLET";
                default:
                    return `${method}_PALLETS`;
            }
        }
        if (path.includes("/locations")) {
            switch (method) {
                case "GET":
                    return "VIEW_LOCATIONS";
                case "POST":
                    return "CREATE_LOCATION";
                case "PUT":
                    return "UPDATE_LOCATION";
                case "DELETE":
                    return "DELETE_LOCATION";
                default:
                    return `${method}_LOCATIONS`;
            }
        }
        if (path.includes("/warehouses")) {
            switch (method) {
                case "GET":
                    return "VIEW_WAREHOUSES";
                case "POST":
                    return "CREATE_WAREHOUSE";
                case "PUT":
                    return "UPDATE_WAREHOUSE";
                case "DELETE":
                    return "DELETE_WAREHOUSE";
                default:
                    return `${method}_WAREHOUSES`;
            }
        }
        return `${method}_${path.split("/")[1]?.toUpperCase() || "UNKNOWN"}`;
    }
    getResourceFromRequest(request) {
        const path = request.path;
        if (path.includes("/pallets"))
            return "Pallet";
        if (path.includes("/locations"))
            return "Location";
        if (path.includes("/warehouses"))
            return "Warehouse";
        if (path.includes("/items"))
            return "Item";
        if (path.includes("/users"))
            return "User";
        if (path.includes("/purchase-orders"))
            return "PurchaseOrder";
        if (path.includes("/shipments"))
            return "Shipment";
        return path.split("/")[1] || "Unknown";
    }
    getUserWarehouseRole(user, warehouseId) {
        if (!warehouseId || !user.getWarehouseRole) {
            return user.role;
        }
        return user.getWarehouseRole(warehouseId) || user.role;
    }
};
exports.SecurityAuditInterceptor = SecurityAuditInterceptor;
exports.SecurityAuditInterceptor = SecurityAuditInterceptor = SecurityAuditInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector,
        security_audit_service_1.SecurityAuditService])
], SecurityAuditInterceptor);
//# sourceMappingURL=security-audit.interceptor.js.map