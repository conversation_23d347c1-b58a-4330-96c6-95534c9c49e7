{"version": 3, "file": "audit-log.interceptor.js", "sourceRoot": "", "sources": ["../../../../src/audit-log/interceptors/audit-log.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAKwB;AACxB,uCAAyC;AAEzC,8CAAiD;AACjD,4DAAuD;AACvD,6EAAuF;AAIhF,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC9B,YACmB,SAAoB,EACpB,eAAgC;QADhC,cAAS,GAAT,SAAS,CAAW;QACpB,oBAAe,GAAf,eAAe,CAAiB;IAChD,CAAC;IAEJ,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAC1C,qCAAc,EACd,OAAO,CAAC,UAAU,EAAE,CACrB,CAAC;QAGF,IAAI,CAAC,iBAAiB,IAAI,CAAC,iBAAiB,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;YACjF,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,IAAI,GAAkC,OAAO,CAAC,IAAI,CAAC;QAGzD,MAAM,UAAU,GAAG,KAAK,EAAE,IAAU,EAAE,KAAW,EAAE,EAAE;YACnD,IAAI,CAAC;gBACH,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,iBAAiB,CAAC;gBAEtE,IAAI,QAA4B,CAAC;gBACjC,IAAI,WAAW,EAAE,CAAC;oBAChB,QAAQ,GAAG,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBACxC,CAAC;qBAAM,IAAI,OAAO,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC;oBAC9B,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC/B,CAAC;qBAAM,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;oBACpB,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC;gBACrB,CAAC;gBAGD,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACZ,OAAO,CAAC,IAAI,CAAC,gEAAgE,MAAM,OAAO,MAAM,iBAAiB,CAAC,CAAC;oBACnH,OAAO;gBACX,CAAC;gBAED,IAAI,OAAwC,CAAC;gBAC7C,IAAI,UAAU,EAAE,CAAC;oBACf,OAAO,GAAG,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC7C,CAAC;qBAAM,IAAI,KAAK,EAAE,CAAC;oBACjB,OAAO,GAAG,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;gBAC1F,CAAC;qBAAM,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAG5F,OAAO,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC;gBAC1C,CAAC;gBAGD,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;oBAChC,MAAM;oBACN,MAAM;oBACN,QAAQ;oBACR,MAAM,EAAE,IAAI,EAAE,EAAE;oBAChB,SAAS,EAAE,IAAI,EAAE,KAAK;oBACtB,QAAQ,EAAE,IAAI,EAAE,QAAQ;oBACxB,OAAO;iBACR,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,QAAQ,EAAE,CAAC;gBAElB,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,QAAQ,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,CAAC;QAEF,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,CAAC,IAAI,EAAE,EAAE;YACX,IAAI,IAAI,EAAE,QAAQ,EAAE,CAAC;gBACjB,UAAU,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACJ,OAAO,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;YACnF,CAAC;QACH,CAAC,CAAC,EACF,IAAA,sBAAU,EAAC,CAAC,KAAK,EAAE,EAAE;YAEnB,IAAI,IAAI,EAAE,QAAQ,EAAE,CAAC;gBACjB,UAAU,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YACjC,CAAC;iBAAM,CAAC;gBACJ,OAAO,CAAC,IAAI,CAAC,6EAA6E,CAAC,CAAC;YAChG,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;CACF,CAAA;AAtFY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAGmB,gBAAS;QACH,mCAAe;GAHxC,mBAAmB,CAsF/B"}