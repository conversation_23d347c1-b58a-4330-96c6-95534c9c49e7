import { NestInterceptor, ExecutionContext, CallHandler } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { Observable } from "rxjs";
import { SecurityAuditService } from "../services/security-audit.service";
export interface SecurityAuditMetadata {
    operation?: string;
    resource?: string;
    logSuccess?: boolean;
    logFailure?: boolean;
    getContext?: (context: ExecutionContext, result?: any) => Record<string, any>;
}
export declare const SECURITY_AUDIT_KEY = "security_audit_metadata";
export declare const SecurityAudit: (metadata?: SecurityAuditMetadata) => void;
export declare class SecurityAuditInterceptor implements NestInterceptor {
    private readonly reflector;
    private readonly securityAuditService;
    private readonly logger;
    constructor(reflector: Reflector, securityAuditService: SecurityAuditService);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
    private extractWarehouseId;
    private getOperationFromRequest;
    private getResourceFromRequest;
    private getUserWarehouseRole;
}
