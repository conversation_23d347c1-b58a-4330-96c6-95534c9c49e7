"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReceivingController = void 0;
const common_1 = require("@nestjs/common");
const receiving_service_1 = require("./receiving.service");
const receive_items_dto_1 = require("./receiving/dto/receive-items.dto");
const jwt_auth_guard_1 = require("./auth/guards/jwt-auth.guard");
const warehouse_permission_guard_1 = require("./auth/guards/warehouse-permission.guard");
const warehouse_permission_decorator_1 = require("./auth/decorators/warehouse-permission.decorator");
let ReceivingController = class ReceivingController {
    constructor(receivingService) {
        this.receivingService = receivingService;
    }
    receiveItems(receiveItemsDto, req) {
        return this.receivingService.receiveItems(receiveItemsDto, req.user);
    }
};
exports.ReceivingController = ReceivingController;
__decorate([
    (0, common_1.Post)(),
    (0, warehouse_permission_decorator_1.RequireWarehouseAccess)(),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true })),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [receive_items_dto_1.ReceiveItemsDto, Object]),
    __metadata("design:returntype", void 0)
], ReceivingController.prototype, "receiveItems", null);
exports.ReceivingController = ReceivingController = __decorate([
    (0, common_1.Controller)("receiving"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, warehouse_permission_guard_1.WarehousePermissionGuard),
    __metadata("design:paramtypes", [receiving_service_1.ReceivingService])
], ReceivingController);
//# sourceMappingURL=receiving.controller.js.map