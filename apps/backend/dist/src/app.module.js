"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const prisma_module_1 = require("./prisma/prisma.module");
const items_module_1 = require("./items/items.module");
const locations_module_1 = require("./locations/locations.module");
const pallets_module_1 = require("./pallets/pallets.module");
const warehouses_module_1 = require("./warehouses/warehouses.module");
const auth_module_1 = require("./auth/auth.module");
const pallet_items_module_1 = require("./pallet-items/pallet-items.module");
const receiving_module_1 = require("./receiving.module");
const shipments_module_1 = require("./shipments/shipments.module");
const receiving_service_1 = require("./receiving.service");
const receiving_controller_1 = require("./receiving.controller");
const onboarding_module_1 = require("./onboarding/onboarding.module");
const purchase_orders_module_1 = require("./purchase-orders/purchase-orders.module");
const users_module_1 = require("./users/users.module");
const audit_log_module_1 = require("./audit-log/audit-log.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: ".env",
            }),
            prisma_module_1.PrismaModule,
            items_module_1.ItemsModule,
            locations_module_1.LocationsModule,
            pallets_module_1.PalletsModule,
            warehouses_module_1.WarehousesModule,
            auth_module_1.AuthModule,
            pallet_items_module_1.PalletItemsModule,
            receiving_module_1.ReceivingModule,
            onboarding_module_1.OnboardingModule,
            purchase_orders_module_1.PurchaseOrdersModule,
            users_module_1.UsersModule,
            audit_log_module_1.AuditLogModule,
            shipments_module_1.ShipmentsModule,
        ],
        controllers: [app_controller_1.AppController, receiving_controller_1.ReceivingController],
        providers: [app_service_1.AppService, receiving_service_1.ReceivingService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map