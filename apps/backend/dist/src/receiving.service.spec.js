"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const receiving_service_1 = require("./receiving.service");
const prisma_service_1 = require("./prisma/prisma.service");
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const mockTenantId = "tenant-123";
const mockWarehouseId = "warehouse-abc";
const mockLocationId = "location-xyz";
const mockItemId1 = "item-001";
const mockItemId2 = "item-002";
const mockUser = {
    id: "user-789",
    tenantId: mockTenantId,
    warehouseUsers: [
        { warehouseId: mockWarehouseId, role: "WAREHOUSE_MEMBER" },
    ],
    email: "<EMAIL>",
    role: client_1.Role.WAREHOUSE_MEMBER,
    name: "Test User",
    authUserId: "auth-user-123",
    createdAt: new Date(),
    updatedAt: new Date(),
};
const mockLocation = {
    id: mockLocationId,
    warehouseId: mockWarehouseId,
    locationType: "Receiving",
    tenantId: mockTenantId,
    warehouse: {
        tenantId: mockTenantId,
    },
};
const mockItem1 = {
    id: mockItemId1,
    tenantId: mockTenantId,
};
const mockNewPallet = {
    id: "pallet-new-123",
    tenantId: mockTenantId,
    status: "RECEIVED",
    locationId: mockLocationId,
    PalletItems: [],
};
describe("ReceivingService", () => {
    let service;
    let prisma;
    beforeEach(async () => {
        const mockPrismaService = {
            location: {
                findUnique: jest.fn(),
            },
            item: {
                findMany: jest.fn(),
            },
            warehouseItem: {
                findUnique: jest.fn(),
                create: jest.fn(),
            },
            pallet: {
                create: jest.fn(),
                findUniqueOrThrow: jest.fn(),
            },
            palletItem: {
                createMany: jest.fn(),
            },
            $transaction: jest.fn().mockImplementation(async (callback) => {
                const mockTx = {
                    warehouseItem: {
                        findUnique: jest.fn(),
                        create: jest.fn(),
                    },
                    pallet: {
                        create: jest.fn(),
                        findUniqueOrThrow: jest.fn(),
                    },
                    palletItem: {
                        createMany: jest.fn(),
                    },
                };
                return callback(mockTx);
            }),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                receiving_service_1.ReceivingService,
                { provide: prisma_service_1.PrismaService, useValue: mockPrismaService },
            ],
        }).compile();
        service = module.get(receiving_service_1.ReceivingService);
        prisma = module.get(prisma_service_1.PrismaService);
    });
    it("should be defined", () => {
        expect(service).toBeDefined();
    });
    describe("receiveItems", () => {
        const receiveItemsDto = {
            poNumber: "PO-TEST-001",
            receivingLocationId: mockLocationId,
            barcode: "TEST-12345",
            description: "Test pallet description",
            items: [{ itemId: mockItemId1, quantity: 10 }],
        };
        it("should throw BadRequestException if no items and no description are provided", async () => {
            const dto = {
                ...receiveItemsDto,
                items: [],
                description: "",
            };
            await expect(service.receiveItems(dto, mockUser)).rejects.toThrow(new common_1.BadRequestException("A pallet must have at least one item or a description."));
        });
        it("should succeed with a description but no items", async () => {
            const dto = {
                poNumber: "PO-TEST-002",
                receivingLocationId: mockLocationId,
                barcode: "TEST-12345",
                items: [],
                description: "Empty pallet received for future use",
            };
            prisma.location.findUnique.mockResolvedValue(mockLocation);
            prisma.item.findMany.mockResolvedValue([]);
            const mockTx = {
                pallet: {
                    create: jest
                        .fn()
                        .mockResolvedValue({ ...mockNewPallet, PalletItems: [] }),
                    findUniqueOrThrow: jest.fn().mockResolvedValue({
                        ...mockNewPallet,
                        PalletItems: [],
                    }),
                },
                palletItem: {
                    createMany: jest.fn(),
                },
            };
            prisma.$transaction.mockImplementation(async (callback) => callback(mockTx));
            const result = await service.receiveItems(dto, mockUser);
            expect(result).toBeDefined();
            expect(mockTx.pallet.create).toHaveBeenCalled();
            expect(mockTx.palletItem.createMany).not.toHaveBeenCalled();
        });
        it("should successfully receive items and create pallet", async () => {
            prisma.location.findUnique.mockResolvedValue(mockLocation);
            prisma.item.findMany.mockResolvedValue([mockItem1]);
            const mockTx = {
                warehouseItem: {
                    findUnique: jest.fn().mockResolvedValue(null),
                    create: jest.fn().mockResolvedValue({
                        id: "wh-item-1",
                        tenantId: mockTenantId,
                        warehouseId: mockWarehouseId,
                        itemId: mockItemId1,
                    }),
                },
                pallet: {
                    create: jest.fn().mockResolvedValue(mockNewPallet),
                    findUniqueOrThrow: jest.fn().mockResolvedValue({
                        ...mockNewPallet,
                        PalletItems: [{ itemId: mockItemId1, quantity: 10 }],
                    }),
                },
                palletItem: {
                    createMany: jest.fn().mockResolvedValue({ count: 1 }),
                },
            };
            prisma.$transaction.mockImplementation(async (callback) => callback(mockTx));
            const result = await service.receiveItems(receiveItemsDto, mockUser);
            expect(prisma.location.findUnique).toHaveBeenCalledWith({
                where: { id: mockLocationId },
                include: { warehouse: { select: { tenantId: true } } },
            });
            expect(prisma.item.findMany).toHaveBeenCalledWith({
                where: { id: { in: [mockItemId1] }, tenantId: mockTenantId },
                select: { id: true },
            });
            expect(mockTx.warehouseItem.findUnique).toHaveBeenCalledWith({
                where: {
                    tenant_warehouse_item_unique: {
                        tenantId: mockTenantId,
                        warehouseId: mockWarehouseId,
                        itemId: mockItemId1,
                    },
                },
            });
            expect(mockTx.warehouseItem.create).toHaveBeenCalledWith({
                data: {
                    tenantId: mockTenantId,
                    itemId: mockItemId1,
                    warehouseId: mockWarehouseId,
                },
            });
            expect(mockTx.pallet.create).toHaveBeenCalledWith({
                data: {
                    tenantId: mockTenantId,
                    status: "RECEIVED",
                    locationId: mockLocationId,
                },
            });
            expect(mockTx.palletItem.createMany).toHaveBeenCalledWith({
                data: [
                    {
                        palletId: mockNewPallet.id,
                        itemId: mockItemId1,
                        quantity: 10,
                        tenantId: mockTenantId,
                    },
                ],
            });
            expect(mockTx.pallet.findUniqueOrThrow).toHaveBeenCalledWith({
                where: { id: mockNewPallet.id },
                include: { palletItems: true, location: true },
            });
            expect(result.id).toBe(mockNewPallet.id);
            expect(result.palletItems).toHaveLength(1);
            expect(result.palletItems[0].quantity).toBe(10);
        });
    });
});
//# sourceMappingURL=receiving.service.spec.js.map