"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const receiving_controller_1 = require("./receiving.controller");
describe('ReceivingController', () => {
    let controller;
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            controllers: [receiving_controller_1.ReceivingController],
        }).compile();
        controller = module.get(receiving_controller_1.ReceivingController);
    });
    it('should be defined', () => {
        expect(controller).toBeDefined();
    });
});
//# sourceMappingURL=receiving.controller.spec.js.map