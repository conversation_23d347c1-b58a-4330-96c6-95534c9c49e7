import { PrismaService } from "./prisma/prisma.service";
import { ReceiveItemsDto } from "./receiving/dto/receive-items.dto";
import { Prisma } from "@prisma/client";
import { AuthenticatedUser } from "./auth/types/authenticated-user.interface";
import { AuditLogService } from "./audit-log/audit-log.service";
declare const palletWithItems: {
    include: {
        palletItems: true;
    };
};
type PalletWithItems = Prisma.PalletGetPayload<typeof palletWithItems>;
export declare class ReceivingService {
    private prisma;
    private auditLogService;
    constructor(prisma: PrismaService, auditLogService: AuditLogService);
    receiveItems(dto: ReceiveItemsDto, currentUser: AuthenticatedUser): Promise<PalletWithItems>;
}
export {};
