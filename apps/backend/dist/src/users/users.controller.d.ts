import { UsersService } from "./users.service";
import { EnhancedUserPayload } from "../auth/types";
import { UserResponseDto } from "./dto/user-response.dto";
import { UpdateUserDto } from "./dto/update-user.dto";
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    findAll(req: {
        user: EnhancedUserPayload;
    }): Promise<UserResponseDto[]>;
    updateProfile(updateUserDto: UpdateUserDto, req: {
        user: EnhancedUserPayload;
    }): Promise<UserResponseDto>;
}
