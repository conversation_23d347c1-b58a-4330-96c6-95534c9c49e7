import { PrismaService } from "../prisma/prisma.service";
import { UserResponseDto } from "./dto/user-response.dto";
import { UpdateUserDto } from "./dto/update-user.dto";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";
export declare class UsersService {
    private prisma;
    constructor(prisma: PrismaService);
    findAllByTenant(tenantId: string): Promise<UserResponseDto[]>;
    updateProfile(userId: string, updateUserDto: UpdateUserDto, currentUser: AuthenticatedUser): Promise<UserResponseDto>;
}
