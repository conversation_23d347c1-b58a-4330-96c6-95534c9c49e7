import { PrismaService } from "../prisma/prisma.service";
import { QueryPurchaseOrderDto, PurchaseOrderResponse } from "./dto";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";
export declare class PurchaseOrdersService {
    private prisma;
    constructor(prisma: PrismaService);
    findAll(currentUser: AuthenticatedUser, queryDto: QueryPurchaseOrderDto, warehouseId?: string): Promise<{
        data: PurchaseOrderResponse[];
        count: number;
    }>;
    findOne(poNumber: string, currentUser: AuthenticatedUser, warehouseId?: string): Promise<PurchaseOrderResponse>;
}
