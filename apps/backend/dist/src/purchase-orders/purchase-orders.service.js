"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PurchaseOrdersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
const purchaseOrderFindUniqueArgs = client_1.Prisma.validator()({
    include: {
        items: {
            include: {
                item: true,
            },
        },
        warehouse: true,
    },
});
let PurchaseOrdersService = class PurchaseOrdersService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findAll(currentUser, queryDto, warehouseId) {
        const { page = 1, limit = 10, status, sortBy = "createdAt", sortOrder = "desc", } = queryDto;
        const skip = (page - 1) * limit;
        const whereClause = {
            warehouse: {
                tenantId: currentUser.tenantId,
            },
        };
        if (currentUser.role !== client_1.Role.TENANT_ADMIN) {
            const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
            if (userWarehouseIds.length === 0) {
                return { data: [], count: 0 };
            }
            whereClause.warehouseId = { in: userWarehouseIds };
        }
        if (warehouseId) {
            whereClause.warehouseId = warehouseId;
        }
        if (status) {
            whereClause.status = status;
        }
        const purchaseOrders = await this.prisma.purchaseOrder.findMany({
            where: whereClause,
            include: {
                items: { include: { item: true } },
                warehouse: true,
            },
            orderBy: {
                [sortBy]: sortOrder,
            },
            skip: skip,
            take: limit,
        });
        const totalCount = await this.prisma.purchaseOrder.count({
            where: whereClause,
        });
        const formattedResponse = purchaseOrders.map((po) => {
            const poAsDetailed = po;
            return {
                id: poAsDetailed.id,
                poNumber: poAsDetailed.poNumber,
                status: poAsDetailed.status,
                supplier: poAsDetailed.supplier,
                notes: poAsDetailed.notes,
                orderDate: poAsDetailed.orderDate,
                expectedDeliveryDate: poAsDetailed.expectedDeliveryDate,
                createdAt: poAsDetailed.createdAt,
                updatedAt: poAsDetailed.updatedAt,
                tenantId: poAsDetailed.tenantId,
                warehouseId: poAsDetailed.warehouseId,
                warehouse: poAsDetailed.warehouse
                    ? {
                        id: poAsDetailed.warehouse.id,
                        name: poAsDetailed.warehouse.name,
                        address: poAsDetailed.warehouse.address,
                        status: poAsDetailed.warehouse.status,
                        tenantId: poAsDetailed.warehouse.tenantId,
                        createdAt: poAsDetailed.warehouse.createdAt,
                        updatedAt: poAsDetailed.warehouse.updatedAt,
                    }
                    : undefined,
                items: poAsDetailed.items.map((item) => ({
                    id: item.id,
                    quantity: item.quantity,
                    unitCost: item.unitCost,
                    receivedQuantity: item.receivedQuantity,
                    purchaseOrderId: item.purchaseOrderId,
                    itemId: item.itemId,
                    item: item.item
                        ? {
                            id: item.item.id,
                            sku: item.item.sku,
                            name: item.item.name,
                            description: item.item.description,
                            unitOfMeasure: item.item.unitOfMeasure,
                            status: item.item.status,
                            createdAt: item.item.createdAt,
                            updatedAt: item.item.updatedAt,
                            tenantId: item.item.tenantId,
                            defaultCost: item.item.defaultCost,
                            lowStockThreshold: item.item.lowStockThreshold,
                        }
                        : undefined,
                })),
            };
        });
        return { data: formattedResponse, count: totalCount };
    }
    async findOne(poNumber, currentUser, warehouseId) {
        const whereClause = {
            poNumber: poNumber,
            warehouse: {
                tenantId: currentUser.tenantId,
            },
        };
        if (currentUser.role !== client_1.Role.TENANT_ADMIN) {
            const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
            if (userWarehouseIds.length === 0) {
                throw new common_1.NotFoundException(`Purchase Order with PO Number '${poNumber}' not found.`);
            }
            whereClause.warehouseId = { in: userWarehouseIds };
        }
        if (warehouseId) {
            whereClause.warehouseId = warehouseId;
        }
        const purchaseOrder = await this.prisma.purchaseOrder.findFirst({
            where: whereClause,
            include: purchaseOrderFindUniqueArgs.include,
        });
        if (!purchaseOrder) {
            throw new common_1.NotFoundException(`Purchase Order with PO Number '${poNumber}' not found.`);
        }
        const formattedPo = {
            id: purchaseOrder.id,
            poNumber: purchaseOrder.poNumber,
            status: purchaseOrder.status,
            supplier: purchaseOrder.supplier,
            notes: purchaseOrder.notes,
            orderDate: purchaseOrder.orderDate,
            expectedDeliveryDate: purchaseOrder.expectedDeliveryDate,
            createdAt: purchaseOrder.createdAt,
            updatedAt: purchaseOrder.updatedAt,
            tenantId: purchaseOrder.tenantId,
            warehouseId: purchaseOrder.warehouseId,
            warehouse: purchaseOrder.warehouse
                ? {
                    id: purchaseOrder.warehouse.id,
                    name: purchaseOrder.warehouse.name,
                    address: purchaseOrder.warehouse.address,
                    status: purchaseOrder.warehouse.status,
                    tenantId: purchaseOrder.warehouse.tenantId,
                    createdAt: purchaseOrder.warehouse.createdAt,
                    updatedAt: purchaseOrder.warehouse.updatedAt,
                }
                : undefined,
            items: purchaseOrder.items.map((poItem) => ({
                id: poItem.id,
                quantity: poItem.quantity,
                unitCost: poItem.unitCost,
                receivedQuantity: poItem.receivedQuantity,
                purchaseOrderId: poItem.purchaseOrderId,
                itemId: poItem.itemId,
                item: poItem.item
                    ? {
                        id: poItem.item.id,
                        sku: poItem.item.sku,
                        name: poItem.item.name,
                        description: poItem.item.description,
                        unitOfMeasure: poItem.item.unitOfMeasure,
                        status: poItem.item.status,
                        createdAt: poItem.item.createdAt,
                        updatedAt: poItem.item.updatedAt,
                        tenantId: poItem.item.tenantId,
                        defaultCost: poItem.item.defaultCost,
                        lowStockThreshold: poItem.item.lowStockThreshold,
                    }
                    : undefined,
            })),
        };
        return formattedPo;
    }
};
exports.PurchaseOrdersService = PurchaseOrdersService;
exports.PurchaseOrdersService = PurchaseOrdersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], PurchaseOrdersService);
//# sourceMappingURL=purchase-orders.service.js.map