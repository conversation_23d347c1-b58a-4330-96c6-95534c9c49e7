"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PurchaseOrdersController = void 0;
const common_1 = require("@nestjs/common");
const purchase_orders_service_1 = require("./purchase-orders.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const warehouse_permission_guard_1 = require("../auth/guards/warehouse-permission.guard");
const warehouse_permission_decorator_1 = require("../auth/decorators/warehouse-permission.decorator");
const dto_1 = require("./dto");
const swagger_1 = require("@nestjs/swagger");
let PurchaseOrdersController = class PurchaseOrdersController {
    constructor(purchaseOrdersService) {
        this.purchaseOrdersService = purchaseOrdersService;
    }
    async findAll(req, queryDto, warehouseId) {
        return this.purchaseOrdersService.findAll(req.user, queryDto, warehouseId);
    }
    async findOne(poNumber, req, warehouseId) {
        return this.purchaseOrdersService.findOne(poNumber, req.user, warehouseId);
    }
};
exports.PurchaseOrdersController = PurchaseOrdersController;
__decorate([
    (0, common_1.Get)(),
    (0, warehouse_permission_decorator_1.AllowWarehouseFiltering)(),
    (0, swagger_1.ApiOperation)({ summary: "Get all purchase orders for the tenant" }),
    (0, swagger_1.ApiQuery)({
        name: "page",
        required: false,
        type: Number,
        description: "Page number for pagination",
    }),
    (0, swagger_1.ApiQuery)({
        name: "limit",
        required: false,
        type: Number,
        description: "Number of items per page",
    }),
    (0, swagger_1.ApiQuery)({
        name: "status",
        required: false,
        type: String,
        description: "Filter by purchase order status",
    }),
    (0, swagger_1.ApiQuery)({
        name: "sortBy",
        required: false,
        type: String,
        description: "Field to sort by (e.g., createdAt, poNumber)",
    }),
    (0, swagger_1.ApiQuery)({
        name: "sortOrder",
        required: false,
        enum: ["asc", "desc"],
        description: "Sort order (asc or desc)",
    }),
    (0, swagger_1.ApiQuery)({
        name: "warehouseId",
        required: false,
        type: String,
        description: "Filter by warehouse ID",
    }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Query)("warehouseId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dto_1.QueryPurchaseOrderDto, String]),
    __metadata("design:returntype", Promise)
], PurchaseOrdersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(":poNumber"),
    (0, warehouse_permission_decorator_1.RequirePurchaseOrderAccess)(),
    (0, swagger_1.ApiOperation)({ summary: "Get a specific purchase order by PO Number" }),
    (0, swagger_1.ApiParam)({
        name: "poNumber",
        type: String,
        description: "The Purchase Order Number",
    }),
    (0, swagger_1.ApiQuery)({
        name: "warehouseId",
        required: false,
        type: String,
        description: "Filter by warehouse ID",
    }),
    __param(0, (0, common_1.Param)("poNumber")),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Query)("warehouseId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String]),
    __metadata("design:returntype", Promise)
], PurchaseOrdersController.prototype, "findOne", null);
exports.PurchaseOrdersController = PurchaseOrdersController = __decorate([
    (0, swagger_1.ApiTags)("Purchase Orders"),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, warehouse_permission_guard_1.WarehousePermissionGuard),
    (0, common_1.Controller)("purchase-orders"),
    __metadata("design:paramtypes", [purchase_orders_service_1.PurchaseOrdersService])
], PurchaseOrdersController);
//# sourceMappingURL=purchase-orders.controller.js.map