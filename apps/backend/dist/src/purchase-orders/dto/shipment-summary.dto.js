"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShipmentSummaryResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const client_1 = require("@prisma/client");
class WarehouseSummaryDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WarehouseSummaryDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], WarehouseSummaryDto.prototype, "name", void 0);
class LocationSummaryDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], LocationSummaryDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], LocationSummaryDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: client_1.LocationCategory }),
    __metadata("design:type", String)
], LocationSummaryDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ type: () => WarehouseSummaryDto }),
    __metadata("design:type", WarehouseSummaryDto)
], LocationSummaryDto.prototype, "warehouse", void 0);
class PalletSummaryDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], PalletSummaryDto.prototype, "palletId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], PalletSummaryDto.prototype, "barcode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], PalletSummaryDto.prototype, "contentsSummary", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => LocationSummaryDto, nullable: true }),
    __metadata("design:type", LocationSummaryDto)
], PalletSummaryDto.prototype, "location", void 0);
class GroupedPalletsDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], GroupedPalletsDto.prototype, "shipToDestination", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => [PalletSummaryDto] }),
    __metadata("design:type", Array)
], GroupedPalletsDto.prototype, "pallets", void 0);
class ShipmentSummaryResponseDto {
}
exports.ShipmentSummaryResponseDto = ShipmentSummaryResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], ShipmentSummaryResponseDto.prototype, "shipmentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], ShipmentSummaryResponseDto.prototype, "referenceNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: () => [GroupedPalletsDto] }),
    __metadata("design:type", Array)
], ShipmentSummaryResponseDto.prototype, "groupedPallets", void 0);
//# sourceMappingURL=shipment-summary.dto.js.map