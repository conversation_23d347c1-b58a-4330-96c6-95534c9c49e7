import { LocationCategory } from "@prisma/client";
declare class WarehouseSummaryDto {
    id: string;
    name: string;
}
declare class LocationSummaryDto {
    id: string;
    name: string;
    category: LocationCategory;
    warehouse?: WarehouseSummaryDto;
}
declare class PalletSummaryDto {
    palletId: string;
    barcode: string;
    contentsSummary: string;
    location: LocationSummaryDto | null;
}
declare class GroupedPalletsDto {
    shipToDestination: string;
    pallets: PalletSummaryDto[];
}
export declare class ShipmentSummaryResponseDto {
    shipmentId: string;
    referenceNumber: string;
    groupedPallets: GroupedPalletsDto[];
}
export {};
