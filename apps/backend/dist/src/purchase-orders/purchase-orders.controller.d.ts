import { PurchaseOrdersService } from "./purchase-orders.service";
import { RequestWithWarehouseContext } from "../auth/decorators/warehouse-permission.decorator";
import { EnhancedUserPayload } from "../auth/types";
import { QueryPurchaseOrderDto, PurchaseOrderResponse } from "./dto";
export declare class PurchaseOrdersController {
    private readonly purchaseOrdersService;
    constructor(purchaseOrdersService: PurchaseOrdersService);
    findAll(req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>, queryDto: QueryPurchaseOrderDto, warehouseId?: string): Promise<{
        data: PurchaseOrderResponse[];
        count: number;
    }>;
    findOne(poNumber: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>, warehouseId?: string): Promise<PurchaseOrderResponse>;
}
