import { Request } from "express";
import { User as PrismaUser, WarehouseUser, Role } from "@prisma/client";
export interface UserPayload extends PrismaUser {
    warehouseUsers: {
        warehouseId: string;
        role: WarehouseUser["role"];
    }[];
}
export interface AuthenticatedRequest extends Request {
    user: UserPayload;
}
export interface JwtPayload {
    userId: string;
    sub: string;
    email: string;
    role: Role;
    tenantId: string | null;
    warehouseAccess?: WarehouseAccessInfo[];
    iat?: number;
    exp?: number;
}
export interface WarehouseAccessInfo {
    warehouseId: string;
    role: Role;
    warehouseName?: string;
}
export interface EnhancedUserPayload extends UserPayload {
    accessibleWarehouses: string[];
    warehouseRoles: Map<string, Role>;
    canAccessWarehouse: (warehouseId: string) => boolean;
    getWarehouseRole: (warehouseId: string) => Role | null;
    hasWarehousePermission: (warehouseId: string, requiredRole: Role) => boolean;
    isTenantAdmin: () => boolean;
}
export interface WarehouseContext {
    warehouseId: string;
    userRole: Role;
    isAdmin: boolean;
    isManager: boolean;
    isMember: boolean;
}
export interface RequestWithWarehouseContext<T = any> extends Request {
    user: EnhancedUserPayload;
    warehouseContext?: WarehouseContext;
}
