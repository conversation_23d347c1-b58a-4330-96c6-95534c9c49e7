import { Role } from "@prisma/client";
export interface AuthenticatedUser {
    id: string;
    email: string;
    role: Role;
    tenantId: string;
    name: string | null;
    authUserId: string | null;
    warehouseUsers?: {
        warehouseId: string;
        role: Role;
    }[];
    accessibleWarehouses?: string[];
    warehouseRoles?: Map<string, Role>;
    canAccessWarehouse?: (warehouseId: string) => boolean;
    getWarehouseRole?: (warehouseId: string) => Role | null;
    hasWarehousePermission?: (warehouseId: string, requiredRole: Role) => boolean;
}
