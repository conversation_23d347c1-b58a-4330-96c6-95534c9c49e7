"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthModule = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const passport_1 = require("@nestjs/passport");
const config_1 = require("@nestjs/config");
const auth_service_1 = require("./auth.service");
const auth_controller_1 = require("./auth.controller");
const jwt_strategy_1 = require("./jwt.strategy");
const constants_1 = require("./constants");
const prisma_module_1 = require("../prisma/prisma.module");
const warehouse_permission_guard_1 = require("./guards/warehouse-permission.guard");
const role_based_guard_1 = require("./guards/role-based.guard");
const warehouses_module_1 = require("../warehouses/warehouses.module");
const audit_log_module_1 = require("../audit-log/audit-log.module");
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            prisma_module_1.PrismaModule,
            passport_1.PassportModule,
            config_1.ConfigModule,
            warehouses_module_1.WarehousesModule,
            audit_log_module_1.AuditLogModule,
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    secret: process.env.NODE_ENV === "test"
                        ? process.env.TEST_JWT_SECRET
                        : configService.get(constants_1.jwtConstants.secretKeyName),
                    signOptions: { expiresIn: "1d" },
                }),
                inject: [config_1.ConfigService],
            }),
        ],
        controllers: [auth_controller_1.AuthController],
        providers: [
            auth_service_1.AuthService,
            jwt_strategy_1.JwtStrategy,
            warehouse_permission_guard_1.WarehousePermissionGuard,
            role_based_guard_1.RoleBasedGuard,
            role_based_guard_1.TenantAdminGuard,
            role_based_guard_1.WarehouseManagerGuard,
            role_based_guard_1.WarehouseMemberGuard,
            role_based_guard_1.WarehouseSpecificRoleGuard,
        ],
        exports: [
            auth_service_1.AuthService,
            jwt_1.JwtModule,
            warehouse_permission_guard_1.WarehousePermissionGuard,
            role_based_guard_1.RoleBasedGuard,
            role_based_guard_1.TenantAdminGuard,
            role_based_guard_1.WarehouseManagerGuard,
            role_based_guard_1.WarehouseMemberGuard,
            role_based_guard_1.WarehouseSpecificRoleGuard,
        ],
    })
], AuthModule);
//# sourceMappingURL=auth.module.js.map