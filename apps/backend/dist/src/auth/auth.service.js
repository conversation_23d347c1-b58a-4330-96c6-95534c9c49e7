"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AuthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const prisma_service_1 = require("../prisma/prisma.service");
const config_1 = require("@nestjs/config");
const constants_1 = require("./constants");
const bcrypt = __importStar(require("bcryptjs"));
const common_2 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const supabase_js_1 = require("@supabase/supabase-js");
let AuthService = AuthService_1 = class AuthService {
    constructor(prisma, jwtService, configService) {
        this.prisma = prisma;
        this.jwtService = jwtService;
        this.configService = configService;
        this.logger = new common_1.Logger(AuthService_1.name);
    }
    async login(loginDto) {
        let supabasePayload;
        try {
            const verificationSecret = process.env.NODE_ENV === "test"
                ? process.env.TEST_JWT_SECRET
                : this.configService.get(constants_1.jwtConstants.secretKeyName);
            this.logger.log(`Attempting JWT verification. NODE_ENV: ${process.env.NODE_ENV}`);
            this.logger.log(`Using secret key name from constants: ${constants_1.jwtConstants.secretKeyName}`);
            this.logger.log(`Retrieved verification secret: ${verificationSecret ? "******** (present)" : "MISSING OR EMPTY"}`);
            if (!verificationSecret) {
                this.logger.error(`JWT verification secret is missing or empty for key name: ${constants_1.jwtConstants.secretKeyName}. Ensure it's set in environment variables.`);
                throw new common_2.InternalServerErrorException("Internal configuration error: JWT secret missing.");
            }
            supabasePayload = await this.jwtService.verifyAsync(loginDto.supabaseToken, { secret: verificationSecret });
            this.logger.debug(`Supabase token verified for sub: ${supabasePayload.sub}`);
        }
        catch (error) {
            this.logger.error(`Supabase token validation failed: ${error.message}`, error.stack);
            if (error.name === "TokenExpiredError") {
                throw new common_1.UnauthorizedException("Token has expired");
            }
            if (error.name === "JsonWebTokenError") {
                throw new common_1.UnauthorizedException("Invalid token");
            }
            throw new common_1.UnauthorizedException("Supabase token validation failed");
        }
        if (!supabasePayload.sub || !supabasePayload.email) {
            this.logger.error("Supabase token missing required claims (sub or email)");
            throw new common_1.UnauthorizedException("Invalid Supabase token claims");
        }
        let user = await this.prisma.user.findUnique({
            where: { authUserId: supabasePayload.sub },
            include: {
                warehouseUsers: {
                    select: {
                        warehouseId: true,
                        role: true,
                    },
                },
            },
        });
        if (!user) {
            this.logger.log(`User with Supabase ID ${supabasePayload.sub} not found locally. Checking for existing email ${supabasePayload.email}.`);
            const existingUserWithEmail = await this.prisma.user.findUnique({
                where: { email: supabasePayload.email },
                include: {
                    warehouseUsers: {
                        select: {
                            warehouseId: true,
                            role: true,
                        },
                    },
                },
            });
            if (existingUserWithEmail) {
                this.logger.log(`Found existing user with email ${supabasePayload.email} (ID: ${existingUserWithEmail.id}). Linking Supabase authUserId.`);
                try {
                    user = await this.prisma.user.update({
                        where: { id: existingUserWithEmail.id },
                        data: { authUserId: supabasePayload.sub },
                        include: { warehouseUsers: true },
                    });
                    this.logger.log(`Successfully linked Supabase authUserId ${supabasePayload.sub} to user ID ${user.id}.`);
                }
                catch (linkError) {
                    this.logger.error(`Failed to link Supabase authUserId ${supabasePayload.sub} to existing user ${existingUserWithEmail.id}: ${linkError.message}`, linkError.stack);
                    throw new common_2.InternalServerErrorException("Failed to link social account to existing user.");
                }
            }
            else {
                this.logger.log(`No existing Quildora user found for email ${supabasePayload.email}. Creating new Quildora user linked to Supabase ID ${supabasePayload.sub}.`);
                try {
                    user = await this.prisma.user.create({
                        data: {
                            authUserId: supabasePayload.sub,
                            email: supabasePayload.email,
                            role: client_1.Role.WAREHOUSE_MEMBER,
                            name: supabasePayload.user_metadata?.full_name ||
                                supabasePayload.email.split("@")[0],
                        },
                        include: {
                            warehouseUsers: {
                                select: {
                                    warehouseId: true,
                                    role: true,
                                },
                            },
                        },
                    });
                    this.logger.log(`New Quildora user created: ${user.email} (ID: ${user.id}). Needs company onboarding.`);
                }
                catch (creationError) {
                    this.logger.error(`Failed to create new user for Supabase ID ${supabasePayload.sub} with email ${supabasePayload.email}: ${creationError.message}`, creationError.stack);
                    if (creationError.code === "P2002" &&
                        creationError.meta?.target?.includes("email")) {
                        throw new common_2.ConflictException("An account with this email already exists but could not be linked. Please contact support.");
                    }
                    else {
                        throw new common_2.InternalServerErrorException("Failed to provision new user account.");
                    }
                }
            }
        }
        const onboardingStatus = user.tenantId
            ? "complete"
            : "pending_company_details";
        const { password, ...userWithoutPassword } = user;
        const warehouseAccess = user.warehouseUsers?.map((wu) => ({
            warehouseId: wu.warehouseId,
            role: wu.role,
        })) || [];
        const jwtPayload = {
            userId: user.id,
            sub: user.id,
            email: user.email,
            role: user.role,
            tenantId: user.tenantId,
            warehouseAccess,
        };
        this.logger.log(`Generating JWT for user: ${user.email} (ID: ${user.id}, Role: ${user.role}, Tenant: ${user.tenantId}), Warehouses: ${warehouseAccess.length}, Onboarding: ${onboardingStatus}`);
        if (warehouseAccess.length > 0) {
            this.logger.debug(`Warehouse access for ${user.email}: ${warehouseAccess
                .map((wa) => `${wa.warehouseId}(${wa.role})`)
                .join(", ")}`);
        }
        return {
            accessToken: await this.jwtService.signAsync(jwtPayload),
            onboardingStatus,
            user: userWithoutPassword,
        };
    }
    async createTenantWithAdmin(createTenantDto) {
        const { companyName, adminEmail, adminPassword } = createTenantDto;
        this.logger.log(`Attempting to create tenant '${companyName}' with admin '${adminEmail}'`);
        const existingUser = await this.prisma.user.findUnique({
            where: { email: adminEmail },
        });
        if (existingUser) {
            this.logger.warn(`Signup attempt failed: Email '${adminEmail}' already exists.`);
            throw new common_2.ConflictException("Email already exists");
        }
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(adminPassword, saltRounds);
        this.logger.debug(`Password hashed for user '${adminEmail}'`);
        try {
            const result = await this.prisma.$transaction(async (tx) => {
                const newTenant = await tx.tenant.create({
                    data: {
                        name: companyName,
                    },
                });
                this.logger.log(`Tenant created: ${newTenant.name} (ID: ${newTenant.id})`);
                const userData = {
                    email: adminEmail,
                    password: hashedPassword,
                    role: client_1.Role.TENANT_ADMIN,
                    name: adminEmail.split("@")[0],
                    tenantId: newTenant.id,
                };
                const newUser = await tx.user.create({
                    data: userData,
                });
                this.logger.log(`TENANT_ADMIN user created: ${newUser.email} (ID: ${newUser.id}) for tenant ${newTenant.id}`);
                return newUser;
            });
            return result;
        }
        catch (error) {
            this.logger.error(`Error during tenant/admin creation for ${adminEmail}: ${error.message}`, error.stack);
            if (error.code === "P2002" && error.meta?.target?.includes("email")) {
                throw new common_2.ConflictException("Email already exists");
            }
            throw new common_2.InternalServerErrorException("Could not create tenant and admin user.");
        }
    }
    async createSupabaseUser(email, password) {
        try {
            const supabaseUrl = this.configService.get("SUPABASE_URL");
            const supabaseServiceKey = this.configService.get("SUPABASE_SERVICE_ROLE_KEY");
            if (!supabaseUrl || !supabaseServiceKey) {
                throw new common_2.InternalServerErrorException("Supabase configuration missing");
            }
            const supabase = (0, supabase_js_1.createClient)(supabaseUrl, supabaseServiceKey);
            const { data, error } = await supabase.auth.admin.createUser({
                email,
                password,
                email_confirm: true,
            });
            if (error) {
                this.logger.error("Failed to create Supabase user:", error);
                throw new common_2.InternalServerErrorException(`Failed to create user: ${error.message}`);
            }
            return data.user;
        }
        catch (error) {
            this.logger.error("Error creating Supabase user:", error);
            throw error;
        }
    }
    async generateAccessToken(user) {
        try {
            const warehouseAccess = await this.prisma.warehouseUser.findMany({
                where: { userId: user.id },
                include: { warehouse: true },
            });
            const warehouseAccessInfo = warehouseAccess.map((wa) => ({
                warehouseId: wa.warehouseId,
                warehouseName: wa.warehouse.name,
                role: wa.role,
            }));
            const jwtPayload = {
                userId: user.id,
                sub: user.id,
                email: user.email,
                role: user.role,
                tenantId: user.tenantId,
                warehouseAccess: warehouseAccessInfo,
            };
            return await this.jwtService.signAsync(jwtPayload);
        }
        catch (error) {
            this.logger.error("Error generating access token:", error);
            throw new common_2.InternalServerErrorException("Failed to generate access token");
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = AuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        jwt_1.JwtService,
        config_1.ConfigService])
], AuthService);
//# sourceMappingURL=auth.service.js.map