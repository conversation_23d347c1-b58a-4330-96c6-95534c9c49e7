import { AuthService } from "./auth.service";
import { LoginDto } from "./dto/login.dto";
import { CreateTenantWithAdminDto } from './dto/create-tenant-with-admin.dto';
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    signIn(loginDto: LoginDto): Promise<{
        accessToken: string;
        onboardingStatus: string;
        user: Omit<import("@prisma/client").User, "password">;
    }>;
    tenantSignUp(createTenantDto: CreateTenantWithAdminDto): Promise<Omit<{
        name: string | null;
        status: string;
        id: string;
        email: string;
        password: string | null;
        authUserId: string | null;
        role: import("@prisma/client").$Enums.Role;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string | null;
    }, "password">>;
}
