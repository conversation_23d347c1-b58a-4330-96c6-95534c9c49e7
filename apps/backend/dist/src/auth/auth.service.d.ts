import { JwtService } from "@nestjs/jwt";
import { PrismaService } from "../prisma/prisma.service";
import { ConfigService } from "@nestjs/config";
import { LoginDto } from "./dto/login.dto";
import { CreateTenantWithAdminDto } from "./dto";
export declare class AuthService {
    private prisma;
    private jwtService;
    private configService;
    private readonly logger;
    constructor(prisma: PrismaService, jwtService: JwtService, configService: ConfigService);
    login(loginDto: LoginDto): Promise<{
        accessToken: string;
        onboardingStatus: string;
        user: Omit<import("@prisma/client").User, "password">;
    }>;
    createTenantWithAdmin(createTenantDto: CreateTenantWithAdminDto): Promise<Omit<import("@prisma/client").User, "password">>;
    createSupabaseUser(email: string, password: string): Promise<any>;
    generateAccessToken(user: any): Promise<string>;
}
