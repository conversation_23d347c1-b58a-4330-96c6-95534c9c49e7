"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const jwt_1 = require("@nestjs/jwt");
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const auth_service_1 = require("./auth.service");
const auth_module_1 = require("./auth.module");
const prisma_module_1 = require("../prisma/prisma.module");
const prisma_service_1 = require("../prisma/prisma.service");
const cleanup_database_1 = require("../util/cleanup-database");
const TEST_JWT_SECRET = process.env.TEST_JWT_SECRET;
if (!TEST_JWT_SECRET) {
    throw new Error('TEST_JWT_SECRET must be set in the environment for integration tests');
}
describe("AuthService Integration", () => {
    let authService;
    let prisma;
    let jwtService;
    let moduleRef;
    let testUser;
    let testTenant;
    const testUserSupabaseId = "supabase|12345";
    beforeAll(async () => {
        moduleRef = await testing_1.Test.createTestingModule({
            imports: [auth_module_1.AuthModule, prisma_module_1.PrismaModule],
        }).compile();
        authService = moduleRef.get(auth_service_1.AuthService);
        prisma = moduleRef.get(prisma_service_1.PrismaService);
        jwtService = moduleRef.get(jwt_1.JwtService);
        await (0, cleanup_database_1.cleanupDatabase)(prisma);
        testTenant = await prisma.tenant.create({
            data: {
                name: "Auth Test Tenant",
            },
        });
        testUser = await prisma.user.create({
            data: {
                email: "<EMAIL>",
                authUserId: testUserSupabaseId,
                role: client_1.Role.WAREHOUSE_MEMBER,
                name: "Auth Test User",
                tenantId: testTenant.id,
            },
        });
    });
    afterAll(async () => {
        await (0, cleanup_database_1.cleanupDatabase)(prisma);
        await moduleRef.close();
    });
    const generateMockSupabaseToken = async (payload, secret = TEST_JWT_SECRET, expiresIn = "15m") => {
        return jwtService.signAsync(payload, { secret, expiresIn });
    };
    it("should successfully login with a valid Supabase token and return an app access token", async () => {
        const validSupabaseToken = await generateMockSupabaseToken({
            sub: testUserSupabaseId,
            email: testUser.email,
        });
        const result = await authService.login({
            supabaseToken: validSupabaseToken,
        });
        expect(result).toHaveProperty("accessToken");
        expect(result.accessToken).toBeDefined();
        const appTokenPayload = await jwtService.verifyAsync(result.accessToken, {
            secret: TEST_JWT_SECRET,
        });
        expect(appTokenPayload.userId).toEqual(testUser.id);
        expect(appTokenPayload.email).toEqual(testUser.email);
        expect(appTokenPayload.role).toEqual(testUser.role);
        expect(appTokenPayload.tenantId).toEqual(testTenant.id);
    });
    it("should throw UnauthorizedException for an invalid Supabase token (bad signature)", async () => {
        const tokenWithWrongSecret = await generateMockSupabaseToken({ sub: testUserSupabaseId, email: testUser.email }, "wrong-secret");
        await expect(authService.login({ supabaseToken: tokenWithWrongSecret })).rejects.toThrow(common_1.UnauthorizedException);
    });
    it("should throw UnauthorizedException for an expired Supabase token", async () => {
        const expiredToken = await generateMockSupabaseToken({ sub: testUserSupabaseId, email: testUser.email }, TEST_JWT_SECRET, "-1s");
        await expect(authService.login({ supabaseToken: expiredToken })).rejects.toThrow(common_1.UnauthorizedException);
    });
    it("should create a new user and log them in if the user does not exist locally", async () => {
        const unknownUserEmail = "<EMAIL>";
        const unknownUserSupabaseId = "supabase|new-user";
        const tokenForUnknownUser = await generateMockSupabaseToken({
            sub: unknownUserSupabaseId,
            email: unknownUserEmail,
        });
        const result = await authService.login({
            supabaseToken: tokenForUnknownUser,
        });
        expect(result).toHaveProperty("accessToken");
        expect(result.onboardingStatus).toBe("pending_company_details");
        expect(result.user).toBeDefined();
        expect(result.user.email).toBe(unknownUserEmail);
        expect(result.user.authUserId).toBe(unknownUserSupabaseId);
        const dbUser = await prisma.user.findUnique({
            where: { authUserId: unknownUserSupabaseId },
        });
        expect(dbUser).not.toBeNull();
        expect(dbUser?.email).toBe(unknownUserEmail);
    });
    it("should throw UnauthorizedException if Supabase token lacks required claims (sub)", async () => {
        const tokenMissingSub = await generateMockSupabaseToken({
            email: testUser.email,
        });
        await expect(authService.login({ supabaseToken: tokenMissingSub })).rejects.toThrow(common_1.UnauthorizedException);
    });
    it("should throw UnauthorizedException if Supabase token lacks required claims (email)", async () => {
        const tokenMissingEmail = await generateMockSupabaseToken({
            sub: testUserSupabaseId,
        });
        await expect(authService.login({ supabaseToken: tokenMissingEmail })).rejects.toThrow(common_1.UnauthorizedException);
    });
});
//# sourceMappingURL=auth.service.integration.spec.js.map