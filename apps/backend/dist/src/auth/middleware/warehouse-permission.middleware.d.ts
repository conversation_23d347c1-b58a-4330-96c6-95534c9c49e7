import { NestMiddleware, Logger } from "@nestjs/common";
import { Request, Response, NextFunction } from "express";
import { Role } from "@prisma/client";
import { EnhancedUserPayload } from "../types";
import { WarehouseValidationUtils } from "../../warehouses/utils/warehouse-validation.utils";
import { SecurityAuditService } from "../../audit-log/services/security-audit.service";
export interface WarehouseRequest extends Request {
    user: EnhancedUserPayload;
    warehouseContext?: {
        warehouseId: string;
        userRole: Role;
        hasAccess: boolean;
        isManager: boolean;
        isAdmin: boolean;
    };
}
export interface WarehousePermissionConfig {
    requireWarehouseId?: boolean;
    minimumRole?: Role;
    extractFromEntity?: {
        entityType: "pallet" | "location" | "purchaseOrder" | "shipment";
        paramName: string;
    };
    customValidation?: (warehouseId: string | undefined, user: EnhancedUserPayload, req: WarehouseRequest) => Promise<boolean>;
    skipForTenantAdmin?: boolean;
}
export declare class WarehousePermissionMiddleware implements NestMiddleware {
    readonly warehouseValidationUtils: WarehouseValidationUtils;
    readonly securityAuditService: SecurityAuditService;
    readonly logger: Logger;
    constructor(warehouseValidationUtils: WarehouseValidationUtils, securityAuditService: SecurityAuditService);
    static configure(config?: WarehousePermissionConfig): {
        new (warehouseValidationUtils: WarehouseValidationUtils, securityAuditService: SecurityAuditService): {
            use(req: WarehouseRequest, res: Response, next: NextFunction): Promise<void>;
            readonly logger: Logger;
            readonly warehouseValidationUtils: WarehouseValidationUtils;
            readonly securityAuditService: SecurityAuditService;
            validateWarehousePermissions(req: WarehouseRequest, res: Response, next: NextFunction, config: WarehousePermissionConfig): Promise<void>;
            extractWarehouseId(req: WarehouseRequest, config: WarehousePermissionConfig): Promise<string | undefined>;
            extractWarehouseFromEntity(req: WarehouseRequest, entityConfig: NonNullable<WarehousePermissionConfig["extractFromEntity"]>): Promise<string | undefined>;
            validateWarehouseAccess(warehouseId: string, user: EnhancedUserPayload, config: WarehousePermissionConfig): Promise<void>;
            setWarehouseContext(req: WarehouseRequest, warehouseId: string | undefined, userRole: Role, hasAccess: boolean): void;
        };
        configure(config?: WarehousePermissionConfig): any;
    };
    use(req: WarehouseRequest, res: Response, next: NextFunction): Promise<void>;
    validateWarehousePermissions(req: WarehouseRequest, res: Response, next: NextFunction, config: WarehousePermissionConfig): Promise<void>;
    extractWarehouseId(req: WarehouseRequest, config: WarehousePermissionConfig): Promise<string | undefined>;
    extractWarehouseFromEntity(req: WarehouseRequest, entityConfig: NonNullable<WarehousePermissionConfig["extractFromEntity"]>): Promise<string | undefined>;
    validateWarehouseAccess(warehouseId: string, user: EnhancedUserPayload, config: WarehousePermissionConfig): Promise<void>;
    setWarehouseContext(req: WarehouseRequest, warehouseId: string | undefined, userRole: Role, hasAccess: boolean): void;
}
export declare const RequireWarehouseAccess: typeof WarehousePermissionMiddleware;
export declare const RequireWarehouseManager: typeof WarehousePermissionMiddleware;
export declare const RequirePalletWarehouseAccess: typeof WarehousePermissionMiddleware;
export declare const RequireLocationWarehouseAccess: typeof WarehousePermissionMiddleware;
