"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WarehousePermissionMiddleware_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequireLocationWarehouseAccess = exports.RequirePalletWarehouseAccess = exports.RequireWarehouseManager = exports.RequireWarehouseAccess = exports.WarehousePermissionMiddleware = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const warehouse_validation_utils_1 = require("../../warehouses/utils/warehouse-validation.utils");
const security_audit_service_1 = require("../../audit-log/services/security-audit.service");
let WarehousePermissionMiddleware = WarehousePermissionMiddleware_1 = class WarehousePermissionMiddleware {
    constructor(warehouseValidationUtils, securityAuditService) {
        this.warehouseValidationUtils = warehouseValidationUtils;
        this.securityAuditService = securityAuditService;
        this.logger = new common_1.Logger(WarehousePermissionMiddleware_1.name);
    }
    static configure(config = {}) {
        class ConfiguredWarehousePermissionMiddleware extends WarehousePermissionMiddleware_1 {
            async use(req, res, next) {
                await this.validateWarehousePermissions(req, res, next, config);
            }
        }
        return ConfiguredWarehousePermissionMiddleware;
    }
    async use(req, res, next) {
        await this.validateWarehousePermissions(req, res, next, {
            requireWarehouseId: false,
            skipForTenantAdmin: true,
        });
    }
    async validateWarehousePermissions(req, res, next, config) {
        try {
            const user = req.user;
            if (!user) {
                throw new common_1.ForbiddenException("User not authenticated");
            }
            this.logger.debug(`Validating warehouse permissions for user ${user.email} on ${req.method} ${req.path}`);
            if (config.skipForTenantAdmin && user.role === client_1.Role.TENANT_ADMIN) {
                this.logger.debug(`Skipping warehouse validation for tenant admin: ${user.email}`);
                this.setWarehouseContext(req, undefined, user.role, true);
                return next();
            }
            const warehouseId = await this.extractWarehouseId(req, config);
            if (config.requireWarehouseId && !warehouseId) {
                throw new common_1.BadRequestException("Warehouse ID is required for this operation");
            }
            if (warehouseId) {
                await this.validateWarehouseAccess(warehouseId, user, config);
                const userRole = user.getWarehouseRole?.(warehouseId) || user.role;
                this.setWarehouseContext(req, warehouseId, userRole, true);
                this.logger.debug(`Warehouse access validated for user ${user.email} in warehouse ${warehouseId} with role ${userRole}`);
                await this.securityAuditService.logWarehouseAccessGranted(warehouseId, user, req, {
                    operation: `${req.method}_${req.path}`,
                    userRole,
                });
            }
            else {
                this.setWarehouseContext(req, undefined, user.role, true);
            }
            if (config.customValidation) {
                const customResult = await config.customValidation(warehouseId, user, req);
                if (!customResult) {
                    throw new common_1.ForbiddenException("Custom validation failed");
                }
            }
            next();
        }
        catch (error) {
            this.logger.error(`Warehouse permission validation failed for ${req.method} ${req.path}: ${error.message}`, error.stack);
            const warehouseId = await this.extractWarehouseId(req, config);
            await this.securityAuditService.logWarehouseAccessDenied(warehouseId, req.user, req, error.message, {
                operation: `${req.method}_${req.path}`,
                userRole: req.user?.role,
            });
            next(error);
        }
    }
    async extractWarehouseId(req, config) {
        let warehouseId = req.params.warehouseId || req.params.warehouse_id;
        if (!warehouseId) {
            warehouseId =
                req.query.warehouseId || req.query.warehouse_id;
        }
        if (!warehouseId) {
            warehouseId = req.headers["x-warehouse-id"];
        }
        if (!warehouseId && req.body?.warehouseId) {
            warehouseId = req.body.warehouseId;
        }
        if (!warehouseId && config.extractFromEntity) {
            warehouseId = await this.extractWarehouseFromEntity(req, config.extractFromEntity);
        }
        return warehouseId;
    }
    async extractWarehouseFromEntity(req, entityConfig) {
        const entityId = req.params[entityConfig.paramName];
        if (!entityId) {
            return undefined;
        }
        try {
            const warehouseId = await this.warehouseValidationUtils.validateEntityWarehouseAccess(entityId, entityConfig.entityType, req.user);
            return warehouseId;
        }
        catch (error) {
            this.logger.warn(`Failed to extract warehouse from ${entityConfig.entityType} ${entityId}: ${error.message}`);
            return undefined;
        }
    }
    async validateWarehouseAccess(warehouseId, user, config) {
        if (user.canAccessWarehouse && !user.canAccessWarehouse(warehouseId)) {
            throw new common_1.ForbiddenException(`Access denied: You do not have permission to access warehouse ${warehouseId}`);
        }
        if (config.minimumRole && user.hasWarehousePermission) {
            if (!user.hasWarehousePermission(warehouseId, config.minimumRole)) {
                throw new common_1.ForbiddenException(`Access denied: This operation requires ${config.minimumRole} role or higher in warehouse ${warehouseId}`);
            }
        }
        await this.warehouseValidationUtils.validateUserWarehouseAccess(warehouseId, user);
        if (config.minimumRole) {
            await this.warehouseValidationUtils.validateUserWarehouseRole(warehouseId, user, config.minimumRole);
        }
    }
    setWarehouseContext(req, warehouseId, userRole, hasAccess) {
        req.warehouseContext = {
            warehouseId: warehouseId || "",
            userRole,
            hasAccess,
            isManager: userRole === client_1.Role.WAREHOUSE_MANAGER || userRole === client_1.Role.TENANT_ADMIN,
            isAdmin: userRole === client_1.Role.TENANT_ADMIN,
        };
    }
};
exports.WarehousePermissionMiddleware = WarehousePermissionMiddleware;
exports.WarehousePermissionMiddleware = WarehousePermissionMiddleware = WarehousePermissionMiddleware_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [warehouse_validation_utils_1.WarehouseValidationUtils,
        security_audit_service_1.SecurityAuditService])
], WarehousePermissionMiddleware);
exports.RequireWarehouseAccess = WarehousePermissionMiddleware.configure({
    requireWarehouseId: true,
    skipForTenantAdmin: false,
});
exports.RequireWarehouseManager = WarehousePermissionMiddleware.configure({
    requireWarehouseId: true,
    minimumRole: client_1.Role.WAREHOUSE_MANAGER,
    skipForTenantAdmin: true,
});
exports.RequirePalletWarehouseAccess = WarehousePermissionMiddleware.configure({
    extractFromEntity: {
        entityType: "pallet",
        paramName: "palletId",
    },
    skipForTenantAdmin: true,
});
exports.RequireLocationWarehouseAccess = WarehousePermissionMiddleware.configure({
    extractFromEntity: {
        entityType: "location",
        paramName: "locationId",
    },
    skipForTenantAdmin: true,
});
//# sourceMappingURL=warehouse-permission.middleware.js.map