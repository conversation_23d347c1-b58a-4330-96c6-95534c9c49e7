"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PalletsControllerNew = exports.PalletsControllerOld = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const jwt_auth_guard_1 = require("../guards/jwt-auth.guard");
const warehouse_permission_guard_1 = require("../guards/warehouse-permission.guard");
const warehouse_permission_decorator_1 = require("../decorators/warehouse-permission.decorator");
let PalletsControllerOld = class PalletsControllerOld {
    findAll(req, warehouseId) {
        return this.palletsService.findAll(req.user, {}, warehouseId);
    }
    findOne(id, req) {
        return this.palletsService.findOne(id, req.user);
    }
    movePallet(id, movePalletDto, req) {
        return this.palletsService.move(id, movePalletDto, req.user);
    }
};
exports.PalletsControllerOld = PalletsControllerOld;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)("warehouseId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], PalletsControllerOld.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(":id"),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], PalletsControllerOld.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(":id/move"),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", void 0)
], PalletsControllerOld.prototype, "movePallet", null);
exports.PalletsControllerOld = PalletsControllerOld = __decorate([
    (0, common_1.Controller)("pallets-old"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard)
], PalletsControllerOld);
let PalletsControllerNew = class PalletsControllerNew {
    findAll(req, warehouseId) {
        const warehouseContext = (0, warehouse_permission_guard_1.getWarehouseContext)(req);
        const accessibleWarehouses = req.user.accessibleWarehouses;
        return this.palletsService.findAll(req.user, {}, warehouseId);
    }
    findOne(palletId, req) {
        const warehouseContext = (0, warehouse_permission_guard_1.getWarehouseContext)(req);
        console.log(`Accessing pallet ${palletId} in warehouse ${warehouseContext?.warehouseId}`);
        return this.palletsService.findOne(palletId, req.user);
    }
    create(createPalletDto, req) {
        const warehouseContext = (0, warehouse_permission_guard_1.getWarehouseContext)(req);
        return this.palletsService.create(createPalletDto, req.user);
    }
    movePallet(palletId, movePalletDto, req) {
        const warehouseContext = (0, warehouse_permission_guard_1.getWarehouseContext)(req);
        if (warehouseContext?.isManager) {
            console.log("User is warehouse manager, allowing move operation");
        }
        return this.palletsService.move(palletId, movePalletDto, req.user);
    }
    remove(palletId, req) {
        return this.palletsService.remove(palletId, req.user);
    }
    bulkImport(importData, req) {
        const warehouseContext = (0, warehouse_permission_guard_1.getWarehouseContext)(req);
        const canAccess = req.user.canAccessWarehouse?.(warehouseContext?.warehouseId || '');
        const userRole = req.user.getWarehouseRole?.(warehouseContext?.warehouseId || '');
        return this.palletsService.bulkImport(importData, req.user, warehouseContext?.warehouseId);
    }
    getMyAccessiblePallets(req) {
        const accessibleWarehouses = req.user.accessibleWarehouses || [];
        const warehouseId = "some-warehouse-id";
        const hasAccess = req.user.canAccessWarehouse?.(warehouseId) || false;
        const userRole = req.user.getWarehouseRole?.(warehouseId);
        const hasManagerPermission = req.user.hasWarehousePermission?.(warehouseId, client_1.Role.WAREHOUSE_MANAGER) || false;
        return {
            accessibleWarehouses,
            hasAccess,
            userRole,
            hasManagerPermission,
        };
    }
};
exports.PalletsControllerNew = PalletsControllerNew;
__decorate([
    (0, common_1.Get)(),
    (0, warehouse_permission_decorator_1.AllowWarehouseFiltering)(),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)("warehouseId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], PalletsControllerNew.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(":palletId"),
    (0, warehouse_permission_decorator_1.RequirePalletAccess)(),
    __param(0, (0, common_1.Param)("palletId")),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], PalletsControllerNew.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)(),
    (0, warehouse_permission_decorator_1.RequireWarehouseAccess)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], PalletsControllerNew.prototype, "create", null);
__decorate([
    (0, common_1.Patch)(":palletId/move"),
    (0, warehouse_permission_decorator_1.RequirePalletAccess)({ minimumRole: client_1.Role.WAREHOUSE_MANAGER }),
    __param(0, (0, common_1.Param)("palletId")),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", void 0)
], PalletsControllerNew.prototype, "movePallet", null);
__decorate([
    (0, common_1.Delete)(":palletId"),
    (0, warehouse_permission_decorator_1.PalletRoute)(client_1.Role.WAREHOUSE_MANAGER),
    __param(0, (0, common_1.Param)("palletId")),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], PalletsControllerNew.prototype, "remove", null);
__decorate([
    (0, common_1.Post)("bulk-import"),
    (0, warehouse_permission_decorator_1.WarehouseManagerRoute)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], PalletsControllerNew.prototype, "bulkImport", null);
__decorate([
    (0, common_1.Get)("my-accessible"),
    (0, warehouse_permission_decorator_1.AllowWarehouseFiltering)(),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], PalletsControllerNew.prototype, "getMyAccessiblePallets", null);
exports.PalletsControllerNew = PalletsControllerNew = __decorate([
    (0, common_1.Controller)("pallets"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, warehouse_permission_guard_1.WarehousePermissionGuard)
], PalletsControllerNew);
//# sourceMappingURL=warehouse-permission-usage.example.js.map