import { RequestWithWarehouseContext } from "../decorators/warehouse-permission.decorator";
import { EnhancedUserPayload } from "../types";
export declare class PalletsControllerOld {
    findAll(req: {
        user: any;
    }, warehouseId?: string): any;
    findOne(id: string, req: {
        user: any;
    }): any;
    movePallet(id: string, movePalletDto: any, req: {
        user: any;
    }): any;
}
export declare class PalletsControllerNew {
    findAll(req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>, warehouseId?: string): any;
    findOne(palletId: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): any;
    create(createPalletDto: any, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): any;
    movePallet(palletId: string, movePalletDto: any, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): any;
    remove(palletId: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): any;
    bulkImport(importData: any, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): any;
    getMyAccessiblePallets(req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): {
        accessibleWarehouses: string[];
        hasAccess: boolean;
        userRole: import("@prisma/client").$Enums.Role;
        hasManagerPermission: boolean;
    };
}
