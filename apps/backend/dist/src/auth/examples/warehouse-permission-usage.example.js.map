{"version": 3, "file": "warehouse-permission-usage.example.js", "sourceRoot": "", "sources": ["../../../../src/auth/examples/warehouse-permission-usage.example.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAOA,2CAWwB;AACxB,2CAAsC;AACtC,6DAAwD;AACxD,qFAAqG;AACrG,iGAQsD;AAO/C,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAG/B,OAAO,CACE,GAAkB,EACH,WAAoB;QAK1C,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,WAAW,CAAC,CAAC;IAChE,CAAC;IAGD,OAAO,CACQ,EAAU,EAChB,GAAkB;QAIzB,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;IACnD,CAAC;IAGD,UAAU,CACK,EAAU,EACf,aAAkB,EACnB,GAAkB;QAIzB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,aAAa,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;IAC/D,CAAC;CACF,CAAA;AAjCY,oDAAoB;AAG/B;IADC,IAAA,YAAG,GAAE;IAEH,WAAA,IAAA,YAAG,GAAE,CAAA;IACL,WAAA,IAAA,cAAK,EAAC,aAAa,CAAC,CAAA;;;;mDAMtB;AAGD;IADC,IAAA,YAAG,EAAC,KAAK,CAAC;IAER,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;IACX,WAAA,IAAA,YAAG,GAAE,CAAA;;;;mDAKP;AAGD;IADC,IAAA,cAAK,EAAC,UAAU,CAAC;IAEf,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;IACX,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,YAAG,GAAE,CAAA;;;;sDAKP;+BAhCU,oBAAoB;IAFhC,IAAA,mBAAU,EAAC,aAAa,CAAC;IACzB,IAAA,kBAAS,EAAC,6BAAY,CAAC;GACX,oBAAoB,CAiChC;AAMM,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAK/B,OAAO,CACE,GAA+D,EAChD,WAAoB;QAG1C,MAAM,gBAAgB,GAAG,IAAA,gDAAmB,EAAC,GAAG,CAAC,CAAC;QAGlD,MAAM,oBAAoB,GAAG,GAAG,CAAC,IAAI,CAAC,oBAAoB,CAAC;QAE3D,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,WAAW,CAAC,CAAC;IAChE,CAAC;IAKD,OAAO,CACc,QAAgB,EAC5B,GAA+D;QAGtE,MAAM,gBAAgB,GAAG,IAAA,gDAAmB,EAAC,GAAG,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,oBAAoB,QAAQ,iBAAiB,gBAAgB,EAAE,WAAW,EAAE,CAAC,CAAC;QAE1F,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;IAKD,MAAM,CACI,eAAoB,EACrB,GAA+D;QAGtE,MAAM,gBAAgB,GAAG,IAAA,gDAAmB,EAAC,GAAG,CAAC,CAAC;QAElD,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,eAAe,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;IAC/D,CAAC;IAKD,UAAU,CACW,QAAgB,EAC3B,aAAkB,EACnB,GAA+D;QAGtE,MAAM,gBAAgB,GAAG,IAAA,gDAAmB,EAAC,GAAG,CAAC,CAAC;QAGlD,IAAI,gBAAgB,EAAE,SAAS,EAAE,CAAC;YAChC,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;QACpE,CAAC;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;IACrE,CAAC;IAKD,MAAM,CACe,QAAgB,EAC5B,GAA+D;QAEtE,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IAKD,UAAU,CACA,UAAe,EAChB,GAA+D;QAEtE,MAAM,gBAAgB,GAAG,IAAA,gDAAmB,EAAC,GAAG,CAAC,CAAC;QAGlD,MAAM,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,gBAAgB,EAAE,WAAW,IAAI,EAAE,CAAC,CAAC;QACrF,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,gBAAgB,EAAE,WAAW,IAAI,EAAE,CAAC,CAAC;QAElF,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,CAAC,IAAI,EAAE,gBAAgB,EAAE,WAAW,CAAC,CAAC;IAC7F,CAAC;IAKD,sBAAsB,CACb,GAA+D;QAGtE,MAAM,oBAAoB,GAAG,GAAG,CAAC,IAAI,CAAC,oBAAoB,IAAI,EAAE,CAAC;QAGjE,MAAM,WAAW,GAAG,mBAAmB,CAAC;QACxC,MAAM,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC;QACtE,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,WAAW,CAAC,CAAC;QAC1D,MAAM,oBAAoB,GAAG,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,WAAW,EAAE,aAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC;QAE7G,OAAO;YACL,oBAAoB;YACpB,SAAS;YACT,QAAQ;YACR,oBAAoB;SACrB,CAAC;IACJ,CAAC;CACF,CAAA;AAhHY,oDAAoB;AAK/B;IAFC,IAAA,YAAG,GAAE;IACL,IAAA,wDAAuB,GAAE;IAEvB,WAAA,IAAA,YAAG,GAAE,CAAA;IACL,WAAA,IAAA,cAAK,EAAC,aAAa,CAAC,CAAA;;;;mDAStB;AAKD;IAFC,IAAA,YAAG,EAAC,WAAW,CAAC;IAChB,IAAA,oDAAmB,GAAE;IAEnB,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;IACjB,WAAA,IAAA,YAAG,GAAE,CAAA;;;;mDAOP;AAKD;IAFC,IAAA,aAAI,GAAE;IACN,IAAA,uDAAsB,GAAE;IAEtB,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,YAAG,GAAE,CAAA;;;;kDAMP;AAKD;IAFC,IAAA,cAAK,EAAC,gBAAgB,CAAC;IACvB,IAAA,oDAAmB,EAAC,EAAE,WAAW,EAAE,aAAI,CAAC,iBAAiB,EAAE,CAAC;IAE1D,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;IACjB,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,YAAG,GAAE,CAAA;;;;sDAWP;AAKD;IAFC,IAAA,eAAM,EAAC,WAAW,CAAC;IACnB,IAAA,4CAAW,EAAC,aAAI,CAAC,iBAAiB,CAAC;IAEjC,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;IACjB,WAAA,IAAA,YAAG,GAAE,CAAA;;;;kDAGP;AAKD;IAFC,IAAA,aAAI,EAAC,aAAa,CAAC;IACnB,IAAA,sDAAqB,GAAE;IAErB,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,YAAG,GAAE,CAAA;;;;sDASP;AAKD;IAFC,IAAA,YAAG,EAAC,eAAe,CAAC;IACpB,IAAA,wDAAuB,GAAE;IAEvB,WAAA,IAAA,YAAG,GAAE,CAAA;;;;kEAiBP;+BA/GU,oBAAoB;IAFhC,IAAA,mBAAU,EAAC,SAAS,CAAC;IACrB,IAAA,kBAAS,EAAC,6BAAY,EAAE,qDAAwB,CAAC;GACrC,oBAAoB,CAgHhC"}