"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationRoute = exports.PalletRoute = exports.TenantAdminRoute = exports.WarehouseMemberRoute = exports.WarehouseManagerRoute = exports.ProtectedWarehouseRoute = exports.AllowWarehouseFiltering = exports.RequireShipmentAccess = exports.RequirePurchaseOrderAccess = exports.RequireLocationAccess = exports.RequirePalletAccess = exports.RequireTenantAdmin = exports.RequireWarehouseMember = exports.RequireWarehouseManager = exports.RequireWarehouseAccess = exports.WarehousePermission = exports.WAREHOUSE_PERMISSION_KEY = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
exports.WAREHOUSE_PERMISSION_KEY = "warehouse_permission";
const WarehousePermission = (options = {}) => (0, common_1.SetMetadata)(exports.WAREHOUSE_PERMISSION_KEY, options);
exports.WarehousePermission = WarehousePermission;
const RequireWarehouseAccess = (options = {}) => (0, exports.WarehousePermission)({
    requireWarehouseId: true,
    skipForTenantAdmin: false,
    ...options,
});
exports.RequireWarehouseAccess = RequireWarehouseAccess;
const RequireWarehouseManager = (options = {}) => (0, exports.WarehousePermission)({
    requireWarehouseId: true,
    minimumRole: client_1.Role.WAREHOUSE_MANAGER,
    skipForTenantAdmin: true,
    ...options,
});
exports.RequireWarehouseManager = RequireWarehouseManager;
const RequireWarehouseMember = (options = {}) => (0, exports.WarehousePermission)({
    requireWarehouseId: true,
    minimumRole: client_1.Role.WAREHOUSE_MEMBER,
    skipForTenantAdmin: true,
    ...options,
});
exports.RequireWarehouseMember = RequireWarehouseMember;
const RequireTenantAdmin = () => (0, exports.WarehousePermission)({
    minimumRole: client_1.Role.TENANT_ADMIN,
    requireWarehouseId: false,
    skipForTenantAdmin: false,
});
exports.RequireTenantAdmin = RequireTenantAdmin;
const RequirePalletAccess = (options = {}) => (0, exports.WarehousePermission)({
    extractFromEntity: {
        entityType: "pallet",
        paramName: "palletId",
    },
    skipForTenantAdmin: true,
    ...options,
});
exports.RequirePalletAccess = RequirePalletAccess;
const RequireLocationAccess = (options = {}) => (0, exports.WarehousePermission)({
    extractFromEntity: {
        entityType: "location",
        paramName: "locationId",
    },
    skipForTenantAdmin: true,
    ...options,
});
exports.RequireLocationAccess = RequireLocationAccess;
const RequirePurchaseOrderAccess = (options = {}) => (0, exports.WarehousePermission)({
    extractFromEntity: {
        entityType: "purchaseOrder",
        paramName: "purchaseOrderId",
    },
    skipForTenantAdmin: true,
    ...options,
});
exports.RequirePurchaseOrderAccess = RequirePurchaseOrderAccess;
const RequireShipmentAccess = (options = {}) => (0, exports.WarehousePermission)({
    extractFromEntity: {
        entityType: "shipment",
        paramName: "shipmentId",
    },
    skipForTenantAdmin: true,
    ...options,
});
exports.RequireShipmentAccess = RequireShipmentAccess;
const AllowWarehouseFiltering = () => (0, exports.WarehousePermission)({
    requireWarehouseId: false,
    skipForTenantAdmin: true,
});
exports.AllowWarehouseFiltering = AllowWarehouseFiltering;
const ProtectedWarehouseRoute = (options = {}) => (0, common_1.applyDecorators)((0, exports.WarehousePermission)(options));
exports.ProtectedWarehouseRoute = ProtectedWarehouseRoute;
const WarehouseManagerRoute = (options = {}) => (0, exports.ProtectedWarehouseRoute)({
    requireWarehouseId: true,
    minimumRole: client_1.Role.WAREHOUSE_MANAGER,
    skipForTenantAdmin: true,
    ...options,
});
exports.WarehouseManagerRoute = WarehouseManagerRoute;
const WarehouseMemberRoute = (options = {}) => (0, exports.ProtectedWarehouseRoute)({
    requireWarehouseId: true,
    minimumRole: client_1.Role.WAREHOUSE_MEMBER,
    skipForTenantAdmin: true,
    ...options,
});
exports.WarehouseMemberRoute = WarehouseMemberRoute;
const TenantAdminRoute = () => (0, exports.ProtectedWarehouseRoute)({
    minimumRole: client_1.Role.TENANT_ADMIN,
    requireWarehouseId: false,
    skipForTenantAdmin: false,
});
exports.TenantAdminRoute = TenantAdminRoute;
const PalletRoute = (minimumRole = client_1.Role.WAREHOUSE_MEMBER) => (0, exports.ProtectedWarehouseRoute)({
    extractFromEntity: {
        entityType: "pallet",
        paramName: "palletId",
    },
    minimumRole,
    skipForTenantAdmin: true,
});
exports.PalletRoute = PalletRoute;
const LocationRoute = (minimumRole = client_1.Role.WAREHOUSE_MEMBER) => (0, exports.ProtectedWarehouseRoute)({
    extractFromEntity: {
        entityType: "location",
        paramName: "locationId",
    },
    minimumRole,
    skipForTenantAdmin: true,
});
exports.LocationRoute = LocationRoute;
//# sourceMappingURL=warehouse-permission.decorator.js.map