import { Role } from "@prisma/client";
export declare const WAREHOUSE_PERMISSION_KEY = "warehouse_permission";
export interface WarehousePermissionOptions {
    requireWarehouseId?: boolean;
    minimumRole?: Role;
    extractFromEntity?: {
        entityType: "pallet" | "location" | "purchaseOrder" | "shipment";
        paramName: string;
    };
    skipForTenantAdmin?: boolean;
    errorMessage?: string;
}
export declare const WarehousePermission: (options?: WarehousePermissionOptions) => import("@nestjs/common").CustomDecorator<string>;
export declare const RequireWarehouseAccess: (options?: Partial<WarehousePermissionOptions>) => import("@nestjs/common").CustomDecorator<string>;
export declare const RequireWarehouseManager: (options?: Partial<WarehousePermissionOptions>) => import("@nestjs/common").CustomDecorator<string>;
export declare const RequireWarehouseMember: (options?: Partial<WarehousePermissionOptions>) => import("@nestjs/common").CustomDecorator<string>;
export declare const RequireTenantAdmin: () => import("@nestjs/common").CustomDecorator<string>;
export declare const RequirePalletAccess: (options?: Partial<WarehousePermissionOptions>) => import("@nestjs/common").CustomDecorator<string>;
export declare const RequireLocationAccess: (options?: Partial<WarehousePermissionOptions>) => import("@nestjs/common").CustomDecorator<string>;
export declare const RequirePurchaseOrderAccess: (options?: Partial<WarehousePermissionOptions>) => import("@nestjs/common").CustomDecorator<string>;
export declare const RequireShipmentAccess: (options?: Partial<WarehousePermissionOptions>) => import("@nestjs/common").CustomDecorator<string>;
export declare const AllowWarehouseFiltering: () => import("@nestjs/common").CustomDecorator<string>;
export declare const ProtectedWarehouseRoute: (options?: WarehousePermissionOptions) => <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare const WarehouseManagerRoute: (options?: Partial<WarehousePermissionOptions>) => <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare const WarehouseMemberRoute: (options?: Partial<WarehousePermissionOptions>) => <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare const TenantAdminRoute: () => <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare const PalletRoute: (minimumRole?: Role) => <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare const LocationRoute: (minimumRole?: Role) => <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export interface WarehouseContextRequest {
    warehouseContext?: {
        warehouseId: string;
        userRole: Role;
        hasAccess: boolean;
        isManager: boolean;
        isAdmin: boolean;
    };
}
export type RequestWithWarehouseContext<T = any> = T & WarehouseContextRequest;
