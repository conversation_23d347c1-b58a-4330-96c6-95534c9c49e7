"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProtectedWarehouseEndpoint = exports.ProtectedEndpoint = exports.WarehouseMemberRole = exports.WarehouseManagerRole = exports.TenantAdminRole = exports.AnyWarehouseRole = exports.AdminOrManager = exports.RequireWarehouseRole = exports.RequireRoles = exports.WarehouseMemberOnly = exports.WarehouseManagerOnly = exports.TenantAdminOnly = exports.WarehouseRole = exports.Roles = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const role_based_guard_1 = require("../guards/role-based.guard");
const jwt_auth_guard_1 = require("../guards/jwt-auth.guard");
const Roles = (...roles) => (0, common_1.SetMetadata)('roles', roles);
exports.Roles = Roles;
const WarehouseRole = (role) => (0, common_1.SetMetadata)('warehouseRole', role);
exports.WarehouseRole = WarehouseRole;
const TenantAdminOnly = () => (0, common_1.applyDecorators)((0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_based_guard_1.TenantAdminGuard));
exports.TenantAdminOnly = TenantAdminOnly;
const WarehouseManagerOnly = () => (0, common_1.applyDecorators)((0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_based_guard_1.WarehouseManagerGuard));
exports.WarehouseManagerOnly = WarehouseManagerOnly;
const WarehouseMemberOnly = () => (0, common_1.applyDecorators)((0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_based_guard_1.WarehouseMemberGuard));
exports.WarehouseMemberOnly = WarehouseMemberOnly;
const RequireRoles = (...roles) => (0, common_1.applyDecorators)((0, exports.Roles)(...roles), (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_based_guard_1.RoleBasedGuard));
exports.RequireRoles = RequireRoles;
const RequireWarehouseRole = (role) => (0, common_1.applyDecorators)((0, exports.WarehouseRole)(role), (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_based_guard_1.WarehouseSpecificRoleGuard));
exports.RequireWarehouseRole = RequireWarehouseRole;
const AdminOrManager = () => (0, exports.RequireRoles)(client_1.Role.TENANT_ADMIN, client_1.Role.WAREHOUSE_MANAGER);
exports.AdminOrManager = AdminOrManager;
const AnyWarehouseRole = () => (0, exports.RequireRoles)(client_1.Role.TENANT_ADMIN, client_1.Role.WAREHOUSE_MANAGER, client_1.Role.WAREHOUSE_MEMBER);
exports.AnyWarehouseRole = AnyWarehouseRole;
const TenantAdminRole = () => (0, exports.RequireRoles)(client_1.Role.TENANT_ADMIN);
exports.TenantAdminRole = TenantAdminRole;
const WarehouseManagerRole = () => (0, exports.RequireWarehouseRole)(client_1.Role.WAREHOUSE_MANAGER);
exports.WarehouseManagerRole = WarehouseManagerRole;
const WarehouseMemberRole = () => (0, exports.RequireWarehouseRole)(client_1.Role.WAREHOUSE_MEMBER);
exports.WarehouseMemberRole = WarehouseMemberRole;
const ProtectedEndpoint = (...roles) => (0, common_1.applyDecorators)((0, exports.Roles)(...roles), (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_based_guard_1.RoleBasedGuard));
exports.ProtectedEndpoint = ProtectedEndpoint;
const ProtectedWarehouseEndpoint = (role) => (0, common_1.applyDecorators)((0, exports.WarehouseRole)(role), (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_based_guard_1.WarehouseSpecificRoleGuard));
exports.ProtectedWarehouseEndpoint = ProtectedWarehouseEndpoint;
//# sourceMappingURL=role-based.decorator.js.map