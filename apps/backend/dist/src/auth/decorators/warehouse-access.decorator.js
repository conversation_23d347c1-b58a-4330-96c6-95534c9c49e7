"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WarehouseMemberAccess = exports.WarehouseManagerAccess = exports.RequireWarehouseAccess = exports.WarehouseAccess = exports.WAREHOUSE_ACCESS_KEY = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
exports.WAREHOUSE_ACCESS_KEY = "warehouse_access";
const WarehouseAccess = (options = {}) => (0, common_1.SetMetadata)(exports.WAREHOUSE_ACCESS_KEY, options);
exports.WarehouseAccess = WarehouseAccess;
const RequireWarehouseAccess = () => (0, exports.WarehouseAccess)({ requireWarehouseId: true });
exports.RequireWarehouseAccess = RequireWarehouseAccess;
const WarehouseManagerAccess = (requireWarehouseId = true) => (0, exports.WarehouseAccess)({
    allowedRoles: [client_1.Role.WAREHOUSE_MANAGER, client_1.Role.TENANT_ADMIN],
    requireWarehouseId,
});
exports.WarehouseManagerAccess = WarehouseManagerAccess;
const WarehouseMemberAccess = (requireWarehouseId = true) => (0, exports.WarehouseAccess)({
    allowedRoles: [client_1.Role.WAREHOUSE_MEMBER, client_1.Role.WAREHOUSE_MANAGER, client_1.Role.TENANT_ADMIN],
    requireWarehouseId,
});
exports.WarehouseMemberAccess = WarehouseMemberAccess;
//# sourceMappingURL=warehouse-access.decorator.js.map