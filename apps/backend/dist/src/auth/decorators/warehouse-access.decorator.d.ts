import { Role } from "@prisma/client";
export declare const WAREHOUSE_ACCESS_KEY = "warehouse_access";
export interface WarehouseAccessOptions {
    requireWarehouseId?: boolean;
    allowedRoles?: Role[];
    paramName?: string;
    queryParam?: string;
    headerName?: string;
}
export declare const WarehouseAccess: (options?: WarehouseAccessOptions) => import("@nestjs/common").CustomDecorator<string>;
export declare const RequireWarehouseAccess: () => import("@nestjs/common").CustomDecorator<string>;
export declare const WarehouseManagerAccess: (requireWarehouseId?: boolean) => import("@nestjs/common").CustomDecorator<string>;
export declare const WarehouseMemberAccess: (requireWarehouseId?: boolean) => import("@nestjs/common").CustomDecorator<string>;
