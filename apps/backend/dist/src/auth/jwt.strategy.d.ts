import { Strategy } from "passport-jwt";
import { ConfigService } from "@nestjs/config";
import { PrismaService } from "../prisma/prisma.service";
import { JwtPayload, EnhancedUserPayload } from "./types";
import { AuthAuditService } from "../audit-log/services/auth-audit.service";
declare const JwtStrategy_base: new (...args: [opt: import("passport-jwt").StrategyOptionsWithRequest] | [opt: import("passport-jwt").StrategyOptionsWithoutRequest]) => Strategy & {
    validate(...args: any[]): unknown;
};
export declare class JwtStrategy extends JwtStrategy_base {
    private configService;
    private prisma;
    private authAuditService;
    private readonly logger;
    constructor(configService: ConfigService, prisma: PrismaService, authAuditService: AuthAuditService);
    validate(payload: JwtPayload): Promise<EnhancedUserPayload>;
    private createEnhancedUserPayload;
}
export {};
