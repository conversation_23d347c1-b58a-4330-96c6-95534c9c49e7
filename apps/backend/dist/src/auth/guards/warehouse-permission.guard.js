"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WarehousePermissionGuard_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WarehousePermissionGuard = void 0;
exports.getWarehouseContext = getWarehouseContext;
exports.getWarehouseId = getWarehouseId;
exports.isWarehouseManager = isWarehouseManager;
exports.isTenantAdmin = isTenantAdmin;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const client_1 = require("@prisma/client");
const warehouse_permission_decorator_1 = require("../decorators/warehouse-permission.decorator");
const warehouse_validation_utils_1 = require("../../warehouses/utils/warehouse-validation.utils");
let WarehousePermissionGuard = WarehousePermissionGuard_1 = class WarehousePermissionGuard {
    constructor(reflector, warehouseValidationUtils) {
        this.reflector = reflector;
        this.warehouseValidationUtils = warehouseValidationUtils;
        this.logger = new common_1.Logger(WarehousePermissionGuard_1.name);
    }
    async canActivate(context) {
        const permissionOptions = this.reflector.getAllAndOverride(warehouse_permission_decorator_1.WAREHOUSE_PERMISSION_KEY, [context.getHandler(), context.getClass()]);
        if (!permissionOptions) {
            return true;
        }
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user) {
            throw new common_1.ForbiddenException("User not authenticated");
        }
        this.logger.debug(`Validating warehouse permissions for user ${user.email} on ${request.method} ${request.path}`);
        try {
            if (permissionOptions.skipForTenantAdmin &&
                user.role === client_1.Role.TENANT_ADMIN) {
                this.logger.debug(`Skipping warehouse validation for tenant admin: ${user.email}`);
                this.setWarehouseContext(request, undefined, user.role, true);
                return true;
            }
            const warehouseId = await this.extractWarehouseId(request, permissionOptions);
            if (permissionOptions.requireWarehouseId && !warehouseId) {
                const errorMessage = permissionOptions.errorMessage ||
                    "Warehouse ID is required for this operation";
                throw new common_1.BadRequestException(errorMessage);
            }
            if (warehouseId) {
                await this.validateWarehouseAccess(warehouseId, user, permissionOptions);
                const userRole = user.getWarehouseRole?.(warehouseId) || user.role;
                this.setWarehouseContext(request, warehouseId, userRole, true);
                this.logger.debug(`Warehouse access validated for user ${user.email} in warehouse ${warehouseId} with role ${userRole}`);
            }
            else {
                this.setWarehouseContext(request, undefined, user.role, true);
            }
            return true;
        }
        catch (error) {
            this.logger.error(`Warehouse permission validation failed for ${request.method} ${request.path}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async extractWarehouseId(request, options) {
        let warehouseId = request.params.warehouseId || request.params.warehouse_id;
        if (!warehouseId) {
            warehouseId = request.query.warehouseId || request.query.warehouse_id;
        }
        if (!warehouseId) {
            warehouseId = request.headers["x-warehouse-id"];
        }
        if (!warehouseId && request.body?.warehouseId) {
            warehouseId = request.body.warehouseId;
        }
        if (!warehouseId && options.extractFromEntity) {
            warehouseId = await this.extractWarehouseFromEntity(request, options.extractFromEntity);
        }
        return warehouseId;
    }
    async extractWarehouseFromEntity(request, entityConfig) {
        const entityId = request.params[entityConfig.paramName];
        if (!entityId) {
            return undefined;
        }
        try {
            if (entityConfig.entityType === "pallet" ||
                entityConfig.entityType === "location" ||
                entityConfig.entityType === "purchaseOrder" ||
                entityConfig.entityType === "shipment") {
                const warehouseId = await this.warehouseValidationUtils.validateEntityWarehouseAccess(entityId, entityConfig.entityType, request.user);
                return warehouseId;
            }
            this.logger.warn(`Entity type ${entityConfig.entityType} not yet supported for warehouse extraction`);
            return undefined;
        }
        catch (error) {
            this.logger.warn(`Failed to extract warehouse from ${entityConfig.entityType} ${entityId}: ${error.message}`);
            throw error;
        }
    }
    async validateWarehouseAccess(warehouseId, user, options) {
        if (user.canAccessWarehouse && !user.canAccessWarehouse(warehouseId)) {
            const errorMessage = options.errorMessage ||
                `Access denied: You do not have permission to access warehouse ${warehouseId}`;
            throw new common_1.ForbiddenException(errorMessage);
        }
        if (options.minimumRole && user.hasWarehousePermission) {
            if (!user.hasWarehousePermission(warehouseId, options.minimumRole)) {
                const errorMessage = options.errorMessage ||
                    `Access denied: This operation requires ${options.minimumRole} role or higher in warehouse ${warehouseId}`;
                throw new common_1.ForbiddenException(errorMessage);
            }
        }
        await this.warehouseValidationUtils.validateUserWarehouseAccess(warehouseId, user);
        if (options.minimumRole) {
            await this.warehouseValidationUtils.validateUserWarehouseRole(warehouseId, user, options.minimumRole);
        }
    }
    setWarehouseContext(request, warehouseId, userRole, hasAccess) {
        request.warehouseContext = {
            warehouseId: warehouseId || "",
            userRole,
            hasAccess,
            isManager: userRole === client_1.Role.WAREHOUSE_MANAGER || userRole === client_1.Role.TENANT_ADMIN,
            isAdmin: userRole === client_1.Role.TENANT_ADMIN,
        };
    }
};
exports.WarehousePermissionGuard = WarehousePermissionGuard;
exports.WarehousePermissionGuard = WarehousePermissionGuard = WarehousePermissionGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector,
        warehouse_validation_utils_1.WarehouseValidationUtils])
], WarehousePermissionGuard);
function getWarehouseContext(request) {
    return request.warehouseContext;
}
function getWarehouseId(request) {
    return request.warehouseContext?.warehouseId;
}
function isWarehouseManager(request) {
    return request.warehouseContext?.isManager || false;
}
function isTenantAdmin(request) {
    return request.warehouseContext?.isAdmin || false;
}
//# sourceMappingURL=warehouse-permission.guard.js.map