"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const client_1 = require("@prisma/client");
const warehouse_permission_guard_1 = require("./warehouse-permission.guard");
const warehouse_validation_utils_1 = require("../../warehouses/utils/warehouse-validation.utils");
describe("WarehousePermissionGuard", () => {
    let guard;
    let reflector;
    let warehouseValidationUtils;
    const mockExecutionContext = (request) => ({
        switchToHttp: () => ({
            getRequest: () => request,
            getResponse: jest.fn(),
            getNext: jest.fn(),
        }),
        getHandler: jest.fn(),
        getClass: jest.fn(),
        getArgs: jest.fn(),
        getArgByIndex: jest.fn(),
        switchToRpc: jest.fn(),
        switchToWs: jest.fn(),
        getType: jest.fn(),
    });
    const createMockUser = (overrides = {}) => ({
        id: "user-1",
        email: "<EMAIL>",
        name: "Test User",
        password: "password",
        authUserId: "auth-1",
        role: client_1.Role.WAREHOUSE_MEMBER,
        status: "ACTIVE",
        createdAt: new Date(),
        updatedAt: new Date(),
        tenantId: "tenant-1",
        warehouseUsers: [],
        accessibleWarehouses: ["warehouse-1", "warehouse-2"],
        warehouseRoles: new Map([
            ["warehouse-1", client_1.Role.WAREHOUSE_MANAGER],
            ["warehouse-2", client_1.Role.WAREHOUSE_MEMBER],
        ]),
        canAccessWarehouse: jest.fn((warehouseId) => ["warehouse-1", "warehouse-2"].includes(warehouseId)),
        getWarehouseRole: jest.fn((warehouseId) => warehouseId === "warehouse-1"
            ? client_1.Role.WAREHOUSE_MANAGER
            : client_1.Role.WAREHOUSE_MEMBER),
        hasWarehousePermission: jest.fn((warehouseId, requiredRole) => {
            const userRole = warehouseId === "warehouse-1"
                ? client_1.Role.WAREHOUSE_MANAGER
                : client_1.Role.WAREHOUSE_MEMBER;
            const roleHierarchy = {
                [client_1.Role.WAREHOUSE_MEMBER]: 1,
                [client_1.Role.WAREHOUSE_MANAGER]: 2,
                [client_1.Role.TENANT_ADMIN]: 3,
            };
            return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
        }),
        isTenantAdmin: jest.fn().mockReturnValue(false),
        ...overrides,
    });
    beforeEach(async () => {
        const mockWarehouseValidationUtils = {
            validateUserWarehouseAccess: jest.fn(),
            validateUserWarehouseRole: jest.fn(),
            validateEntityWarehouseAccess: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                warehouse_permission_guard_1.WarehousePermissionGuard,
                {
                    provide: core_1.Reflector,
                    useValue: {
                        getAllAndOverride: jest.fn(),
                    },
                },
                {
                    provide: warehouse_validation_utils_1.WarehouseValidationUtils,
                    useValue: mockWarehouseValidationUtils,
                },
            ],
        }).compile();
        guard = module.get(warehouse_permission_guard_1.WarehousePermissionGuard);
        reflector = module.get(core_1.Reflector);
        warehouseValidationUtils = module.get(warehouse_validation_utils_1.WarehouseValidationUtils);
    });
    describe("canActivate", () => {
        it("should allow access when no warehouse permission requirements specified", async () => {
            const request = { user: createMockUser() };
            const context = mockExecutionContext(request);
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(undefined);
            const result = await guard.canActivate(context);
            expect(result).toBe(true);
        });
        it("should deny access when user is not authenticated", async () => {
            const request = { user: null };
            const context = mockExecutionContext(request);
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
                requireWarehouseId: true,
            });
            await expect(guard.canActivate(context)).rejects.toThrow(common_1.ForbiddenException);
        });
        it("should skip validation for tenant admin when configured", async () => {
            const user = createMockUser({ role: client_1.Role.TENANT_ADMIN });
            const request = { user };
            const context = mockExecutionContext(request);
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
                requireWarehouseId: true,
                skipForTenantAdmin: true,
            });
            const result = await guard.canActivate(context);
            expect(result).toBe(true);
            expect(request.warehouseContext).toBeDefined();
            expect(request.warehouseContext.isAdmin).toBe(true);
        });
        it("should require warehouse ID when configured", async () => {
            const request = {
                user: createMockUser(),
                params: {},
                query: {},
                headers: {},
                body: {},
            };
            const context = mockExecutionContext(request);
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
                requireWarehouseId: true,
            });
            await expect(guard.canActivate(context)).rejects.toThrow(common_1.BadRequestException);
        });
        it("should extract warehouse ID from URL parameters", async () => {
            const user = createMockUser();
            const request = {
                user,
                params: { warehouseId: "warehouse-1" },
                query: {},
                headers: {},
                body: {},
            };
            const context = mockExecutionContext(request);
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
                requireWarehouseId: true,
            });
            const result = await guard.canActivate(context);
            expect(result).toBe(true);
            expect(request.warehouseContext).toBeDefined();
            expect(request.warehouseContext.warehouseId).toBe("warehouse-1");
            expect(request.warehouseContext.userRole).toBe(client_1.Role.WAREHOUSE_MANAGER);
        });
        it("should extract warehouse ID from query parameters", async () => {
            const user = createMockUser();
            const request = {
                user,
                params: {},
                query: { warehouseId: "warehouse-2" },
                headers: {},
                body: {},
            };
            const context = mockExecutionContext(request);
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
                requireWarehouseId: true,
            });
            const result = await guard.canActivate(context);
            expect(result).toBe(true);
            expect(request.warehouseContext.warehouseId).toBe("warehouse-2");
            expect(request.warehouseContext.userRole).toBe(client_1.Role.WAREHOUSE_MEMBER);
        });
        it("should extract warehouse ID from headers", async () => {
            const user = createMockUser();
            const request = {
                user,
                params: {},
                query: {},
                headers: { "x-warehouse-id": "warehouse-1" },
                body: {},
            };
            const context = mockExecutionContext(request);
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
                requireWarehouseId: true,
            });
            const result = await guard.canActivate(context);
            expect(result).toBe(true);
            expect(request.warehouseContext.warehouseId).toBe("warehouse-1");
        });
        it("should extract warehouse ID from entity relationships", async () => {
            const user = createMockUser();
            const request = {
                user,
                params: { palletId: "pallet-1" },
                query: {},
                headers: {},
                body: {},
            };
            const context = mockExecutionContext(request);
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
                extractFromEntity: {
                    entityType: "pallet",
                    paramName: "palletId",
                },
            });
            warehouseValidationUtils.validateEntityWarehouseAccess.mockResolvedValue("warehouse-1");
            const result = await guard.canActivate(context);
            expect(result).toBe(true);
            expect(warehouseValidationUtils.validateEntityWarehouseAccess).toHaveBeenCalledWith("pallet-1", "pallet", user);
            expect(request.warehouseContext.warehouseId).toBe("warehouse-1");
        });
        it("should validate minimum role requirements", async () => {
            const user = createMockUser();
            const request = {
                user,
                params: { warehouseId: "warehouse-2" },
                query: {},
                headers: {},
                body: {},
            };
            const context = mockExecutionContext(request);
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
                requireWarehouseId: true,
                minimumRole: client_1.Role.WAREHOUSE_MANAGER,
            });
            await expect(guard.canActivate(context)).rejects.toThrow(common_1.ForbiddenException);
        });
        it("should allow access when user has sufficient role", async () => {
            const user = createMockUser();
            const request = {
                user,
                params: { warehouseId: "warehouse-1" },
                query: {},
                headers: {},
                body: {},
            };
            const context = mockExecutionContext(request);
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
                requireWarehouseId: true,
                minimumRole: client_1.Role.WAREHOUSE_MANAGER,
            });
            const result = await guard.canActivate(context);
            expect(result).toBe(true);
            expect(request.warehouseContext.isManager).toBe(true);
        });
        it("should deny access to inaccessible warehouse", async () => {
            const user = createMockUser();
            const request = {
                user,
                params: { warehouseId: "warehouse-3" },
                query: {},
                headers: {},
                body: {},
            };
            const context = mockExecutionContext(request);
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
                requireWarehouseId: true,
            });
            await expect(guard.canActivate(context)).rejects.toThrow(common_1.ForbiddenException);
        });
        it("should set warehouse context correctly", async () => {
            const user = createMockUser();
            const request = {
                user,
                params: { warehouseId: "warehouse-1" },
                query: {},
                headers: {},
                body: {},
            };
            const context = mockExecutionContext(request);
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue({
                requireWarehouseId: true,
            });
            await guard.canActivate(context);
            expect(request.warehouseContext).toEqual({
                warehouseId: "warehouse-1",
                userRole: client_1.Role.WAREHOUSE_MANAGER,
                hasAccess: true,
                isManager: true,
                isAdmin: false,
            });
        });
    });
});
//# sourceMappingURL=warehouse-permission.guard.spec.js.map