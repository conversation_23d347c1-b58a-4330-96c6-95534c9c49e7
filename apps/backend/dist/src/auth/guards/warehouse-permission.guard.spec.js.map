{"version": 3, "file": "warehouse-permission.guard.spec.js", "sourceRoot": "", "sources": ["../../../../src/auth/guards/warehouse-permission.guard.spec.ts"], "names": [], "mappings": ";;AAAA,6CAAsD;AACtD,2CAIwB;AACxB,uCAAyC;AACzC,2CAAsC;AACtC,6EAAwE;AACxE,kGAA6F;AAa7F,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;IACxC,IAAI,KAA+B,CAAC;IACpC,IAAI,SAAoB,CAAC;IACzB,IAAI,wBAA+D,CAAC;IAEpE,MAAM,oBAAoB,GAAG,CAAC,OAAY,EAAE,EAAE,CAC5C,CAAC;QACC,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC;YACnB,UAAU,EAAE,GAAG,EAAE,CAAC,OAAO;YACzB,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;YACtB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;SACnB,CAAC;QACF,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;QACnB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;QACxB,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;QACtB,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;KACE,CAAA,CAAC;IAEzB,MAAM,cAAc,GAAG,CACrB,YAA0C,EAAE,EACvB,EAAE,CAAC,CAAC;QACzB,EAAE,EAAE,QAAQ;QACZ,KAAK,EAAE,kBAAkB;QACzB,IAAI,EAAE,WAAW;QACjB,QAAQ,EAAE,UAAU;QACpB,UAAU,EAAE,QAAQ;QACpB,IAAI,EAAE,aAAI,CAAC,gBAAgB;QAC3B,MAAM,EAAE,QAAQ;QAChB,SAAS,EAAE,IAAI,IAAI,EAAE;QACrB,SAAS,EAAE,IAAI,IAAI,EAAE;QACrB,QAAQ,EAAE,UAAU;QACpB,cAAc,EAAE,EAAE;QAClB,oBAAoB,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;QACpD,cAAc,EAAE,IAAI,GAAG,CAAC;YACtB,CAAC,aAAa,EAAE,aAAI,CAAC,iBAAiB,CAAC;YACvC,CAAC,aAAa,EAAE,aAAI,CAAC,gBAAgB,CAAC;SACvC,CAAC;QACF,kBAAkB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,WAAmB,EAAE,EAAE,CAClD,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CACrD;QACD,gBAAgB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,WAAmB,EAAE,EAAE,CAChD,WAAW,KAAK,aAAa;YAC3B,CAAC,CAAC,aAAI,CAAC,iBAAiB;YACxB,CAAC,CAAC,aAAI,CAAC,gBAAgB,CAC1B;QACD,sBAAsB,EAAE,IAAI,CAAC,EAAE,CAC7B,CAAC,WAAmB,EAAE,YAAkB,EAAE,EAAE;YAC1C,MAAM,QAAQ,GACZ,WAAW,KAAK,aAAa;gBAC3B,CAAC,CAAC,aAAI,CAAC,iBAAiB;gBACxB,CAAC,CAAC,aAAI,CAAC,gBAAgB,CAAC;YAC5B,MAAM,aAAa,GAAG;gBACpB,CAAC,aAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC1B,CAAC,aAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBAC3B,CAAC,aAAI,CAAC,YAAY,CAAC,EAAE,CAAC;aACvB,CAAC;YACF,OAAO,aAAa,CAAC,QAAQ,CAAC,IAAI,aAAa,CAAC,YAAY,CAAC,CAAC;QAChE,CAAC,CACF;QACD,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC;QAC/C,GAAG,SAAS;KACb,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,4BAA4B,GAAG;YACnC,2BAA2B,EAAE,IAAI,CAAC,EAAE,EAAE;YACtC,yBAAyB,EAAE,IAAI,CAAC,EAAE,EAAE;YACpC,6BAA6B,EAAE,IAAI,CAAC,EAAE,EAAE;SACzC,CAAC;QAEF,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,qDAAwB;gBACxB;oBACE,OAAO,EAAE,gBAAS;oBAClB,QAAQ,EAAE;wBACR,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;qBAC7B;iBACF;gBACD;oBACE,OAAO,EAAE,qDAAwB;oBACjC,QAAQ,EAAE,4BAA4B;iBACvC;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,KAAK,GAAG,MAAM,CAAC,GAAG,CAA2B,qDAAwB,CAAC,CAAC;QACvE,SAAS,GAAG,MAAM,CAAC,GAAG,CAAY,gBAAS,CAAC,CAAC;QAC7C,wBAAwB,GAAG,MAAM,CAAC,GAAG,CAAC,qDAAwB,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,yEAAyE,EAAE,KAAK,IAAI,EAAE;YACvF,MAAM,OAAO,GAAgB,EAAE,IAAI,EAAE,cAAc,EAAE,EAAE,CAAC;YACxD,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE9C,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAEtE,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,OAAO,GAAgB,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;YAC5C,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE9C,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC;gBACzD,kBAAkB,EAAE,IAAI;aACzB,CAAC,CAAC;YAEH,MAAM,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CACtD,2BAAkB,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;YACvE,MAAM,IAAI,GAAG,cAAc,CAAC,EAAE,IAAI,EAAE,aAAI,CAAC,YAAY,EAAE,CAAC,CAAC;YACzD,MAAM,OAAO,GAAgB,EAAE,IAAI,EAAE,CAAC;YACtC,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE9C,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC;gBACzD,kBAAkB,EAAE,IAAI;gBACxB,kBAAkB,EAAE,IAAI;aACzB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,OAAO,GAAgB;gBAC3B,IAAI,EAAE,cAAc,EAAE;gBACtB,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,EAAE;gBACT,OAAO,EAAE,EAAE;gBACX,IAAI,EAAE,EAAE;aACT,CAAC;YACF,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE9C,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC;gBACzD,kBAAkB,EAAE,IAAI;aACzB,CAAC,CAAC;YAEH,MAAM,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CACtD,4BAAmB,CACpB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,IAAI,GAAG,cAAc,EAAE,CAAC;YAC9B,MAAM,OAAO,GAAgB;gBAC3B,IAAI;gBACJ,MAAM,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE;gBACtC,KAAK,EAAE,EAAE;gBACT,OAAO,EAAE,EAAE;gBACX,IAAI,EAAE,EAAE;aACT,CAAC;YACF,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE9C,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC;gBACzD,kBAAkB,EAAE,IAAI;aACzB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACjE,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,aAAI,CAAC,iBAAiB,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,IAAI,GAAG,cAAc,EAAE,CAAC;YAC9B,MAAM,OAAO,GAAgB;gBAC3B,IAAI;gBACJ,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE;gBACrC,OAAO,EAAE,EAAE;gBACX,IAAI,EAAE,EAAE;aACT,CAAC;YACF,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE9C,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC;gBACzD,kBAAkB,EAAE,IAAI;aACzB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACjE,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,aAAI,CAAC,gBAAgB,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,IAAI,GAAG,cAAc,EAAE,CAAC;YAC9B,MAAM,OAAO,GAAgB;gBAC3B,IAAI;gBACJ,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,EAAE;gBACT,OAAO,EAAE,EAAE,gBAAgB,EAAE,aAAa,EAAE;gBAC5C,IAAI,EAAE,EAAE;aACT,CAAC;YACF,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE9C,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC;gBACzD,kBAAkB,EAAE,IAAI;aACzB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,IAAI,GAAG,cAAc,EAAE,CAAC;YAC9B,MAAM,OAAO,GAAgB;gBAC3B,IAAI;gBACJ,MAAM,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE;gBAChC,KAAK,EAAE,EAAE;gBACT,OAAO,EAAE,EAAE;gBACX,IAAI,EAAE,EAAE;aACT,CAAC;YACF,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE9C,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC;gBACzD,iBAAiB,EAAE;oBACjB,UAAU,EAAE,QAAQ;oBACpB,SAAS,EAAE,UAAU;iBACtB;aACF,CAAC,CAAC;YAEH,wBAAwB,CAAC,6BAA6B,CAAC,iBAAiB,CACtE,aAAa,CACd,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,MAAM,CACJ,wBAAwB,CAAC,6BAA6B,CACvD,CAAC,oBAAoB,CAAC,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,IAAI,GAAG,cAAc,EAAE,CAAC;YAC9B,MAAM,OAAO,GAAgB;gBAC3B,IAAI;gBACJ,MAAM,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE;gBACtC,KAAK,EAAE,EAAE;gBACT,OAAO,EAAE,EAAE;gBACX,IAAI,EAAE,EAAE;aACT,CAAC;YACF,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE9C,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC;gBACzD,kBAAkB,EAAE,IAAI;gBACxB,WAAW,EAAE,aAAI,CAAC,iBAAiB;aACpC,CAAC,CAAC;YAEH,MAAM,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CACtD,2BAAkB,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,IAAI,GAAG,cAAc,EAAE,CAAC;YAC9B,MAAM,OAAO,GAAgB;gBAC3B,IAAI;gBACJ,MAAM,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE;gBACtC,KAAK,EAAE,EAAE;gBACT,OAAO,EAAE,EAAE;gBACX,IAAI,EAAE,EAAE;aACT,CAAC;YACF,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE9C,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC;gBACzD,kBAAkB,EAAE,IAAI;gBACxB,WAAW,EAAE,aAAI,CAAC,iBAAiB;aACpC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,IAAI,GAAG,cAAc,EAAE,CAAC;YAC9B,MAAM,OAAO,GAAgB;gBAC3B,IAAI;gBACJ,MAAM,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE;gBACtC,KAAK,EAAE,EAAE;gBACT,OAAO,EAAE,EAAE;gBACX,IAAI,EAAE,EAAE;aACT,CAAC;YACF,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE9C,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC;gBACzD,kBAAkB,EAAE,IAAI;aACzB,CAAC,CAAC;YAEH,MAAM,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CACtD,2BAAkB,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,IAAI,GAAG,cAAc,EAAE,CAAC;YAC9B,MAAM,OAAO,GAAgB;gBAC3B,IAAI;gBACJ,MAAM,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE;gBACtC,KAAK,EAAE,EAAE;gBACT,OAAO,EAAE,EAAE;gBACX,IAAI,EAAE,EAAE;aACT,CAAC;YACF,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE9C,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC;gBACzD,kBAAkB,EAAE,IAAI;aACzB,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEjC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC;gBACvC,WAAW,EAAE,aAAa;gBAC1B,QAAQ,EAAE,aAAI,CAAC,iBAAiB;gBAChC,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}