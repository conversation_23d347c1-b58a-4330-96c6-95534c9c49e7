"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WarehouseAuthGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const client_1 = require("@prisma/client");
const warehouse_access_decorator_1 = require("../decorators/warehouse-access.decorator");
let WarehouseAuthGuard = class WarehouseAuthGuard {
    constructor(reflector) {
        this.reflector = reflector;
    }
    canActivate(context) {
        const warehouseAccessOptions = this.reflector.getAllAndOverride(warehouse_access_decorator_1.WAREHOUSE_ACCESS_KEY, [context.getHandler(), context.getClass()]);
        if (!warehouseAccessOptions) {
            return true;
        }
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user) {
            throw new common_1.ForbiddenException("User not authenticated");
        }
        if (warehouseAccessOptions.allowedRoles && warehouseAccessOptions.allowedRoles.length > 0) {
            if (!warehouseAccessOptions.allowedRoles.includes(user.role)) {
                throw new common_1.ForbiddenException(`Access denied. Required roles: ${warehouseAccessOptions.allowedRoles.join(", ")}`);
            }
        }
        if (user.role === client_1.Role.TENANT_ADMIN) {
            return true;
        }
        const warehouseId = this.extractWarehouseId(request, warehouseAccessOptions);
        if (warehouseAccessOptions.requireWarehouseId && !warehouseId) {
            throw new common_1.ForbiddenException("Warehouse ID is required for this operation");
        }
        if (!warehouseId) {
            return true;
        }
        const userWarehouseIds = user.warehouseUsers?.map((wu) => wu.warehouseId) || [];
        if (!userWarehouseIds.includes(warehouseId)) {
            throw new common_1.ForbiddenException(`Access denied. You do not have permission to access warehouse: ${warehouseId}`);
        }
        return true;
    }
    extractWarehouseId(request, options) {
        const paramName = options.paramName || 'warehouseId';
        const queryParam = options.queryParam || 'warehouseId';
        const headerName = options.headerName || 'x-warehouse-id';
        if (request.params && request.params[paramName]) {
            return request.params[paramName];
        }
        if (request.query && request.query[queryParam]) {
            return request.query[queryParam];
        }
        if (request.headers && request.headers[headerName.toLowerCase()]) {
            return request.headers[headerName.toLowerCase()];
        }
        if (request.body && request.body.warehouseId) {
            return request.body.warehouseId;
        }
        return undefined;
    }
};
exports.WarehouseAuthGuard = WarehouseAuthGuard;
exports.WarehouseAuthGuard = WarehouseAuthGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector])
], WarehouseAuthGuard);
//# sourceMappingURL=warehouse-auth.guard.js.map