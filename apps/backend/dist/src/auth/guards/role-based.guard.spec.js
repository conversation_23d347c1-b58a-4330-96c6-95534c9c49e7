"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const role_based_guard_1 = require("./role-based.guard");
describe("Role-Based Guards", () => {
    let mockReflector;
    let mockExecutionContext;
    let mockRequest;
    beforeEach(() => {
        mockReflector = {
            getAllAndOverride: jest.fn(),
        };
        mockRequest = {
            user: null,
            warehouseContext: null,
        };
        mockExecutionContext = {
            switchToHttp: jest.fn().mockReturnValue({
                getRequest: jest.fn().mockReturnValue(mockRequest),
            }),
            getHandler: jest.fn(),
            getClass: jest.fn(),
        };
    });
    const createMockUser = (overrides = {}) => ({
        id: "user-1",
        email: "<EMAIL>",
        name: "Test User",
        password: "password",
        authUserId: "auth-1",
        role: client_1.Role.WAREHOUSE_MEMBER,
        status: "ACTIVE",
        createdAt: new Date(),
        updatedAt: new Date(),
        tenantId: "tenant-1",
        warehouseUsers: [],
        accessibleWarehouses: [],
        warehouseRoles: new Map(),
        isTenantAdmin: jest.fn().mockReturnValue(false),
        canAccessWarehouse: jest.fn(),
        getWarehouseRole: jest.fn(),
        hasWarehousePermission: jest.fn(),
        ...overrides,
    });
    describe("RoleBasedGuard", () => {
        let guard;
        beforeEach(() => {
            guard = new role_based_guard_1.RoleBasedGuard(mockReflector);
        });
        it("should allow access when no roles are required", () => {
            mockReflector.getAllAndOverride.mockReturnValue(undefined);
            mockRequest.user = createMockUser();
            const result = guard.canActivate(mockExecutionContext);
            expect(result).toBe(true);
        });
        it("should throw ForbiddenException when user is not authenticated", () => {
            mockReflector.getAllAndOverride.mockReturnValue([client_1.Role.WAREHOUSE_MEMBER]);
            mockRequest.user = null;
            expect(() => guard.canActivate(mockExecutionContext)).toThrow(common_1.ForbiddenException);
        });
        it("should allow access for tenant admin", () => {
            mockReflector.getAllAndOverride.mockReturnValue([client_1.Role.TENANT_ADMIN]);
            const mockUser = createMockUser();
            mockUser.isTenantAdmin.mockReturnValue(true);
            mockRequest.user = mockUser;
            const result = guard.canActivate(mockExecutionContext);
            expect(result).toBe(true);
        });
        it("should allow access for warehouse manager", () => {
            mockReflector.getAllAndOverride.mockReturnValue([client_1.Role.WAREHOUSE_MANAGER]);
            mockRequest.user = createMockUser({
                warehouseUsers: [{ warehouseId: "wh-1", role: client_1.Role.WAREHOUSE_MANAGER }],
            });
            const result = guard.canActivate(mockExecutionContext);
            expect(result).toBe(true);
        });
        it("should allow access for warehouse member", () => {
            mockReflector.getAllAndOverride.mockReturnValue([client_1.Role.WAREHOUSE_MEMBER]);
            mockRequest.user = createMockUser({
                warehouseUsers: [{ warehouseId: "wh-1", role: client_1.Role.WAREHOUSE_MEMBER }],
            });
            const result = guard.canActivate(mockExecutionContext);
            expect(result).toBe(true);
        });
        it("should deny access when user lacks required role", () => {
            mockReflector.getAllAndOverride.mockReturnValue([client_1.Role.WAREHOUSE_MANAGER]);
            mockRequest.user = createMockUser({
                warehouseUsers: [{ warehouseId: "wh-1", role: client_1.Role.WAREHOUSE_MEMBER }],
            });
            expect(() => guard.canActivate(mockExecutionContext)).toThrow(common_1.ForbiddenException);
        });
    });
    describe("TenantAdminGuard", () => {
        let guard;
        beforeEach(() => {
            guard = new role_based_guard_1.TenantAdminGuard();
        });
        it("should allow access for tenant admin", () => {
            const mockUser = createMockUser();
            mockUser.isTenantAdmin.mockReturnValue(true);
            mockRequest.user = mockUser;
            const result = guard.canActivate(mockExecutionContext);
            expect(result).toBe(true);
        });
        it("should deny access for non-tenant admin", () => {
            const mockUser = createMockUser();
            mockUser.isTenantAdmin.mockReturnValue(false);
            mockRequest.user = mockUser;
            expect(() => guard.canActivate(mockExecutionContext)).toThrow(common_1.ForbiddenException);
        });
        it("should throw ForbiddenException when user is not authenticated", () => {
            mockRequest.user = null;
            expect(() => guard.canActivate(mockExecutionContext)).toThrow(common_1.ForbiddenException);
        });
    });
    describe("WarehouseManagerGuard", () => {
        let guard;
        beforeEach(() => {
            guard = new role_based_guard_1.WarehouseManagerGuard();
        });
        it("should allow access for tenant admin", () => {
            const mockUser = createMockUser();
            mockUser.isTenantAdmin.mockReturnValue(true);
            mockRequest.user = mockUser;
            const result = guard.canActivate(mockExecutionContext);
            expect(result).toBe(true);
        });
        it("should allow access for warehouse manager", () => {
            mockRequest.user = createMockUser({
                warehouseUsers: [{ warehouseId: "wh-1", role: client_1.Role.WAREHOUSE_MANAGER }],
            });
            const result = guard.canActivate(mockExecutionContext);
            expect(result).toBe(true);
        });
        it("should deny access for warehouse member only", () => {
            mockRequest.user = createMockUser({
                warehouseUsers: [{ warehouseId: "wh-1", role: client_1.Role.WAREHOUSE_MEMBER }],
            });
            expect(() => guard.canActivate(mockExecutionContext)).toThrow(common_1.ForbiddenException);
        });
    });
    describe("WarehouseMemberGuard", () => {
        let guard;
        beforeEach(() => {
            guard = new role_based_guard_1.WarehouseMemberGuard();
        });
        it("should allow access for tenant admin", () => {
            const mockUser = createMockUser();
            mockUser.isTenantAdmin.mockReturnValue(true);
            mockRequest.user = mockUser;
            const result = guard.canActivate(mockExecutionContext);
            expect(result).toBe(true);
        });
        it("should allow access for any warehouse role", () => {
            mockRequest.user = createMockUser({
                warehouseUsers: [{ warehouseId: "wh-1", role: client_1.Role.WAREHOUSE_MEMBER }],
            });
            const result = guard.canActivate(mockExecutionContext);
            expect(result).toBe(true);
        });
        it("should deny access for users with no warehouse access", () => {
            mockRequest.user = createMockUser({
                warehouseUsers: [],
            });
            expect(() => guard.canActivate(mockExecutionContext)).toThrow(common_1.ForbiddenException);
        });
    });
    describe("WarehouseSpecificRoleGuard", () => {
        let guard;
        beforeEach(() => {
            guard = new role_based_guard_1.WarehouseSpecificRoleGuard(mockReflector);
        });
        it("should allow access when no warehouse role is required", () => {
            mockReflector.getAllAndOverride.mockReturnValue(undefined);
            mockRequest.user = createMockUser();
            const result = guard.canActivate(mockExecutionContext);
            expect(result).toBe(true);
        });
        it("should allow access for tenant admin", () => {
            mockReflector.getAllAndOverride.mockReturnValue(client_1.Role.WAREHOUSE_MANAGER);
            const mockUser = createMockUser();
            mockUser.isTenantAdmin.mockReturnValue(true);
            mockRequest.user = mockUser;
            mockRequest.warehouseContext = { warehouseId: "wh-1" };
            const result = guard.canActivate(mockExecutionContext);
            expect(result).toBe(true);
        });
        it("should allow access when user has required role in warehouse", () => {
            mockReflector.getAllAndOverride.mockReturnValue(client_1.Role.WAREHOUSE_MANAGER);
            const mockUser = createMockUser();
            mockUser.getWarehouseRole.mockReturnValue(client_1.Role.WAREHOUSE_MANAGER);
            mockUser.hasWarehousePermission.mockReturnValue(true);
            mockRequest.user = mockUser;
            mockRequest.warehouseContext = { warehouseId: "wh-1" };
            const result = guard.canActivate(mockExecutionContext);
            expect(result).toBe(true);
        });
        it("should deny access when warehouse context is missing", () => {
            mockReflector.getAllAndOverride.mockReturnValue(client_1.Role.WAREHOUSE_MANAGER);
            mockRequest.user = createMockUser();
            mockRequest.warehouseContext = null;
            expect(() => guard.canActivate(mockExecutionContext)).toThrow(common_1.ForbiddenException);
        });
        it("should deny access when user has no access to warehouse", () => {
            mockReflector.getAllAndOverride.mockReturnValue(client_1.Role.WAREHOUSE_MANAGER);
            const mockUser = createMockUser();
            mockUser.getWarehouseRole.mockReturnValue(null);
            mockRequest.user = mockUser;
            mockRequest.warehouseContext = { warehouseId: "wh-1" };
            expect(() => guard.canActivate(mockExecutionContext)).toThrow(common_1.ForbiddenException);
        });
        it("should deny access when user lacks required role in warehouse", () => {
            mockReflector.getAllAndOverride.mockReturnValue(client_1.Role.WAREHOUSE_MANAGER);
            const mockUser = createMockUser();
            mockUser.getWarehouseRole.mockReturnValue(client_1.Role.WAREHOUSE_MEMBER);
            mockUser.hasWarehousePermission.mockReturnValue(false);
            mockRequest.user = mockUser;
            mockRequest.warehouseContext = { warehouseId: "wh-1" };
            expect(() => guard.canActivate(mockExecutionContext)).toThrow(common_1.ForbiddenException);
        });
    });
});
//# sourceMappingURL=role-based.guard.spec.js.map