"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WarehouseSpecificRoleGuard = exports.WarehouseMemberGuard = exports.WarehouseManagerGuard = exports.TenantAdminGuard = exports.RoleBasedGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const client_1 = require("@prisma/client");
let RoleBasedGuard = class RoleBasedGuard {
    constructor(reflector) {
        this.reflector = reflector;
    }
    canActivate(context) {
        const requiredRoles = this.reflector.getAllAndOverride("roles", [
            context.getHandler(),
            context.getClass(),
        ]);
        if (!requiredRoles) {
            return true;
        }
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user) {
            throw new common_1.ForbiddenException("User not authenticated");
        }
        const hasRequiredRole = requiredRoles.some((role) => {
            switch (role) {
                case client_1.Role.TENANT_ADMIN:
                    return user.isTenantAdmin();
                case client_1.Role.WAREHOUSE_MANAGER:
                    return user.warehouseUsers.some((wu) => wu.role === client_1.Role.WAREHOUSE_MANAGER);
                case client_1.Role.WAREHOUSE_MEMBER:
                    return user.warehouseUsers.length > 0;
                default:
                    return false;
            }
        });
        if (!hasRequiredRole) {
            throw new common_1.ForbiddenException(`Access denied. Required roles: ${requiredRoles.join(", ")}`);
        }
        return true;
    }
};
exports.RoleBasedGuard = RoleBasedGuard;
exports.RoleBasedGuard = RoleBasedGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector])
], RoleBasedGuard);
let TenantAdminGuard = class TenantAdminGuard {
    canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user) {
            throw new common_1.ForbiddenException("User not authenticated");
        }
        if (!user.isTenantAdmin()) {
            throw new common_1.ForbiddenException("Access denied. Tenant admin role required.");
        }
        return true;
    }
};
exports.TenantAdminGuard = TenantAdminGuard;
exports.TenantAdminGuard = TenantAdminGuard = __decorate([
    (0, common_1.Injectable)()
], TenantAdminGuard);
let WarehouseManagerGuard = class WarehouseManagerGuard {
    canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user) {
            throw new common_1.ForbiddenException("User not authenticated");
        }
        if (user.isTenantAdmin()) {
            return true;
        }
        const hasManagerRole = user.warehouseUsers.some((wu) => wu.role === client_1.Role.WAREHOUSE_MANAGER);
        if (!hasManagerRole) {
            throw new common_1.ForbiddenException("Access denied. Warehouse manager role required.");
        }
        return true;
    }
};
exports.WarehouseManagerGuard = WarehouseManagerGuard;
exports.WarehouseManagerGuard = WarehouseManagerGuard = __decorate([
    (0, common_1.Injectable)()
], WarehouseManagerGuard);
let WarehouseMemberGuard = class WarehouseMemberGuard {
    canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user) {
            throw new common_1.ForbiddenException("User not authenticated");
        }
        if (user.isTenantAdmin()) {
            return true;
        }
        if (user.warehouseUsers.length === 0) {
            throw new common_1.ForbiddenException("Access denied. Warehouse access required.");
        }
        return true;
    }
};
exports.WarehouseMemberGuard = WarehouseMemberGuard;
exports.WarehouseMemberGuard = WarehouseMemberGuard = __decorate([
    (0, common_1.Injectable)()
], WarehouseMemberGuard);
let WarehouseSpecificRoleGuard = class WarehouseSpecificRoleGuard {
    constructor(reflector) {
        this.reflector = reflector;
    }
    canActivate(context) {
        const requiredRole = this.reflector.getAllAndOverride("warehouseRole", [context.getHandler(), context.getClass()]);
        if (!requiredRole) {
            return true;
        }
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        const warehouseId = request.warehouseContext?.warehouseId;
        if (!user) {
            throw new common_1.ForbiddenException("User not authenticated");
        }
        if (!warehouseId) {
            throw new common_1.ForbiddenException("Warehouse context required for role validation");
        }
        if (user.isTenantAdmin()) {
            return true;
        }
        const userRole = user.getWarehouseRole(warehouseId);
        if (!userRole) {
            throw new common_1.ForbiddenException(`Access denied. No access to warehouse ${warehouseId}`);
        }
        const hasPermission = user.hasWarehousePermission(warehouseId, requiredRole);
        if (!hasPermission) {
            throw new common_1.ForbiddenException(`Access denied. Required role: ${requiredRole} in warehouse ${warehouseId}`);
        }
        return true;
    }
};
exports.WarehouseSpecificRoleGuard = WarehouseSpecificRoleGuard;
exports.WarehouseSpecificRoleGuard = WarehouseSpecificRoleGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector])
], WarehouseSpecificRoleGuard);
//# sourceMappingURL=role-based.guard.js.map