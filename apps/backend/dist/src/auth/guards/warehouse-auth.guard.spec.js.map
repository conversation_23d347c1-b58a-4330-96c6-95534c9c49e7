{"version": 3, "file": "warehouse-auth.guard.spec.js", "sourceRoot": "", "sources": ["../../../../src/auth/guards/warehouse-auth.guard.spec.ts"], "names": [], "mappings": ";;AAAA,6CAAsD;AACtD,uCAAyC;AACzC,2CAAsE;AACtE,2CAAsC;AACtC,iEAGgC;AAIhC,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;IAClC,IAAI,KAAyB,CAAC;IAC9B,IAAI,SAAoB,CAAC;IAEzB,MAAM,QAAQ,GAAsB;QAClC,EAAE,EAAE,QAAQ;QACZ,KAAK,EAAE,kBAAkB;QACzB,IAAI,EAAE,aAAI,CAAC,gBAAgB;QAC3B,QAAQ,EAAE,UAAU;QACpB,IAAI,EAAE,WAAW;QACjB,UAAU,EAAE,QAAQ;QACpB,cAAc,EAAE;YACd,EAAE,WAAW,EAAE,aAAa,EAAE,IAAI,EAAE,aAAI,CAAC,gBAAgB,EAAE;YAC3D,EAAE,WAAW,EAAE,aAAa,EAAE,IAAI,EAAE,aAAI,CAAC,gBAAgB,EAAE;SAC5D;KACF,CAAC;IAEF,MAAM,eAAe,GAAsB;QACzC,GAAG,QAAQ;QACX,IAAI,EAAE,aAAI,CAAC,YAAY;KACxB,CAAC;IAEF,MAAM,0BAA0B,GAAG,CACjC,IAA8B,EAC9B,SAAc,EAAE,EAChB,QAAa,EAAE,EACf,UAAe,EAAE,EACjB,OAAY,EAAE,EACI,EAAE;QACpB,OAAO;YACL,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC;gBACnB,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC;oBACjB,IAAI;oBACJ,MAAM;oBACN,KAAK;oBACL,OAAO;oBACP,IAAI;iBACL,CAAC;aACH,CAAC;YACF,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;YACrB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;SACb,CAAC;IACX,CAAC,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,yCAAkB;gBAClB;oBACE,OAAO,EAAE,gBAAS;oBAClB,QAAQ,EAAE;wBACR,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;qBAC7B;iBACF;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,KAAK,GAAG,MAAM,CAAC,GAAG,CAAqB,yCAAkB,CAAC,CAAC;QAC3D,SAAS,GAAG,MAAM,CAAC,GAAG,CAAY,gBAAS,CAAC,CAAC;IAC/C,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,kEAAkE,EAAE,GAAG,EAAE;YAC1E,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YACtE,MAAM,OAAO,GAAG,0BAA0B,CAAC,QAAQ,CAAC,CAAC;YAErD,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,MAAM,OAAO,GAA2B,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC;YACrE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,OAAO,GAAG,0BAA0B,CAAC,IAAI,CAAC,CAAC;YAEjD,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,2BAAkB,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,OAAO,GAA2B,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC;YACrE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,OAAO,GAAG,0BAA0B,CAAC,eAAe,EAAE;gBAC1D,WAAW,EAAE,eAAe;aAC7B,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,OAAO,GAA2B;gBACtC,YAAY,EAAE,CAAC,aAAI,CAAC,iBAAiB,CAAC;gBACtC,kBAAkB,EAAE,IAAI;aACzB,CAAC;YACF,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,OAAO,GAAG,0BAA0B,CAAC,QAAQ,CAAC,CAAC;YAErD,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,2BAAkB,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,OAAO,GAA2B;gBACtC,YAAY,EAAE,CAAC,aAAI,CAAC,gBAAgB,CAAC;gBACrC,kBAAkB,EAAE,KAAK;aAC1B,CAAC;YACF,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,OAAO,GAAG,0BAA0B,CAAC,QAAQ,CAAC,CAAC;YAErD,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+EAA+E,EAAE,GAAG,EAAE;YACvF,MAAM,OAAO,GAA2B,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC;YACrE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,OAAO,GAAG,0BAA0B,CAAC,QAAQ,CAAC,CAAC;YAErD,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,2BAAkB,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uEAAuE,EAAE,GAAG,EAAE;YAC/E,MAAM,OAAO,GAA2B,EAAE,kBAAkB,EAAE,KAAK,EAAE,CAAC;YACtE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,OAAO,GAAG,0BAA0B,CAAC,QAAQ,CAAC,CAAC;YAErD,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qEAAqE,EAAE,GAAG,EAAE;YAC7E,MAAM,OAAO,GAA2B,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC;YACrE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,OAAO,GAAG,0BAA0B,CAAC,QAAQ,EAAE;gBACnD,WAAW,EAAE,aAAa;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6EAA6E,EAAE,GAAG,EAAE;YACrF,MAAM,OAAO,GAA2B,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC;YACrE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,OAAO,GAAG,0BAA0B,CAAC,QAAQ,EAAE;gBACnD,WAAW,EAAE,aAAa;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,2BAAkB,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,OAAO,GAA2B,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC;YACrE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,OAAO,GAAG,0BAA0B,CACxC,QAAQ,EACR,EAAE,EACF,EAAE,WAAW,EAAE,aAAa,EAAE,CAC/B,CAAC;YAEF,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,OAAO,GAA2B,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC;YACrE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,OAAO,GAAG,0BAA0B,CACxC,QAAQ,EACR,EAAE,EACF,EAAE,EACF,EAAE,gBAAgB,EAAE,aAAa,EAAE,CACpC,CAAC;YAEF,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,OAAO,GAA2B,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC;YACrE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,OAAO,GAAG,0BAA0B,CACxC,QAAQ,EACR,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,WAAW,EAAE,aAAa,EAAE,CAC/B,CAAC;YAEF,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,OAAO,GAA2B;gBACtC,kBAAkB,EAAE,IAAI;gBACxB,SAAS,EAAE,IAAI;gBACf,UAAU,EAAE,WAAW;gBACvB,UAAU,EAAE,oBAAoB;aACjC,CAAC;YACF,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,OAAO,GAAG,0BAA0B,CAAC,QAAQ,EAAE;gBACnD,EAAE,EAAE,aAAa;aAClB,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}