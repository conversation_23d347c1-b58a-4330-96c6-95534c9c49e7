"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const warehouse_auth_guard_1 = require("./warehouse-auth.guard");
describe("WarehouseAuthGuard", () => {
    let guard;
    let reflector;
    const mockUser = {
        id: "user-1",
        email: "<EMAIL>",
        role: client_1.Role.WAREHOUSE_MEMBER,
        tenantId: "tenant-1",
        name: "Test User",
        authUserId: "auth-1",
        warehouseUsers: [
            { warehouseId: "warehouse-1", role: client_1.Role.WAREHOUSE_MEMBER },
            { warehouseId: "warehouse-2", role: client_1.Role.WAREHOUSE_MEMBER },
        ],
    };
    const mockTenantAdmin = {
        ...mockUser,
        role: client_1.Role.TENANT_ADMIN,
    };
    const createMockExecutionContext = (user, params = {}, query = {}, headers = {}, body = {}) => {
        return {
            switchToHttp: () => ({
                getRequest: () => ({
                    user,
                    params,
                    query,
                    headers,
                    body,
                }),
            }),
            getHandler: jest.fn(),
            getClass: jest.fn(),
        };
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                warehouse_auth_guard_1.WarehouseAuthGuard,
                {
                    provide: core_1.Reflector,
                    useValue: {
                        getAllAndOverride: jest.fn(),
                    },
                },
            ],
        }).compile();
        guard = module.get(warehouse_auth_guard_1.WarehouseAuthGuard);
        reflector = module.get(core_1.Reflector);
    });
    describe("canActivate", () => {
        it("should allow access when no warehouse access options are defined", () => {
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(undefined);
            const context = createMockExecutionContext(mockUser);
            expect(guard.canActivate(context)).toBe(true);
        });
        it("should throw ForbiddenException when user is not authenticated", () => {
            const options = { requireWarehouseId: true };
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
            const context = createMockExecutionContext(null);
            expect(() => guard.canActivate(context)).toThrow(common_1.ForbiddenException);
        });
        it("should allow TENANT_ADMIN access to any warehouse", () => {
            const options = { requireWarehouseId: true };
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
            const context = createMockExecutionContext(mockTenantAdmin, {
                warehouseId: "any-warehouse",
            });
            expect(guard.canActivate(context)).toBe(true);
        });
        it("should check role-based access when allowedRoles is specified", () => {
            const options = {
                allowedRoles: [client_1.Role.WAREHOUSE_MANAGER],
                requireWarehouseId: true,
            };
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
            const context = createMockExecutionContext(mockUser);
            expect(() => guard.canActivate(context)).toThrow(common_1.ForbiddenException);
        });
        it("should allow access when user has required role", () => {
            const options = {
                allowedRoles: [client_1.Role.WAREHOUSE_MEMBER],
                requireWarehouseId: false,
            };
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
            const context = createMockExecutionContext(mockUser);
            expect(guard.canActivate(context)).toBe(true);
        });
        it("should throw ForbiddenException when warehouseId is required but not provided", () => {
            const options = { requireWarehouseId: true };
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
            const context = createMockExecutionContext(mockUser);
            expect(() => guard.canActivate(context)).toThrow(common_1.ForbiddenException);
        });
        it("should allow access when warehouseId is not required and not provided", () => {
            const options = { requireWarehouseId: false };
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
            const context = createMockExecutionContext(mockUser);
            expect(guard.canActivate(context)).toBe(true);
        });
        it("should allow access when user has access to the specified warehouse", () => {
            const options = { requireWarehouseId: true };
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
            const context = createMockExecutionContext(mockUser, {
                warehouseId: "warehouse-1",
            });
            expect(guard.canActivate(context)).toBe(true);
        });
        it("should throw ForbiddenException when user does not have access to warehouse", () => {
            const options = { requireWarehouseId: true };
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
            const context = createMockExecutionContext(mockUser, {
                warehouseId: "warehouse-3",
            });
            expect(() => guard.canActivate(context)).toThrow(common_1.ForbiddenException);
        });
        it("should extract warehouseId from query parameters", () => {
            const options = { requireWarehouseId: true };
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
            const context = createMockExecutionContext(mockUser, {}, { warehouseId: "warehouse-1" });
            expect(guard.canActivate(context)).toBe(true);
        });
        it("should extract warehouseId from headers", () => {
            const options = { requireWarehouseId: true };
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
            const context = createMockExecutionContext(mockUser, {}, {}, { "x-warehouse-id": "warehouse-1" });
            expect(guard.canActivate(context)).toBe(true);
        });
        it("should extract warehouseId from request body", () => {
            const options = { requireWarehouseId: true };
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
            const context = createMockExecutionContext(mockUser, {}, {}, {}, { warehouseId: "warehouse-1" });
            expect(guard.canActivate(context)).toBe(true);
        });
        it("should use custom parameter names", () => {
            const options = {
                requireWarehouseId: true,
                paramName: "id",
                queryParam: "warehouse",
                headerName: "x-custom-warehouse",
            };
            jest.spyOn(reflector, "getAllAndOverride").mockReturnValue(options);
            const context = createMockExecutionContext(mockUser, {
                id: "warehouse-1",
            });
            expect(guard.canActivate(context)).toBe(true);
        });
    });
});
//# sourceMappingURL=warehouse-auth.guard.spec.js.map