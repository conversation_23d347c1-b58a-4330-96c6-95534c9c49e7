import { CanActivate, ExecutionContext } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { Role } from "@prisma/client";
import { WarehouseValidationUtils } from "../../warehouses/utils/warehouse-validation.utils";
export declare class WarehousePermissionGuard implements CanActivate {
    private readonly reflector;
    private readonly warehouseValidationUtils;
    private readonly logger;
    constructor(reflector: Reflector, warehouseValidationUtils: WarehouseValidationUtils);
    canActivate(context: ExecutionContext): Promise<boolean>;
    private extractWarehouseId;
    private extractWarehouseFromEntity;
    private validateWarehouseAccess;
    private setWarehouseContext;
}
export declare function getWarehouseContext(request: any): {
    warehouseId: string;
    userRole: Role;
    hasAccess: boolean;
    isManager: boolean;
    isAdmin: boolean;
} | undefined;
export declare function getWarehouseId(request: any): string | undefined;
export declare function isWarehouseManager(request: any): boolean;
export declare function isTenantAdmin(request: any): boolean;
