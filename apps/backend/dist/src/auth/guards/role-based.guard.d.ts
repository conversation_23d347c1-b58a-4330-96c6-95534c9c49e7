import { CanActivate, ExecutionContext } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
export declare class RoleBasedGuard implements CanActivate {
    private reflector;
    constructor(reflector: Reflector);
    canActivate(context: ExecutionContext): boolean;
}
export declare class TenantAdminGuard implements CanActivate {
    canActivate(context: ExecutionContext): boolean;
}
export declare class WarehouseManagerGuard implements CanActivate {
    canActivate(context: ExecutionContext): boolean;
}
export declare class WarehouseMemberGuard implements CanActivate {
    canActivate(context: ExecutionContext): boolean;
}
export declare class WarehouseSpecificRoleGuard implements CanActivate {
    private reflector;
    constructor(reflector: Reflector);
    canActivate(context: ExecutionContext): boolean;
}
