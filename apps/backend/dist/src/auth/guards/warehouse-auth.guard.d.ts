import { CanActivate, ExecutionContext } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { Role } from "@prisma/client";
export interface WarehouseAccessOptions {
    requireWarehouseId?: boolean;
    allowedRoles?: Role[];
    paramName?: string;
    queryParam?: string;
    headerName?: string;
}
export declare class WarehouseAuthGuard implements CanActivate {
    private reflector;
    constructor(reflector: Reflector);
    canActivate(context: ExecutionContext): boolean;
    private extractWarehouseId;
}
