{"version": 3, "file": "role-based.guard.spec.js", "sourceRoot": "", "sources": ["../../../../src/auth/guards/role-based.guard.spec.ts"], "names": [], "mappings": ";;AAAA,2CAAsE;AAEtE,2CAAsC;AACtC,yDAM4B;AAG5B,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;IACjC,IAAI,aAAqC,CAAC;IAC1C,IAAI,oBAAmD,CAAC;IACxD,IAAI,WAAgB,CAAC;IAErB,UAAU,CAAC,GAAG,EAAE;QACd,aAAa,GAAG;YACd,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;SACtB,CAAC;QAET,WAAW,GAAG;YACZ,IAAI,EAAE,IAAI;YACV,gBAAgB,EAAE,IAAI;SACvB,CAAC;QAEF,oBAAoB,GAAG;YACrB,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC;gBACtC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,WAAW,CAAC;aACnD,CAAC;YACF,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;YACrB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;SACb,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,MAAM,cAAc,GAAG,CACrB,YAA0C,EAAE,EACvB,EAAE,CAAC,CAAC;QACzB,EAAE,EAAE,QAAQ;QACZ,KAAK,EAAE,kBAAkB;QACzB,IAAI,EAAE,WAAW;QACjB,QAAQ,EAAE,UAAU;QACpB,UAAU,EAAE,QAAQ;QACpB,IAAI,EAAE,aAAI,CAAC,gBAAgB;QAC3B,MAAM,EAAE,QAAQ;QAChB,SAAS,EAAE,IAAI,IAAI,EAAE;QACrB,SAAS,EAAE,IAAI,IAAI,EAAE;QACrB,QAAQ,EAAE,UAAU;QACpB,cAAc,EAAE,EAAE;QAClB,oBAAoB,EAAE,EAAE;QACxB,cAAc,EAAE,IAAI,GAAG,EAAE;QACzB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC;QAC/C,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC7B,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC3B,sBAAsB,EAAE,IAAI,CAAC,EAAE,EAAE;QACjC,GAAG,SAAS;KACb,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,IAAI,KAAqB,CAAC;QAE1B,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,IAAI,iCAAc,CAAC,aAAa,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,aAAa,CAAC,iBAAiB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAC3D,WAAW,CAAC,IAAI,GAAG,cAAc,EAAE,CAAC;YAEpC,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,aAAa,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,aAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;YACzE,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC;YAExB,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAAC,OAAO,CAC3D,2BAAkB,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,aAAa,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,aAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YACrE,MAAM,QAAQ,GAAG,cAAc,EAAE,CAAC;YACjC,QAAQ,CAAC,aAA2B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC5D,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC;YAE5B,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,aAAa,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,aAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAC1E,WAAW,CAAC,IAAI,GAAG,cAAc,CAAC;gBAChC,cAAc,EAAE,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,aAAI,CAAC,iBAAiB,EAAE,CAAC;aACxE,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,aAAa,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,aAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;YACzE,WAAW,CAAC,IAAI,GAAG,cAAc,CAAC;gBAChC,cAAc,EAAE,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,aAAI,CAAC,gBAAgB,EAAE,CAAC;aACvE,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,aAAa,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,aAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAC1E,WAAW,CAAC,IAAI,GAAG,cAAc,CAAC;gBAChC,cAAc,EAAE,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,aAAI,CAAC,gBAAgB,EAAE,CAAC;aACvE,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAAC,OAAO,CAC3D,2BAAkB,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,IAAI,KAAuB,CAAC;QAE5B,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,IAAI,mCAAgB,EAAE,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,QAAQ,GAAG,cAAc,EAAE,CAAC;YACjC,QAAQ,CAAC,aAA2B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC5D,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC;YAE5B,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,QAAQ,GAAG,cAAc,EAAE,CAAC;YACjC,QAAQ,CAAC,aAA2B,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC7D,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC;YAE5B,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAAC,OAAO,CAC3D,2BAAkB,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACxE,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC;YAExB,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAAC,OAAO,CAC3D,2BAAkB,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,IAAI,KAA4B,CAAC;QAEjC,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,IAAI,wCAAqB,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,QAAQ,GAAG,cAAc,EAAE,CAAC;YACjC,QAAQ,CAAC,aAA2B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC5D,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC;YAE5B,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,WAAW,CAAC,IAAI,GAAG,cAAc,CAAC;gBAChC,cAAc,EAAE,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,aAAI,CAAC,iBAAiB,EAAE,CAAC;aACxE,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,WAAW,CAAC,IAAI,GAAG,cAAc,CAAC;gBAChC,cAAc,EAAE,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,aAAI,CAAC,gBAAgB,EAAE,CAAC;aACvE,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAAC,OAAO,CAC3D,2BAAkB,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,IAAI,KAA2B,CAAC;QAEhC,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,IAAI,uCAAoB,EAAE,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,QAAQ,GAAG,cAAc,EAAE,CAAC;YACjC,QAAQ,CAAC,aAA2B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC5D,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC;YAE5B,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,WAAW,CAAC,IAAI,GAAG,cAAc,CAAC;gBAChC,cAAc,EAAE,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,aAAI,CAAC,gBAAgB,EAAE,CAAC;aACvE,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,WAAW,CAAC,IAAI,GAAG,cAAc,CAAC;gBAChC,cAAc,EAAE,EAAE;aACnB,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAAC,OAAO,CAC3D,2BAAkB,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,IAAI,KAAiC,CAAC;QAEtC,UAAU,CAAC,GAAG,EAAE;YACd,KAAK,GAAG,IAAI,6CAA0B,CAAC,aAAa,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,aAAa,CAAC,iBAAiB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAC3D,WAAW,CAAC,IAAI,GAAG,cAAc,EAAE,CAAC;YAEpC,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,aAAa,CAAC,iBAAiB,CAAC,eAAe,CAAC,aAAI,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,QAAQ,GAAG,cAAc,EAAE,CAAC;YACjC,QAAQ,CAAC,aAA2B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC5D,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC;YAC5B,WAAW,CAAC,gBAAgB,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;YAEvD,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,aAAa,CAAC,iBAAiB,CAAC,eAAe,CAAC,aAAI,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,QAAQ,GAAG,cAAc,EAAE,CAAC;YACjC,QAAQ,CAAC,gBAA8B,CAAC,eAAe,CACtD,aAAI,CAAC,iBAAiB,CACvB,CAAC;YACD,QAAQ,CAAC,sBAAoC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACrE,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC;YAC5B,WAAW,CAAC,gBAAgB,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;YAEvD,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,aAAa,CAAC,iBAAiB,CAAC,eAAe,CAAC,aAAI,CAAC,iBAAiB,CAAC,CAAC;YACxE,WAAW,CAAC,IAAI,GAAG,cAAc,EAAE,CAAC;YACpC,WAAW,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAEpC,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAAC,OAAO,CAC3D,2BAAkB,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,aAAa,CAAC,iBAAiB,CAAC,eAAe,CAAC,aAAI,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,QAAQ,GAAG,cAAc,EAAE,CAAC;YACjC,QAAQ,CAAC,gBAA8B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC/D,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC;YAC5B,WAAW,CAAC,gBAAgB,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;YAEvD,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAAC,OAAO,CAC3D,2BAAkB,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,aAAa,CAAC,iBAAiB,CAAC,eAAe,CAAC,aAAI,CAAC,iBAAiB,CAAC,CAAC;YACxE,MAAM,QAAQ,GAAG,cAAc,EAAE,CAAC;YACjC,QAAQ,CAAC,gBAA8B,CAAC,eAAe,CACtD,aAAI,CAAC,gBAAgB,CACtB,CAAC;YACD,QAAQ,CAAC,sBAAoC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACtE,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC;YAC5B,WAAW,CAAC,gBAAgB,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;YAEvD,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAAC,OAAO,CAC3D,2BAAkB,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}