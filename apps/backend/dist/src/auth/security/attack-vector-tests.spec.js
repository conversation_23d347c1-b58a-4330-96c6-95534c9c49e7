"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const jwt_1 = require("@nestjs/jwt");
const prisma_service_1 = require("../../prisma/prisma.service");
const client_1 = require("@prisma/client");
const supertest_1 = __importDefault(require("supertest"));
describe("Attack Vector Security Tests", () => {
    let app;
    let jwtService;
    let prisma;
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({}).compile();
        app = moduleFixture.createNestApplication();
        await app.init();
        jwtService = moduleFixture.get(jwt_1.JwtService);
        prisma = moduleFixture.get(prisma_service_1.PrismaService);
    });
    afterAll(async () => {
        await app.close();
    });
    describe("JWT Manipulation Attacks", () => {
        it("should reject JWT with modified payload", async () => {
            const validToken = jwtService.sign({
                userId: "user-123",
                tenantId: "tenant-123",
                role: client_1.Role.WAREHOUSE_MEMBER,
            });
            const [header, payload, signature] = validToken.split(".");
            const decodedPayload = JSON.parse(Buffer.from(payload, "base64").toString());
            decodedPayload.role = client_1.Role.TENANT_ADMIN;
            const modifiedPayload = Buffer.from(JSON.stringify(decodedPayload)).toString("base64");
            const modifiedToken = `${header}.${modifiedPayload}.${signature}`;
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/warehouses")
                .set("Authorization", `Bearer ${modifiedToken}`)
                .expect(401);
        });
        it("should reject JWT with modified tenant ID", async () => {
            const validToken = jwtService.sign({
                userId: "user-123",
                tenantId: "tenant-123",
                role: client_1.Role.WAREHOUSE_MEMBER,
            });
            const [header, payload, signature] = validToken.split(".");
            const decodedPayload = JSON.parse(Buffer.from(payload, "base64").toString());
            decodedPayload.tenantId = "different-tenant";
            const modifiedPayload = Buffer.from(JSON.stringify(decodedPayload)).toString("base64");
            const modifiedToken = `${header}.${modifiedPayload}.${signature}`;
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/warehouses")
                .set("Authorization", `Bearer ${modifiedToken}`)
                .expect(401);
        });
        it("should reject JWT with none algorithm", async () => {
            const noneHeader = Buffer.from(JSON.stringify({ alg: "none", typ: "JWT" })).toString("base64");
            const payload = Buffer.from(JSON.stringify({
                userId: "user-123",
                tenantId: "tenant-123",
                role: client_1.Role.TENANT_ADMIN,
            })).toString("base64");
            const noneToken = `${noneHeader}.${payload}.`;
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/warehouses")
                .set("Authorization", `Bearer ${noneToken}`)
                .expect(401);
        });
    });
    describe("Parameter Pollution Attacks", () => {
        it("should handle duplicate warehouse ID parameters safely", async () => {
            const token = generateValidToken();
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/pallets")
                .set("Authorization", `Bearer ${token}`)
                .query("warehouseId=warehouse-1&warehouseId=warehouse-2")
                .expect(400);
        });
        it("should sanitize array parameters", async () => {
            const token = generateValidToken();
            await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/pallets/bulk-update")
                .set("Authorization", `Bearer ${token}`)
                .send({
                palletIds: ["valid-id", null, undefined, "", "another-valid-id"],
                updates: { status: "SHIPPED" },
            })
                .expect(400);
        });
        it("should prevent SQL injection in query parameters", async () => {
            const token = generateValidToken();
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/pallets")
                .set("Authorization", `Bearer ${token}`)
                .query({ search: "'; DROP TABLE pallets; --" })
                .expect(400);
        });
    });
    describe("Authorization Bypass Attempts", () => {
        it("should prevent header injection attacks", async () => {
            const token = generateValidToken();
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/warehouses/warehouse-123/pallets")
                .set("Authorization", `Bearer ${token}`)
                .set("X-Warehouse-Id", "different-warehouse")
                .expect(403);
        });
        it("should prevent method override attacks", async () => {
            const token = generateValidToken();
            await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/warehouses/warehouse-123/pallets")
                .set("Authorization", `Bearer ${token}`)
                .set("X-HTTP-Method-Override", "DELETE")
                .expect(405);
        });
        it("should prevent path traversal in warehouse ID", async () => {
            const token = generateValidToken();
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/warehouses/../admin/users")
                .set("Authorization", `Bearer ${token}`)
                .expect(404);
        });
    });
    describe("IDOR (Insecure Direct Object Reference) Prevention", () => {
        it("should prevent access to other tenant resources by ID guessing", async () => {
            const token = generateValidToken();
            const suspiciousIds = [
                "warehouse-000001",
                "warehouse-000002",
                "pallet-123456",
                "user-admin-001",
            ];
            for (const id of suspiciousIds) {
                await (0, supertest_1.default)(app.getHttpServer())
                    .get(`/api/warehouses/${id}`)
                    .set("Authorization", `Bearer ${token}`)
                    .expect(403);
            }
        });
        it("should prevent UUID enumeration attacks", async () => {
            const token = generateValidToken();
            const predictableUuids = [
                "00000000-0000-0000-0000-000000000001",
                "11111111-1111-1111-1111-111111111111",
                "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa",
            ];
            for (const uuid of predictableUuids) {
                await (0, supertest_1.default)(app.getHttpServer())
                    .get(`/api/pallets/${uuid}`)
                    .set("Authorization", `Bearer ${token}`)
                    .expect(404);
            }
        });
        it("should prevent bulk operation IDOR attacks", async () => {
            const token = generateValidToken();
            await (0, supertest_1.default)(app.getHttpServer())
                .delete("/api/pallets/bulk")
                .set("Authorization", `Bearer ${token}`)
                .send({
                palletIds: [
                    "user-owned-pallet-1",
                    "other-tenant-pallet-1",
                    "admin-pallet-1",
                ],
            })
                .expect(403);
        });
    });
    describe("Session and Token Security", () => {
        it("should prevent token reuse after logout", async () => {
            const token = generateValidToken();
            await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/auth/logout")
                .set("Authorization", `Bearer ${token}`)
                .expect(200);
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/warehouses")
                .set("Authorization", `Bearer ${token}`)
                .expect(401);
        });
        it("should prevent concurrent session attacks", async () => {
            const userCredentials = {
                email: "<EMAIL>",
                password: "password123",
            };
            const response1 = await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/auth/login")
                .send(userCredentials)
                .expect(200);
            const token1 = response1.body.token;
            const response2 = await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/auth/login")
                .send(userCredentials)
                .expect(200);
            const token2 = response2.body.token;
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/warehouses")
                .set("Authorization", `Bearer ${token1}`)
                .expect(200);
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/warehouses")
                .set("Authorization", `Bearer ${token2}`)
                .expect(200);
        });
        it("should prevent token fixation attacks", async () => {
            const fixedToken = "fixed-token-value";
            await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/auth/login")
                .set("Authorization", `Bearer ${fixedToken}`)
                .send({
                email: "<EMAIL>",
                password: "password123",
            })
                .expect(400);
        });
    });
    describe("Rate Limiting and Brute Force Protection", () => {
        it("should rate limit login attempts", async () => {
            const invalidCredentials = {
                email: "<EMAIL>",
                password: "wrongpassword",
            };
            const attempts = Array(10).fill(null);
            const responses = await Promise.all(attempts.map(() => (0, supertest_1.default)(app.getHttpServer())
                .post("/api/auth/login")
                .send(invalidCredentials)));
            const rateLimitedResponses = responses.slice(-3);
            rateLimitedResponses.forEach((response) => {
                expect([401, 429]).toContain(response.status);
            });
        });
        it("should rate limit API requests per user", async () => {
            const token = generateValidToken();
            const requests = Array(50).fill(null);
            const responses = await Promise.all(requests.map(() => (0, supertest_1.default)(app.getHttpServer())
                .get("/api/warehouses")
                .set("Authorization", `Bearer ${token}`)));
            const rateLimitedCount = responses.filter((r) => r.status === 429).length;
            expect(rateLimitedCount).toBeGreaterThan(0);
        });
    });
    function generateValidToken() {
        return jwtService.sign({
            userId: "test-user-123",
            tenantId: "test-tenant-123",
            email: "<EMAIL>",
            role: client_1.Role.WAREHOUSE_MEMBER,
        });
    }
});
//# sourceMappingURL=attack-vector-tests.spec.js.map