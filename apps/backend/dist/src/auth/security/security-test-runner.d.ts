import { TestingModule } from '@nestjs/testing';
export declare class SecurityTestRunner {
    private readonly moduleFixture;
    private app;
    private testUtils;
    private securityAuditService;
    private testResults;
    constructor(moduleFixture: TestingModule);
    runAllTests(): Promise<SecurityTestReport>;
    private setupTestEnvironment;
    private runAuthenticationTests;
    private runAuthorizationTests;
    private runDataIsolationTests;
    private runInjectionTests;
    private runAuditLoggingTests;
    private runPerformanceSecurityTests;
    private testJWTManipulation;
    private testTokenExpiration;
    private testBruteForceProtection;
    private testRoleEscalationPrevention;
    private testWarehouseAccessControl;
    private testCrossTenantIsolation;
    private testIDORPrevention;
    private testDataLeakagePrevention;
    private testBulkOperationSecurity;
    private testSQLInjectionPrevention;
    private testNoSQLInjectionPrevention;
    private testXSSPrevention;
    private testSecurityEventLogging;
    private testAuditLogIntegrity;
    private testAuditLogAccessControl;
    private testRateLimiting;
    private testResourceExhaustionPrevention;
    private testDoSProtection;
    private addTestResult;
    private generateReport;
    private groupResultsBySuite;
    private calculateSecurityScore;
    private generateRecommendations;
    private cleanupTestEnvironment;
}
interface SecurityTestResult {
    suite: string;
    test: string;
    passed: boolean;
    error?: string;
    timestamp: Date;
}
interface SecurityTestReport {
    summary: {
        totalTests: number;
        passedTests: number;
        failedTests: number;
        passRate: number;
        securityScore: number;
    };
    suiteResults: Record<string, SecurityTestResult[]>;
    failedTests: SecurityTestResult[];
    recommendations: string[];
    timestamp: Date;
}
export {};
