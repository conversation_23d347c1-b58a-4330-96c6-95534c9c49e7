{"version": 3, "file": "security-testing.spec.js", "sourceRoot": "", "sources": ["../../../../src/auth/security/security-testing.spec.ts"], "names": [], "mappings": ";;;;;AAAA,6CAAsD;AAEtD,qCAAyC;AACzC,gEAA4D;AAC5D,kDAA8C;AAG9C,4FAAuF;AACvF,2CAAsE;AAEtE,0DAAgC;AAYhC,QAAQ,CAAC,mDAAmD,EAAE,GAAG,EAAE;IACjE,IAAI,GAAqB,CAAC;IAC1B,IAAI,MAAqB,CAAC;IAC1B,IAAI,UAAsB,CAAC;IAC3B,IAAI,WAAwB,CAAC;IAC7B,IAAI,oBAA0C,CAAC;IAG/C,IAAI,WAAiB,CAAC;IACtB,IAAI,gBAAsB,CAAC;IAC3B,IAAI,eAAqB,CAAC;IAC1B,IAAI,gBAAsB,CAAC;IAC3B,IAAI,UAAqB,CAAC;IAC1B,IAAI,UAAqB,CAAC;IAC1B,IAAI,oBAA+B,CAAC;IAEpC,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC,EAGnE,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC5C,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;QAEjB,MAAM,GAAG,aAAa,CAAC,GAAG,CAAgB,8BAAa,CAAC,CAAC;QACzD,UAAU,GAAG,aAAa,CAAC,GAAG,CAAa,gBAAU,CAAC,CAAC;QACvD,WAAW,GAAG,aAAa,CAAC,GAAG,CAAc,0BAAW,CAAC,CAAC;QAC1D,oBAAoB;YAClB,aAAa,CAAC,GAAG,CAAuB,6CAAoB,CAAC,CAAC;QAGhE,MAAM,aAAa,EAAE,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,eAAe,EAAE,CAAC;QACxB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,EAAE,CAAC,2DAA2D,EAAE,KAAK,IAAI,EAAE;YACzE,MAAM,KAAK,GAAG,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;YAErD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,mBAAmB,UAAU,CAAC,EAAE,UAAU,CAAC;iBAC/C,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QAI3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,KAAK,GAAG,oBAAoB,CAAC,eAAe,CAAC,CAAC;YACpD,MAAM,sBAAsB,GAAG,2BAA2B,CAAC;YAE3D,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,mBAAmB,sBAAsB,UAAU,CAAC;iBACxD,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,mBAAmB,UAAU,CAAC,EAAE,UAAU,CAAC;iBAC/C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,mBAAmB,UAAU,CAAC,EAAE,UAAU,CAAC;iBAC/C,GAAG,CAAC,eAAe,EAAE,sBAAsB,CAAC;iBAC5C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,YAAY,GAAG,UAAU,CAAC,IAAI,CAClC,EAAE,MAAM,EAAE,eAAe,CAAC,EAAE,EAAE,QAAQ,EAAE,eAAe,CAAC,QAAQ,EAAE,EAClE,EAAE,SAAS,EAAE,KAAK,EAAE,CACrB,CAAC;YAEF,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,mBAAmB,UAAU,CAAC,EAAE,UAAU,CAAC;iBAC/C,GAAG,CAAC,eAAe,EAAE,UAAU,YAAY,EAAE,CAAC;iBAC9C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,oEAAoE,EAAE,KAAK,IAAI,EAAE;YAClF,MAAM,KAAK,GAAG,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAGpD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,MAAM,CAAC,mBAAmB,UAAU,CAAC,EAAE,EAAE,CAAC;iBAC1C,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,KAAK,IAAI,EAAE;YACzE,MAAM,KAAK,GAAG,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAEpD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CACF,mBAAmB,UAAU,CAAC,EAAE,UAAU,eAAe,CAAC,EAAE,OAAO,CACpE;iBACA,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,IAAI,CAAC,EAAE,IAAI,EAAE,aAAI,CAAC,iBAAiB,EAAE,CAAC;iBACtC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yEAAyE,EAAE,KAAK,IAAI,EAAE;YACvF,MAAM,KAAK,GAAG,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;YAGrD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,iBAAiB,CAAC;iBACvB,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,IAAI,CAAC;gBACJ,IAAI,EAAE,wBAAwB;gBAC9B,OAAO,EAAE,aAAa;aACvB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,KAAK,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC;YAGhD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,mBAAmB,UAAU,CAAC,EAAE,UAAU,CAAC;iBAC/C,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yCAAyC,EAAE,GAAG,EAAE;QACvD,EAAE,CAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;YAC5E,MAAM,KAAK,GAAG,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAEpD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,mBAAmB,UAAU,CAAC,EAAE,UAAU,CAAC;iBAC/C,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,KAAK,IAAI,EAAE;YAC7E,MAAM,KAAK,GAAG,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAGpD,MAAM,cAAc,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACtD,IAAI,CAAC,mBAAmB,UAAU,CAAC,EAAE,UAAU,CAAC;iBAChD,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,IAAI,CAAC;gBACJ,OAAO,EAAE,iBAAiB;gBAC1B,WAAW,EAAE,aAAa;aAC3B,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,QAAQ,GAAG,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;YAGxC,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,gBAAgB,QAAQ,OAAO,CAAC;iBACpC,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,IAAI,CAAC;gBACJ,WAAW,EAAE,UAAU,CAAC,EAAE;gBAC1B,UAAU,EAAE,6BAA6B;aAC1C,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,KAAK,GAAG,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAEpD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,mBAAmB,oBAAoB,CAAC,EAAE,UAAU,CAAC;iBACzD,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,KAAK,GAAG,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAEpD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,cAAc,CAAC;iBACnB,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;YAGf,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC9B,OAAO,CAAC,OAAO,CAAC,CAAC,MAAW,EAAE,EAAE;gBAC9B,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,EAAE,CAAC,mEAAmE,EAAE,KAAK,IAAI,EAAE;YACjF,MAAM,KAAK,GAAG,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAGpD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,0BAA0B,CAAC;iBAChC,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,IAAI,CAAC;gBACJ,SAAS,EAAE,CAAC,sBAAsB,EAAE,sBAAsB,CAAC;gBAC3D,OAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE;aAC/B,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;YAC3E,MAAM,KAAK,GAAG,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAGpD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,mBAAmB,UAAU,CAAC,EAAE,6BAA6B,CAAC;iBAClE,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,KAAK,IAAI,EAAE;YAClF,MAAM,KAAK,GAAG,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAGpD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,mBAAmB,UAAU,CAAC,EAAE,UAAU,CAAC;iBAC/C,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,KAAK,CAAC,EAAE,QAAQ,EAAE,qBAAqB,EAAE,CAAC;iBAC1C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qCAAqC,EAAE,GAAG,EAAE;QACnD,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,wBAAwB,CAAC,CAAC;YAE1E,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,iBAAiB,CAAC;iBACvB,IAAI,CAAC;gBACJ,KAAK,EAAE,yBAAyB;gBAChC,QAAQ,EAAE,eAAe;aAC1B,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,MAAM,CAAC,CAAC,oBAAoB,CACjC,eAAe,EACf,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,CAAC;aACvD,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,wBAAwB,CAAC,CAAC;YAC1E,MAAM,KAAK,GAAG,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAEpD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,MAAM,CAAC,mBAAmB,UAAU,CAAC,EAAE,EAAE,CAAC;iBAC1C,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,MAAM,CAAC,CAAC,oBAAoB,CACjC,mBAAmB,EACnB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,MAAM,CAAC,gBAAgB,CAAC;gBACtB,YAAY,EAAE,aAAI,CAAC,iBAAiB;gBACpC,QAAQ,EAAE,aAAI,CAAC,gBAAgB;aAChC,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CACvB,oBAAoB,EACpB,0BAA0B,CAC3B,CAAC;YACF,MAAM,KAAK,GAAG,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAEpD,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,mBAAmB,UAAU,CAAC,EAAE,UAAU,CAAC;iBAC/C,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,CAAC,MAAM,CAAC,CAAC,oBAAoB,CACjC,UAAU,CAAC,EAAE,EACb,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,MAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC,EACxC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAGH,KAAK,UAAU,aAAa;IAI5B,CAAC;IAED,KAAK,UAAU,eAAe;IAG9B,CAAC;IAED,SAAS,oBAAoB,CAAC,IAAU;QACtC,OAAO,UAAU,CAAC,IAAI,CAAC;YACrB,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC"}