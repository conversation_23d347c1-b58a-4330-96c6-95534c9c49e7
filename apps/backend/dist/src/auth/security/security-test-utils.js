"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityTestUtils = void 0;
const client_1 = require("@prisma/client");
class SecurityTestUtils {
    constructor(prisma, jwtService) {
        this.prisma = prisma;
        this.jwtService = jwtService;
    }
    async createTestUsers() {
        const testTenantId = "test-tenant-security";
        const altTenantId = "alt-tenant-security";
        const tenantAdmin = await this.prisma.user.create({
            data: {
                email: "<EMAIL>",
                name: "Security Test Admin",
                password: "hashed-password",
                authUserId: "auth-admin-123",
                tenantId: testTenantId,
                role: client_1.Role.TENANT_ADMIN,
                status: "ACTIVE",
            },
        });
        const warehouseManager = await this.prisma.user.create({
            data: {
                email: "<EMAIL>",
                name: "Security Test Manager",
                password: "hashed-password",
                authUserId: "auth-manager-123",
                tenantId: testTenantId,
                role: client_1.Role.WAREHOUSE_MANAGER,
                status: "ACTIVE",
            },
        });
        const warehouseMember = await this.prisma.user.create({
            data: {
                email: "<EMAIL>",
                name: "Security Test Member",
                password: "hashed-password",
                authUserId: "auth-member-123",
                tenantId: testTenantId,
                role: client_1.Role.WAREHOUSE_MEMBER,
                status: "ACTIVE",
            },
        });
        const unauthorizedUser = await this.prisma.user.create({
            data: {
                email: "<EMAIL>",
                name: "Unauthorized User",
                password: "hashed-password",
                authUserId: "auth-unauthorized-123",
                tenantId: altTenantId,
                role: client_1.Role.WAREHOUSE_MEMBER,
                status: "ACTIVE",
            },
        });
        return {
            tenantAdmin,
            warehouseManager,
            warehouseMember,
            unauthorizedUser,
            testTenantId,
            altTenantId,
        };
    }
    async createTestWarehouses(testTenantId, altTenantId) {
        const warehouse1 = await this.prisma.warehouse.create({
            data: {
                name: "Security Test Warehouse 1",
                address: "123 Security Test St",
                tenantId: testTenantId,
            },
        });
        const warehouse2 = await this.prisma.warehouse.create({
            data: {
                name: "Security Test Warehouse 2",
                address: "456 Security Test Ave",
                tenantId: testTenantId,
            },
        });
        const crossTenantWarehouse = await this.prisma.warehouse.create({
            data: {
                name: "Cross Tenant Warehouse",
                address: "789 Cross Tenant Blvd",
                tenantId: altTenantId,
            },
        });
        return {
            warehouse1,
            warehouse2,
            crossTenantWarehouse,
        };
    }
    async setupWarehouseAccess(users, warehouses) {
        await this.prisma.warehouseUser.create({
            data: {
                userId: users.warehouseManager.id,
                warehouseId: warehouses.warehouse1.id,
                role: client_1.Role.WAREHOUSE_MANAGER,
            },
        });
        await this.prisma.warehouseUser.create({
            data: {
                userId: users.warehouseMember.id,
                warehouseId: warehouses.warehouse1.id,
                role: client_1.Role.WAREHOUSE_MEMBER,
            },
        });
        await this.prisma.warehouseUser.create({
            data: {
                userId: users.warehouseManager.id,
                warehouseId: warehouses.warehouse2.id,
                role: client_1.Role.WAREHOUSE_MANAGER,
            },
        });
    }
    async createTestPallets(warehouses, users) {
        const location1 = await this.prisma.location.create({
            data: {
                name: "Security Test Location 1",
                category: "Storage",
                locationType: "RACK",
                warehouseId: warehouses.warehouse1.id,
            },
        });
        const location2 = await this.prisma.location.create({
            data: {
                name: "Security Test Location 2",
                category: "Storage",
                locationType: "RACK",
                warehouseId: warehouses.warehouse2.id,
            },
        });
        const crossTenantLocation = await this.prisma.location.create({
            data: {
                name: "Cross Tenant Location",
                category: "Storage",
                locationType: "RACK",
                warehouseId: warehouses.crossTenantWarehouse.id,
            },
        });
        const pallet1 = await this.prisma.pallet.create({
            data: {
                label: "SEC-TEST-001",
                barcode: "SEC-TEST-001",
                description: "Security Test Pallet 1",
                locationId: location1.id,
            },
        });
        const pallet2 = await this.prisma.pallet.create({
            data: {
                label: "SEC-TEST-002",
                barcode: "SEC-TEST-002",
                description: "Security Test Pallet 2",
                locationId: location2.id,
            },
        });
        const crossTenantPallet = await this.prisma.pallet.create({
            data: {
                label: "CROSS-TENANT-001",
                barcode: "CROSS-TENANT-001",
                description: "Cross Tenant Pallet",
                locationId: crossTenantLocation.id,
            },
        });
        return {
            pallet1,
            pallet2,
            crossTenantPallet,
            location1,
            location2,
            crossTenantLocation,
        };
    }
    generateTokenForUser(user, warehouseAccess) {
        const payload = {
            userId: user.id,
            tenantId: user.tenantId,
            email: user.email,
            role: user.role,
            warehouseAccess: warehouseAccess?.map((wa) => ({
                warehouseId: wa.warehouseId,
                role: wa.role,
            })) || [],
        };
        return this.jwtService.sign(payload);
    }
    generateMaliciousTokens() {
        return {
            escalatedRole: this.jwtService.sign({
                userId: "user-123",
                tenantId: "tenant-123",
                role: client_1.Role.TENANT_ADMIN,
            }),
            crossTenant: this.jwtService.sign({
                userId: "user-123",
                tenantId: "different-tenant",
                role: client_1.Role.WAREHOUSE_MEMBER,
            }),
            expired: this.jwtService.sign({
                userId: "user-123",
                tenantId: "tenant-123",
                role: client_1.Role.WAREHOUSE_MEMBER,
            }, { expiresIn: "-1h" }),
            malformed: "invalid.jwt.token",
            empty: "",
        };
    }
    generateInjectionPayloads() {
        return {
            sql: [
                "'; DROP TABLE users; --",
                "' OR '1'='1",
                "'; UPDATE users SET role='TENANT_ADMIN' WHERE id='user-123'; --",
                "' UNION SELECT * FROM users WHERE role='TENANT_ADMIN' --",
            ],
            nosql: [
                "{ $ne: null }",
                "{ $where: 'this.role == \"TENANT_ADMIN\"' }",
                "'; return true; //",
            ],
            xss: [
                "<script>alert('xss')</script>",
                "javascript:alert('xss')",
                "<img src=x onerror=alert('xss')>",
            ],
            pathTraversal: [
                "../../../etc/passwd",
                "..\\..\\..\\windows\\system32\\config\\sam",
                "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            ],
        };
    }
    async cleanupTestData() {
        await this.prisma.pallet.deleteMany({
            where: {
                barcode: {
                    startsWith: "SEC-TEST-",
                },
            },
        });
        await this.prisma.pallet.deleteMany({
            where: {
                barcode: {
                    startsWith: "CROSS-TENANT-",
                },
            },
        });
        await this.prisma.warehouseUser.deleteMany({
            where: {
                user: {
                    email: {
                        endsWith: "@security-test.com",
                    },
                },
            },
        });
        await this.prisma.warehouse.deleteMany({
            where: {
                name: {
                    startsWith: "Security Test",
                },
            },
        });
        await this.prisma.warehouse.deleteMany({
            where: {
                name: "Cross Tenant Warehouse",
            },
        });
        await this.prisma.user.deleteMany({
            where: {
                email: {
                    endsWith: "@security-test.com",
                },
            },
        });
        await this.prisma.user.deleteMany({
            where: {
                email: "<EMAIL>",
            },
        });
    }
    async verifySecurityAuditLog(action, userId, warehouseId) {
        const auditLog = await this.prisma.auditLog.findFirst({
            where: {
                action,
                userId,
                details: warehouseId
                    ? {
                        path: ["warehouseId"],
                        equals: warehouseId,
                    }
                    : undefined,
            },
            orderBy: {
                timestamp: "desc",
            },
        });
        return !!auditLog;
    }
    createEnhancedUserPayload(user, warehouseAccess = []) {
        const accessibleWarehouses = warehouseAccess.map((wa) => wa.warehouseId);
        const warehouseRoles = new Map(warehouseAccess.map((wa) => [wa.warehouseId, wa.role]));
        return {
            ...user,
            accessibleWarehouses,
            warehouseRoles,
            warehouseUsers: warehouseAccess,
            isTenantAdmin: () => user.role === client_1.Role.TENANT_ADMIN,
            canAccessWarehouse: (warehouseId) => user.role === client_1.Role.TENANT_ADMIN ||
                accessibleWarehouses.includes(warehouseId),
            getWarehouseRole: (warehouseId) => warehouseRoles.get(warehouseId) || null,
            hasWarehousePermission: (warehouseId, requiredRole) => {
                if (user.role === client_1.Role.TENANT_ADMIN)
                    return true;
                const userRole = warehouseRoles.get(warehouseId);
                if (!userRole)
                    return false;
                const roleHierarchy = [
                    client_1.Role.WAREHOUSE_MEMBER,
                    client_1.Role.WAREHOUSE_MANAGER,
                    client_1.Role.TENANT_ADMIN,
                ];
                const userRoleIndex = roleHierarchy.indexOf(userRole);
                const requiredRoleIndex = roleHierarchy.indexOf(requiredRole);
                return userRoleIndex >= requiredRoleIndex;
            },
        };
    }
}
exports.SecurityTestUtils = SecurityTestUtils;
//# sourceMappingURL=security-test-utils.js.map