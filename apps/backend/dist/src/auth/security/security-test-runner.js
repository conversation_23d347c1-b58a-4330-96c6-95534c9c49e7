"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityTestRunner = void 0;
const jwt_1 = require("@nestjs/jwt");
const prisma_service_1 = require("../../prisma/prisma.service");
const security_test_utils_1 = require("./security-test-utils");
const security_audit_service_1 = require("../../audit-log/services/security-audit.service");
class SecurityTestRunner {
    constructor(moduleFixture) {
        this.moduleFixture = moduleFixture;
        this.testResults = [];
        this.app = moduleFixture.createNestApplication();
        const prisma = moduleFixture.get(prisma_service_1.PrismaService);
        const jwtService = moduleFixture.get(jwt_1.JwtService);
        this.testUtils = new security_test_utils_1.SecurityTestUtils(prisma, jwtService);
        this.securityAuditService = moduleFixture.get(security_audit_service_1.SecurityAuditService);
    }
    async runAllTests() {
        await this.app.init();
        try {
            await this.setupTestEnvironment();
            await this.runAuthenticationTests();
            await this.runAuthorizationTests();
            await this.runDataIsolationTests();
            await this.runInjectionTests();
            await this.runAuditLoggingTests();
            await this.runPerformanceSecurityTests();
            return this.generateReport();
        }
        finally {
            await this.cleanupTestEnvironment();
            await this.app.close();
        }
    }
    async setupTestEnvironment() {
        const users = await this.testUtils.createTestUsers();
        const warehouses = await this.testUtils.createTestWarehouses(users.testTenantId, users.altTenantId);
        await this.testUtils.setupWarehouseAccess({ warehouseManager: users.warehouseManager, warehouseMember: users.warehouseMember }, { warehouse1: warehouses.warehouse1, warehouse2: warehouses.warehouse2 });
        await this.testUtils.createTestPallets(warehouses, users);
    }
    async runAuthenticationTests() {
        const testSuite = 'Authentication Security';
        try {
            await this.testJWTManipulation();
            this.addTestResult(testSuite, 'JWT Manipulation Prevention', true);
            await this.testTokenExpiration();
            this.addTestResult(testSuite, 'Token Expiration Enforcement', true);
            await this.testBruteForceProtection();
            this.addTestResult(testSuite, 'Brute Force Protection', true);
        }
        catch (error) {
            this.addTestResult(testSuite, 'Authentication Tests', false, error.message);
        }
    }
    async runAuthorizationTests() {
        const testSuite = 'Authorization Security';
        try {
            await this.testRoleEscalationPrevention();
            this.addTestResult(testSuite, 'Role Escalation Prevention', true);
            await this.testWarehouseAccessControl();
            this.addTestResult(testSuite, 'Warehouse Access Control', true);
            await this.testCrossTenantIsolation();
            this.addTestResult(testSuite, 'Cross-Tenant Isolation', true);
        }
        catch (error) {
            this.addTestResult(testSuite, 'Authorization Tests', false, error.message);
        }
    }
    async runDataIsolationTests() {
        const testSuite = 'Data Isolation Security';
        try {
            await this.testIDORPrevention();
            this.addTestResult(testSuite, 'IDOR Prevention', true);
            await this.testDataLeakagePrevention();
            this.addTestResult(testSuite, 'Data Leakage Prevention', true);
            await this.testBulkOperationSecurity();
            this.addTestResult(testSuite, 'Bulk Operation Security', true);
        }
        catch (error) {
            this.addTestResult(testSuite, 'Data Isolation Tests', false, error.message);
        }
    }
    async runInjectionTests() {
        const testSuite = 'Injection Attack Prevention';
        try {
            await this.testSQLInjectionPrevention();
            this.addTestResult(testSuite, 'SQL Injection Prevention', true);
            await this.testNoSQLInjectionPrevention();
            this.addTestResult(testSuite, 'NoSQL Injection Prevention', true);
            await this.testXSSPrevention();
            this.addTestResult(testSuite, 'XSS Prevention', true);
        }
        catch (error) {
            this.addTestResult(testSuite, 'Injection Tests', false, error.message);
        }
    }
    async runAuditLoggingTests() {
        const testSuite = 'Security Audit Logging';
        try {
            await this.testSecurityEventLogging();
            this.addTestResult(testSuite, 'Security Event Logging', true);
            await this.testAuditLogIntegrity();
            this.addTestResult(testSuite, 'Audit Log Integrity', true);
            await this.testAuditLogAccessControl();
            this.addTestResult(testSuite, 'Audit Log Access Control', true);
        }
        catch (error) {
            this.addTestResult(testSuite, 'Audit Logging Tests', false, error.message);
        }
    }
    async runPerformanceSecurityTests() {
        const testSuite = 'Performance Security';
        try {
            await this.testRateLimiting();
            this.addTestResult(testSuite, 'Rate Limiting', true);
            await this.testResourceExhaustionPrevention();
            this.addTestResult(testSuite, 'Resource Exhaustion Prevention', true);
            await this.testDoSProtection();
            this.addTestResult(testSuite, 'DoS Protection', true);
        }
        catch (error) {
            this.addTestResult(testSuite, 'Performance Security Tests', false, error.message);
        }
    }
    async testJWTManipulation() {
    }
    async testTokenExpiration() {
    }
    async testBruteForceProtection() {
    }
    async testRoleEscalationPrevention() {
    }
    async testWarehouseAccessControl() {
    }
    async testCrossTenantIsolation() {
    }
    async testIDORPrevention() {
    }
    async testDataLeakagePrevention() {
    }
    async testBulkOperationSecurity() {
    }
    async testSQLInjectionPrevention() {
    }
    async testNoSQLInjectionPrevention() {
    }
    async testXSSPrevention() {
    }
    async testSecurityEventLogging() {
    }
    async testAuditLogIntegrity() {
    }
    async testAuditLogAccessControl() {
    }
    async testRateLimiting() {
    }
    async testResourceExhaustionPrevention() {
    }
    async testDoSProtection() {
    }
    addTestResult(suite, test, passed, error) {
        this.testResults.push({
            suite,
            test,
            passed,
            error,
            timestamp: new Date(),
        });
    }
    generateReport() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;
        const passRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;
        const suiteResults = this.groupResultsBySuite();
        return {
            summary: {
                totalTests,
                passedTests,
                failedTests,
                passRate,
                securityScore: this.calculateSecurityScore(),
            },
            suiteResults,
            failedTests: this.testResults.filter(r => !r.passed),
            recommendations: this.generateRecommendations(),
            timestamp: new Date(),
        };
    }
    groupResultsBySuite() {
        return this.testResults.reduce((acc, result) => {
            if (!acc[result.suite]) {
                acc[result.suite] = [];
            }
            acc[result.suite].push(result);
            return acc;
        }, {});
    }
    calculateSecurityScore() {
        if (this.testResults.length === 0)
            return 0;
        const criticalTests = this.testResults.filter(r => r.test.includes('JWT') ||
            r.test.includes('Authorization') ||
            r.test.includes('Cross-Tenant'));
        const criticalPassed = criticalTests.filter(r => r.passed).length;
        const criticalWeight = 0.7;
        const generalWeight = 0.3;
        const criticalScore = criticalTests.length > 0 ?
            (criticalPassed / criticalTests.length) * 100 : 100;
        const generalTests = this.testResults.filter(r => !criticalTests.includes(r));
        const generalPassed = generalTests.filter(r => r.passed).length;
        const generalScore = generalTests.length > 0 ?
            (generalPassed / generalTests.length) * 100 : 100;
        return (criticalScore * criticalWeight) + (generalScore * generalWeight);
    }
    generateRecommendations() {
        const recommendations = [];
        const failedTests = this.testResults.filter(r => !r.passed);
        if (failedTests.some(t => t.test.includes('JWT'))) {
            recommendations.push('Review JWT implementation and signature validation');
        }
        if (failedTests.some(t => t.test.includes('Authorization'))) {
            recommendations.push('Strengthen authorization controls and role validation');
        }
        if (failedTests.some(t => t.test.includes('Injection'))) {
            recommendations.push('Implement input validation and parameterized queries');
        }
        if (failedTests.some(t => t.test.includes('Audit'))) {
            recommendations.push('Enhance security audit logging and monitoring');
        }
        if (recommendations.length === 0) {
            recommendations.push('Security posture is strong - continue regular testing');
        }
        return recommendations;
    }
    async cleanupTestEnvironment() {
        await this.testUtils.cleanupTestData();
    }
}
exports.SecurityTestRunner = SecurityTestRunner;
//# sourceMappingURL=security-test-runner.js.map