{"version": 3, "file": "security-test-runner.js", "sourceRoot": "", "sources": ["../../../../src/auth/security/security-test-runner.ts"], "names": [], "mappings": ";;;AAEA,qCAAyC;AACzC,gEAA4D;AAC5D,+DAA0D;AAC1D,4FAAuF;AASvF,MAAa,kBAAkB;IAM7B,YACmB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAHvC,gBAAW,GAAyB,EAAE,CAAC;QAK7C,IAAI,CAAC,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QACjD,MAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAgB,8BAAa,CAAC,CAAC;QAC/D,MAAM,UAAU,GAAG,aAAa,CAAC,GAAG,CAAa,gBAAU,CAAC,CAAC;QAC7D,IAAI,CAAC,SAAS,GAAG,IAAI,uCAAiB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAC3D,IAAI,CAAC,oBAAoB,GAAG,aAAa,CAAC,GAAG,CAAuB,6CAAoB,CAAC,CAAC;IAC5F,CAAC;IAKD,KAAK,CAAC,WAAW;QACf,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAEtB,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAGlC,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACpC,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC/B,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAClC,MAAM,IAAI,CAAC,2BAA2B,EAAE,CAAC;YAGzC,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;QAC/B,CAAC;gBAAS,CAAC;YACT,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACpC,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,oBAAoB;QAChC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;QACrD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAC1D,KAAK,CAAC,YAAY,EAClB,KAAK,CAAC,WAAW,CAClB,CAAC;QAEF,MAAM,IAAI,CAAC,SAAS,CAAC,oBAAoB,CACvC,EAAE,gBAAgB,EAAE,KAAK,CAAC,gBAAgB,EAAE,eAAe,EAAE,KAAK,CAAC,eAAe,EAAE,EACpF,EAAE,UAAU,EAAE,UAAU,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,UAAU,EAAE,CACzE,CAAC;QAEF,MAAM,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;IAKO,KAAK,CAAC,sBAAsB;QAClC,MAAM,SAAS,GAAG,yBAAyB,CAAC;QAE5C,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACjC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,6BAA6B,EAAE,IAAI,CAAC,CAAC;YAGnE,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACjC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,8BAA8B,EAAE,IAAI,CAAC,CAAC;YAGpE,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,wBAAwB,EAAE,IAAI,CAAC,CAAC;QAEhE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,sBAAsB,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,qBAAqB;QACjC,MAAM,SAAS,GAAG,wBAAwB,CAAC;QAE3C,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,4BAA4B,EAAE,CAAC;YAC1C,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,4BAA4B,EAAE,IAAI,CAAC,CAAC;YAGlE,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;YACxC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,0BAA0B,EAAE,IAAI,CAAC,CAAC;YAGhE,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,wBAAwB,EAAE,IAAI,CAAC,CAAC;QAEhE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,qBAAqB,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,qBAAqB;QACjC,MAAM,SAAS,GAAG,yBAAyB,CAAC;QAE5C,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAChC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC;YAGvD,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACvC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,yBAAyB,EAAE,IAAI,CAAC,CAAC;YAG/D,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACvC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,yBAAyB,EAAE,IAAI,CAAC,CAAC;QAEjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,sBAAsB,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,iBAAiB;QAC7B,MAAM,SAAS,GAAG,6BAA6B,CAAC;QAEhD,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;YACxC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,0BAA0B,EAAE,IAAI,CAAC,CAAC;YAGhE,MAAM,IAAI,CAAC,4BAA4B,EAAE,CAAC;YAC1C,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,4BAA4B,EAAE,IAAI,CAAC,CAAC;YAGlE,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC/B,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;QAExD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,iBAAiB,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,oBAAoB;QAChC,MAAM,SAAS,GAAG,wBAAwB,CAAC;QAE3C,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,wBAAwB,EAAE,IAAI,CAAC,CAAC;YAG9D,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACnC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;YAG3D,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACvC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,0BAA0B,EAAE,IAAI,CAAC,CAAC;QAElE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,qBAAqB,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,2BAA2B;QACvC,MAAM,SAAS,GAAG,sBAAsB,CAAC;QAEzC,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC9B,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;YAGrD,MAAM,IAAI,CAAC,gCAAgC,EAAE,CAAC;YAC9C,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,gCAAgC,EAAE,IAAI,CAAC,CAAC;YAGtE,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC/B,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;QAExD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,4BAA4B,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QACpF,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,mBAAmB;IAGjC,CAAC;IAEO,KAAK,CAAC,mBAAmB;IAEjC,CAAC;IAEO,KAAK,CAAC,wBAAwB;IAEtC,CAAC;IAEO,KAAK,CAAC,4BAA4B;IAE1C,CAAC;IAEO,KAAK,CAAC,0BAA0B;IAExC,CAAC;IAEO,KAAK,CAAC,wBAAwB;IAEtC,CAAC;IAEO,KAAK,CAAC,kBAAkB;IAEhC,CAAC;IAEO,KAAK,CAAC,yBAAyB;IAEvC,CAAC;IAEO,KAAK,CAAC,yBAAyB;IAEvC,CAAC;IAEO,KAAK,CAAC,0BAA0B;IAExC,CAAC;IAEO,KAAK,CAAC,4BAA4B;IAE1C,CAAC;IAEO,KAAK,CAAC,iBAAiB;IAE/B,CAAC;IAEO,KAAK,CAAC,wBAAwB;IAEtC,CAAC;IAEO,KAAK,CAAC,qBAAqB;IAEnC,CAAC;IAEO,KAAK,CAAC,yBAAyB;IAEvC,CAAC;IAEO,KAAK,CAAC,gBAAgB;IAE9B,CAAC;IAEO,KAAK,CAAC,gCAAgC;IAE9C,CAAC;IAEO,KAAK,CAAC,iBAAiB;IAE/B,CAAC;IAKO,aAAa,CACnB,KAAa,EACb,IAAY,EACZ,MAAe,EACf,KAAc;QAEd,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YACpB,KAAK;YACL,IAAI;YACJ,MAAM;YACN,KAAK;YACL,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAKO,cAAc;QACpB,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;QAClE,MAAM,WAAW,GAAG,UAAU,GAAG,WAAW,CAAC;QAC7C,MAAM,QAAQ,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEvE,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEhD,OAAO;YACL,OAAO,EAAE;gBACP,UAAU;gBACV,WAAW;gBACX,WAAW;gBACX,QAAQ;gBACR,aAAa,EAAE,IAAI,CAAC,sBAAsB,EAAE;aAC7C;YACD,YAAY;YACZ,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YACpD,eAAe,EAAE,IAAI,CAAC,uBAAuB,EAAE;YAC/C,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAKO,mBAAmB;QACzB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YAC7C,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvB,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;YACzB,CAAC;YACD,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC/B,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA0C,CAAC,CAAC;IACjD,CAAC;IAKO,sBAAsB;QAC5B,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAE5C,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAChD,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YACtB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC;YAChC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAChC,CAAC;QAEF,MAAM,cAAc,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;QAClE,MAAM,cAAc,GAAG,GAAG,CAAC;QAC3B,MAAM,aAAa,GAAG,GAAG,CAAC;QAE1B,MAAM,aAAa,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC9C,CAAC,cAAc,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAEtD,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9E,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;QAChE,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC5C,CAAC,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAEpD,OAAO,CAAC,aAAa,GAAG,cAAc,CAAC,GAAG,CAAC,YAAY,GAAG,aAAa,CAAC,CAAC;IAC3E,CAAC;IAKO,uBAAuB;QAC7B,MAAM,eAAe,GAAa,EAAE,CAAC;QACrC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAE5D,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAClD,eAAe,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAC7E,CAAC;QAED,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC;YAC5D,eAAe,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;QAChF,CAAC;QAED,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC;YACxD,eAAe,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;YACpD,eAAe,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,eAAe,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;QAChF,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAKO,KAAK,CAAC,sBAAsB;QAClC,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;IACzC,CAAC;CACF;AA9YD,gDA8YC"}