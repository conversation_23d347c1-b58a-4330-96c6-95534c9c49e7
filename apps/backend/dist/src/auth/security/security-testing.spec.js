"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const jwt_1 = require("@nestjs/jwt");
const prisma_service_1 = require("../../prisma/prisma.service");
const auth_service_1 = require("../auth.service");
const security_audit_service_1 = require("../../audit-log/services/security-audit.service");
const client_1 = require("@prisma/client");
const supertest_1 = __importDefault(require("supertest"));
describe("Security Testing Suite - Warehouse Access Control", () => {
    let app;
    let prisma;
    let jwtService;
    let authService;
    let securityAuditService;
    let tenantAdmin;
    let warehouseManager;
    let warehouseMember;
    let unauthorizedUser;
    let warehouse1;
    let warehouse2;
    let crossTenantWarehouse;
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({}).compile();
        app = moduleFixture.createNestApplication();
        await app.init();
        prisma = moduleFixture.get(prisma_service_1.PrismaService);
        jwtService = moduleFixture.get(jwt_1.JwtService);
        authService = moduleFixture.get(auth_service_1.AuthService);
        securityAuditService =
            moduleFixture.get(security_audit_service_1.SecurityAuditService);
        await setupTestData();
    });
    afterAll(async () => {
        await cleanupTestData();
        await app.close();
    });
    describe("Unauthorized Access Prevention", () => {
        it("should deny access to users without warehouse permissions", async () => {
            const token = generateTokenForUser(unauthorizedUser);
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/warehouses/${warehouse1.id}/pallets`)
                .set("Authorization", `Bearer ${token}`)
                .expect(403);
            expect(response.body.message).toContain("Access denied");
        });
        it("should deny access to non-existent warehouse", async () => {
            const token = generateTokenForUser(warehouseMember);
            const nonExistentWarehouseId = "non-existent-warehouse-id";
            await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/warehouses/${nonExistentWarehouseId}/pallets`)
                .set("Authorization", `Bearer ${token}`)
                .expect(404);
        });
        it("should deny access without authentication token", async () => {
            await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/warehouses/${warehouse1.id}/pallets`)
                .expect(401);
        });
        it("should deny access with invalid token", async () => {
            await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/warehouses/${warehouse1.id}/pallets`)
                .set("Authorization", "Bearer invalid-token")
                .expect(401);
        });
        it("should deny access with expired token", async () => {
            const expiredToken = jwtService.sign({ userId: warehouseMember.id, tenantId: warehouseMember.tenantId }, { expiresIn: "-1h" });
            await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/warehouses/${warehouse1.id}/pallets`)
                .set("Authorization", `Bearer ${expiredToken}`)
                .expect(401);
        });
    });
    describe("Role Escalation Prevention", () => {
        it("should prevent warehouse member from performing manager operations", async () => {
            const token = generateTokenForUser(warehouseMember);
            await (0, supertest_1.default)(app.getHttpServer())
                .delete(`/api/warehouses/${warehouse1.id}`)
                .set("Authorization", `Bearer ${token}`)
                .expect(403);
        });
        it("should prevent warehouse member from modifying user roles", async () => {
            const token = generateTokenForUser(warehouseMember);
            await (0, supertest_1.default)(app.getHttpServer())
                .put(`/api/warehouses/${warehouse1.id}/users/${warehouseMember.id}/role`)
                .set("Authorization", `Bearer ${token}`)
                .send({ role: client_1.Role.WAREHOUSE_MANAGER })
                .expect(403);
        });
        it("should prevent warehouse manager from accessing tenant-admin operations", async () => {
            const token = generateTokenForUser(warehouseManager);
            await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/warehouses")
                .set("Authorization", `Bearer ${token}`)
                .send({
                name: "Unauthorized Warehouse",
                address: "123 Test St",
            })
                .expect(403);
        });
        it("should allow tenant admin to access all operations", async () => {
            const token = generateTokenForUser(tenantAdmin);
            await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/warehouses/${warehouse1.id}/pallets`)
                .set("Authorization", `Bearer ${token}`)
                .expect(200);
        });
    });
    describe("Cross-Warehouse Data Leakage Prevention", () => {
        it("should prevent access to pallets from unauthorized warehouse", async () => {
            const token = generateTokenForUser(warehouseMember);
            await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/warehouses/${warehouse2.id}/pallets`)
                .set("Authorization", `Bearer ${token}`)
                .expect(403);
        });
        it("should prevent moving pallets between unauthorized warehouses", async () => {
            const token = generateTokenForUser(warehouseMember);
            const palletResponse = await (0, supertest_1.default)(app.getHttpServer())
                .post(`/api/warehouses/${warehouse1.id}/pallets`)
                .set("Authorization", `Bearer ${token}`)
                .send({
                barcode: "TEST-PALLET-001",
                description: "Test Pallet",
            })
                .expect(201);
            const palletId = palletResponse.body.id;
            await (0, supertest_1.default)(app.getHttpServer())
                .put(`/api/pallets/${palletId}/move`)
                .set("Authorization", `Bearer ${token}`)
                .send({
                warehouseId: warehouse2.id,
                locationId: "some-location-in-warehouse2",
            })
                .expect(403);
        });
        it("should prevent cross-tenant data access", async () => {
            const token = generateTokenForUser(warehouseMember);
            await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/warehouses/${crossTenantWarehouse.id}/pallets`)
                .set("Authorization", `Bearer ${token}`)
                .expect(403);
        });
        it("should isolate warehouse data in list operations", async () => {
            const token = generateTokenForUser(warehouseMember);
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/pallets")
                .set("Authorization", `Bearer ${token}`)
                .expect(200);
            const pallets = response.body;
            pallets.forEach((pallet) => {
                expect(pallet.warehouseId).toBe(warehouse1.id);
            });
        });
    });
    describe("Permission Boundary Enforcement", () => {
        it("should enforce warehouse-specific permissions for bulk operations", async () => {
            const token = generateTokenForUser(warehouseMember);
            await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/pallets/bulk-update")
                .set("Authorization", `Bearer ${token}`)
                .send({
                palletIds: ["pallet-in-warehouse1", "pallet-in-warehouse2"],
                updates: { status: "SHIPPED" },
            })
                .expect(403);
        });
        it("should validate warehouse context in nested resource access", async () => {
            const token = generateTokenForUser(warehouseMember);
            await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/warehouses/${warehouse2.id}/locations/some-location-id`)
                .set("Authorization", `Bearer ${token}`)
                .expect(403);
        });
        it("should prevent privilege escalation through parameter manipulation", async () => {
            const token = generateTokenForUser(warehouseMember);
            await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/warehouses/${warehouse1.id}/pallets`)
                .set("Authorization", `Bearer ${token}`)
                .query({ tenantId: "different-tenant-id" })
                .expect(403);
        });
    });
    describe("Security Audit Logging Verification", () => {
        it("should log failed authentication attempts", async () => {
            const logSpy = jest.spyOn(securityAuditService, "logAuthenticationEvent");
            await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/auth/login")
                .send({
                email: "<EMAIL>",
                password: "wrongpassword",
            })
                .expect(401);
            expect(logSpy).toHaveBeenCalledWith("LOGIN_FAILURE", expect.any(Object), expect.any(Object), expect.objectContaining({
                reason: expect.stringContaining("Invalid credentials"),
            }));
        });
        it("should log permission violations", async () => {
            const logSpy = jest.spyOn(securityAuditService, "logPermissionViolation");
            const token = generateTokenForUser(warehouseMember);
            await (0, supertest_1.default)(app.getHttpServer())
                .delete(`/api/warehouses/${warehouse1.id}`)
                .set("Authorization", `Bearer ${token}`)
                .expect(403);
            expect(logSpy).toHaveBeenCalledWith("INSUFFICIENT_ROLE", expect.any(Object), expect.any(Object), expect.objectContaining({
                requiredRole: client_1.Role.WAREHOUSE_MANAGER,
                userRole: client_1.Role.WAREHOUSE_MEMBER,
            }));
        });
        it("should log cross-warehouse access attempts", async () => {
            const logSpy = jest.spyOn(securityAuditService, "logWarehouseAccessDenied");
            const token = generateTokenForUser(warehouseMember);
            await (0, supertest_1.default)(app.getHttpServer())
                .get(`/api/warehouses/${warehouse2.id}/pallets`)
                .set("Authorization", `Bearer ${token}`)
                .expect(403);
            expect(logSpy).toHaveBeenCalledWith(warehouse2.id, expect.any(Object), expect.any(Object), expect.stringContaining("Access denied"), expect.any(Object));
        });
    });
    async function setupTestData() {
    }
    async function cleanupTestData() {
    }
    function generateTokenForUser(user) {
        return jwtService.sign({
            userId: user.id,
            tenantId: user.tenantId,
            email: user.email,
            role: user.role,
        });
    }
});
//# sourceMappingURL=security-testing.spec.js.map