import { JwtService } from "@nestjs/jwt";
import { PrismaService } from "../../prisma/prisma.service";
import { User, Warehouse, WarehouseUser } from "@prisma/client";
import { EnhancedUserPayload } from "../types";
export declare class SecurityTestUtils {
    private readonly prisma;
    private readonly jwtService;
    constructor(prisma: PrismaService, jwtService: JwtService);
    createTestUsers(): Promise<{
        tenantAdmin: {
            name: string | null;
            status: string;
            id: string;
            email: string;
            password: string | null;
            authUserId: string | null;
            role: import("@prisma/client").$Enums.Role;
            createdAt: Date;
            updatedAt: Date;
            tenantId: string | null;
        };
        warehouseManager: {
            name: string | null;
            status: string;
            id: string;
            email: string;
            password: string | null;
            authUserId: string | null;
            role: import("@prisma/client").$Enums.Role;
            createdAt: Date;
            updatedAt: Date;
            tenantId: string | null;
        };
        warehouseMember: {
            name: string | null;
            status: string;
            id: string;
            email: string;
            password: string | null;
            authUserId: string | null;
            role: import("@prisma/client").$Enums.Role;
            createdAt: Date;
            updatedAt: Date;
            tenantId: string | null;
        };
        unauthorizedUser: {
            name: string | null;
            status: string;
            id: string;
            email: string;
            password: string | null;
            authUserId: string | null;
            role: import("@prisma/client").$Enums.Role;
            createdAt: Date;
            updatedAt: Date;
            tenantId: string | null;
        };
        testTenantId: string;
        altTenantId: string;
    }>;
    createTestWarehouses(testTenantId: string, altTenantId: string): Promise<{
        warehouse1: {
            name: string;
            status: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            tenantId: string;
            address: string | null;
        };
        warehouse2: {
            name: string;
            status: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            tenantId: string;
            address: string | null;
        };
        crossTenantWarehouse: {
            name: string;
            status: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            tenantId: string;
            address: string | null;
        };
    }>;
    setupWarehouseAccess(users: {
        warehouseManager: User;
        warehouseMember: User;
    }, warehouses: {
        warehouse1: Warehouse;
        warehouse2: Warehouse;
    }): Promise<void>;
    createTestPallets(warehouses: {
        warehouse1: Warehouse;
        warehouse2: Warehouse;
        crossTenantWarehouse: Warehouse;
    }, users: {
        warehouseMember: User;
    }): Promise<{
        pallet1: {
            description: string | null;
            status: string;
            id: string;
            label: string;
            barcode: string | null;
            shipToDestination: string | null;
            destinationCode: string | null;
            dateCreated: Date;
            lastMovedDate: Date;
            locationId: string | null;
            shipmentId: string | null;
        };
        pallet2: {
            description: string | null;
            status: string;
            id: string;
            label: string;
            barcode: string | null;
            shipToDestination: string | null;
            destinationCode: string | null;
            dateCreated: Date;
            lastMovedDate: Date;
            locationId: string | null;
            shipmentId: string | null;
        };
        crossTenantPallet: {
            description: string | null;
            status: string;
            id: string;
            label: string;
            barcode: string | null;
            shipToDestination: string | null;
            destinationCode: string | null;
            dateCreated: Date;
            lastMovedDate: Date;
            locationId: string | null;
            shipmentId: string | null;
        };
        location1: {
            name: string;
            status: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            warehouseId: string;
            category: import("@prisma/client").$Enums.LocationCategory;
            locationType: import("@prisma/client").$Enums.LocationType;
        };
        location2: {
            name: string;
            status: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            warehouseId: string;
            category: import("@prisma/client").$Enums.LocationCategory;
            locationType: import("@prisma/client").$Enums.LocationType;
        };
        crossTenantLocation: {
            name: string;
            status: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            warehouseId: string;
            category: import("@prisma/client").$Enums.LocationCategory;
            locationType: import("@prisma/client").$Enums.LocationType;
        };
    }>;
    generateTokenForUser(user: User, warehouseAccess?: WarehouseUser[]): string;
    generateMaliciousTokens(): {
        escalatedRole: string;
        crossTenant: string;
        expired: string;
        malformed: string;
        empty: string;
    };
    generateInjectionPayloads(): {
        sql: string[];
        nosql: string[];
        xss: string[];
        pathTraversal: string[];
    };
    cleanupTestData(): Promise<void>;
    verifySecurityAuditLog(action: string, userId?: string, warehouseId?: string): Promise<boolean>;
    createEnhancedUserPayload(user: User, warehouseAccess?: WarehouseUser[]): EnhancedUserPayload;
}
