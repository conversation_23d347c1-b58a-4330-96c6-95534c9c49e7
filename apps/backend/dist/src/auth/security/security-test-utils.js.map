{"version": 3, "file": "security-test-utils.js", "sourceRoot": "", "sources": ["../../../../src/auth/security/security-test-utils.ts"], "names": [], "mappings": ";;;AAEA,2CAAsE;AAQtE,MAAa,iBAAiB;IAC5B,YACmB,MAAqB,EACrB,UAAsB;QADtB,WAAM,GAAN,MAAM,CAAe;QACrB,eAAU,GAAV,UAAU,CAAY;IACtC,CAAC;IAKJ,KAAK,CAAC,eAAe;QACnB,MAAM,YAAY,GAAG,sBAAsB,CAAC;QAC5C,MAAM,WAAW,GAAG,qBAAqB,CAAC;QAG1C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAChD,IAAI,EAAE;gBACJ,KAAK,EAAE,yBAAyB;gBAChC,IAAI,EAAE,qBAAqB;gBAC3B,QAAQ,EAAE,iBAAiB;gBAC3B,UAAU,EAAE,gBAAgB;gBAC5B,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,aAAI,CAAC,YAAY;gBACvB,MAAM,EAAE,QAAQ;aACjB;SACF,CAAC,CAAC;QAGH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACrD,IAAI,EAAE;gBACJ,KAAK,EAAE,2BAA2B;gBAClC,IAAI,EAAE,uBAAuB;gBAC7B,QAAQ,EAAE,iBAAiB;gBAC3B,UAAU,EAAE,kBAAkB;gBAC9B,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,aAAI,CAAC,iBAAiB;gBAC5B,MAAM,EAAE,QAAQ;aACjB;SACF,CAAC,CAAC;QAGH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpD,IAAI,EAAE;gBACJ,KAAK,EAAE,0BAA0B;gBACjC,IAAI,EAAE,sBAAsB;gBAC5B,QAAQ,EAAE,iBAAiB;gBAC3B,UAAU,EAAE,iBAAiB;gBAC7B,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,aAAI,CAAC,gBAAgB;gBAC3B,MAAM,EAAE,QAAQ;aACjB;SACF,CAAC,CAAC;QAGH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACrD,IAAI,EAAE;gBACJ,KAAK,EAAE,gCAAgC;gBACvC,IAAI,EAAE,mBAAmB;gBACzB,QAAQ,EAAE,iBAAiB;gBAC3B,UAAU,EAAE,uBAAuB;gBACnC,QAAQ,EAAE,WAAW;gBACrB,IAAI,EAAE,aAAI,CAAC,gBAAgB;gBAC3B,MAAM,EAAE,QAAQ;aACjB;SACF,CAAC,CAAC;QAEH,OAAO;YACL,WAAW;YACX,gBAAgB;YAChB,eAAe;YACf,gBAAgB;YAChB,YAAY;YACZ,WAAW;SACZ,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,oBAAoB,CAAC,YAAoB,EAAE,WAAmB;QAClE,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YACpD,IAAI,EAAE;gBACJ,IAAI,EAAE,2BAA2B;gBACjC,OAAO,EAAE,sBAAsB;gBAC/B,QAAQ,EAAE,YAAY;aACvB;SACF,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YACpD,IAAI,EAAE;gBACJ,IAAI,EAAE,2BAA2B;gBACjC,OAAO,EAAE,uBAAuB;gBAChC,QAAQ,EAAE,YAAY;aACvB;SACF,CAAC,CAAC;QAEH,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YAC9D,IAAI,EAAE;gBACJ,IAAI,EAAE,wBAAwB;gBAC9B,OAAO,EAAE,uBAAuB;gBAChC,QAAQ,EAAE,WAAW;aACtB;SACF,CAAC,CAAC;QAEH,OAAO;YACL,UAAU;YACV,UAAU;YACV,oBAAoB;SACrB,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,oBAAoB,CACxB,KAAwD,EACxD,UAA4D;QAG5D,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACrC,IAAI,EAAE;gBACJ,MAAM,EAAE,KAAK,CAAC,gBAAgB,CAAC,EAAE;gBACjC,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC,EAAE;gBACrC,IAAI,EAAE,aAAI,CAAC,iBAAiB;aAC7B;SACF,CAAC,CAAC;QAGH,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACrC,IAAI,EAAE;gBACJ,MAAM,EAAE,KAAK,CAAC,eAAe,CAAC,EAAE;gBAChC,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC,EAAE;gBACrC,IAAI,EAAE,aAAI,CAAC,gBAAgB;aAC5B;SACF,CAAC,CAAC;QAGH,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACrC,IAAI,EAAE;gBACJ,MAAM,EAAE,KAAK,CAAC,gBAAgB,CAAC,EAAE;gBACjC,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC,EAAE;gBACrC,IAAI,EAAE,aAAI,CAAC,iBAAiB;aAC7B;SACF,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,iBAAiB,CACrB,UAIC,EACD,KAAgC;QAGhC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAClD,IAAI,EAAE;gBACJ,IAAI,EAAE,0BAA0B;gBAChC,QAAQ,EAAE,SAAS;gBACnB,YAAY,EAAE,MAAM;gBACpB,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC,EAAE;aACtC;SACF,CAAC,CAAC;QAEH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAClD,IAAI,EAAE;gBACJ,IAAI,EAAE,0BAA0B;gBAChC,QAAQ,EAAE,SAAS;gBACnB,YAAY,EAAE,MAAM;gBACpB,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC,EAAE;aACtC;SACF,CAAC,CAAC;QAEH,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5D,IAAI,EAAE;gBACJ,IAAI,EAAE,uBAAuB;gBAC7B,QAAQ,EAAE,SAAS;gBACnB,YAAY,EAAE,MAAM;gBACpB,WAAW,EAAE,UAAU,CAAC,oBAAoB,CAAC,EAAE;aAChD;SACF,CAAC,CAAC;QAGH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;YAC9C,IAAI,EAAE;gBACJ,KAAK,EAAE,cAAc;gBACrB,OAAO,EAAE,cAAc;gBACvB,WAAW,EAAE,wBAAwB;gBACrC,UAAU,EAAE,SAAS,CAAC,EAAE;aACzB;SACF,CAAC,CAAC;QAGH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;YAC9C,IAAI,EAAE;gBACJ,KAAK,EAAE,cAAc;gBACrB,OAAO,EAAE,cAAc;gBACvB,WAAW,EAAE,wBAAwB;gBACrC,UAAU,EAAE,SAAS,CAAC,EAAE;aACzB;SACF,CAAC,CAAC;QAGH,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;YACxD,IAAI,EAAE;gBACJ,KAAK,EAAE,kBAAkB;gBACzB,OAAO,EAAE,kBAAkB;gBAC3B,WAAW,EAAE,qBAAqB;gBAClC,UAAU,EAAE,mBAAmB,CAAC,EAAE;aACnC;SACF,CAAC,CAAC;QAEH,OAAO;YACL,OAAO;YACP,OAAO;YACP,iBAAiB;YACjB,SAAS;YACT,SAAS;YACT,mBAAmB;SACpB,CAAC;IACJ,CAAC;IAKD,oBAAoB,CAAC,IAAU,EAAE,eAAiC;QAChE,MAAM,OAAO,GAAG;YACd,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,eAAe,EACb,eAAe,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC5B,WAAW,EAAE,EAAE,CAAC,WAAW;gBAC3B,IAAI,EAAE,EAAE,CAAC,IAAI;aACd,CAAC,CAAC,IAAI,EAAE;SACZ,CAAC;QAEF,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;IAKD,uBAAuB;QACrB,OAAO;YAEL,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;gBAClC,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,aAAI,CAAC,YAAY;aACxB,CAAC;YAGF,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;gBAChC,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,kBAAkB;gBAC5B,IAAI,EAAE,aAAI,CAAC,gBAAgB;aAC5B,CAAC;YAGF,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAC3B;gBACE,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,aAAI,CAAC,gBAAgB;aAC5B,EACD,EAAE,SAAS,EAAE,KAAK,EAAE,CACrB;YAGD,SAAS,EAAE,mBAAmB;YAG9B,KAAK,EAAE,EAAE;SACV,CAAC;IACJ,CAAC;IAKD,yBAAyB;QACvB,OAAO;YACL,GAAG,EAAE;gBACH,yBAAyB;gBACzB,aAAa;gBACb,iEAAiE;gBACjE,0DAA0D;aAC3D;YACD,KAAK,EAAE;gBACL,eAAe;gBACf,6CAA6C;gBAC7C,oBAAoB;aACrB;YACD,GAAG,EAAE;gBACH,+BAA+B;gBAC/B,yBAAyB;gBACzB,kCAAkC;aACnC;YACD,aAAa,EAAE;gBACb,qBAAqB;gBACrB,4CAA4C;gBAC5C,yCAAyC;aAC1C;SACF,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,eAAe;QAEnB,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YAClC,KAAK,EAAE;gBACL,OAAO,EAAE;oBACP,UAAU,EAAE,WAAW;iBACxB;aACF;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YAClC,KAAK,EAAE;gBACL,OAAO,EAAE;oBACP,UAAU,EAAE,eAAe;iBAC5B;aACF;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;YACzC,KAAK,EAAE;gBACL,IAAI,EAAE;oBACJ,KAAK,EAAE;wBACL,QAAQ,EAAE,oBAAoB;qBAC/B;iBACF;aACF;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC;YACrC,KAAK,EAAE;gBACL,IAAI,EAAE;oBACJ,UAAU,EAAE,eAAe;iBAC5B;aACF;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC;YACrC,KAAK,EAAE;gBACL,IAAI,EAAE,wBAAwB;aAC/B;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAChC,KAAK,EAAE;gBACL,KAAK,EAAE;oBACL,QAAQ,EAAE,oBAAoB;iBAC/B;aACF;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAChC,KAAK,EAAE;gBACL,KAAK,EAAE,gCAAgC;aACxC;SACF,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAC1B,MAAc,EACd,MAAe,EACf,WAAoB;QAEpB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YACpD,KAAK,EAAE;gBACL,MAAM;gBACN,MAAM;gBACN,OAAO,EAAE,WAAW;oBAClB,CAAC,CAAC;wBACE,IAAI,EAAE,CAAC,aAAa,CAAC;wBACrB,MAAM,EAAE,WAAW;qBACpB;oBACH,CAAC,CAAC,SAAS;aACd;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,MAAM;aAClB;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,CAAC,QAAQ,CAAC;IACpB,CAAC;IAKD,yBAAyB,CACvB,IAAU,EACV,kBAAmC,EAAE;QAErC,MAAM,oBAAoB,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;QACzE,MAAM,cAAc,GAAG,IAAI,GAAG,CAC5B,eAAe,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CACvD,CAAC;QAEF,OAAO;YACL,GAAG,IAAI;YACP,oBAAoB;YACpB,cAAc;YACd,cAAc,EAAE,eAAe;YAC/B,aAAa,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,aAAI,CAAC,YAAY;YACpD,kBAAkB,EAAE,CAAC,WAAmB,EAAE,EAAE,CAC1C,IAAI,CAAC,IAAI,KAAK,aAAI,CAAC,YAAY;gBAC/B,oBAAoB,CAAC,QAAQ,CAAC,WAAW,CAAC;YAC5C,gBAAgB,EAAE,CAAC,WAAmB,EAAE,EAAE,CACxC,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,IAAI;YACzC,sBAAsB,EAAE,CAAC,WAAmB,EAAE,YAAkB,EAAE,EAAE;gBAClE,IAAI,IAAI,CAAC,IAAI,KAAK,aAAI,CAAC,YAAY;oBAAE,OAAO,IAAI,CAAC;gBACjD,MAAM,QAAQ,GAAG,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBACjD,IAAI,CAAC,QAAQ;oBAAE,OAAO,KAAK,CAAC;gBAE5B,MAAM,aAAa,GAAG;oBACpB,aAAI,CAAC,gBAAgB;oBACrB,aAAI,CAAC,iBAAiB;oBACtB,aAAI,CAAC,YAAY;iBAClB,CAAC;gBACF,MAAM,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACtD,MAAM,iBAAiB,GAAG,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;gBAE9D,OAAO,aAAa,IAAI,iBAAiB,CAAC;YAC5C,CAAC;SACqB,CAAC;IAC3B,CAAC;CACF;AApbD,8CAobC"}