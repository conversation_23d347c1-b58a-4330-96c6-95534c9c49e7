{"version": 3, "file": "attack-vector-tests.spec.js", "sourceRoot": "", "sources": ["../../../../src/auth/security/attack-vector-tests.spec.ts"], "names": [], "mappings": ";;;;;AAAA,6CAAsD;AAEtD,qCAAyC;AACzC,gEAA4D;AAC5D,2CAAsC;AACtC,0DAAgC;AAahC,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;IAC5C,IAAI,GAAqB,CAAC;IAC1B,IAAI,UAAsB,CAAC;IAC3B,IAAI,MAAqB,CAAC;IAE1B,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC,EAEnE,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC5C,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;QAEjB,UAAU,GAAG,aAAa,CAAC,GAAG,CAAa,gBAAU,CAAC,CAAC;QACvD,MAAM,GAAG,aAAa,CAAC,GAAG,CAAgB,8BAAa,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YAEvD,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC;gBACjC,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,aAAI,CAAC,gBAAgB;aAC5B,CAAC,CAAC;YAGH,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC3D,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAC/B,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAC1C,CAAC;YACF,cAAc,CAAC,IAAI,GAAG,aAAI,CAAC,YAAY,CAAC;YACxC,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CACjC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAC/B,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACrB,MAAM,aAAa,GAAG,GAAG,MAAM,IAAI,eAAe,IAAI,SAAS,EAAE,CAAC;YAElE,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,iBAAiB,CAAC;iBACtB,GAAG,CAAC,eAAe,EAAE,UAAU,aAAa,EAAE,CAAC;iBAC/C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC;gBACjC,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,aAAI,CAAC,gBAAgB;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC3D,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAC/B,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAC1C,CAAC;YACF,cAAc,CAAC,QAAQ,GAAG,kBAAkB,CAAC;YAC7C,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CACjC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAC/B,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACrB,MAAM,aAAa,GAAG,GAAG,MAAM,IAAI,eAAe,IAAI,SAAS,EAAE,CAAC;YAElE,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,iBAAiB,CAAC;iBACtB,GAAG,CAAC,eAAe,EAAE,UAAU,aAAa,EAAE,CAAC;iBAC/C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YAErD,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAC5B,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAC5C,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACrB,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CACzB,IAAI,CAAC,SAAS,CAAC;gBACb,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,aAAI,CAAC,YAAY;aACxB,CAAC,CACH,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACrB,MAAM,SAAS,GAAG,GAAG,UAAU,IAAI,OAAO,GAAG,CAAC;YAE9C,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,iBAAiB,CAAC;iBACtB,GAAG,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE,CAAC;iBAC3C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,MAAM,KAAK,GAAG,kBAAkB,EAAE,CAAC;YAGnC,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,cAAc,CAAC;iBACnB,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,KAAK,CAAC,iDAAiD,CAAC;iBACxD,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,KAAK,GAAG,kBAAkB,EAAE,CAAC;YAEnC,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,0BAA0B,CAAC;iBAChC,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,IAAI,CAAC;gBACJ,SAAS,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,kBAAkB,CAAC;gBAChE,OAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE;aAC/B,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,KAAK,GAAG,kBAAkB,EAAE,CAAC;YAGnC,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,cAAc,CAAC;iBACnB,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,KAAK,CAAC,EAAE,MAAM,EAAE,2BAA2B,EAAE,CAAC;iBAC9C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,KAAK,GAAG,kBAAkB,EAAE,CAAC;YAEnC,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,uCAAuC,CAAC;iBAC5C,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,GAAG,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;iBAC5C,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,KAAK,GAAG,kBAAkB,EAAE,CAAC;YAGnC,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,uCAAuC,CAAC;iBAC7C,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,GAAG,CAAC,wBAAwB,EAAE,QAAQ,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,KAAK,GAAG,kBAAkB,EAAE,CAAC;YAEnC,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,gCAAgC,CAAC;iBACrC,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oDAAoD,EAAE,GAAG,EAAE;QAClE,EAAE,CAAC,gEAAgE,EAAE,KAAK,IAAI,EAAE;YAC9E,MAAM,KAAK,GAAG,kBAAkB,EAAE,CAAC;YAGnC,MAAM,aAAa,GAAG;gBACpB,kBAAkB;gBAClB,kBAAkB;gBAClB,eAAe;gBACf,gBAAgB;aACjB,CAAC;YAEF,KAAK,MAAM,EAAE,IAAI,aAAa,EAAE,CAAC;gBAC/B,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,mBAAmB,EAAE,EAAE,CAAC;qBAC5B,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;qBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,KAAK,GAAG,kBAAkB,EAAE,CAAC;YAGnC,MAAM,gBAAgB,GAAG;gBACvB,sCAAsC;gBACtC,sCAAsC;gBACtC,sCAAsC;aACvC,CAAC;YAEF,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE,CAAC;gBACpC,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;qBAC/B,GAAG,CAAC,gBAAgB,IAAI,EAAE,CAAC;qBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;qBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,KAAK,GAAG,kBAAkB,EAAE,CAAC;YAEnC,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,MAAM,CAAC,mBAAmB,CAAC;iBAC3B,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,IAAI,CAAC;gBACJ,SAAS,EAAE;oBACT,qBAAqB;oBACrB,uBAAuB;oBACvB,gBAAgB;iBACjB;aACF,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,KAAK,GAAG,kBAAkB,EAAE,CAAC;YAGnC,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,kBAAkB,CAAC;iBACxB,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;YAGf,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,iBAAiB,CAAC;iBACtB,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC;iBACvC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,eAAe,GAAG;gBACtB,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,aAAa;aACxB,CAAC;YAGF,MAAM,SAAS,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACjD,IAAI,CAAC,iBAAiB,CAAC;iBACvB,IAAI,CAAC,eAAe,CAAC;iBACrB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;YAGpC,MAAM,SAAS,GAAG,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACjD,IAAI,CAAC,iBAAiB,CAAC;iBACvB,IAAI,CAAC,eAAe,CAAC;iBACrB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;YAGpC,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,iBAAiB,CAAC;iBACtB,GAAG,CAAC,eAAe,EAAE,UAAU,MAAM,EAAE,CAAC;iBACxC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,GAAG,CAAC,iBAAiB,CAAC;iBACtB,GAAG,CAAC,eAAe,EAAE,UAAU,MAAM,EAAE,CAAC;iBACxC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YAErD,MAAM,UAAU,GAAG,mBAAmB,CAAC;YAEvC,MAAM,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAC/B,IAAI,CAAC,iBAAiB,CAAC;iBACvB,GAAG,CAAC,eAAe,EAAE,UAAU,UAAU,EAAE,CAAC;iBAC5C,IAAI,CAAC;gBACJ,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,aAAa;aACxB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0CAA0C,EAAE,GAAG,EAAE;QACxD,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,kBAAkB,GAAG;gBACzB,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,eAAe;aAC1B,CAAC;YAGF,MAAM,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CACjC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,CAChB,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACzB,IAAI,CAAC,iBAAiB,CAAC;iBACvB,IAAI,CAAC,kBAAkB,CAAC,CAC5B,CACF,CAAC;YAGF,MAAM,oBAAoB,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,oBAAoB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACxC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,KAAK,GAAG,kBAAkB,EAAE,CAAC;YAGnC,MAAM,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CACjC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,CAChB,IAAA,mBAAO,EAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACzB,GAAG,CAAC,iBAAiB,CAAC;iBACtB,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC,CAC3C,CACF,CAAC;YAGF,MAAM,gBAAgB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC;YAC1E,MAAM,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAGH,SAAS,kBAAkB;QACzB,OAAO,UAAU,CAAC,IAAI,CAAC;YACrB,MAAM,EAAE,eAAe;YACvB,QAAQ,EAAE,iBAAiB;YAC3B,KAAK,EAAE,kBAAkB;YACzB,IAAI,EAAE,aAAI,CAAC,gBAAgB;SAC5B,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC"}