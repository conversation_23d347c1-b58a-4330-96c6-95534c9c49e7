"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var JwtStrategy_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtStrategy = void 0;
const passport_jwt_1 = require("passport-jwt");
const passport_1 = require("@nestjs/passport");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const constants_1 = require("./constants");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
const auth_audit_service_1 = require("../audit-log/services/auth-audit.service");
let JwtStrategy = JwtStrategy_1 = class JwtStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy) {
    constructor(configService, prisma, authAuditService) {
        const strategySecret = process.env.NODE_ENV === "test"
            ? process.env.TEST_JWT_SECRET
            : configService.get(constants_1.jwtConstants.secretKeyName);
        if (!strategySecret) {
            const errorMsg = process.env.NODE_ENV === "test"
                ? "TEST_JWT_SECRET environment variable is not set for JwtStrategy in test mode."
                : `JWT secret key (${constants_1.jwtConstants.secretKeyName}) is not set in environment variables.`;
            new common_1.Logger(JwtStrategy_1.name).error(errorMsg);
            throw new Error("JWT secret configuration error");
        }
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: strategySecret,
        });
        this.configService = configService;
        this.prisma = prisma;
        this.authAuditService = authAuditService;
        this.logger = new common_1.Logger(JwtStrategy_1.name);
        this.logger.debug(`JwtStrategy initialized with secret source: ${process.env.NODE_ENV === "test" ? "TEST_ENV_VAR" : "ConfigService"}`);
    }
    async validate(payload) {
        const { userId, tenantId: jwtTenantId, warehouseAccess } = payload;
        if (!userId) {
            this.logger.warn("JWT payload missing userId.");
            await this.authAuditService.logTokenValidationFailure("Invalid token: User identifier missing");
            throw new common_1.UnauthorizedException("Invalid token: User identifier missing.");
        }
        this.logger.debug(`Validating JWT payload for userId: ${userId}, tenantId: ${jwtTenantId}, warehouses: ${warehouseAccess?.length || 0}`);
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            include: {
                warehouseUsers: {
                    select: {
                        warehouseId: true,
                        role: true,
                    },
                },
            },
        });
        if (!user) {
            this.logger.warn(`User not found during JWT validation: ${userId}`);
            await this.authAuditService.logTokenValidationFailure("User not found", userId, undefined, jwtTenantId);
            throw new common_1.UnauthorizedException("User not found.");
        }
        if (user.tenantId !== jwtTenantId) {
            this.logger.error(`Tenant ID mismatch for user ${userId}. JWT tenantId: ${jwtTenantId}, DB tenantId: ${user.tenantId}`);
            await this.authAuditService.logTokenValidationFailure("Tenant ID mismatch", userId, user.email, jwtTenantId);
            throw new common_1.UnauthorizedException("Tenant ID mismatch.");
        }
        const currentWarehouseIds = user.warehouseUsers.map((wu) => wu.warehouseId);
        const jwtWarehouseIds = warehouseAccess?.map((wa) => wa.warehouseId) || [];
        const hasOutdatedAccess = jwtWarehouseIds.some((id) => !currentWarehouseIds.includes(id));
        if (hasOutdatedAccess) {
            this.logger.warn(`Outdated warehouse access in JWT for user ${userId}. JWT warehouses: [${jwtWarehouseIds.join(", ")}], Current warehouses: [${currentWarehouseIds.join(", ")}]`);
        }
        const enhancedUser = this.createEnhancedUserPayload(user);
        this.logger.debug(`JWT validation successful for ${user.email}. Accessible warehouses: ${enhancedUser.accessibleWarehouses.length}`);
        await this.authAuditService.logTokenValidationSuccess(enhancedUser);
        return enhancedUser;
    }
    createEnhancedUserPayload(user) {
        const accessibleWarehouses = user.warehouseUsers.map((wu) => wu.warehouseId);
        const warehouseRoles = new Map();
        user.warehouseUsers.forEach((wu) => {
            warehouseRoles.set(wu.warehouseId, wu.role);
        });
        return {
            ...user,
            accessibleWarehouses,
            warehouseRoles,
            canAccessWarehouse: (warehouseId) => {
                return accessibleWarehouses.includes(warehouseId);
            },
            getWarehouseRole: (warehouseId) => {
                return warehouseRoles.get(warehouseId) || null;
            },
            hasWarehousePermission: (warehouseId, requiredRole) => {
                const userRole = warehouseRoles.get(warehouseId);
                if (!userRole)
                    return false;
                const roleHierarchy = {
                    [client_1.Role.TENANT_ADMIN]: 3,
                    [client_1.Role.WAREHOUSE_MANAGER]: 2,
                    [client_1.Role.WAREHOUSE_MEMBER]: 1,
                };
                return ((roleHierarchy[userRole] || 0) >= (roleHierarchy[requiredRole] || 0));
            },
            isTenantAdmin: () => {
                return user.role === client_1.Role.TENANT_ADMIN;
            },
        };
    }
};
exports.JwtStrategy = JwtStrategy;
exports.JwtStrategy = JwtStrategy = JwtStrategy_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        prisma_service_1.PrismaService,
        auth_audit_service_1.AuthAuditService])
], JwtStrategy);
//# sourceMappingURL=jwt.strategy.js.map