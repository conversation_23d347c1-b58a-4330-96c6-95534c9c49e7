{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../src/auth/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,+CAAoD;AACpD,+CAAoD;AACpD,2CAA2E;AAC3E,2CAA+C;AAC/C,2CAA2C;AAC3C,6DAAyD;AAEzD,2CAAsC;AACtC,iFAA4E;AAGrE,IAAM,WAAW,mBAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IAGzD,YACU,aAA4B,EAC5B,MAAqB,EACrB,gBAAkC;QAG1C,MAAM,cAAc,GAClB,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM;YAC7B,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe;YAC7B,CAAC,CAAC,aAAa,CAAC,GAAG,CAAS,wBAAY,CAAC,aAAa,CAAC,CAAC;QAE5D,IAAI,CAAC,cAAc,EAAE,CAAC;YAEpB,MAAM,QAAQ,GACZ,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM;gBAC7B,CAAC,CAAC,+EAA+E;gBACjF,CAAC,CAAC,mBAAmB,wBAAY,CAAC,aAAa,wCAAwC,CAAC;YAC5F,IAAI,eAAM,CAAC,aAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,KAAK,CAAC;YACJ,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,KAAK;YACvB,WAAW,EAAE,cAAc;SAC5B,CAAC,CAAC;QAxBK,kBAAa,GAAb,aAAa,CAAe;QAC5B,WAAM,GAAN,MAAM,CAAe;QACrB,qBAAgB,GAAhB,gBAAgB,CAAkB;QAL3B,WAAM,GAAG,IAAI,eAAM,CAAC,aAAW,CAAC,IAAI,CAAC,CAAC;QA6BrD,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,+CACE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,eACrD,EAAE,CACH,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAmB;QAEhC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC;QAEnE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAChD,MAAM,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,CACnD,wCAAwC,CACzC,CAAC;YACF,MAAM,IAAI,8BAAqB,CAC7B,yCAAyC,CAC1C,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,sCAAsC,MAAM,eAAe,WAAW,iBACpE,eAAe,EAAE,MAAM,IAAI,CAC7B,EAAE,CACH,CAAC;QAGF,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;YACrB,OAAO,EAAE;gBACP,cAAc,EAAE;oBACd,MAAM,EAAE;wBACN,WAAW,EAAE,IAAI;wBACjB,IAAI,EAAE,IAAI;qBACX;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yCAAyC,MAAM,EAAE,CAAC,CAAC;YACpE,MAAM,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,CACnD,gBAAgB,EAChB,MAAM,EACN,SAAS,EACT,WAAW,CACZ,CAAC;YACF,MAAM,IAAI,8BAAqB,CAAC,iBAAiB,CAAC,CAAC;QACrD,CAAC;QAGD,IAAI,IAAI,CAAC,QAAQ,KAAK,WAAW,EAAE,CAAC;YAClC,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,+BAA+B,MAAM,mBAAmB,WAAW,kBAAkB,IAAI,CAAC,QAAQ,EAAE,CACrG,CAAC;YACF,MAAM,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,CACnD,oBAAoB,EACpB,MAAM,EACN,IAAI,CAAC,KAAK,EACV,WAAW,CACZ,CAAC;YACF,MAAM,IAAI,8BAAqB,CAAC,qBAAqB,CAAC,CAAC;QACzD,CAAC;QAID,MAAM,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;QAC5E,MAAM,eAAe,GAAG,eAAe,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;QAG3E,MAAM,iBAAiB,GAAG,eAAe,CAAC,IAAI,CAC5C,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAC1C,CAAC;QACF,IAAI,iBAAiB,EAAE,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,6CAA6C,MAAM,sBAAsB,eAAe,CAAC,IAAI,CAC3F,IAAI,CACL,2BAA2B,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAC9D,CAAC;QAGJ,CAAC;QAGD,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;QAE1D,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,iCAAiC,IAAI,CAAC,KAAK,4BAA4B,YAAY,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAClH,CAAC;QAGF,MAAM,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC;QAEpE,OAAO,YAAY,CAAC;IACtB,CAAC;IAKO,yBAAyB,CAAC,IAAS;QACzC,MAAM,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAClD,CAAC,EAAO,EAAE,EAAE,CAAC,EAAE,CAAC,WAAW,CAC5B,CAAC;QACF,MAAM,cAAc,GAAG,IAAI,GAAG,EAAgB,CAAC;QAG/C,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,EAAO,EAAE,EAAE;YACtC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,GAAG,IAAI;YACP,oBAAoB;YACpB,cAAc;YAGd,kBAAkB,EAAE,CAAC,WAAmB,EAAW,EAAE;gBACnD,OAAO,oBAAoB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACpD,CAAC;YAGD,gBAAgB,EAAE,CAAC,WAAmB,EAAe,EAAE;gBACrD,OAAO,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;YACjD,CAAC;YAGD,sBAAsB,EAAE,CACtB,WAAmB,EACnB,YAAkB,EACT,EAAE;gBACX,MAAM,QAAQ,GAAG,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBACjD,IAAI,CAAC,QAAQ;oBAAE,OAAO,KAAK,CAAC;gBAG5B,MAAM,aAAa,GAAG;oBACpB,CAAC,aAAI,CAAC,YAAY,CAAC,EAAE,CAAC;oBACtB,CAAC,aAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC;oBAC3B,CAAC,aAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;iBAC3B,CAAC;gBAEF,OAAO,CACL,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CACrE,CAAC;YACJ,CAAC;YAGD,aAAa,EAAE,GAAY,EAAE;gBAC3B,OAAO,IAAI,CAAC,IAAI,KAAK,aAAI,CAAC,YAAY,CAAC;YACzC,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAA;AAtLY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;qCAKc,sBAAa;QACpB,8BAAa;QACH,qCAAgB;GANjC,WAAW,CAsLvB"}