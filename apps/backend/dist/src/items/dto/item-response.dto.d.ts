export declare class ItemWarehouseDto {
    id: string;
    name: string;
}
export declare class ItemLocationDto {
    id: string;
    name: string;
    warehouse: ItemWarehouseDto;
}
export declare class PalletLocationStockDto {
    palletId: string;
    palletBarcode: string;
    locationName?: string;
    warehouseName?: string;
    quantity: number;
    location?: ItemLocationDto;
}
export declare class ItemWarehouseStockDto {
    warehouse: ItemWarehouseDto;
    totalQuantity: number;
    palletCount: number;
    locationCount: number;
}
export declare class ItemDetailResponseDto {
    id: string;
    name: string;
    sku?: string;
    description?: string;
    unitOfMeasure?: string;
    defaultCost?: number;
    lowStockThreshold?: number;
    status: string;
    createdAt: Date;
    updatedAt: Date;
    tenantId: string;
    quantityOnHand: number;
    numberOfPallets: number;
    stockByLocationAndPallet: PalletLocationStockDto[];
    stockByWarehouse?: ItemWarehouseStockDto[];
}
export declare class ItemResponseDto {
    id: string;
    name: string;
    sku?: string;
    description?: string;
    unitOfMeasure?: string;
    defaultCost?: number;
    lowStockThreshold?: number;
    status: string;
    createdAt: Date;
    updatedAt: Date;
    tenantId: string;
    quantityOnHand?: number;
}
export declare class ItemListResponseDto {
    data: ItemResponseDto[];
    count: number;
    page?: number;
    limit?: number;
    totalPages?: number;
}
