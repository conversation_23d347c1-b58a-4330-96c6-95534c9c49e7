"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ItemListResponseDto = exports.ItemResponseDto = exports.ItemDetailResponseDto = exports.ItemWarehouseStockDto = exports.PalletLocationStockDto = exports.ItemLocationDto = exports.ItemWarehouseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ItemWarehouseDto {
}
exports.ItemWarehouseDto = ItemWarehouseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Warehouse ID" }),
    __metadata("design:type", String)
], ItemWarehouseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Warehouse name" }),
    __metadata("design:type", String)
], ItemWarehouseDto.prototype, "name", void 0);
class ItemLocationDto {
}
exports.ItemLocationDto = ItemLocationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Location ID" }),
    __metadata("design:type", String)
], ItemLocationDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Location name" }),
    __metadata("design:type", String)
], ItemLocationDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Warehouse information",
        type: ItemWarehouseDto,
    }),
    __metadata("design:type", ItemWarehouseDto)
], ItemLocationDto.prototype, "warehouse", void 0);
class PalletLocationStockDto {
}
exports.PalletLocationStockDto = PalletLocationStockDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Pallet ID" }),
    __metadata("design:type", String)
], PalletLocationStockDto.prototype, "palletId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Pallet barcode" }),
    __metadata("design:type", String)
], PalletLocationStockDto.prototype, "palletBarcode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Location name where pallet is stored" }),
    __metadata("design:type", String)
], PalletLocationStockDto.prototype, "locationName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Warehouse name where pallet is stored" }),
    __metadata("design:type", String)
], PalletLocationStockDto.prototype, "warehouseName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Quantity of this item on the pallet" }),
    __metadata("design:type", Number)
], PalletLocationStockDto.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: "Detailed location information",
        type: ItemLocationDto,
    }),
    __metadata("design:type", ItemLocationDto)
], PalletLocationStockDto.prototype, "location", void 0);
class ItemWarehouseStockDto {
}
exports.ItemWarehouseStockDto = ItemWarehouseStockDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Warehouse information",
        type: ItemWarehouseDto,
    }),
    __metadata("design:type", ItemWarehouseDto)
], ItemWarehouseStockDto.prototype, "warehouse", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Total quantity in this warehouse" }),
    __metadata("design:type", Number)
], ItemWarehouseStockDto.prototype, "totalQuantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Number of pallets in this warehouse" }),
    __metadata("design:type", Number)
], ItemWarehouseStockDto.prototype, "palletCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Number of locations with this item" }),
    __metadata("design:type", Number)
], ItemWarehouseStockDto.prototype, "locationCount", void 0);
class ItemDetailResponseDto {
}
exports.ItemDetailResponseDto = ItemDetailResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Item ID" }),
    __metadata("design:type", String)
], ItemDetailResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Item name" }),
    __metadata("design:type", String)
], ItemDetailResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Item SKU" }),
    __metadata("design:type", String)
], ItemDetailResponseDto.prototype, "sku", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Item description" }),
    __metadata("design:type", String)
], ItemDetailResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Unit of measure" }),
    __metadata("design:type", String)
], ItemDetailResponseDto.prototype, "unitOfMeasure", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Default cost" }),
    __metadata("design:type", Number)
], ItemDetailResponseDto.prototype, "defaultCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Low stock threshold" }),
    __metadata("design:type", Number)
], ItemDetailResponseDto.prototype, "lowStockThreshold", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Item status" }),
    __metadata("design:type", String)
], ItemDetailResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Creation timestamp" }),
    __metadata("design:type", Date)
], ItemDetailResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Last update timestamp" }),
    __metadata("design:type", Date)
], ItemDetailResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Tenant ID" }),
    __metadata("design:type", String)
], ItemDetailResponseDto.prototype, "tenantId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Total quantity on hand across all warehouses" }),
    __metadata("design:type", Number)
], ItemDetailResponseDto.prototype, "quantityOnHand", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Total number of pallets containing this item" }),
    __metadata("design:type", Number)
], ItemDetailResponseDto.prototype, "numberOfPallets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Stock breakdown by pallet and location",
        type: [PalletLocationStockDto],
    }),
    __metadata("design:type", Array)
], ItemDetailResponseDto.prototype, "stockByLocationAndPallet", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: "Stock summary by warehouse",
        type: [ItemWarehouseStockDto],
    }),
    __metadata("design:type", Array)
], ItemDetailResponseDto.prototype, "stockByWarehouse", void 0);
class ItemResponseDto {
}
exports.ItemResponseDto = ItemResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Item ID" }),
    __metadata("design:type", String)
], ItemResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Item name" }),
    __metadata("design:type", String)
], ItemResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Item SKU" }),
    __metadata("design:type", String)
], ItemResponseDto.prototype, "sku", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Item description" }),
    __metadata("design:type", String)
], ItemResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Unit of measure" }),
    __metadata("design:type", String)
], ItemResponseDto.prototype, "unitOfMeasure", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Default cost" }),
    __metadata("design:type", Number)
], ItemResponseDto.prototype, "defaultCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Low stock threshold" }),
    __metadata("design:type", Number)
], ItemResponseDto.prototype, "lowStockThreshold", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Item status" }),
    __metadata("design:type", String)
], ItemResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Creation timestamp" }),
    __metadata("design:type", Date)
], ItemResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Last update timestamp" }),
    __metadata("design:type", Date)
], ItemResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Tenant ID" }),
    __metadata("design:type", String)
], ItemResponseDto.prototype, "tenantId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Total quantity on hand" }),
    __metadata("design:type", Number)
], ItemResponseDto.prototype, "quantityOnHand", void 0);
class ItemListResponseDto {
}
exports.ItemListResponseDto = ItemListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Array of items",
        type: [ItemResponseDto],
    }),
    __metadata("design:type", Array)
], ItemListResponseDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Total number of items" }),
    __metadata("design:type", Number)
], ItemListResponseDto.prototype, "count", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Current page number" }),
    __metadata("design:type", Number)
], ItemListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Number of items per page" }),
    __metadata("design:type", Number)
], ItemListResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "Total number of pages" }),
    __metadata("design:type", Number)
], ItemListResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=item-response.dto.js.map