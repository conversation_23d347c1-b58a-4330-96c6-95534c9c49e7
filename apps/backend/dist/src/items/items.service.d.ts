import { PrismaService } from "../prisma/prisma.service";
import { CreateItemDto } from "./dto/create-item.dto";
import { UpdateItemDto } from "./dto/update-item.dto";
import { Item } from "@prisma/client";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";
interface PalletLocationStock {
    palletId: string;
    locationName: string | null;
    warehouseName: string | null;
    quantity: number;
}
export interface ItemDetailResponse extends Item {
    quantityOnHand: number;
    numberOfPallets: number;
    stockByLocationAndPallet: PalletLocationStock[];
}
export declare class ItemsService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createItemDto: CreateItemDto, currentUser: AuthenticatedUser): Promise<Item>;
    findAll(currentUser: AuthenticatedUser): Promise<Item[]>;
    findOne(id: string, currentUser: AuthenticatedUser): Promise<ItemDetailResponse>;
    update(id: string, updateItemDto: UpdateItemDto, currentUser: AuthenticatedUser): Promise<Item>;
    remove(id: string, currentUser: AuthenticatedUser): Promise<Item>;
}
export {};
