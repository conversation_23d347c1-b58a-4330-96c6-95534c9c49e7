import { ItemsService } from "./items.service";
import { CreateItemDto } from "./dto/create-item.dto";
import { UpdateItemDto } from "./dto/update-item.dto";
import { EnhancedUserPayload } from "../auth/types";
export declare class ItemsController {
    private readonly itemsService;
    constructor(itemsService: ItemsService);
    create(createItemDto: CreateItemDto, req: {
        user: EnhancedUserPayload;
    }): Promise<{
        name: string;
        sku: string | null;
        description: string | null;
        unitOfMeasure: string;
        defaultCost: number | null;
        lowStockThreshold: number | null;
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
    }>;
    findAll(req: {
        user: EnhancedUserPayload;
    }): Promise<{
        name: string;
        sku: string | null;
        description: string | null;
        unitOfMeasure: string;
        defaultCost: number | null;
        lowStockThreshold: number | null;
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
    }[]>;
    findOne(id: string, req: {
        user: EnhancedUserPayload;
    }): Promise<import("./items.service").ItemDetailResponse>;
    update(id: string, updateItemDto: UpdateItemDto, req: {
        user: EnhancedUserPayload;
    }): Promise<{
        name: string;
        sku: string | null;
        description: string | null;
        unitOfMeasure: string;
        defaultCost: number | null;
        lowStockThreshold: number | null;
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
    }>;
    remove(id: string, req: {
        user: EnhancedUserPayload;
    }): Promise<{
        name: string;
        sku: string | null;
        description: string | null;
        unitOfMeasure: string;
        defaultCost: number | null;
        lowStockThreshold: number | null;
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
    }>;
}
