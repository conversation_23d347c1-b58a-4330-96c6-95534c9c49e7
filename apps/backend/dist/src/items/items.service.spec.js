"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const testing_1 = require("@nestjs/testing");
const prisma_service_1 = require("../prisma/prisma.service");
const items_service_1 = require("./items.service");
const items_module_1 = require("./items.module");
const prisma_module_1 = require("../prisma/prisma.module");
const db_cleanup_1 = require("../../test/utils/db-cleanup");
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
describe("ItemsService Integration", () => {
    let service;
    let prisma;
    let module;
    let testTenant;
    let testWarehouse1;
    let testWarehouse2;
    let mockAdminUser;
    let mockWarehouseAdminUser;
    let mockWarehouseMemberUser;
    let mockUserNoWarehouses;
    beforeAll(async () => {
    });
    afterAll(async () => {
        await module?.close();
    });
    beforeEach(async () => {
        module = await testing_1.Test.createTestingModule({
            imports: [prisma_module_1.PrismaModule, items_module_1.ItemsModule],
        }).compile();
        prisma = module.get(prisma_service_1.PrismaService);
        service = module.get(items_service_1.ItemsService);
        await (0, db_cleanup_1.cleanupDatabase)(prisma);
        testTenant = await prisma.tenant.create({
            data: { name: "Test Tenant for Items" },
        });
        testWarehouse1 = await prisma.warehouse.create({
            data: { name: "Test Warehouse 1", tenantId: testTenant.id },
        });
        testWarehouse2 = await prisma.warehouse.create({
            data: { name: "Test Warehouse 2", tenantId: testTenant.id },
        });
        mockAdminUser = {
            id: "admin-user-id",
            email: "<EMAIL>",
            role: client_1.Role.TENANT_ADMIN,
            tenantId: testTenant.id,
            name: "Admin User",
            authUserId: "auth-admin-user-id",
            warehouseUsers: [
                { warehouseId: testWarehouse1.id, role: client_1.Role.WAREHOUSE_MEMBER },
                { warehouseId: testWarehouse2.id, role: client_1.Role.WAREHOUSE_MEMBER },
            ],
        };
        mockWarehouseAdminUser = {
            id: "whadmin-user-id",
            email: "<EMAIL>",
            role: client_1.Role.WAREHOUSE_MANAGER,
            tenantId: testTenant.id,
            name: "Warehouse Admin User",
            authUserId: "auth-whadmin-user-id",
            warehouseUsers: [
                { warehouseId: testWarehouse1.id, role: client_1.Role.WAREHOUSE_MEMBER },
            ],
        };
        mockWarehouseMemberUser = {
            id: "whmember-user-id",
            email: "<EMAIL>",
            role: client_1.Role.WAREHOUSE_MEMBER,
            tenantId: testTenant.id,
            name: "Warehouse Member User",
            authUserId: "auth-whmember-user-id",
            warehouseUsers: [
                { warehouseId: testWarehouse2.id, role: client_1.Role.WAREHOUSE_MEMBER },
            ],
        };
        mockUserNoWarehouses = {
            id: "nowh-user-id",
            email: "<EMAIL>",
            role: client_1.Role.WAREHOUSE_MEMBER,
            tenantId: testTenant.id,
            name: "No Warehouse User",
            authUserId: "auth-nowh-user-id",
            warehouseUsers: [],
        };
    });
    afterEach(async () => {
    });
    it("should be defined", () => {
        expect(service).toBeDefined();
    });
    describe("create()", () => {
        const createDto = {
            name: "Test Item Global",
            sku: "TEST_ITEM_001",
            description: "A test item description",
            unitOfMeasure: "pcs",
        };
        it("ADMIN should create a new item without warehouse associations", async () => {
            const item = await service.create(createDto, mockAdminUser);
            expect(item).toBeDefined();
            expect(item.name).toBe(createDto.name);
            expect(item.tenantId).toBe(mockAdminUser.tenantId);
            const dbItem = await prisma.item.findUnique({ where: { id: item.id } });
            expect(dbItem).toBeDefined();
            const warehouseItems = await prisma.warehouseItem.findMany({
                where: { itemId: item.id },
            });
            expect(warehouseItems.length).toBe(0);
        });
        it("ADMIN should create a new item and associate with warehouses in their tenant", async () => {
            const dtoWithWarehouses = {
                ...createDto,
                sku: "TEST_ITEM_002",
                warehouseIds: [testWarehouse1.id, testWarehouse2.id],
            };
            const item = await service.create(dtoWithWarehouses, mockAdminUser);
            expect(item).toBeDefined();
            const warehouseItems = await prisma.warehouseItem.findMany({
                where: { itemId: item.id, tenantId: mockAdminUser.tenantId },
            });
            expect(warehouseItems.length).toBe(2);
            expect(warehouseItems.map((wi) => wi.warehouseId).sort()).toEqual([testWarehouse1.id, testWarehouse2.id].sort());
            warehouseItems.forEach((wi) => expect(wi.tenantId).toBe(mockAdminUser.tenantId));
        });
        it("should throw ForbiddenException if ADMIN tries to associate with warehouse from another tenant", async () => {
            const otherTenant = await prisma.tenant.create({
                data: { name: "Other Tenant" },
            });
            const otherTenantWarehouse = await prisma.warehouse.create({
                data: { name: "Other Tenant Warehouse", tenantId: otherTenant.id },
            });
            const dtoWithInvalidWarehouse = {
                ...createDto,
                sku: "TEST_ITEM_003",
                warehouseIds: [otherTenantWarehouse.id],
            };
            await expect(service.create(dtoWithInvalidWarehouse, mockAdminUser)).rejects.toThrow(common_1.ForbiddenException);
        });
        it("should throw ForbiddenException if SKU already exists globally", async () => {
            await service.create({ ...createDto, sku: "DUPLICATE_SKU_GLOBAL" }, mockAdminUser);
            await expect(service.create({ name: "Another Item", sku: "DUPLICATE_SKU_GLOBAL" }, mockAdminUser)).rejects.toThrow(common_1.ForbiddenException);
        });
    });
    describe("findAll()", () => {
        let item1, item2, item3;
        beforeEach(async () => {
            item1 = await prisma.item.create({
                data: { name: "Alpha Item", tenantId: testTenant.id },
            });
            item2 = await prisma.item.create({
                data: { name: "Bravo Item", tenantId: testTenant.id },
            });
            item3 = await prisma.item.create({
                data: { name: "Charlie Item - No WH", tenantId: testTenant.id },
            });
            await prisma.warehouseItem.create({
                data: {
                    itemId: item1.id,
                    warehouseId: testWarehouse1.id,
                    tenantId: testTenant.id,
                },
            });
            await prisma.warehouseItem.create({
                data: {
                    itemId: item2.id,
                    warehouseId: testWarehouse2.id,
                    tenantId: testTenant.id,
                },
            });
        });
        it("WAREHOUSE_ADMIN should find items associated with their warehouses in their tenant", async () => {
            const items = await service.findAll(mockWarehouseAdminUser);
            expect(items.length).toBe(1);
            expect(items[0].id).toBe(item1.id);
        });
        it("WAREHOUSE_MEMBER should find items associated with their warehouses in their tenant", async () => {
            const items = await service.findAll(mockWarehouseMemberUser);
            expect(items.length).toBe(1);
            expect(items[0].id).toBe(item2.id);
        });
        it("ADMIN should find all items associated with any of their warehouses in their tenant", async () => {
            const items = await service.findAll(mockAdminUser);
            expect(items.length).toBe(2);
            expect(items.map((i) => i.id).sort()).toEqual([item1.id, item2.id].sort());
        });
        it("should return empty array if user has no associated warehouses with items", async () => {
            const items = await service.findAll(mockUserNoWarehouses);
            expect(items).toEqual([]);
        });
        it("should return empty array if no items are associated with user's warehouses, even if items exist", async () => {
            const newWarehouse = await prisma.warehouse.create({
                data: { name: "Empty WH", tenantId: testTenant.id },
            });
            const userWithEmptyWarehouse = {
                ...mockWarehouseMemberUser,
                id: "empty-wh-user",
                warehouseUsers: [
                    { warehouseId: newWarehouse.id, role: client_1.Role.WAREHOUSE_MEMBER },
                ],
            };
            const items = await service.findAll(userWithEmptyWarehouse);
            expect(items).toEqual([]);
        });
    });
    describe("findOne()", () => {
        let itemLinkedToWh1;
        let itemLinkedToWh2;
        let itemNotLinked;
        beforeEach(async () => {
            itemLinkedToWh1 = await prisma.item.create({
                data: { name: "Item For WH1", tenantId: testTenant.id },
            });
            itemLinkedToWh2 = await prisma.item.create({
                data: { name: "Item For WH2", tenantId: testTenant.id },
            });
            itemNotLinked = await prisma.item.create({
                data: { name: "Unlinked Item", tenantId: testTenant.id },
            });
            await prisma.warehouseItem.create({
                data: {
                    itemId: itemLinkedToWh1.id,
                    warehouseId: testWarehouse1.id,
                    tenantId: testTenant.id,
                },
            });
            await prisma.warehouseItem.create({
                data: {
                    itemId: itemLinkedToWh2.id,
                    warehouseId: testWarehouse2.id,
                    tenantId: testTenant.id,
                },
            });
        });
        it("WAREHOUSE_ADMIN should find an item linked to their warehouse in their tenant", async () => {
            const found = await service.findOne(itemLinkedToWh1.id, mockWarehouseAdminUser);
            expect(found.id).toBe(itemLinkedToWh1.id);
        });
        it("WAREHOUSE_ADMIN should NOT find an item linked to another warehouse in their tenant", async () => {
            await expect(service.findOne(itemLinkedToWh2.id, mockWarehouseAdminUser)).rejects.toThrow(common_1.NotFoundException);
        });
        it("WAREHOUSE_ADMIN should NOT find an unlinked item (even if it exists globally)", async () => {
            await expect(service.findOne(itemNotLinked.id, mockWarehouseAdminUser)).rejects.toThrow(common_1.NotFoundException);
        });
        it("ADMIN should find an item linked to any of their warehouses in their tenant", async () => {
            const found1 = await service.findOne(itemLinkedToWh1.id, mockAdminUser);
            expect(found1.id).toBe(itemLinkedToWh1.id);
            const found2 = await service.findOne(itemLinkedToWh2.id, mockAdminUser);
            expect(found2.id).toBe(itemLinkedToWh2.id);
        });
        it("should throw NotFoundException if item ID does not exist globally", async () => {
            await expect(service.findOne("non-existent-id", mockAdminUser)).rejects.toThrow(common_1.NotFoundException);
        });
        it("User with no warehouses should not find any item", async () => {
            await expect(service.findOne(itemLinkedToWh1.id, mockUserNoWarehouses)).rejects.toThrow(common_1.NotFoundException);
        });
    });
    describe("update()", () => {
        let itemToUpdate;
        const updateDto = { name: "Updated Item Name" };
        beforeEach(async () => {
            itemToUpdate = await prisma.item.create({
                data: {
                    name: "Original Name",
                    sku: "UPDATE_SKU_001",
                    tenantId: testTenant.id,
                },
            });
        });
        it("ADMIN should update master item data", async () => {
            const updated = await service.update(itemToUpdate.id, updateDto, mockAdminUser);
            expect(updated.name).toBe(updateDto.name);
            const dbItem = await prisma.item.findUnique({
                where: { id: itemToUpdate.id },
            });
            expect(dbItem.name).toBe(updateDto.name);
        });
        it("ADMIN should update item and sync warehouse associations within their tenant", async () => {
            const dtoWithWarehouses = {
                ...updateDto,
                name: "Updated and Associated",
                warehouseIds: [testWarehouse1.id],
            };
            const updated = await service.update(itemToUpdate.id, dtoWithWarehouses, mockAdminUser);
            expect(updated.name).toBe(dtoWithWarehouses.name);
            const warehouseItems = await prisma.warehouseItem.findMany({
                where: { itemId: itemToUpdate.id, tenantId: mockAdminUser.tenantId },
            });
            expect(warehouseItems.length).toBe(1);
            expect(warehouseItems[0].warehouseId).toBe(testWarehouse1.id);
            expect(warehouseItems[0].tenantId).toBe(mockAdminUser.tenantId);
            const dtoSwitchWarehouses = {
                warehouseIds: [testWarehouse2.id],
            };
            await service.update(itemToUpdate.id, dtoSwitchWarehouses, mockAdminUser);
            const finalWarehouseItems = await prisma.warehouseItem.findMany({
                where: { itemId: itemToUpdate.id, tenantId: mockAdminUser.tenantId },
                orderBy: { warehouseId: "asc" },
            });
            expect(finalWarehouseItems.length).toBe(1);
            expect(finalWarehouseItems[0].warehouseId).toBe(testWarehouse2.id);
            await service.update(itemToUpdate.id, { warehouseIds: [] }, mockAdminUser);
            const noWarehouseItems = await prisma.warehouseItem.findMany({
                where: { itemId: itemToUpdate.id, tenantId: mockAdminUser.tenantId },
            });
            expect(noWarehouseItems.length).toBe(0);
        });
        it("ADMIN should throw ForbiddenException if trying to associate item with a warehouse from another tenant", async () => {
            const otherTenant = await prisma.tenant.create({
                data: { name: "Other Tenant For Update" },
            });
            const otherTenantWarehouse = await prisma.warehouse.create({
                data: { name: "Other WH Update", tenantId: otherTenant.id },
            });
            const dtoWithInvalidWarehouse = {
                warehouseIds: [otherTenantWarehouse.id],
            };
            await expect(service.update(itemToUpdate.id, dtoWithInvalidWarehouse, mockAdminUser)).rejects.toThrow(common_1.ForbiddenException);
        });
        it("should throw ForbiddenException if non-ADMIN tries to update", async () => {
            await expect(service.update(itemToUpdate.id, updateDto, mockWarehouseAdminUser)).rejects.toThrow(common_1.ForbiddenException);
            await expect(service.update(itemToUpdate.id, updateDto, mockWarehouseMemberUser)).rejects.toThrow(common_1.ForbiddenException);
        });
        it("should throw NotFoundException if item to update does not exist", async () => {
            await expect(service.update("non-existent-id", updateDto, mockAdminUser)).rejects.toThrow(common_1.NotFoundException);
        });
        it("ADMIN should throw ForbiddenException if updating to a SKU that already exists globally on another item", async () => {
            const item1 = await service.create({ name: "Item SKU1", sku: "UNIQUE_SKU1" }, mockAdminUser);
            const item2 = await service.create({ name: "Item SKU2", sku: "UNIQUE_SKU2" }, mockAdminUser);
            await expect(service.update(item2.id, { sku: "UNIQUE_SKU1" }, mockAdminUser)).rejects.toThrow(common_1.ForbiddenException);
        });
    });
    describe("remove()", () => {
        let itemToRemove;
        beforeEach(async () => {
            itemToRemove = await prisma.item.create({
                data: { name: "Item to Delete", tenantId: testTenant.id },
            });
            await prisma.warehouseItem.create({
                data: {
                    itemId: itemToRemove.id,
                    warehouseId: testWarehouse1.id,
                    tenantId: mockAdminUser.tenantId,
                },
            });
            const otherTenant = await prisma.tenant.create({
                data: { name: "Other Tenant For Remove" },
            });
            const otherTenantWarehouse = await prisma.warehouse.create({
                data: { name: "Other WH Remove", tenantId: otherTenant.id },
            });
            await prisma.warehouseItem.create({
                data: {
                    itemId: itemToRemove.id,
                    warehouseId: otherTenantWarehouse.id,
                    tenantId: otherTenant.id,
                },
            });
        });
        it("ADMIN should delete an existing master item and its WarehouseItem associations globally", async () => {
            const warehouseItemCountBefore = await prisma.warehouseItem.count({
                where: { itemId: itemToRemove.id },
            });
            expect(warehouseItemCountBefore).toBe(2);
            await service.remove(itemToRemove.id, mockAdminUser);
            const dbItem = await prisma.item.findUnique({
                where: { id: itemToRemove.id },
            });
            expect(dbItem).toBeNull();
            const warehouseItemCountAfter = await prisma.warehouseItem.count({
                where: { itemId: itemToRemove.id },
            });
            expect(warehouseItemCountAfter).toBe(0);
        });
        it("should throw ForbiddenException if non-ADMIN tries to delete", async () => {
            await expect(service.remove(itemToRemove.id, mockWarehouseAdminUser)).rejects.toThrow(common_1.ForbiddenException);
            await expect(service.remove(itemToRemove.id, mockWarehouseMemberUser)).rejects.toThrow(common_1.ForbiddenException);
        });
        it("should throw NotFoundException if item to delete does not exist", async () => {
            await expect(service.remove("non-existent-id", mockAdminUser)).rejects.toThrow(common_1.NotFoundException);
        });
    });
});
//# sourceMappingURL=items.service.spec.js.map