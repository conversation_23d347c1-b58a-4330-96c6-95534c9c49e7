import { LocationType, LocationCategory } from '@prisma/client';
export declare class LocationWarehouseDto {
    id: string;
    name: string;
    address?: string;
}
export declare class LocationPalletDto {
    id: string;
    barcode: string;
    status: string;
    shipToDestination?: string;
}
export declare class LocationResponseDto {
    id: string;
    name: string;
    locationType: LocationType;
    category: LocationCategory;
    status: string;
    createdAt: Date;
    updatedAt: Date;
    warehouse: LocationWarehouseDto;
    pallets?: LocationPalletDto[];
    palletCount?: number;
}
export declare class LocationListResponseDto {
    data: LocationResponseDto[];
    count: number;
    page?: number;
    limit?: number;
    totalPages?: number;
}
