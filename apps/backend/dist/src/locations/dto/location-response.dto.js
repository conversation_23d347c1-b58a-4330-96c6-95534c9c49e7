"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationListResponseDto = exports.LocationResponseDto = exports.LocationPalletDto = exports.LocationWarehouseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const client_1 = require("@prisma/client");
class LocationWarehouseDto {
}
exports.LocationWarehouseDto = LocationWarehouseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Warehouse ID' }),
    __metadata("design:type", String)
], LocationWarehouseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Warehouse name' }),
    __metadata("design:type", String)
], LocationWarehouseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Warehouse address' }),
    __metadata("design:type", String)
], LocationWarehouseDto.prototype, "address", void 0);
class LocationPalletDto {
}
exports.LocationPalletDto = LocationPalletDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Pallet ID' }),
    __metadata("design:type", String)
], LocationPalletDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Pallet barcode' }),
    __metadata("design:type", String)
], LocationPalletDto.prototype, "barcode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Pallet status' }),
    __metadata("design:type", String)
], LocationPalletDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Ship to destination' }),
    __metadata("design:type", String)
], LocationPalletDto.prototype, "shipToDestination", void 0);
class LocationResponseDto {
}
exports.LocationResponseDto = LocationResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Location ID' }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Location name' }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Location type',
        enum: client_1.LocationType
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "locationType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Location category',
        enum: client_1.LocationCategory
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Location status' }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Creation timestamp' }),
    __metadata("design:type", Date)
], LocationResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last update timestamp' }),
    __metadata("design:type", Date)
], LocationResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Warehouse information',
        type: LocationWarehouseDto
    }),
    __metadata("design:type", LocationWarehouseDto)
], LocationResponseDto.prototype, "warehouse", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Pallets currently at this location',
        type: [LocationPalletDto]
    }),
    __metadata("design:type", Array)
], LocationResponseDto.prototype, "pallets", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of pallets at this location' }),
    __metadata("design:type", Number)
], LocationResponseDto.prototype, "palletCount", void 0);
class LocationListResponseDto {
}
exports.LocationListResponseDto = LocationListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Array of locations',
        type: [LocationResponseDto]
    }),
    __metadata("design:type", Array)
], LocationListResponseDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of locations' }),
    __metadata("design:type", Number)
], LocationListResponseDto.prototype, "count", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Current page number' }),
    __metadata("design:type", Number)
], LocationListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of items per page' }),
    __metadata("design:type", Number)
], LocationListResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Total number of pages' }),
    __metadata("design:type", Number)
], LocationListResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=location-response.dto.js.map