"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
const locationWithWarehouse = client_1.Prisma.validator()({
    include: { warehouse: true },
});
let LocationsService = class LocationsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async checkWarehouseExists(warehouseId, tenantId) {
        const warehouse = await this.prisma.warehouse.findFirst({
            where: {
                id: warehouseId,
                tenantId,
            },
        });
        if (!warehouse) {
            throw new common_1.BadRequestException(`Warehouse with ID \"${warehouseId}\" not found within your tenant.`);
        }
    }
    async create(createLocationDto, currentUser) {
        const { name, warehouseId, locationType, status, category } = createLocationDto;
        await this.checkWarehouseExists(createLocationDto.warehouseId, currentUser.tenantId);
        if (currentUser.role !== client_1.Role.TENANT_ADMIN) {
            const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
            if (!userWarehouseIds.includes(createLocationDto.warehouseId)) {
                throw new common_1.BadRequestException(`You are not authorized to create locations in warehouse ID "${createLocationDto.warehouseId}".`);
            }
        }
        return this.prisma.location.create({
            data: {
                name,
                warehouseId,
                locationType,
                status,
                category,
            },
        });
    }
    async findAll(currentUser, type, warehouseId, search, category) {
        const whereClause = {
            warehouse: {
                tenantId: currentUser.tenantId,
            },
        };
        if (type) {
            if (!Object.values(client_1.LocationType).includes(type)) {
                throw new common_1.BadRequestException(`Invalid location type: ${type}`);
            }
            whereClause.locationType = type;
        }
        if (search) {
            whereClause.name = { contains: search, mode: "insensitive" };
        }
        if (category) {
            whereClause.category = category;
        }
        if (warehouseId) {
            if (currentUser.role !== client_1.Role.TENANT_ADMIN) {
                const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
                if (!userWarehouseIds.includes(warehouseId)) {
                    return [];
                }
            }
            whereClause.warehouseId = warehouseId;
        }
        else {
            if (currentUser.role !== client_1.Role.TENANT_ADMIN) {
                const warehouseIdsFilter = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
                if (warehouseIdsFilter.length === 0) {
                    return [];
                }
                whereClause.warehouseId = { in: warehouseIdsFilter };
            }
        }
        return this.prisma.location.findMany({
            where: whereClause,
            include: { warehouse: true },
            orderBy: [{ warehouse: { name: "asc" } }, { name: "asc" }],
        });
    }
    async findOne(id, currentUser) {
        const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
        const whereClause = {
            id,
            warehouse: {
                tenantId: currentUser.tenantId,
            },
        };
        if (currentUser.role !== client_1.Role.TENANT_ADMIN) {
            if (userWarehouseIds.length === 0) {
                throw new common_1.NotFoundException(`You do not have access to any warehouses.`);
            }
            whereClause.warehouseId = { in: userWarehouseIds };
        }
        const location = await this.prisma.location.findFirst({
            where: whereClause,
            include: { warehouse: true },
        });
        if (!location) {
            throw new common_1.NotFoundException(`Location with ID "${id}" not found or not accessible within your warehouses.`);
        }
        return location;
    }
    async update(id, updateLocationDto, currentUser) {
        const existingLocation = await this.findOne(id, currentUser);
        if (updateLocationDto.warehouseId &&
            updateLocationDto.warehouseId !== existingLocation.warehouseId) {
            await this.checkWarehouseExists(updateLocationDto.warehouseId, currentUser.tenantId);
            if (currentUser.role !== client_1.Role.TENANT_ADMIN) {
                const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
                if (!userWarehouseIds.includes(updateLocationDto.warehouseId)) {
                    throw new common_1.BadRequestException(`You are not authorized to move locations to warehouse ID "${updateLocationDto.warehouseId}".`);
                }
            }
        }
        const { tenantId, ...dtoWithoutTenantId } = updateLocationDto;
        try {
            return await this.prisma.location.update({
                where: {
                    id,
                },
                data: dtoWithoutTenantId,
            });
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError &&
                error.code === "P2025") {
                throw new common_1.NotFoundException(`Location with ID "${id}" not found within your warehouses to update.`);
            }
            throw new Error(`Could not update location: ${error.message}`);
        }
    }
    async remove(id, currentUser) {
        await this.findOne(id, currentUser);
        try {
            return await this.prisma.location.delete({
                where: {
                    id,
                },
            });
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError &&
                error.code === "P2003") {
                throw new common_1.BadRequestException("Cannot delete location because it has associated records (e.g., pallets).");
            }
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError &&
                error.code === "P2025") {
                throw new common_1.NotFoundException(`Location with ID "${id}" not found within your warehouses to delete.`);
            }
            throw error;
        }
    }
};
exports.LocationsService = LocationsService;
exports.LocationsService = LocationsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], LocationsService);
//# sourceMappingURL=locations.service.js.map