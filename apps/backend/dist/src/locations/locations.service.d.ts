import { PrismaService } from "../prisma/prisma.service";
import { CreateLocationDto } from "./dto/create-location.dto";
import { UpdateLocationDto } from "./dto/update-location.dto";
import { Location, Prisma, LocationCategory } from "@prisma/client";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";
declare const locationWithWarehouse: {
    include: {
        warehouse: true;
    };
};
type LocationWithWarehouse = Prisma.LocationGetPayload<typeof locationWithWarehouse>;
export declare class LocationsService {
    private prisma;
    constructor(prisma: PrismaService);
    private checkWarehouseExists;
    create(createLocationDto: CreateLocationDto, currentUser: AuthenticatedUser): Promise<Location>;
    findAll(currentUser: AuthenticatedUser, type?: string, warehouseId?: string, search?: string, category?: LocationCategory): Promise<LocationWithWarehouse[]>;
    findOne(id: string, currentUser: AuthenticatedUser): Promise<LocationWithWarehouse>;
    update(id: string, updateLocationDto: UpdateLocationDto, currentUser: AuthenticatedUser): Promise<Location>;
    remove(id: string, currentUser: AuthenticatedUser): Promise<Location>;
}
export {};
