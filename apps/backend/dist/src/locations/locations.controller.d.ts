import { LocationsService } from "./locations.service";
import { CreateLocationDto } from "./dto/create-location.dto";
import { UpdateLocationDto } from "./dto/update-location.dto";
import { RequestWithWarehouseContext } from "../auth/decorators/warehouse-permission.decorator";
import { EnhancedUserPayload } from "../auth/types";
import { LocationCategory } from "@prisma/client";
export declare class LocationsController {
    private readonly locationsService;
    constructor(locationsService: LocationsService);
    create(createLocationDto: CreateLocationDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        name: string;
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        warehouseId: string;
        category: import("@prisma/client").$Enums.LocationCategory;
        locationType: import("@prisma/client").$Enums.LocationType;
    }>;
    findAll(req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>, type?: string, warehouseId?: string, search?: string, category?: LocationCategory): Promise<({
        warehouse: {
            name: string;
            status: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            tenantId: string;
            address: string | null;
        };
    } & {
        name: string;
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        warehouseId: string;
        category: import("@prisma/client").$Enums.LocationCategory;
        locationType: import("@prisma/client").$Enums.LocationType;
    })[]>;
    findOne(id: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        warehouse: {
            name: string;
            status: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            tenantId: string;
            address: string | null;
        };
    } & {
        name: string;
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        warehouseId: string;
        category: import("@prisma/client").$Enums.LocationCategory;
        locationType: import("@prisma/client").$Enums.LocationType;
    }>;
    update(id: string, updateLocationDto: UpdateLocationDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        name: string;
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        warehouseId: string;
        category: import("@prisma/client").$Enums.LocationCategory;
        locationType: import("@prisma/client").$Enums.LocationType;
    }>;
    remove(id: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        name: string;
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        warehouseId: string;
        category: import("@prisma/client").$Enums.LocationCategory;
        locationType: import("@prisma/client").$Enums.LocationType;
    }>;
}
