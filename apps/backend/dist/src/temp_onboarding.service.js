"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnboardingService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("@/prisma/prisma.service");
const client_1 = require("@prisma/client");
let OnboardingService = exports.OnboardingService = class OnboardingService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async completeProfile(userId, completeProfileDto) {
        const { companyName } = completeProfileDto;
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found.');
        }
        if (user.tenantId) {
            throw new common_1.ForbiddenException('User already has a tenant. Onboarding already completed or not applicable.');
        }
        try {
            const newTenant = await this.prisma.tenant.create({
                data: {
                    name: companyName,
                },
            });
            const updatedUser = await this.prisma.user.update({
                where: { id: userId },
                data: {
                    tenantId: newTenant.id,
                    role: client_1.Role.TENANT_ADMIN,
                    name: user.name || companyName,
                },
            });
            const { password, ...userWithoutPassword } = updatedUser;
            return userWithoutPassword;
        }
        catch (error) {
            console.error('Error during completeProfile:', error);
            throw new common_1.InternalServerErrorException('Failed to complete profile and create tenant.');
        }
    }
};
exports.OnboardingService = OnboardingService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof prisma_service_1.PrismaService !== "undefined" && prisma_service_1.PrismaService) === "function" ? _a : Object])
], OnboardingService);
//# sourceMappingURL=temp_onboarding.service.js.map