import { OnboardingService } from './onboarding.service';
import { CompleteProfileDto } from './dto/complete-profile.dto';
import { AuthenticatedRequest } from '@/auth/types';
export declare class OnboardingController {
    private readonly onboardingService;
    constructor(onboardingService: OnboardingService);
    completeProfile(req: AuthenticatedRequest, completeProfileDto: CompleteProfileDto): Promise<any>;
}
