"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cleanupDatabase = cleanupDatabase;
async function cleanupDatabase(prisma) {
    const tablenames = await prisma.$queryRaw `SELECT tablename FROM pg_tables WHERE schemaname='public'`;
    const tables = tablenames
        .map(({ tablename }) => tablename)
        .filter((name) => name !== "_prisma_migrations")
        .map((name) => `"${name}"`);
    if (tables.length === 0) {
        console.log("No tables found to clean.");
        return;
    }
    try {
        const truncateQuery = `TRUNCATE TABLE ${tables.join(", ")} RESTART IDENTITY CASCADE;`;
        await prisma.$executeRawUnsafe(truncateQuery);
    }
    catch (error) {
        console.error("Error cleaning database:", error);
        throw new Error("Could not clean database");
    }
}
//# sourceMappingURL=cleanup-database.js.map