"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const morgan_1 = __importDefault(require("morgan"));
const rotating_file_stream_1 = __importDefault(require("rotating-file-stream"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const logDirectory = path_1.default.resolve(__dirname, '../../log');
fs_1.default.existsSync(logDirectory) || fs_1.default.mkdirSync(logDirectory);
const accessLogStream = (0, rotating_file_stream_1.default)('access.log', {
    interval: '1d',
    path: logDirectory
});
exports.default = {
    dev: (0, morgan_1.default)('dev'),
    combined: (0, morgan_1.default)('combined', { stream: accessLogStream })
};
//# sourceMappingURL=logger.js.map