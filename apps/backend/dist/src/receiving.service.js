"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReceivingService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("./prisma/prisma.service");
const client_1 = require("@prisma/client");
const audit_log_service_1 = require("./audit-log/audit-log.service");
const palletWithItems = client_1.Prisma.validator()({
    include: { palletItems: true },
});
let ReceivingService = class ReceivingService {
    constructor(prisma, auditLogService) {
        this.prisma = prisma;
        this.auditLogService = auditLogService;
    }
    async receiveItems(dto, currentUser) {
        const { poNumber, receivingLocationId, items, barcode, shipToDestination, destinationCode, description, } = dto;
        if ((!items || items.length === 0) &&
            (!description || description.trim().length === 0)) {
            throw new common_1.BadRequestException("A pallet must have at least one item or a description.");
        }
        const location = await this.prisma.location.findUnique({
            where: { id: receivingLocationId },
            include: { warehouse: { select: { tenantId: true, id: true } } },
        });
        if (!location) {
            throw new common_1.NotFoundException(`Receiving location with ID "${receivingLocationId}" not found.`);
        }
        if (location.category !== client_1.LocationCategory.Receiving) {
            throw new common_1.BadRequestException("Invalid location. Pallets can only be received at a Receiving location.");
        }
        if (location.warehouse.tenantId !== currentUser.tenantId) {
            throw new common_1.BadRequestException(`Receiving location with ID "${receivingLocationId}" is not in your tenant.`);
        }
        const purchaseOrder = await this.prisma.purchaseOrder.upsert({
            where: {
                tenantId_poNumber: {
                    tenantId: currentUser.tenantId,
                    poNumber: poNumber,
                },
            },
            update: {
                status: "Receiving",
                warehouseId: location.warehouse.id,
            },
            create: {
                poNumber: poNumber,
                tenantId: currentUser.tenantId,
                warehouseId: location.warehouse.id,
                status: "Receiving",
                shipments: {
                    create: {
                        status: "Processing",
                        tenantId: currentUser.tenantId,
                    },
                },
            },
            include: {
                shipments: true,
            },
        });
        if (!purchaseOrder.shipments || purchaseOrder.shipments.length === 0) {
            throw new Error("Failed to find or create a shipment for the purchase order.");
        }
        const shipmentId = purchaseOrder.shipments[0].id;
        if (currentUser.role !== "TENANT_ADMIN") {
            const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
            if (!userWarehouseIds.includes(location.warehouseId)) {
                throw new common_1.BadRequestException(`You are not authorized to receive items into warehouse ID "${location.warehouseId}".`);
            }
        }
        const itemIds = items.map((item) => item.itemId);
        if (itemIds.length > 0) {
            const foundItems = await this.prisma.item.findMany({
                where: {
                    id: { in: itemIds },
                    tenantId: currentUser.tenantId,
                },
                select: { id: true },
            });
            if (foundItems.length !== itemIds.length) {
                const foundItemIds = new Set(foundItems.map((item) => item.id));
                const missingItemIds = itemIds.filter((id) => !foundItemIds.has(id));
                throw new common_1.NotFoundException(`Items with IDs not found: ${missingItemIds.join(", ")}`);
            }
        }
        return this.prisma.$transaction(async (tx) => {
            if (items && items.length > 0) {
                for (const itemDetail of items) {
                    await tx.warehouseItem.upsert({
                        where: {
                            tenant_warehouse_item_unique: {
                                tenantId: currentUser.tenantId,
                                warehouseId: location.warehouseId,
                                itemId: itemDetail.itemId,
                            },
                        },
                        update: {},
                        create: {
                            tenantId: currentUser.tenantId,
                            warehouseId: location.warehouseId,
                            itemId: itemDetail.itemId,
                        },
                    });
                }
            }
            const newPallet = await tx.pallet.create({
                data: {
                    barcode,
                    label: `Pallet ${barcode}`,
                    description,
                    shipToDestination,
                    destinationCode,
                    status: "Receiving",
                    locationId: receivingLocationId,
                    shipmentId: shipmentId,
                    palletItems: {
                        create: items.map((item) => ({
                            itemId: item.itemId,
                            quantity: item.quantity,
                        })),
                    },
                },
                include: { palletItems: true },
            });
            await this.auditLogService.create({
                userId: currentUser.id,
                userEmail: currentUser.email,
                action: "CREATE_PALLET_RECEIVING",
                entity: "Pallet",
                entityId: newPallet.id,
                tenantId: currentUser.tenantId,
                details: {
                    barcode: newPallet.barcode,
                    label: newPallet.label,
                    description: newPallet.description,
                    shipToDestination: newPallet.shipToDestination,
                    status: newPallet.status,
                    locationName: location.name,
                    poNumber: poNumber,
                    itemCount: items.length,
                    createdVia: "receiving_workflow",
                },
            }, tx);
            return newPallet;
        });
    }
};
exports.ReceivingService = ReceivingService;
exports.ReceivingService = ReceivingService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        audit_log_service_1.AuditLogService])
], ReceivingService);
//# sourceMappingURL=receiving.service.js.map