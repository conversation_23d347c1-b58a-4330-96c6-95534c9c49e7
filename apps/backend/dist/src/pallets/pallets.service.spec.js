"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const testing_1 = require("@nestjs/testing");
const prisma_service_1 = require("../prisma/prisma.service");
const pallets_service_1 = require("./pallets.service");
const pallets_module_1 = require("./pallets.module");
const prisma_module_1 = require("../prisma/prisma.module");
const db_cleanup_1 = require("../../test/utils/db-cleanup");
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const mockTenantId = "test-tenant-id-pallets";
const mockCurrentUser = {
    id: "test-user-pallet-id",
    email: "<EMAIL>",
    role: client_1.Role.WAREHOUSE_MANAGER,
    tenantId: mockTenantId,
    name: "Pallet Test User",
    authUserId: "pallet-test-auth-user-id",
    warehouseUsers: [],
};
describe("PalletsService Integration", () => {
    let service;
    let prisma;
    let module;
    let testWarehouse;
    let testLocation1;
    let testLocation2;
    let testItem;
    beforeAll(async () => { });
    afterAll(async () => {
        await module?.close();
    });
    beforeEach(async () => {
        module = await testing_1.Test.createTestingModule({
            imports: [prisma_module_1.PrismaModule, pallets_module_1.PalletsModule],
        }).compile();
        prisma = module.get(prisma_service_1.PrismaService);
        service = module.get(pallets_service_1.PalletsService);
        await (0, db_cleanup_1.cleanupDatabase)(prisma);
        testWarehouse = await prisma.warehouse.create({
            data: {
                name: "Pallet Test WH",
                tenantId: mockCurrentUser.tenantId,
            },
        });
        testLocation1 = await prisma.location.create({
            data: {
                name: "Pallet Test Loc 1",
                locationType: client_1.LocationType.ZONE,
                warehouseId: testWarehouse.id,
            },
        });
        testLocation2 = await prisma.location.create({
            data: {
                name: "Pallet Test Loc 2",
                locationType: client_1.LocationType.DOCK,
                category: client_1.LocationCategory.Receiving,
                warehouseId: testWarehouse.id,
            },
        });
        testItem = await prisma.item.create({
            data: {
                name: "Pallet Test Item",
                tenantId: mockCurrentUser.tenantId,
            },
        });
        mockCurrentUser.warehouseUsers = [
            { warehouseId: testWarehouse.id, role: client_1.Role.WAREHOUSE_MEMBER },
        ];
    });
    it("should be defined", () => {
        expect(service).toBeDefined();
    });
    describe("create()", () => {
        it("should create a new pallet with a specific status and location within the tenant", async () => {
            const createDto = {
                status: "Receiving",
                locationId: testLocation1.id,
            };
            const createdPallet = await service.create(createDto, mockCurrentUser);
            expect(createdPallet).toBeDefined();
            expect(createdPallet.id).toBeDefined();
            expect(createdPallet.status).toEqual("Receiving");
            expect(createdPallet.locationId).toEqual(testLocation1.id);
            const dbPallet = await prisma.pallet.findUnique({
                where: { id: createdPallet.id },
                include: { location: { include: { warehouse: true } } },
            });
            expect(dbPallet).toBeDefined();
            expect(dbPallet.status).toEqual("Receiving");
            expect(dbPallet.locationId).toEqual(testLocation1.id);
            expect(dbPallet.location.warehouse.tenantId).toEqual(mockCurrentUser.tenantId);
        });
        it("should throw BadRequestException if locationId is not provided", async () => {
            const createDto = { status: "Empty" };
            await expect(service.create(createDto, mockCurrentUser)).rejects.toThrow(common_1.BadRequestException);
            await expect(service.create(createDto, mockCurrentUser)).rejects.toThrow("Initial Location ID is required to create a pallet.");
        });
        it("should throw BadRequestException if locationId does not exist in the current tenant", async () => {
            const nonExistentLocationId = "clxxxxxxxxxnonexistent";
            const createDto = { locationId: nonExistentLocationId };
            await expect(service.create(createDto, mockCurrentUser)).rejects.toThrow(common_1.BadRequestException);
            await expect(service.create(createDto, mockCurrentUser)).rejects.toThrow(`Location with ID \"${nonExistentLocationId}\" not found within your tenant.`);
        });
        it("should throw BadRequestException if user not authorized for warehouse of the location", async () => {
            const anotherWarehouse = await prisma.warehouse.create({
                data: { name: "Another WH", tenantId: mockCurrentUser.tenantId },
            });
            const locationInAnotherWarehouse = await prisma.location.create({
                data: {
                    name: "Loc in Another WH",
                    locationType: client_1.LocationType.ZONE,
                    warehouseId: anotherWarehouse.id,
                },
            });
            const createDto = {
                locationId: locationInAnotherWarehouse.id,
            };
            await expect(service.create(createDto, mockCurrentUser)).rejects.toThrow(common_1.BadRequestException);
            await expect(service.create(createDto, mockCurrentUser)).rejects.toThrow(`You are not authorized to create pallets in the warehouse of location ID \"${locationInAnotherWarehouse.id}\".`);
        });
    });
    describe("findAll()", () => {
        it("should return pallets from user's tenant and accessible warehouses", async () => {
            const pallet1 = await service.create({ locationId: testLocation1.id }, mockCurrentUser);
            const otherWarehouse = await prisma.warehouse.create({
                data: { name: "OtherFindAllWH", tenantId: mockCurrentUser.tenantId },
            });
            const locationInOtherWarehouse = await prisma.location.create({
                data: {
                    name: "LocOtherFindAllWH",
                    warehouseId: otherWarehouse.id,
                    locationType: client_1.LocationType.ZONE,
                },
            });
            await prisma.pallet.create({
                data: {
                    label: "Pallet In Other WH For FindAll",
                    locationId: locationInOtherWarehouse.id,
                    barcode: `FINDALL-PALLET-${Date.now()}`,
                    shipToDestination: "FindAll Destination",
                },
            });
            const otherTenant = await prisma.tenant.create({
                data: { name: "Other Tenant" },
            });
            const otherTenantWarehouse = await prisma.warehouse.create({
                data: { name: "Other Tenant WH", tenantId: otherTenant.id },
            });
            const otherTenantLocation = await prisma.location.create({
                data: {
                    name: "Other Tenant Loc",
                    warehouseId: otherTenantWarehouse.id,
                    locationType: client_1.LocationType.ZONE,
                },
            });
            await prisma.pallet.create({
                data: {
                    label: "Other Tenant Pallet For FindAll",
                    locationId: otherTenantLocation.id,
                    barcode: `OTHER-TENANT-FINDALL-${Date.now()}`,
                    shipToDestination: "Other Tenant Destination",
                },
            });
            const pallets = await service.findAll(mockCurrentUser, {});
            expect(pallets.length).toBe(1);
            expect(pallets[0].id).toEqual(pallet1.id);
        });
        it("should return an empty array if no pallets exist in accessible locations/tenant", async () => {
            const pallets = await service.findAll(mockCurrentUser, {});
            expect(pallets).toEqual([]);
        });
    });
    describe("findOne()", () => {
        it("should return a pallet by ID if in user's tenant and accessible warehouse", async () => {
            const createdPallet = await service.create({ locationId: testLocation1.id }, mockCurrentUser);
            await prisma.palletItem.create({
                data: {
                    palletId: createdPallet.id,
                    itemId: testItem.id,
                    quantity: 5,
                },
            });
            const foundPallet = (await service.findOne(createdPallet.id, mockCurrentUser));
            expect(foundPallet).toBeDefined();
            expect(foundPallet.id).toEqual(createdPallet.id);
        });
        it("should throw NotFoundException if pallet ID does not exist in tenant", async () => {
            const nonExistentId = "clxxxxxxxxxnonexistent";
            await expect(service.findOne(nonExistentId, mockCurrentUser)).rejects.toThrow(common_1.NotFoundException);
        });
        it("should throw NotFoundException if pallet belongs to another tenant", async () => {
            const otherTenant = await prisma.tenant.create({
                data: { name: "Other Tenant findOne" },
            });
            const otherTenantWarehouse = await prisma.warehouse.create({
                data: { name: "OTF1 WH", tenantId: otherTenant.id },
            });
            const otherTenantLocation = await prisma.location.create({
                data: {
                    name: "OTF1 Loc",
                    warehouseId: otherTenantWarehouse.id,
                    locationType: client_1.LocationType.ZONE,
                },
            });
            const otherTenantPallet = await prisma.pallet.create({
                data: {
                    label: "Other Tenant Pallet For FindOne",
                    locationId: otherTenantLocation.id,
                    barcode: `OTHER-TENANT-FINDONE-${Date.now()}`,
                    shipToDestination: "Other Tenant FindOne Destination",
                },
            });
            await expect(service.findOne(otherTenantPallet.id, mockCurrentUser)).rejects.toThrow(common_1.NotFoundException);
        });
        it("should throw NotFoundException if pallet is in an inaccessible warehouse within the same tenant", async () => {
            const inaccessibleWarehouse = await prisma.warehouse.create({
                data: { name: "InaccessibleWH", tenantId: mockCurrentUser.tenantId },
            });
            const locationInInaccessibleWarehouse = await prisma.location.create({
                data: {
                    name: "LocInaccWH",
                    warehouseId: inaccessibleWarehouse.id,
                    locationType: client_1.LocationType.ZONE,
                },
            });
            const palletInInaccessibleWarehouse = await prisma.pallet.create({
                data: {
                    label: "Inaccessible WH Pallet For FindOne",
                    locationId: locationInInaccessibleWarehouse.id,
                    barcode: `INACCESSIBLE-FINDONE-${Date.now()}`,
                    shipToDestination: "Inaccessible FindOne Destination",
                },
            });
            await expect(service.findOne(palletInInaccessibleWarehouse.id, mockCurrentUser)).rejects.toThrow(common_1.NotFoundException);
        });
    });
    describe("update()", () => {
        it("should update pallet's status and location if accessible", async () => {
            const createdPallet = await service.create({ locationId: testLocation1.id }, mockCurrentUser);
            const updateDto = {
                status: "Stored",
                locationId: testLocation2.id,
            };
            const updatedPallet = await service.update(createdPallet.id, updateDto, mockCurrentUser);
            expect(updatedPallet).toBeDefined();
            expect(updatedPallet.status).toEqual("Stored");
            expect(updatedPallet.locationId).toEqual(testLocation2.id);
        });
        it("should throw NotFoundException if pallet to update does not exist in tenant", async () => {
            const nonExistentId = "clxxxxxxxxxnonexistent";
            await expect(service.update(nonExistentId, { status: "Picking" }, mockCurrentUser)).rejects.toThrow(common_1.NotFoundException);
        });
        it("should throw BadRequestException if updating to a non-existent locationId in tenant", async () => {
            const createdPallet = await service.create({ locationId: testLocation1.id }, mockCurrentUser);
            const nonExistentLocationId = "clxxxxxxxxxnonexistent";
            const updateDto = { locationId: nonExistentLocationId };
            await expect(service.update(createdPallet.id, updateDto, mockCurrentUser)).rejects.toThrow(common_1.BadRequestException);
        });
        it("should throw BadRequestException if updating to a location in an inaccessible warehouse", async () => {
            const createdPallet = await service.create({ locationId: testLocation1.id }, mockCurrentUser);
            const inaccessibleWarehouse = await prisma.warehouse.create({
                data: { name: "UpdateInaccWH", tenantId: mockCurrentUser.tenantId },
            });
            const locationInInaccessibleWarehouse = await prisma.location.create({
                data: {
                    name: "UpdateLocInaccWH",
                    warehouseId: inaccessibleWarehouse.id,
                    locationType: client_1.LocationType.ZONE,
                },
            });
            const updateDto = {
                locationId: locationInInaccessibleWarehouse.id,
            };
            await expect(service.update(createdPallet.id, updateDto, mockCurrentUser)).rejects.toThrow(common_1.BadRequestException);
        });
    });
    describe("remove()", () => {
        it("should delete an existing pallet if accessible and remove its items", async () => {
            const createdPallet = await service.create({ locationId: testLocation1.id }, mockCurrentUser);
            await prisma.palletItem.create({
                data: {
                    palletId: createdPallet.id,
                    itemId: testItem.id,
                    quantity: 10,
                },
            });
            await service.remove(createdPallet.id, mockCurrentUser);
            const dbPallet = await prisma.pallet.findUnique({
                where: { id: createdPallet.id },
            });
            expect(dbPallet).toBeNull();
            const dbPalletItems = await prisma.palletItem.findMany({
                where: {
                    palletId: createdPallet.id,
                },
            });
            expect(dbPalletItems.length).toBe(0);
        });
        it("should throw NotFoundException if pallet to delete does not exist in tenant", async () => {
            const nonExistentId = "clxxxxxxxxxnonexistent";
            await expect(service.remove(nonExistentId, mockCurrentUser)).rejects.toThrow(common_1.NotFoundException);
        });
        describe("shipToDestination", () => {
            it("should create a pallet with shipToDestination", async () => {
                const createDto = {
                    locationId: testLocation1.id,
                    shipToDestination: "Warehouse A",
                };
                const createdPallet = await service.create(createDto, mockCurrentUser);
                expect(createdPallet).toBeDefined();
                expect(createdPallet.shipToDestination).toEqual("Warehouse A");
                const dbPallet = await prisma.pallet.findUnique({
                    where: { id: createdPallet.id },
                    select: { shipToDestination: true },
                });
                expect(dbPallet).toBeDefined();
                expect(dbPallet.shipToDestination).toEqual("Warehouse A");
            });
            it("should update pallet's shipToDestination", async () => {
                const createDto = {
                    locationId: testLocation1.id,
                };
                const createdPallet = await service.create(createDto, mockCurrentUser);
                const updateDto = {
                    shipToDestination: "New Warehouse",
                };
                const updatedPallet = await service.update(createdPallet.id, updateDto, mockCurrentUser);
                expect(updatedPallet.shipToDestination).toEqual("New Warehouse");
                const dbPallet = await prisma.pallet.findUnique({
                    where: { id: createdPallet.id },
                    select: { shipToDestination: true },
                });
                expect(dbPallet).toBeDefined();
                expect(dbPallet.shipToDestination).toEqual("New Warehouse");
            });
            it("should clear shipToDestination when updating with null", async () => {
                const createDto = {
                    locationId: testLocation1.id,
                    shipToDestination: "Initial Warehouse",
                };
                const createdPallet = await service.create(createDto, mockCurrentUser);
                const updateDto = {
                    shipToDestination: null,
                };
                const updatedPallet = await service.update(createdPallet.id, updateDto, mockCurrentUser);
                expect(updatedPallet.shipToDestination).toBeNull();
                const dbPallet = await prisma.pallet.findUnique({
                    where: { id: createdPallet.id },
                    select: { shipToDestination: true },
                });
                expect(dbPallet).toBeDefined();
                expect(dbPallet.shipToDestination).toBeNull();
            });
            it("should find pallets by shipToDestination", async () => {
                const pallet1 = await service.create({ locationId: testLocation1.id, shipToDestination: "Warehouse A" }, mockCurrentUser);
                const pallet2 = await service.create({ locationId: testLocation1.id, shipToDestination: "Warehouse B" }, mockCurrentUser);
                const pallet3 = await service.create({ locationId: testLocation1.id }, mockCurrentUser);
                const palletsWithDestA = await service.findAll(mockCurrentUser, {
                    shipToDestination: "Warehouse A",
                });
                expect(palletsWithDestA.length).toBe(1);
                expect(palletsWithDestA[0].id).toBe(pallet1.id);
                const palletsWithNoDest = await service.findAll(mockCurrentUser, {
                    shipToDestination: null,
                });
                expect(palletsWithNoDest.length).toBe(1);
                expect(palletsWithNoDest[0].id).toBe(pallet3.id);
            });
        });
    });
});
//# sourceMappingURL=pallets.service.spec.js.map