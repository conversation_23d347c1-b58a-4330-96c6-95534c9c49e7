"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var PlacardPdfService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlacardPdfService = void 0;
const common_1 = require("@nestjs/common");
const pdf_lib_1 = require("pdf-lib");
const bwip = __importStar(require("bwip-js"));
let PlacardPdfService = PlacardPdfService_1 = class PlacardPdfService {
    constructor() {
        this.logger = new common_1.Logger(PlacardPdfService_1.name);
    }
    wrapText(text, font, fontSize, maxWidth) {
        if (!text)
            return [];
        const words = text.split(" ");
        const lines = [];
        let currentLine = "";
        for (const word of words) {
            const testLine = currentLine ? `${currentLine} ${word}` : word;
            const testWidth = font.widthOfTextAtSize(testLine, fontSize);
            if (testWidth <= maxWidth) {
                currentLine = testLine;
            }
            else {
                if (currentLine) {
                    lines.push(currentLine);
                    currentLine = word;
                }
                else {
                    lines.push(word);
                }
            }
        }
        if (currentLine) {
            lines.push(currentLine);
        }
        return lines;
    }
    hasContent(value) {
        return (value !== null && value !== undefined && value !== "" && value !== "N/A");
    }
    async createPlacardPage(pdfDoc, placardData, palletCount) {
        try {
            this.logger.log(`Creating placard page for pallet ${placardData.palletId}`);
            const page = pdfDoc.addPage([pdf_lib_1.PageSizes.Letter[1], pdf_lib_1.PageSizes.Letter[0]]);
            const { width, height } = page.getSize();
            const margin = 36;
            const font = await pdfDoc.embedFont(pdf_lib_1.StandardFonts.Helvetica);
            const fontBold = await pdfDoc.embedFont(pdf_lib_1.StandardFonts.HelveticaBold);
            const fontMono = await pdfDoc.embedFont(pdf_lib_1.StandardFonts.Courier);
            const headerY = height - margin - 20;
            page.drawText("LOCATION", {
                x: margin,
                y: headerY,
                font: fontBold,
                size: 14,
            });
            page.drawText(placardData.currentLocation.name, {
                x: margin,
                y: headerY - 35,
                font: fontBold,
                size: 28,
            });
            const rightEdge = width - margin;
            let currentY = headerY;
            if (placardData.contents && placardData.contents.length > 0) {
                const itemsLabelWidth = fontBold.widthOfTextAtSize("ITEMS", 14);
                page.drawText("ITEMS", {
                    x: rightEdge - itemsLabelWidth,
                    y: currentY,
                    font: fontBold,
                    size: 14,
                });
                currentY -= 25;
                const maxItemsToShow = 6;
                const itemsToShow = placardData.contents.slice(0, maxItemsToShow);
                for (const item of itemsToShow) {
                    const itemText = `${item.quantity}x ${item.itemName}`;
                    const itemTextWidth = font.widthOfTextAtSize(itemText, 12);
                    page.drawText(itemText, {
                        x: rightEdge - itemTextWidth,
                        y: currentY,
                        font: font,
                        size: 12,
                    });
                    currentY -= 16;
                }
                if (placardData.contents.length > maxItemsToShow) {
                    const ellipsisWidth = font.widthOfTextAtSize("...", 12);
                    page.drawText("...", {
                        x: rightEdge - ellipsisWidth,
                        y: currentY,
                        font: font,
                        size: 12,
                    });
                }
            }
            const palletCountWidth = fontBold.widthOfTextAtSize(palletCount, 18);
            page.drawText(palletCount, {
                x: (width - palletCountWidth) / 2,
                y: headerY - 60,
                font: fontBold,
                size: 18,
            });
            page.drawLine({
                start: { x: margin, y: headerY - 80 },
                end: { x: width - margin, y: headerY - 80 },
                thickness: 2,
                color: (0, pdf_lib_1.rgb)(0, 0, 0),
                opacity: 0.5,
            });
            const dateCodeY = headerY - 110;
            const destinationCodeFontSize = 26;
            if (placardData.dateCreated) {
                const dateObj = new Date(placardData.dateCreated);
                const formattedDate = dateObj.toLocaleDateString("en-US", {
                    month: "2-digit",
                    day: "2-digit",
                    year: "numeric",
                });
                page.drawText(formattedDate, {
                    x: margin,
                    y: dateCodeY,
                    font: font,
                    size: destinationCodeFontSize,
                });
            }
            if (this.hasContent(placardData.destinationCode)) {
                const rightEdge = width - margin;
                const codeWidth = font.widthOfTextAtSize(placardData.destinationCode, destinationCodeFontSize);
                page.drawText(placardData.destinationCode, {
                    x: rightEdge - codeWidth,
                    y: dateCodeY,
                    font: font,
                    size: destinationCodeFontSize,
                });
            }
            const shipToY = height / 2 - 20;
            const maxDestinationWidth = width - margin * 2;
            const destinationFontSize = 96;
            const destinationText = placardData.shipToDestination || "";
            const singleLineWidth = fontBold.widthOfTextAtSize(destinationText, destinationFontSize);
            if (singleLineWidth <= maxDestinationWidth) {
                page.drawText(destinationText, {
                    x: (width - singleLineWidth) / 2,
                    y: shipToY,
                    font: fontBold,
                    size: destinationFontSize,
                });
            }
            else {
                const wrappedLines = this.wrapText(destinationText, fontBold, destinationFontSize, maxDestinationWidth);
                const lineHeight = destinationFontSize + 10;
                const totalTextHeight = wrappedLines.length * lineHeight;
                let startY = shipToY + totalTextHeight / 2;
                for (let i = 0; i < wrappedLines.length; i++) {
                    const line = wrappedLines[i];
                    const lineWidth = fontBold.widthOfTextAtSize(line, destinationFontSize);
                    page.drawText(line, {
                        x: (width - lineWidth) / 2,
                        y: startY - i * lineHeight,
                        font: fontBold,
                        size: destinationFontSize,
                    });
                }
            }
            const footerY = margin + 140;
            page.drawLine({
                start: { x: margin, y: footerY },
                end: { x: width - margin, y: footerY },
                thickness: 2,
                color: (0, pdf_lib_1.rgb)(0, 0, 0),
                opacity: 0.5,
            });
            const barcodePng = await bwip.toBuffer({
                bcid: "code128",
                text: placardData.barcode,
                scale: 3,
                height: 15,
                includetext: false,
                textxalign: "center",
            });
            const barcodeImage = await pdfDoc.embedPng(barcodePng);
            const desiredBarcodeHeight = 75;
            const scaleFactor = desiredBarcodeHeight / barcodeImage.height;
            const scaledWidth = barcodeImage.width * scaleFactor;
            page.drawImage(barcodeImage, {
                x: (width - scaledWidth) / 2,
                y: footerY - 110,
                width: scaledWidth,
                height: desiredBarcodeHeight,
            });
            const barcodeTextWidth = fontMono.widthOfTextAtSize(placardData.barcode, 36);
            page.drawText(placardData.barcode, {
                x: (width - barcodeTextWidth) / 2,
                y: footerY - 135,
                font: fontMono,
                size: 36,
            });
            if (this.hasContent(placardData.description)) {
                const rightEdge = width - margin;
                const descriptionY = footerY - 25;
                const maxDescriptionWidth = 180;
                const descriptionLines = this.wrapText(placardData.description, font, 10, maxDescriptionWidth);
                const linesToShow = descriptionLines.slice(0, 3);
                let descCurrentY = descriptionY;
                for (const line of linesToShow) {
                    const lineWidth = font.widthOfTextAtSize(line, 10);
                    page.drawText(line, {
                        x: rightEdge - lineWidth,
                        y: descCurrentY,
                        font: font,
                        size: 10,
                    });
                    descCurrentY -= 14;
                }
                if (descriptionLines.length > 3) {
                    const ellipsisWidth = font.widthOfTextAtSize("...", 10);
                    page.drawText("...", {
                        x: rightEdge - ellipsisWidth,
                        y: descCurrentY,
                        font: font,
                        size: 10,
                    });
                }
            }
            this.logger.log(`Successfully created placard page for pallet ${placardData.palletId}`);
        }
        catch (error) {
            this.logger.error(`Error creating placard page for pallet ${placardData.palletId}:`, error);
            throw error;
        }
    }
};
exports.PlacardPdfService = PlacardPdfService;
exports.PlacardPdfService = PlacardPdfService = PlacardPdfService_1 = __decorate([
    (0, common_1.Injectable)()
], PlacardPdfService);
//# sourceMappingURL=placard-pdf.service.js.map