declare class PlacardWarehouseDto {
    id: string;
    name: string;
}
declare class PlacardLocationDto {
    name: string;
    warehouse?: PlacardWarehouseDto;
}
declare class PlacardContentDto {
    itemId: string;
    itemName: string;
    quantity: number;
    unit: string;
}
export declare class PlacardDataResponseDto {
    palletId: string;
    barcode: string;
    shipToDestination: string;
    destinationCode?: string;
    dateCreated: string;
    currentLocation: PlacardLocationDto;
    description: string;
    contents: PlacardContentDto[];
}
export {};
