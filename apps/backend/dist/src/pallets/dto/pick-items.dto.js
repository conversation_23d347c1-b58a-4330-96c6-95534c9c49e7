"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PickItemsDto = exports.PickItemDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
class PickItemDto {
}
exports.PickItemDto = PickItemDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: "ID of the pallet item to pick from" }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PickItemDto.prototype, "palletItemId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: "Quantity to pick (must be positive)" }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], PickItemDto.prototype, "pickedQuantity", void 0);
class PickItemsDto {
}
exports.PickItemsDto = PickItemsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        type: [PickItemDto],
        description: "Array of items to pick with their quantities",
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => PickItemDto),
    __metadata("design:type", Array)
], PickItemsDto.prototype, "items", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: "Optional notes about the item picking",
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PickItemsDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: "Name or identifier of the person/entity receiving the items",
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PickItemsDto.prototype, "releasedTo", void 0);
//# sourceMappingURL=pick-items.dto.js.map