export declare class PalletWarehouseDto {
    id: string;
    name: string;
}
export declare class PalletLocationDto {
    id: string;
    name: string;
    locationType: string;
    warehouse: PalletWarehouseDto;
}
export declare class PalletItemDto {
    id: string;
    itemId: string;
    itemName: string;
    itemSku?: string;
    quantity: number;
    unitOfMeasure: string;
}
export declare class PalletResponseDto {
    id: string;
    label: string;
    barcode: string;
    status: string;
    description?: string;
    shipToDestination?: string;
    dateCreated: Date;
    lastMovedDate?: Date;
    location?: PalletLocationDto;
    palletItems?: PalletItemDto[];
    itemCount?: number;
    tenantId: string;
}
export declare class PalletListResponseDto {
    data: PalletResponseDto[];
    count: number;
    page?: number;
    limit?: number;
    totalPages?: number;
}
