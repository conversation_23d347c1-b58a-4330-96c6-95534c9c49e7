"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlacardDataResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class PlacardWarehouseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ example: "clxkrg99f0000abcd1234efgh" }),
    __metadata("design:type", String)
], PlacardWarehouseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "Main Warehouse" }),
    __metadata("design:type", String)
], PlacardWarehouseDto.prototype, "name", void 0);
class PlacardLocationDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ example: "A1-R2-S3" }),
    __metadata("design:type", String)
], PlacardLocationDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)(),
    __metadata("design:type", PlacardWarehouseDto)
], PlacardLocationDto.prototype, "warehouse", void 0);
class PlacardContentDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ example: "clxkrg99f0000abcd1234efgh" }),
    __metadata("design:type", String)
], PlacardContentDto.prototype, "itemId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "Drywall Sheet 4x8" }),
    __metadata("design:type", String)
], PlacardContentDto.prototype, "itemName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 15 }),
    __metadata("design:type", Number)
], PlacardContentDto.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "sheets" }),
    __metadata("design:type", String)
], PlacardContentDto.prototype, "unit", void 0);
class PlacardDataResponseDto {
}
exports.PlacardDataResponseDto = PlacardDataResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: "clxkrg99f0000abcd1234efgh" }),
    __metadata("design:type", String)
], PlacardDataResponseDto.prototype, "palletId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "P-12345" }),
    __metadata("design:type", String)
], PlacardDataResponseDto.prototype, "barcode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "Job Site 42" }),
    __metadata("design:type", String)
], PlacardDataResponseDto.prototype, "shipToDestination", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: "12345" }),
    __metadata("design:type", String)
], PlacardDataResponseDto.prototype, "destinationCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "2024-01-15T10:30:00.000Z" }),
    __metadata("design:type", String)
], PlacardDataResponseDto.prototype, "dateCreated", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", PlacardLocationDto)
], PlacardDataResponseDto.prototype, "currentLocation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: "Description of the pallet" }),
    __metadata("design:type", String)
], PlacardDataResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [PlacardContentDto] }),
    __metadata("design:type", Array)
], PlacardDataResponseDto.prototype, "contents", void 0);
//# sourceMappingURL=placard.dto.js.map