"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PalletListResponseDto = exports.PalletResponseDto = exports.PalletItemDto = exports.PalletLocationDto = exports.PalletWarehouseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class PalletWarehouseDto {
}
exports.PalletWarehouseDto = PalletWarehouseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Warehouse ID' }),
    __metadata("design:type", String)
], PalletWarehouseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Warehouse name' }),
    __metadata("design:type", String)
], PalletWarehouseDto.prototype, "name", void 0);
class PalletLocationDto {
}
exports.PalletLocationDto = PalletLocationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Location ID' }),
    __metadata("design:type", String)
], PalletLocationDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Location name' }),
    __metadata("design:type", String)
], PalletLocationDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Location type' }),
    __metadata("design:type", String)
], PalletLocationDto.prototype, "locationType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Warehouse information',
        type: PalletWarehouseDto
    }),
    __metadata("design:type", PalletWarehouseDto)
], PalletLocationDto.prototype, "warehouse", void 0);
class PalletItemDto {
}
exports.PalletItemDto = PalletItemDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Pallet item ID' }),
    __metadata("design:type", String)
], PalletItemDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Item ID' }),
    __metadata("design:type", String)
], PalletItemDto.prototype, "itemId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Item name' }),
    __metadata("design:type", String)
], PalletItemDto.prototype, "itemName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Item SKU' }),
    __metadata("design:type", String)
], PalletItemDto.prototype, "itemSku", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Quantity of this item on the pallet' }),
    __metadata("design:type", Number)
], PalletItemDto.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unit of measure' }),
    __metadata("design:type", String)
], PalletItemDto.prototype, "unitOfMeasure", void 0);
class PalletResponseDto {
}
exports.PalletResponseDto = PalletResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Pallet ID' }),
    __metadata("design:type", String)
], PalletResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Pallet label' }),
    __metadata("design:type", String)
], PalletResponseDto.prototype, "label", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Pallet barcode' }),
    __metadata("design:type", String)
], PalletResponseDto.prototype, "barcode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Pallet status' }),
    __metadata("design:type", String)
], PalletResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Pallet description' }),
    __metadata("design:type", String)
], PalletResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Ship to destination' }),
    __metadata("design:type", String)
], PalletResponseDto.prototype, "shipToDestination", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Creation timestamp' }),
    __metadata("design:type", Date)
], PalletResponseDto.prototype, "dateCreated", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Last moved timestamp' }),
    __metadata("design:type", Date)
], PalletResponseDto.prototype, "lastMovedDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Current location information',
        type: PalletLocationDto
    }),
    __metadata("design:type", PalletLocationDto)
], PalletResponseDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Items on this pallet',
        type: [PalletItemDto]
    }),
    __metadata("design:type", Array)
], PalletResponseDto.prototype, "palletItems", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of items on this pallet' }),
    __metadata("design:type", Number)
], PalletResponseDto.prototype, "itemCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tenant ID' }),
    __metadata("design:type", String)
], PalletResponseDto.prototype, "tenantId", void 0);
class PalletListResponseDto {
}
exports.PalletListResponseDto = PalletListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Array of pallets',
        type: [PalletResponseDto]
    }),
    __metadata("design:type", Array)
], PalletListResponseDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of pallets' }),
    __metadata("design:type", Number)
], PalletListResponseDto.prototype, "count", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Current page number' }),
    __metadata("design:type", Number)
], PalletListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of items per page' }),
    __metadata("design:type", Number)
], PalletListResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Total number of pages' }),
    __metadata("design:type", Number)
], PalletListResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=pallet-response.dto.js.map