import { PrismaService } from "../prisma/prisma.service";
import { CreatePalletDto } from "./dto/create-pallet.dto";
import { UpdatePalletDto } from "./dto/update-pallet.dto";
import { QueryPalletDto } from "./dto/query-pallet.dto";
import { MovePalletDto } from "./dto/move-pallet.dto";
import { ReleasePalletDto } from "./dto/release-pallet.dto";
import { PickItemsDto } from "./dto/pick-items.dto";
import { Pallet, Prisma } from "@prisma/client";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";
import { AuditLogService } from "../audit-log/audit-log.service";
import { PlacardDataResponseDto } from "./dto/placard.dto";
import { PlacardPdfService } from "./placard-pdf.service";
import { PrintPlacardsQueryDto } from "./dto/print-placards.dto";
declare const palletWithAllRelations: {
    include: {
        location: true;
        palletItems: {
            include: {
                item: true;
            };
        };
    };
};
type PalletWithAllRelations = Prisma.PalletGetPayload<typeof palletWithAllRelations>;
export declare class PalletsService {
    private prisma;
    private auditLogService;
    private placardPdfService;
    private readonly logger;
    constructor(prisma: PrismaService, auditLogService: AuditLogService, placardPdfService: PlacardPdfService);
    private mapPalletToPlacardDto;
    private checkLocationExists;
    generatePlacardsPdf(query: PrintPlacardsQueryDto, currentUser: AuthenticatedUser): Promise<Uint8Array>;
    create(createPalletDto: CreatePalletDto, currentUser: AuthenticatedUser): Promise<Pallet>;
    findAll(currentUser: AuthenticatedUser, query: QueryPalletDto, warehouseId?: string): Promise<Pallet[]>;
    getDestinations(currentUser: AuthenticatedUser, warehouseId?: string): Promise<string[]>;
    getDestinationsWithCodes(currentUser: AuthenticatedUser, warehouseId?: string): Promise<{
        name: string;
        code?: string | null;
        displayName: string;
    }[]>;
    findDestinationByCode(code: string, currentUser: AuthenticatedUser, warehouseId?: string): Promise<{
        name: string;
        code?: string | null;
        displayName: string;
    } | null>;
    findDestinationsByName(nameQuery: string, currentUser: AuthenticatedUser, warehouseId?: string): Promise<{
        name: string;
        code?: string | null;
        displayName: string;
    }[]>;
    releasePallet(id: string, releasePalletDto: ReleasePalletDto, currentUser: AuthenticatedUser): Promise<Pallet>;
    pickItems(palletId: string, pickItemsDto: PickItemsDto, currentUser: AuthenticatedUser): Promise<Pallet>;
    getPlacardData(id: string, currentUser: AuthenticatedUser): Promise<PlacardDataResponseDto>;
    findOne(id: string, currentUser: AuthenticatedUser): Promise<PalletWithAllRelations>;
    update(id: string, updatePalletDto: UpdatePalletDto, currentUser: AuthenticatedUser): Promise<Pallet>;
    remove(id: string, currentUser: AuthenticatedUser): Promise<Pallet>;
    movePallet(id: string, movePalletDto: MovePalletDto, currentUser: AuthenticatedUser): Promise<Pallet>;
    getAuditLogs(id: string, currentUser: AuthenticatedUser): Promise<{
        id: string;
        tenantId: string;
        userId: string | null;
        userEmail: string | null;
        action: string;
        entity: string;
        entityId: string;
        details: Prisma.JsonValue | null;
        timestamp: Date;
    }[]>;
}
export {};
