"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const pallets_service_1 = require("./pallets.service");
const prisma_service_1 = require("../prisma/prisma.service");
const audit_log_service_1 = require("../audit-log/audit-log.service");
const client_1 = require("@prisma/client");
const jest_mock_extended_1 = require("jest-mock-extended");
const common_1 = require("@nestjs/common");
const placard_pdf_service_1 = require("./placard-pdf.service");
describe("PalletsService Integration", () => {
    let service;
    let mockPrisma;
    let mockAuditLogService;
    const mockTenant = {
        id: "tenant-123",
        name: "Test Tenant",
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const mockWarehouse = {
        id: "warehouse-123",
        name: "Main Warehouse",
        address: "123 Main St",
        status: "Active",
        tenantId: mockTenant.id,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const mockLocation = {
        id: "location-123",
        name: "A-1-1",
        category: "Storage",
        locationType: client_1.LocationType.RACK,
        status: "Active",
        warehouseId: mockWarehouse.id,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const mockTenantAdminUser = {
        id: "admin-user-123",
        tenantId: mockTenant.id,
        email: "<EMAIL>",
        role: client_1.Role.TENANT_ADMIN,
        name: "Admin User",
        authUserId: "auth-admin-123",
        warehouseUsers: [],
    };
    const mockRegularUser = {
        id: "regular-user-123",
        tenantId: mockTenant.id,
        email: "<EMAIL>",
        role: client_1.Role.WAREHOUSE_MEMBER,
        name: "Regular User",
        authUserId: "auth-user-123",
        warehouseUsers: [
            { warehouseId: mockWarehouse.id, role: client_1.Role.WAREHOUSE_MEMBER },
        ],
    };
    const mockPallet = {
        id: "pallet-123",
        description: "A test pallet",
        status: "Stored",
        locationId: mockLocation.id,
        shipmentId: null,
        dateCreated: new Date(),
        lastMovedDate: new Date(),
        barcode: "BC12345",
        label: "Pallet 123",
        shipToDestination: "Destination A",
        destinationCode: "12345",
    };
    let mockPlacardPdfService;
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                pallets_service_1.PalletsService,
                { provide: prisma_service_1.PrismaService, useValue: (0, jest_mock_extended_1.mockDeep)() },
                { provide: audit_log_service_1.AuditLogService, useValue: (0, jest_mock_extended_1.mockDeep)() },
                { provide: placard_pdf_service_1.PlacardPdfService, useValue: (0, jest_mock_extended_1.mockDeep)() },
            ],
        }).compile();
        service = module.get(pallets_service_1.PalletsService);
        mockPrisma = module.get(prisma_service_1.PrismaService);
        mockAuditLogService = module.get(audit_log_service_1.AuditLogService);
        mockPlacardPdfService = module.get(placard_pdf_service_1.PlacardPdfService);
        mockPrisma.pallet.findMany.mockResolvedValue([mockPallet]);
        mockPrisma.pallet.count.mockResolvedValue(1);
        mockPrisma.location.findFirst.mockResolvedValue(mockLocation);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    it("should be defined", () => {
        expect(service).toBeDefined();
    });
    describe("create", () => {
        it("should create a pallet and log the action", async () => {
            const createDto = {
                label: "New Pallet",
                locationId: mockLocation.id,
                status: "Receiving",
            };
            const createdPalletMock = {
                ...mockPallet,
                ...createDto,
                PalletItems: [],
            };
            mockPrisma.location.findFirst.mockResolvedValue(mockLocation);
            mockPrisma.pallet.create.mockResolvedValue(createdPalletMock);
            const { locationId, ...palletData } = createDto;
            const result = await service.create(createDto, mockTenantAdminUser);
            expect(mockPrisma.pallet.create).toHaveBeenCalledWith({
                data: {
                    ...palletData,
                    label: expect.any(String),
                    tenant: { connect: { id: mockTenantAdminUser.tenantId } },
                    location: { connect: { id: locationId } },
                    PalletItems: undefined,
                },
            });
            expect(result.label).toBe("New Pallet");
            expect(mockAuditLogService.create).toHaveBeenCalled();
        });
    });
    describe("findAll", () => {
        it("should return pallets for an admin", async () => {
            const query = {};
            mockPrisma.pallet.findMany.mockResolvedValue([mockPallet]);
            mockPrisma.pallet.findMany.mockResolvedValue([mockPallet]);
            const result = await service.findAll(mockTenantAdminUser, query);
            expect(mockPrisma.pallet.findMany).toHaveBeenCalledWith(expect.objectContaining({
                where: { tenantId: mockTenantAdminUser.tenantId },
            }));
            expect(result).toEqual([mockPallet]);
        });
        it("should filter pallets by warehouse for a regular user", async () => {
            const query = {};
            await service.findAll(mockRegularUser, query);
            expect(mockPrisma.pallet.findMany).toHaveBeenCalledWith(expect.objectContaining({
                where: {
                    tenantId: mockRegularUser.tenantId,
                    location: {
                        warehouseId: {
                            in: [mockWarehouse.id],
                        },
                    },
                },
            }));
        });
    });
    describe("findOne", () => {
        it("should return a pallet if found", async () => {
            mockPrisma.pallet.findFirst.mockResolvedValue(mockPallet);
            const result = await service.findOne(mockPallet.id, mockTenantAdminUser);
            expect(result).toEqual(mockPallet);
            expect(mockPrisma.pallet.findFirst).toHaveBeenCalledWith({
                where: { id: mockPallet.id, tenantId: mockTenantAdminUser.tenantId },
                include: expect.any(Object),
            });
        });
        it("should throw NotFoundException if pallet not found", async () => {
            mockPrisma.pallet.findFirst.mockResolvedValue(null);
            await expect(service.findOne("not-found", mockTenantAdminUser)).rejects.toThrow(common_1.NotFoundException);
        });
    });
    describe("update", () => {
        it("should update a pallet and log the action", async () => {
            const updateDto = { description: "Updated description" };
            const updatedPallet = { ...mockPallet, ...updateDto };
            mockPrisma.pallet.findFirst.mockResolvedValue(mockPallet);
            mockPrisma.pallet.update.mockResolvedValue(updatedPallet);
            const result = await service.update(mockPallet.id, updateDto, mockTenantAdminUser);
            expect(mockPrisma.pallet.update).toHaveBeenCalled();
            expect(result.description).toBe("Updated description");
            expect(mockAuditLogService.create).toHaveBeenCalled();
        });
    });
    describe("movePallet", () => {
        it("should move a pallet to a new location and log the action", async () => {
            const newLocationId = "new-location-456";
            const moveDto = { newLocationId };
            const newLocation = { ...mockLocation, id: newLocationId };
            mockPrisma.pallet.findFirst.mockResolvedValue(mockPallet);
            mockPrisma.location.findFirst.mockResolvedValue(newLocation);
            mockPrisma.pallet.update.mockResolvedValue({
                ...mockPallet,
                locationId: newLocationId,
            });
            const result = await service.movePallet(mockPallet.id, moveDto, mockTenantAdminUser);
            expect(mockPrisma.pallet.update).toHaveBeenCalledWith({
                where: { id: mockPallet.id, tenantId: mockTenantAdminUser.tenantId },
                data: { locationId: newLocationId, lastMovedDate: expect.any(Date) },
            });
            expect(result.locationId).toBe(newLocationId);
            expect(mockAuditLogService.create).toHaveBeenCalled();
        });
        it("should throw NotFoundException if the new location does not exist", async () => {
            mockPrisma.pallet.findFirst.mockResolvedValue(mockPallet);
            mockPrisma.location.findFirst.mockResolvedValue(null);
            const moveDto = { newLocationId: "not-found-loc" };
            await expect(service.movePallet(mockPallet.id, moveDto, mockTenantAdminUser)).rejects.toThrow(common_1.NotFoundException);
        });
    });
    describe("remove", () => {
        it("should remove an empty pallet and log the action", async () => {
            const emptyPallet = { ...mockPallet, status: "Empty", PalletItems: [] };
            mockPrisma.pallet.findFirst.mockResolvedValue(emptyPallet);
            mockPrisma.pallet.delete.mockResolvedValue(emptyPallet);
            await service.remove(emptyPallet.id, mockTenantAdminUser);
            expect(mockPrisma.pallet.delete).toHaveBeenCalledWith({
                where: { id: emptyPallet.id, tenantId: mockTenantAdminUser.tenantId },
            });
            expect(mockAuditLogService.create).toHaveBeenCalled();
        });
        it("should throw BadRequestException if pallet is not empty", async () => {
            const palletWithItems = {
                ...mockPallet,
                status: "Stored",
                PalletItems: [
                    {
                        id: "pi-1",
                        quantity: 10,
                        dateAdded: new Date(),
                        palletId: mockPallet.id,
                        itemId: "item-123",
                        tenantId: mockTenantAdminUser.tenantId,
                    },
                ],
            };
            mockPrisma.pallet.findFirst.mockResolvedValue(palletWithItems);
            await expect(service.remove(mockPallet.id, mockTenantAdminUser)).rejects.toThrow(common_1.BadRequestException);
        });
    });
    describe("generatePlacardsPdf", () => {
        const mockItem = {
            id: "item-123",
            name: "Test Item",
            description: "An item for testing",
            sku: "SKU123",
            unitOfMeasure: "EACH",
            tenantId: mockTenant.id,
            createdAt: new Date(),
            updatedAt: new Date(),
            status: "ACTIVE",
            defaultCost: 10.0,
            lowStockThreshold: 10,
        };
        const mockPalletWithRelations = {
            ...mockPallet,
            location: mockLocation,
            PalletItems: [
                {
                    id: "pi-1",
                    quantity: 10,
                    dateAdded: new Date(),
                    palletId: mockPallet.id,
                    itemId: mockItem.id,
                    tenantId: mockTenantAdminUser.tenantId,
                    item: mockItem,
                },
            ],
        };
        it("should generate a PDF when a valid palletId is provided", async () => {
            mockPrisma.pallet.findMany.mockResolvedValue([mockPalletWithRelations]);
            mockPlacardPdfService.createPlacardPage.mockResolvedValue(undefined);
            const result = await service.generatePlacardsPdf({ palletId: mockPallet.id }, mockTenantAdminUser);
            expect(mockPrisma.pallet.findMany).toHaveBeenCalledWith({
                where: { id: mockPallet.id, tenantId: mockTenantAdminUser.tenantId },
                include: expect.any(Object),
                orderBy: [{ shipToDestination: "asc" }, { id: "asc" }],
            });
            expect(mockPlacardPdfService.createPlacardPage).toHaveBeenCalledTimes(1);
            expect(result).toBeInstanceOf(Uint8Array);
        });
        it("should throw NotFoundException if no pallets match the query", async () => {
            mockPrisma.pallet.findMany.mockResolvedValue([]);
            await expect(service.generatePlacardsPdf({ palletId: "not-found" }, mockTenantAdminUser)).rejects.toThrow(common_1.NotFoundException);
        });
        it("should throw BadRequestException if neither palletId nor destination is provided", async () => {
            await expect(service.generatePlacardsPdf({}, mockTenantAdminUser)).rejects.toThrow(common_1.BadRequestException);
        });
    });
});
//# sourceMappingURL=pallets.service.integration.spec.js.map