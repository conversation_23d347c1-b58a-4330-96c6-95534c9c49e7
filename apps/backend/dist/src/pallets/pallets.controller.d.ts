import { StreamableFile } from "@nestjs/common";
import { PalletsService } from "./pallets.service";
import { CreatePalletDto } from "./dto/create-pallet.dto";
import { UpdatePalletDto } from "./dto/update-pallet.dto";
import { MovePalletDto } from "./dto/move-pallet.dto";
import { QueryPalletDto } from "./dto/query-pallet.dto";
import { ReleasePalletDto } from "./dto/release-pallet.dto";
import { PickItemsDto } from "./dto/pick-items.dto";
import { PlacardDataResponseDto } from "./dto/placard.dto";
import { PrintPlacardsQueryDto } from "./dto/print-placards.dto";
import { RequestWithWarehouseContext } from "../auth/decorators/warehouse-permission.decorator";
import { EnhancedUserPayload } from "../auth/types";
export declare class PalletsController {
    private readonly palletsService;
    private readonly logger;
    constructor(palletsService: PalletsService);
    create(createPalletDto: CreatePalletDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        description: string | null;
        status: string;
        id: string;
        label: string;
        barcode: string | null;
        shipToDestination: string | null;
        destinationCode: string | null;
        dateCreated: Date;
        lastMovedDate: Date;
        locationId: string | null;
        shipmentId: string | null;
    }>;
    findAll(req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>, query: QueryPalletDto): Promise<{
        description: string | null;
        status: string;
        id: string;
        label: string;
        barcode: string | null;
        shipToDestination: string | null;
        destinationCode: string | null;
        dateCreated: Date;
        lastMovedDate: Date;
        locationId: string | null;
        shipmentId: string | null;
    }[]>;
    getDestinations(req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>, warehouseId?: string): Promise<string[]>;
    getDestinationsWithCodes(req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>, warehouseId?: string): Promise<{
        name: string;
        code?: string | null;
        displayName: string;
    }[]>;
    findDestinationByCode(code: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>, warehouseId?: string): Promise<{
        name: string;
        code?: string | null;
        displayName: string;
    }>;
    findDestinationsByName(nameQuery: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>, warehouseId?: string): Promise<{
        name: string;
        code?: string | null;
        displayName: string;
    }[]>;
    releasePallet(id: string, releasePalletDto: ReleasePalletDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        description: string | null;
        status: string;
        id: string;
        label: string;
        barcode: string | null;
        shipToDestination: string | null;
        destinationCode: string | null;
        dateCreated: Date;
        lastMovedDate: Date;
        locationId: string | null;
        shipmentId: string | null;
    }>;
    pickItems(palletId: string, pickItemsDto: PickItemsDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        description: string | null;
        status: string;
        id: string;
        label: string;
        barcode: string | null;
        shipToDestination: string | null;
        destinationCode: string | null;
        dateCreated: Date;
        lastMovedDate: Date;
        locationId: string | null;
        shipmentId: string | null;
    }>;
    getPlacardData(id: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<PlacardDataResponseDto>;
    printPlacards(query: PrintPlacardsQueryDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<StreamableFile>;
    findOne(id: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        location: {
            name: string;
            status: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            warehouseId: string;
            category: import("@prisma/client").$Enums.LocationCategory;
            locationType: import("@prisma/client").$Enums.LocationType;
        };
        palletItems: ({
            item: {
                name: string;
                sku: string | null;
                description: string | null;
                unitOfMeasure: string;
                defaultCost: number | null;
                lowStockThreshold: number | null;
                status: string;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                tenantId: string;
            };
        } & {
            id: string;
            itemId: string;
            quantity: number;
            dateAdded: Date;
            palletId: string;
        })[];
    } & {
        description: string | null;
        status: string;
        id: string;
        label: string;
        barcode: string | null;
        shipToDestination: string | null;
        destinationCode: string | null;
        dateCreated: Date;
        lastMovedDate: Date;
        locationId: string | null;
        shipmentId: string | null;
    }>;
    update(id: string, updatePalletDto: UpdatePalletDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        description: string | null;
        status: string;
        id: string;
        label: string;
        barcode: string | null;
        shipToDestination: string | null;
        destinationCode: string | null;
        dateCreated: Date;
        lastMovedDate: Date;
        locationId: string | null;
        shipmentId: string | null;
    }>;
    remove(id: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        description: string | null;
        status: string;
        id: string;
        label: string;
        barcode: string | null;
        shipToDestination: string | null;
        destinationCode: string | null;
        dateCreated: Date;
        lastMovedDate: Date;
        locationId: string | null;
        shipmentId: string | null;
    }>;
    movePallet(id: string, movePalletDto: MovePalletDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        description: string | null;
        status: string;
        id: string;
        label: string;
        barcode: string | null;
        shipToDestination: string | null;
        destinationCode: string | null;
        dateCreated: Date;
        lastMovedDate: Date;
        locationId: string | null;
        shipmentId: string | null;
    }>;
    getAuditLogs(id: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<{
        id: string;
        tenantId: string;
        userId: string | null;
        userEmail: string | null;
        action: string;
        entity: string;
        entityId: string;
        details: import("@prisma/client/runtime/library").JsonValue | null;
        timestamp: Date;
    }[]>;
}
