"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PalletsModule = void 0;
const common_1 = require("@nestjs/common");
const pallets_service_1 = require("./pallets.service");
const pallets_controller_1 = require("./pallets.controller");
const placard_pdf_service_1 = require("./placard-pdf.service");
const audit_log_module_1 = require("../audit-log/audit-log.module");
const auth_module_1 = require("../auth/auth.module");
const warehouses_module_1 = require("../warehouses/warehouses.module");
let PalletsModule = class PalletsModule {
};
exports.PalletsModule = PalletsModule;
exports.PalletsModule = PalletsModule = __decorate([
    (0, common_1.Module)({
        imports: [audit_log_module_1.AuditLogModule, auth_module_1.AuthModule, warehouses_module_1.WarehousesModule],
        providers: [pallets_service_1.PalletsService, placard_pdf_service_1.PlacardPdfService],
        controllers: [pallets_controller_1.PalletsController],
    })
], PalletsModule);
//# sourceMappingURL=pallets.module.js.map