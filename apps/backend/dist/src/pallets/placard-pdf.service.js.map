{"version": 3, "file": "placard-pdf.service.js", "sourceRoot": "", "sources": ["../../../src/pallets/placard-pdf.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,qCAA8E;AAC9E,8CAAgC;AAIzB,IAAM,iBAAiB,yBAAvB,MAAM,iBAAiB;IAAvB;QACY,WAAM,GAAG,IAAI,eAAM,CAAC,mBAAiB,CAAC,IAAI,CAAC,CAAC;IA0U/D,CAAC;IArUS,QAAQ,CACd,IAAY,EACZ,IAAa,EACb,QAAgB,EAChB,QAAgB;QAEhB,IAAI,CAAC,IAAI;YAAE,OAAO,EAAE,CAAC;QAErB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,IAAI,WAAW,GAAG,EAAE,CAAC;QAErB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YAC/D,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAE7D,IAAI,SAAS,IAAI,QAAQ,EAAE,CAAC;gBAC1B,WAAW,GAAG,QAAQ,CAAC;YACzB,CAAC;iBAAM,CAAC;gBACN,IAAI,WAAW,EAAE,CAAC;oBAChB,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBACxB,WAAW,GAAG,IAAI,CAAC;gBACrB,CAAC;qBAAM,CAAC;oBAEN,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnB,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YAChB,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1B,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAKO,UAAU,CAAC,KAAU;QAC3B,OAAO,CACL,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,CACzE,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,MAAmB,EACnB,WAAmC,EACnC,WAAmB;QAEnB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,oCAAoC,WAAW,CAAC,QAAQ,EAAE,CAC3D,CAAC;YAEF,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,mBAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,mBAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YACzC,MAAM,MAAM,GAAG,EAAE,CAAC;YAGlB,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,uBAAa,CAAC,SAAS,CAAC,CAAC;YAC7D,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,uBAAa,CAAC,aAAa,CAAC,CAAC;YACrE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,uBAAa,CAAC,OAAO,CAAC,CAAC;YAG/D,MAAM,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,EAAE,CAAC;YAErC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACxB,CAAC,EAAE,MAAM;gBACT,CAAC,EAAE,OAAO;gBACV,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,EAAE;aACT,CAAC,CAAC;YACH,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,EAAE;gBAC9C,CAAC,EAAE,MAAM;gBACT,CAAC,EAAE,OAAO,GAAG,EAAE;gBACf,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,EAAE;aACT,CAAC,CAAC;YAGH,MAAM,SAAS,GAAG,KAAK,GAAG,MAAM,CAAC;YACjC,IAAI,QAAQ,GAAG,OAAO,CAAC;YAEvB,IAAI,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAE5D,MAAM,eAAe,GAAG,QAAQ,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;gBAChE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;oBACrB,CAAC,EAAE,SAAS,GAAG,eAAe;oBAC9B,CAAC,EAAE,QAAQ;oBACX,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,EAAE;iBACT,CAAC,CAAC;gBACH,QAAQ,IAAI,EAAE,CAAC;gBAEf,MAAM,cAAc,GAAG,CAAC,CAAC;gBACzB,MAAM,WAAW,GAAG,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;gBAElE,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;oBAC/B,MAAM,QAAQ,GAAG,GAAG,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACtD,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;oBAC3D,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;wBACtB,CAAC,EAAE,SAAS,GAAG,aAAa;wBAC5B,CAAC,EAAE,QAAQ;wBACX,IAAI,EAAE,IAAI;wBACV,IAAI,EAAE,EAAE;qBACT,CAAC,CAAC;oBACH,QAAQ,IAAI,EAAE,CAAC;gBACjB,CAAC;gBAGD,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,GAAG,cAAc,EAAE,CAAC;oBACjD,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;oBACxD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;wBACnB,CAAC,EAAE,SAAS,GAAG,aAAa;wBAC5B,CAAC,EAAE,QAAQ;wBACX,IAAI,EAAE,IAAI;wBACV,IAAI,EAAE,EAAE;qBACT,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAGD,MAAM,gBAAgB,GAAG,QAAQ,CAAC,iBAAiB,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;YACrE,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;gBACzB,CAAC,EAAE,CAAC,KAAK,GAAG,gBAAgB,CAAC,GAAG,CAAC;gBACjC,CAAC,EAAE,OAAO,GAAG,EAAE;gBACf,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,EAAE;aACT,CAAC,CAAC;YAGH,IAAI,CAAC,QAAQ,CAAC;gBACZ,KAAK,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,OAAO,GAAG,EAAE,EAAE;gBACrC,GAAG,EAAE,EAAE,CAAC,EAAE,KAAK,GAAG,MAAM,EAAE,CAAC,EAAE,OAAO,GAAG,EAAE,EAAE;gBAC3C,SAAS,EAAE,CAAC;gBACZ,KAAK,EAAE,IAAA,aAAG,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACnB,OAAO,EAAE,GAAG;aACb,CAAC,CAAC;YAGH,MAAM,SAAS,GAAG,OAAO,GAAG,GAAG,CAAC;YAChC,MAAM,uBAAuB,GAAG,EAAE,CAAC;YAGnC,IAAI,WAAW,CAAC,WAAW,EAAE,CAAC;gBAC5B,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;gBAClD,MAAM,aAAa,GAAG,OAAO,CAAC,kBAAkB,CAAC,OAAO,EAAE;oBACxD,KAAK,EAAE,SAAS;oBAChB,GAAG,EAAE,SAAS;oBACd,IAAI,EAAE,SAAS;iBAChB,CAAC,CAAC;gBAGH,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE;oBAC3B,CAAC,EAAE,MAAM;oBACT,CAAC,EAAE,SAAS;oBACZ,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,uBAAuB;iBAC9B,CAAC,CAAC;YACL,CAAC;YAGD,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,eAAe,CAAC,EAAE,CAAC;gBACjD,MAAM,SAAS,GAAG,KAAK,GAAG,MAAM,CAAC;gBAGjC,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CACtC,WAAW,CAAC,eAAe,EAC3B,uBAAuB,CACxB,CAAC;gBACF,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,eAAe,EAAE;oBACzC,CAAC,EAAE,SAAS,GAAG,SAAS;oBACxB,CAAC,EAAE,SAAS;oBACZ,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,uBAAuB;iBAC9B,CAAC,CAAC;YACL,CAAC;YAGD,MAAM,OAAO,GAAG,MAAM,GAAG,CAAC,GAAG,EAAE,CAAC;YAGhC,MAAM,mBAAmB,GAAG,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC;YAC/C,MAAM,mBAAmB,GAAG,EAAE,CAAC;YAC/B,MAAM,eAAe,GAAG,WAAW,CAAC,iBAAiB,IAAI,EAAE,CAAC;YAG5D,MAAM,eAAe,GAAG,QAAQ,CAAC,iBAAiB,CAChD,eAAe,EACf,mBAAmB,CACpB,CAAC;YAEF,IAAI,eAAe,IAAI,mBAAmB,EAAE,CAAC;gBAE3C,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE;oBAC7B,CAAC,EAAE,CAAC,KAAK,GAAG,eAAe,CAAC,GAAG,CAAC;oBAChC,CAAC,EAAE,OAAO;oBACV,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,mBAAmB;iBAC1B,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBAEN,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAChC,eAAe,EACf,QAAQ,EACR,mBAAmB,EACnB,mBAAmB,CACpB,CAAC;gBACF,MAAM,UAAU,GAAG,mBAAmB,GAAG,EAAE,CAAC;gBAC5C,MAAM,eAAe,GAAG,YAAY,CAAC,MAAM,GAAG,UAAU,CAAC;gBACzD,IAAI,MAAM,GAAG,OAAO,GAAG,eAAe,GAAG,CAAC,CAAC;gBAE3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC7C,MAAM,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;oBAC7B,MAAM,SAAS,GAAG,QAAQ,CAAC,iBAAiB,CAC1C,IAAI,EACJ,mBAAmB,CACpB,CAAC;oBACF,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;wBAClB,CAAC,EAAE,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC;wBAC1B,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,UAAU;wBAC1B,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,mBAAmB;qBAC1B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAGD,MAAM,OAAO,GAAG,MAAM,GAAG,GAAG,CAAC;YAC7B,IAAI,CAAC,QAAQ,CAAC;gBACZ,KAAK,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE;gBAChC,GAAG,EAAE,EAAE,CAAC,EAAE,KAAK,GAAG,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE;gBACtC,SAAS,EAAE,CAAC;gBACZ,KAAK,EAAE,IAAA,aAAG,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACnB,OAAO,EAAE,GAAG;aACb,CAAC,CAAC;YAGH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC;gBACrC,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,WAAW,CAAC,OAAO;gBACzB,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,EAAE;gBACV,WAAW,EAAE,KAAK;gBAClB,UAAU,EAAE,QAAQ;aACrB,CAAC,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAGvD,MAAM,oBAAoB,GAAG,EAAE,CAAC;YAChC,MAAM,WAAW,GAAG,oBAAoB,GAAG,YAAY,CAAC,MAAM,CAAC;YAC/D,MAAM,WAAW,GAAG,YAAY,CAAC,KAAK,GAAG,WAAW,CAAC;YAErD,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;gBAC3B,CAAC,EAAE,CAAC,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC;gBAC5B,CAAC,EAAE,OAAO,GAAG,GAAG;gBAChB,KAAK,EAAE,WAAW;gBAClB,MAAM,EAAE,oBAAoB;aAC7B,CAAC,CAAC;YAGH,MAAM,gBAAgB,GAAG,QAAQ,CAAC,iBAAiB,CACjD,WAAW,CAAC,OAAO,EACnB,EAAE,CACH,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,EAAE;gBACjC,CAAC,EAAE,CAAC,KAAK,GAAG,gBAAgB,CAAC,GAAG,CAAC;gBACjC,CAAC,EAAE,OAAO,GAAG,GAAG;gBAChB,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,EAAE;aACT,CAAC,CAAC;YAGH,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC7C,MAAM,SAAS,GAAG,KAAK,GAAG,MAAM,CAAC;gBACjC,MAAM,YAAY,GAAG,OAAO,GAAG,EAAE,CAAC;gBAGlC,MAAM,mBAAmB,GAAG,GAAG,CAAC;gBAChC,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CACpC,WAAW,CAAC,WAAW,EACvB,IAAI,EACJ,EAAE,EACF,mBAAmB,CACpB,CAAC;gBAGF,MAAM,WAAW,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACjD,IAAI,YAAY,GAAG,YAAY,CAAC;gBAEhC,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;oBAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;oBACnD,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;wBAClB,CAAC,EAAE,SAAS,GAAG,SAAS;wBACxB,CAAC,EAAE,YAAY;wBACf,IAAI,EAAE,IAAI;wBACV,IAAI,EAAE,EAAE;qBACT,CAAC,CAAC;oBACH,YAAY,IAAI,EAAE,CAAC;gBACrB,CAAC;gBAGD,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAChC,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;oBACxD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;wBACnB,CAAC,EAAE,SAAS,GAAG,aAAa;wBAC5B,CAAC,EAAE,YAAY;wBACf,IAAI,EAAE,IAAI;wBACV,IAAI,EAAE,EAAE;qBACT,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,gDAAgD,WAAW,CAAC,QAAQ,EAAE,CACvE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,0CAA0C,WAAW,CAAC,QAAQ,GAAG,EACjE,KAAK,CACN,CAAC;YACF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AA3UY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;GACA,iBAAiB,CA2U7B"}