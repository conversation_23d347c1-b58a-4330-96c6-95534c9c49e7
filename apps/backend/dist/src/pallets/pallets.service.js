"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PalletsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PalletsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
const audit_log_service_1 = require("../audit-log/audit-log.service");
const placard_pdf_service_1 = require("./placard-pdf.service");
const pdf_lib_1 = require("pdf-lib");
const palletWithAllRelations = client_1.Prisma.validator()({
    include: {
        location: true,
        palletItems: {
            include: {
                item: true,
            },
        },
    },
});
let PalletsService = PalletsService_1 = class PalletsService {
    constructor(prisma, auditLogService, placardPdfService) {
        this.prisma = prisma;
        this.auditLogService = auditLogService;
        this.placardPdfService = placardPdfService;
        this.logger = new common_1.Logger(PalletsService_1.name);
    }
    mapPalletToPlacardDto(pallet) {
        return {
            palletId: pallet.id,
            barcode: pallet.barcode,
            shipToDestination: pallet.shipToDestination,
            destinationCode: pallet.destinationCode,
            dateCreated: pallet.dateCreated.toISOString(),
            currentLocation: {
                name: pallet.location?.name || "N/A",
            },
            description: pallet.description,
            contents: pallet.palletItems.map((pi) => ({
                itemId: pi.item.id,
                itemName: pi.item.name,
                quantity: pi.quantity,
                unit: pi.item.unitOfMeasure,
            })),
        };
    }
    async checkLocationExists(locationId, tenantId) {
        if (!locationId) {
            throw new common_1.BadRequestException("Location ID must be provided.");
        }
        const location = await this.prisma.location.findFirst({
            where: {
                id: locationId,
                warehouse: {
                    tenantId,
                },
            },
            include: {
                warehouse: true,
            },
        });
        if (!location) {
            throw new common_1.NotFoundException(`Location with ID "${locationId}" not found within your tenant's warehouses.`);
        }
        return location;
    }
    async generatePlacardsPdf(query, currentUser) {
        this.logger.log(`Generating placards PDF for tenant ${currentUser.tenantId} with query: ${JSON.stringify(query)}`);
        let palletsToPrint;
        let requestedPalletIds = [];
        if (query.palletId) {
            const requestedPallet = await this.prisma.pallet.findFirst({
                where: {
                    id: query.palletId,
                    location: {
                        warehouse: {
                            tenantId: currentUser.tenantId,
                        },
                    },
                },
                include: palletWithAllRelations.include,
            });
            if (!requestedPallet) {
                throw new common_1.NotFoundException(`Pallet with ID "${query.palletId}" not found or not accessible.`);
            }
            palletsToPrint = await this.prisma.pallet.findMany({
                where: {
                    location: {
                        warehouse: {
                            tenantId: currentUser.tenantId,
                        },
                    },
                    shipToDestination: {
                        equals: requestedPallet.shipToDestination,
                        mode: "insensitive",
                    },
                },
                include: palletWithAllRelations.include,
                orderBy: [{ shipToDestination: "asc" }, { dateCreated: "asc" }],
            });
            requestedPalletIds = [query.palletId];
        }
        else if (query.destination) {
            palletsToPrint = await this.prisma.pallet.findMany({
                where: {
                    location: {
                        warehouse: {
                            tenantId: currentUser.tenantId,
                        },
                    },
                    shipToDestination: {
                        equals: query.destination,
                        mode: "insensitive",
                    },
                },
                include: palletWithAllRelations.include,
                orderBy: [{ shipToDestination: "asc" }, { dateCreated: "asc" }],
            });
            requestedPalletIds = palletsToPrint.map((p) => p.id);
        }
        else {
            throw new common_1.BadRequestException("Either palletId or destination must be provided for placard generation.");
        }
        if (palletsToPrint.length === 0) {
            throw new common_1.NotFoundException("No pallets found matching the specified criteria.");
        }
        const palletsByDestination = palletsToPrint.reduce((acc, pallet) => {
            const destination = pallet.shipToDestination?.trim() || "unknown";
            const key = destination.toLowerCase();
            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(pallet);
            return acc;
        }, {});
        Object.values(palletsByDestination).forEach((pallets) => {
            pallets.sort((a, b) => a.dateCreated.getTime() - b.dateCreated.getTime());
        });
        this.logger.debug(`Grouped ${palletsToPrint.length} pallets into ${Object.keys(palletsByDestination).length} destinations:`);
        Object.entries(palletsByDestination).forEach(([dest, pallets]) => {
            const originalDestinations = pallets
                .map((p) => p.shipToDestination)
                .join(", ");
            this.logger.debug(`  "${dest}": ${pallets.length} pallets (original: ${originalDestinations})`);
        });
        const pdfDoc = await pdf_lib_1.PDFDocument.create();
        const palletsToGenerate = palletsToPrint.filter((pallet) => requestedPalletIds.includes(pallet.id));
        for (const pallet of palletsToGenerate) {
            const destination = pallet.shipToDestination?.trim() || "unknown";
            const destinationKey = destination.toLowerCase();
            const destinationGroup = palletsByDestination[destinationKey];
            if (!destinationGroup) {
                throw new Error(`Destination group not found for pallet ${pallet.id}`);
            }
            const palletIndex = destinationGroup.findIndex((p) => p.id === pallet.id);
            const palletCountStr = `Pallet ${palletIndex + 1} of ${destinationGroup.length}`;
            this.logger.debug(`Generating placard for pallet ${pallet.id}: ${palletCountStr} (destination: ${destination})`);
            const placardData = this.mapPalletToPlacardDto(pallet);
            await this.placardPdfService.createPlacardPage(pdfDoc, placardData, palletCountStr);
        }
        this.logger.log("PDF generation complete.");
        return pdfDoc.save();
    }
    async create(createPalletDto, currentUser) {
        const { locationId, items, ...palletData } = createPalletDto;
        if (!locationId) {
            throw new common_1.BadRequestException("Initial Location ID is required to create a pallet.");
        }
        const location = await this.checkLocationExists(locationId, currentUser.tenantId);
        if (currentUser.role !== "TENANT_ADMIN") {
            const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
            if (!userWarehouseIds.includes(location.warehouseId)) {
                throw new common_1.ForbiddenException(`You are not authorized to create pallets in the warehouse of location ID "${location.id}".`);
            }
        }
        const newPallet = await this.prisma.pallet.create({
            data: {
                ...palletData,
                label: palletData.label || `Pallet-${new Date().getTime()}`,
                locationId: locationId,
                palletItems: items
                    ? {
                        create: items.map((item) => ({
                            ...item,
                        })),
                    }
                    : undefined,
            },
        });
        await this.auditLogService.create({
            userId: currentUser.id,
            userEmail: currentUser.email,
            action: "CREATE_PALLET",
            entity: "PALLET",
            entityId: newPallet.id,
            tenantId: currentUser.tenantId,
            details: {
                barcode: newPallet.barcode,
                label: newPallet.label,
                description: newPallet.description,
                shipToDestination: newPallet.shipToDestination,
                status: newPallet.status,
                locationName: location.name,
                itemCount: items?.length || 0,
                createdVia: "create_pallet_dialog",
            },
        });
        return newPallet;
    }
    async findAll(currentUser, query, warehouseId) {
        const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
        const whereClause = {
            location: {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
            },
        };
        if (query.shipToDestination) {
            whereClause.shipToDestination = {
                contains: query.shipToDestination,
                mode: "insensitive",
            };
        }
        if (query.description) {
            whereClause.description = {
                contains: query.description,
                mode: "insensitive",
            };
        }
        if (query.locationId) {
            whereClause.locationId = query.locationId;
        }
        if (query.status) {
            whereClause.status = query.status;
        }
        if (currentUser.role !== "TENANT_ADMIN") {
            if (userWarehouseIds.length === 0)
                return [];
            whereClause.location = {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
                warehouseId: { in: userWarehouseIds },
            };
        }
        if (warehouseId) {
            whereClause.location = {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
                warehouseId: warehouseId,
            };
        }
        return this.prisma.pallet.findMany({
            where: whereClause,
            include: {
                location: {
                    include: { warehouse: true },
                },
                palletItems: {
                    select: {
                        id: true,
                        quantity: true,
                        dateAdded: true,
                        item: true,
                    },
                },
            },
            orderBy: { lastMovedDate: "desc" },
        });
    }
    async getDestinations(currentUser, warehouseId) {
        this.logger.log(`[getDestinations] Fetching unique destinations for tenant: ${currentUser.tenantId}`);
        const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
        const whereClause = {
            location: {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
            },
            shipToDestination: { not: null },
            status: { not: "Released" },
        };
        if (currentUser.role !== "TENANT_ADMIN") {
            if (userWarehouseIds.length === 0)
                return [];
            whereClause.location = {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
                warehouseId: { in: userWarehouseIds },
            };
        }
        if (warehouseId) {
            whereClause.location = {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
                warehouseId: warehouseId,
            };
        }
        const pallets = await this.prisma.pallet.findMany({
            where: whereClause,
            select: { shipToDestination: true },
            distinct: ["shipToDestination"],
        });
        const destinations = pallets
            .map((p) => p.shipToDestination)
            .filter((dest) => dest !== null)
            .sort();
        this.logger.log(`[getDestinations] Found ${destinations.length} unique destinations`);
        return destinations;
    }
    async getDestinationsWithCodes(currentUser, warehouseId) {
        this.logger.log(`[getDestinationsWithCodes] Fetching destinations with codes for tenant: ${currentUser.tenantId}`);
        const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
        const whereClause = {
            location: {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
            },
            shipToDestination: { not: null },
            status: { not: "Released" },
        };
        if (currentUser.role !== "TENANT_ADMIN") {
            if (userWarehouseIds.length === 0)
                return [];
            whereClause.location = {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
                warehouseId: { in: userWarehouseIds },
            };
        }
        if (warehouseId) {
            whereClause.location = {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
                warehouseId: warehouseId,
            };
        }
        const pallets = await this.prisma.pallet.findMany({
            where: whereClause,
            select: {
                shipToDestination: true,
                destinationCode: true,
            },
            distinct: ["shipToDestination", "destinationCode"],
        });
        const destinations = pallets
            .filter((p) => p.shipToDestination !== null)
            .map((p) => ({
            name: p.shipToDestination,
            code: p.destinationCode,
            displayName: p.destinationCode
                ? `${p.shipToDestination} (${p.destinationCode})`
                : p.shipToDestination,
        }))
            .sort((a, b) => a.name.localeCompare(b.name));
        this.logger.log(`[getDestinationsWithCodes] Found ${destinations.length} unique destinations with codes`);
        return destinations;
    }
    async findDestinationByCode(code, currentUser, warehouseId) {
        this.logger.log(`[findDestinationByCode] Looking up destination by code: ${code} for tenant: ${currentUser.tenantId}`);
        const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
        const whereClause = {
            location: {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
            },
            destinationCode: code,
            shipToDestination: { not: null },
        };
        if (currentUser.role !== "TENANT_ADMIN") {
            if (userWarehouseIds.length === 0)
                return null;
            whereClause.location = {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
                warehouseId: { in: userWarehouseIds },
            };
        }
        if (warehouseId) {
            whereClause.location = {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
                warehouseId: warehouseId,
            };
        }
        const pallet = await this.prisma.pallet.findFirst({
            where: whereClause,
            select: {
                shipToDestination: true,
                destinationCode: true,
            },
        });
        if (!pallet) {
            this.logger.log(`[findDestinationByCode] No destination found for code: ${code}`);
            return null;
        }
        const result = {
            name: pallet.shipToDestination,
            code: pallet.destinationCode,
            displayName: `${pallet.shipToDestination} (${code})`,
        };
        this.logger.log(`[findDestinationByCode] Found destination: ${result.displayName}`);
        return result;
    }
    async findDestinationsByName(nameQuery, currentUser, warehouseId) {
        this.logger.log(`[findDestinationsByName] Searching destinations by name: ${nameQuery} for tenant: ${currentUser.tenantId}`);
        const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
        const whereClause = {
            location: {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
            },
            shipToDestination: {
                contains: nameQuery,
                mode: "insensitive",
            },
        };
        if (currentUser.role !== "TENANT_ADMIN") {
            if (userWarehouseIds.length === 0)
                return [];
            whereClause.location = {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
                warehouseId: { in: userWarehouseIds },
            };
        }
        if (warehouseId) {
            whereClause.location = {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
                warehouseId: warehouseId,
            };
        }
        const pallets = await this.prisma.pallet.findMany({
            where: whereClause,
            select: {
                shipToDestination: true,
                destinationCode: true,
            },
            distinct: ["shipToDestination", "destinationCode"],
            take: 10,
        });
        const destinations = pallets
            .filter((p) => p.shipToDestination !== null)
            .map((p) => ({
            name: p.shipToDestination,
            code: p.destinationCode,
            displayName: p.destinationCode
                ? `${p.shipToDestination} (${p.destinationCode})`
                : p.shipToDestination,
        }))
            .sort((a, b) => a.name.localeCompare(b.name));
        this.logger.log(`[findDestinationsByName] Found ${destinations.length} destinations matching: ${nameQuery}`);
        return destinations;
    }
    async releasePallet(id, releasePalletDto, currentUser) {
        this.logger.log(`[releasePallet] Releasing pallet ID: ${id} for tenant: ${currentUser.tenantId}`);
        const pallet = await this.findOne(id, currentUser);
        if (pallet.status === "Released") {
            throw new common_1.BadRequestException(`Pallet with ID "${id}" is already released.`);
        }
        const updatedPallet = await this.prisma.pallet.update({
            where: { id },
            data: { status: "Released" },
            include: {
                location: {
                    include: { warehouse: true },
                },
                palletItems: {
                    include: { item: true },
                },
            },
        });
        await this.auditLogService.create({
            userId: currentUser.id,
            userEmail: currentUser.email,
            action: "RELEASE_PALLET",
            entity: "PALLET",
            entityId: id,
            tenantId: currentUser.tenantId,
            details: {
                notes: releasePalletDto.notes,
                releasedTo: releasePalletDto.releasedTo,
                previousStatus: pallet.status,
                newStatus: "Released",
            },
        });
        this.logger.log(`[releasePallet] Successfully released pallet ID: ${id}`);
        return updatedPallet;
    }
    async pickItems(palletId, pickItemsDto, currentUser) {
        this.logger.log(`[pickItems] Picking items from pallet ID: ${palletId} for tenant: ${currentUser.tenantId}`);
        const pallet = await this.findOne(palletId, currentUser);
        if (pallet.status === "Released") {
            throw new common_1.BadRequestException(`Cannot pick items from released pallet with ID "${palletId}".`);
        }
        const result = await this.prisma.$transaction(async (prisma) => {
            const pickedItemsSummary = [];
            for (const pickItem of pickItemsDto.items) {
                const palletItem = await prisma.palletItem.findFirst({
                    where: {
                        id: pickItem.palletItemId,
                        palletId: palletId,
                    },
                    include: { item: true },
                });
                if (!palletItem) {
                    throw new common_1.NotFoundException(`Pallet item with ID "${pickItem.palletItemId}" not found on pallet "${palletId}".`);
                }
                if (pickItem.pickedQuantity > palletItem.quantity) {
                    throw new common_1.BadRequestException(`Cannot pick ${pickItem.pickedQuantity} of "${palletItem.item.name}" - only ${palletItem.quantity} available.`);
                }
                const newQuantity = palletItem.quantity - pickItem.pickedQuantity;
                pickedItemsSummary.push({
                    itemName: palletItem.item.name,
                    itemSku: palletItem.item.sku,
                    pickedQuantity: pickItem.pickedQuantity,
                    remainingQuantity: newQuantity,
                });
                if (newQuantity === 0) {
                    await prisma.palletItem.delete({
                        where: { id: pickItem.palletItemId },
                    });
                }
                else {
                    await prisma.palletItem.update({
                        where: { id: pickItem.palletItemId },
                        data: { quantity: newQuantity },
                    });
                }
            }
            await this.auditLogService.create({
                userId: currentUser.id,
                userEmail: currentUser.email,
                action: "PICK_ITEMS",
                entity: "PALLET",
                entityId: palletId,
                tenantId: currentUser.tenantId,
                details: {
                    notes: pickItemsDto.notes,
                    releasedTo: pickItemsDto.releasedTo,
                    pickedItems: pickedItemsSummary,
                },
            });
            return prisma.pallet.findFirst({
                where: {
                    id: palletId,
                    location: {
                        warehouse: {
                            tenantId: currentUser.tenantId,
                        },
                    },
                },
                include: {
                    location: {
                        include: { warehouse: true },
                    },
                    palletItems: {
                        include: { item: true },
                    },
                },
            });
        });
        this.logger.log(`[pickItems] Successfully picked items from pallet ID: ${palletId}`);
        return result;
    }
    async getPlacardData(id, currentUser) {
        this.logger.log(`[getPlacardData] Fetching placard data for pallet ID: ${id}`);
        const pallet = await this.findOne(id, currentUser);
        return this.mapPalletToPlacardDto(pallet);
    }
    async findOne(id, currentUser) {
        this.logger.log(`[findOne] Searching for pallet ID: ${id} for tenant: ${currentUser.tenantId}`);
        const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
        const whereClause = {
            id,
            location: {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
            },
        };
        if (currentUser.role !== "TENANT_ADMIN") {
            if (userWarehouseIds.length === 0) {
                throw new common_1.ForbiddenException(`You do not have access to any warehouses.`);
            }
            whereClause.location = {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
                warehouseId: { in: userWarehouseIds },
            };
        }
        const pallet = await this.prisma.pallet.findFirst({
            where: whereClause,
            include: palletWithAllRelations.include,
        });
        if (!pallet) {
            throw new common_1.NotFoundException(`Pallet with ID "${id}" not found or not accessible within your warehouses.`);
        }
        return pallet;
    }
    async update(id, updatePalletDto, currentUser) {
        await this.findOne(id, currentUser);
        const { locationId, ...dataToUpdate } = updatePalletDto;
        if (locationId) {
            throw new common_1.BadRequestException("Use the /pallets/:id/move endpoint to change a pallet's location.");
        }
        const updatedPallet = await this.prisma.pallet.update({
            where: { id },
            data: dataToUpdate,
        });
        await this.auditLogService.create({
            userId: currentUser.id,
            userEmail: currentUser.email,
            action: "UPDATE_PALLET",
            entity: "PALLET",
            entityId: id,
            tenantId: currentUser.tenantId,
            details: {
                updatedFields: dataToUpdate,
                previousValues: {},
            },
        });
        return updatedPallet;
    }
    async remove(id, currentUser) {
        const pallet = await this.findOne(id, currentUser);
        if (pallet.palletItems && pallet.palletItems.length > 0) {
            throw new common_1.BadRequestException("Cannot delete a pallet with items. Please move or remove items first.");
        }
        const deletedPallet = await this.prisma.pallet.delete({
            where: { id },
        });
        await this.auditLogService.create({
            userId: currentUser.id,
            userEmail: currentUser.email,
            action: "DELETE_PALLET",
            entity: "PALLET",
            entityId: id,
            tenantId: currentUser.tenantId,
            details: {
                deletedPallet: {
                    barcode: pallet.barcode,
                    label: pallet.label,
                    description: pallet.description,
                    status: pallet.status,
                    locationName: pallet.location?.name || "unknown",
                },
            },
        });
        return deletedPallet;
    }
    async movePallet(id, movePalletDto, currentUser) {
        const { newLocationId } = movePalletDto;
        return this.prisma.$transaction(async (tx) => {
            const pallet = await tx.pallet.findUniqueOrThrow({
                where: {
                    id,
                    location: {
                        warehouse: {
                            tenantId: currentUser.tenantId,
                        },
                    },
                },
                include: { location: true },
            });
            const oldLocationName = pallet.location?.name || "unassigned";
            let finalLocationId = pallet.locationId;
            let newLocationName = oldLocationName;
            if (newLocationId) {
                const newLocation = await this.checkLocationExists(newLocationId, currentUser.tenantId);
                if (currentUser.role !== "TENANT_ADMIN") {
                    const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
                    if (!userWarehouseIds.includes(newLocation.warehouseId)) {
                        throw new common_1.ForbiddenException(`You are not authorized to move pallets to location ${newLocation.name}.`);
                    }
                }
                finalLocationId = newLocationId;
                newLocationName = newLocation.name;
            }
            else {
                if (pallet.location?.category === "Receiving") {
                    throw new common_1.BadRequestException("Pallet needs a destination storage location.");
                }
            }
            const updatedPallet = await tx.pallet.update({
                where: { id },
                data: {
                    locationId: finalLocationId,
                    status: "Stored",
                    lastMovedDate: new Date(),
                },
                include: {
                    location: true,
                    palletItems: {
                        include: {
                            item: true,
                        },
                    },
                },
            });
            await this.auditLogService.create({
                userId: currentUser.id,
                userEmail: currentUser.email,
                action: "MOVE_PALLET",
                entity: "Pallet",
                entityId: id,
                tenantId: currentUser.tenantId,
                details: {
                    fromLocation: oldLocationName,
                    toLocation: newLocationName,
                    statusUpdated: "Stored",
                    moveDescription: `Moved from ${oldLocationName} to ${newLocationName}`,
                },
            });
            return updatedPallet;
        });
    }
    async getAuditLogs(id, currentUser) {
        const pallet = await this.findOne(id, currentUser);
        if (!pallet) {
            throw new common_1.NotFoundException(`Pallet with ID "${id}" not found.`);
        }
        return this.auditLogService.findByEntityIdWithWarehouseContext(id, currentUser, "Pallet");
    }
};
exports.PalletsService = PalletsService;
exports.PalletsService = PalletsService = PalletsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        audit_log_service_1.AuditLogService,
        placard_pdf_service_1.PlacardPdfService])
], PalletsService);
//# sourceMappingURL=pallets.service.js.map