"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PalletsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PalletsController = void 0;
const common_1 = require("@nestjs/common");
const pallets_service_1 = require("./pallets.service");
const create_pallet_dto_1 = require("./dto/create-pallet.dto");
const update_pallet_dto_1 = require("./dto/update-pallet.dto");
const move_pallet_dto_1 = require("./dto/move-pallet.dto");
const query_pallet_dto_1 = require("./dto/query-pallet.dto");
const release_pallet_dto_1 = require("./dto/release-pallet.dto");
const pick_items_dto_1 = require("./dto/pick-items.dto");
const print_placards_dto_1 = require("./dto/print-placards.dto");
const audit_log_interceptor_1 = require("../audit-log/interceptors/audit-log.interceptor");
const log_action_decorator_1 = require("../audit-log/decorators/log-action.decorator");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const warehouse_permission_guard_1 = require("../auth/guards/warehouse-permission.guard");
const warehouse_permission_decorator_1 = require("../auth/decorators/warehouse-permission.decorator");
const client_1 = require("@prisma/client");
const swagger_1 = require("@nestjs/swagger");
let PalletsController = PalletsController_1 = class PalletsController {
    constructor(palletsService) {
        this.palletsService = palletsService;
        this.logger = new common_1.Logger(PalletsController_1.name);
    }
    create(createPalletDto, req) {
        return this.palletsService.create(createPalletDto, req.user);
    }
    findAll(req, query) {
        return this.palletsService.findAll(req.user, query, query.warehouseId);
    }
    getDestinations(req, warehouseId) {
        return this.palletsService.getDestinations(req.user, warehouseId);
    }
    getDestinationsWithCodes(req, warehouseId) {
        return this.palletsService.getDestinationsWithCodes(req.user, warehouseId);
    }
    findDestinationByCode(code, req, warehouseId) {
        return this.palletsService.findDestinationByCode(code, req.user, warehouseId);
    }
    findDestinationsByName(nameQuery, req, warehouseId) {
        return this.palletsService.findDestinationsByName(nameQuery, req.user, warehouseId);
    }
    releasePallet(id, releasePalletDto, req) {
        return this.palletsService.releasePallet(id, releasePalletDto, req.user);
    }
    pickItems(palletId, pickItemsDto, req) {
        return this.palletsService.pickItems(palletId, pickItemsDto, req.user);
    }
    async getPlacardData(id, req) {
        this.logger.log(`Received request for placard data for pallet ID: ${id}`);
        return this.palletsService.getPlacardData(id, req.user);
    }
    async printPlacards(query, req) {
        this.logger.log(`Received request to print placards with query: ${JSON.stringify(query)}`);
        const pdfBuffer = await this.palletsService.generatePlacardsPdf(query, req.user);
        return new common_1.StreamableFile(pdfBuffer);
    }
    async findOne(id, req) {
        const pallet = await this.palletsService.findOne(id, req.user);
        if (!pallet) {
            throw new common_1.NotFoundException(`Pallet with ID "${id}" not found or not accessible.`);
        }
        return pallet;
    }
    update(id, updatePalletDto, req) {
        return this.palletsService.update(id, updatePalletDto, req.user);
    }
    remove(id, req) {
        return this.palletsService.remove(id, req.user);
    }
    async movePallet(id, movePalletDto, req) {
        return this.palletsService.movePallet(id, movePalletDto, req.user);
    }
    getAuditLogs(id, req) {
        return this.palletsService.getAuditLogs(id, req.user);
    }
};
exports.PalletsController = PalletsController;
__decorate([
    (0, common_1.Post)(),
    (0, warehouse_permission_decorator_1.RequireWarehouseAccess)(),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true })),
    (0, log_action_decorator_1.LogAction)({
        action: "CREATE_PALLET",
        entity: "Pallet",
        getEntityId: (_context, result) => result?.id,
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_pallet_dto_1.CreatePalletDto, Object]),
    __metadata("design:returntype", void 0)
], PalletsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, warehouse_permission_decorator_1.AllowWarehouseFiltering)(),
    (0, swagger_1.ApiOperation)({
        summary: "Get all pallets with optional warehouse filtering",
    }),
    (0, swagger_1.ApiQuery)({
        name: "warehouseId",
        required: false,
        type: String,
        description: "Filter by warehouse ID",
    }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, query_pallet_dto_1.QueryPalletDto]),
    __metadata("design:returntype", void 0)
], PalletsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)("destinations"),
    (0, warehouse_permission_decorator_1.AllowWarehouseFiltering)(),
    (0, swagger_1.ApiOperation)({
        summary: "Get all destinations with optional warehouse filtering",
    }),
    (0, swagger_1.ApiQuery)({
        name: "warehouseId",
        required: false,
        type: String,
        description: "Filter by warehouse ID",
    }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)("warehouseId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], PalletsController.prototype, "getDestinations", null);
__decorate([
    (0, common_1.Get)("destinations/with-codes"),
    (0, warehouse_permission_decorator_1.AllowWarehouseFiltering)(),
    (0, swagger_1.ApiOperation)({
        summary: "Get all destinations with codes and display names",
    }),
    (0, swagger_1.ApiQuery)({
        name: "warehouseId",
        required: false,
        type: String,
        description: "Filter by warehouse ID",
    }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)("warehouseId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], PalletsController.prototype, "getDestinationsWithCodes", null);
__decorate([
    (0, common_1.Get)("destinations/by-code/:code"),
    (0, warehouse_permission_decorator_1.AllowWarehouseFiltering)(),
    (0, swagger_1.ApiOperation)({
        summary: "Find destination by code",
    }),
    (0, swagger_1.ApiParam)({
        name: "code",
        type: String,
        description: "Destination code to lookup",
    }),
    (0, swagger_1.ApiQuery)({
        name: "warehouseId",
        required: false,
        type: String,
        description: "Filter by warehouse ID",
    }),
    __param(0, (0, common_1.Param)("code")),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Query)("warehouseId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String]),
    __metadata("design:returntype", void 0)
], PalletsController.prototype, "findDestinationByCode", null);
__decorate([
    (0, common_1.Get)("destinations/by-name"),
    (0, warehouse_permission_decorator_1.AllowWarehouseFiltering)(),
    (0, swagger_1.ApiOperation)({
        summary: "Search destinations by name",
    }),
    (0, swagger_1.ApiQuery)({
        name: "q",
        required: true,
        type: String,
        description: "Name query to search for",
    }),
    (0, swagger_1.ApiQuery)({
        name: "warehouseId",
        required: false,
        type: String,
        description: "Filter by warehouse ID",
    }),
    __param(0, (0, common_1.Query)("q")),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Query)("warehouseId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String]),
    __metadata("design:returntype", void 0)
], PalletsController.prototype, "findDestinationsByName", null);
__decorate([
    (0, common_1.Post)(":id/release"),
    (0, warehouse_permission_decorator_1.RequirePalletAccess)({ minimumRole: client_1.Role.WAREHOUSE_MEMBER }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true })),
    (0, log_action_decorator_1.LogAction)({
        action: "RELEASE_PALLET",
        entity: "Pallet",
        getEntityId: (context) => context.switchToHttp().getRequest().params.id,
    }),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, release_pallet_dto_1.ReleasePalletDto, Object]),
    __metadata("design:returntype", void 0)
], PalletsController.prototype, "releasePallet", null);
__decorate([
    (0, common_1.Post)(":palletId/pick-items"),
    (0, warehouse_permission_decorator_1.RequirePalletAccess)({ minimumRole: client_1.Role.WAREHOUSE_MEMBER }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true })),
    (0, log_action_decorator_1.LogAction)({
        action: "PICK_ITEMS",
        entity: "Pallet",
        getEntityId: (context) => context.switchToHttp().getRequest().params.palletId,
    }),
    __param(0, (0, common_1.Param)("palletId")),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, pick_items_dto_1.PickItemsDto, Object]),
    __metadata("design:returntype", void 0)
], PalletsController.prototype, "pickItems", null);
__decorate([
    (0, common_1.Get)(":id/placard-data"),
    (0, warehouse_permission_decorator_1.RequirePalletAccess)(),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PalletsController.prototype, "getPlacardData", null);
__decorate([
    (0, common_1.Get)("placards/print"),
    (0, warehouse_permission_decorator_1.AllowWarehouseFiltering)(),
    (0, common_1.Header)("Content-Type", "application/pdf"),
    (0, common_1.Header)("Content-Disposition", 'inline; filename="placards.pdf"'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [print_placards_dto_1.PrintPlacardsQueryDto, Object]),
    __metadata("design:returntype", Promise)
], PalletsController.prototype, "printPlacards", null);
__decorate([
    (0, common_1.Get)(":id"),
    (0, warehouse_permission_decorator_1.RequirePalletAccess)(),
    (0, swagger_1.ApiOperation)({ summary: "Get a specific pallet by ID" }),
    (0, swagger_1.ApiParam)({ name: "id", type: String, description: "Pallet ID" }),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PalletsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(":id"),
    (0, warehouse_permission_decorator_1.RequirePalletAccess)({ minimumRole: client_1.Role.WAREHOUSE_MEMBER }),
    (0, swagger_1.ApiOperation)({ summary: "Update a pallet" }),
    (0, swagger_1.ApiParam)({ name: "id", type: String, description: "Pallet ID" }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true })),
    (0, log_action_decorator_1.LogAction)({
        action: "UPDATE_PALLET",
        entity: "Pallet",
        getEntityId: (context) => context.switchToHttp().getRequest().params.id,
    }),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_pallet_dto_1.UpdatePalletDto, Object]),
    __metadata("design:returntype", void 0)
], PalletsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(":id"),
    (0, warehouse_permission_decorator_1.RequirePalletAccess)({ minimumRole: client_1.Role.WAREHOUSE_MANAGER }),
    (0, log_action_decorator_1.LogAction)({
        action: "DELETE_PALLET",
        entity: "Pallet",
        getEntityId: (context) => context.switchToHttp().getRequest().params.id,
    }),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], PalletsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(":id/move"),
    (0, warehouse_permission_decorator_1.RequirePalletAccess)({ minimumRole: client_1.Role.WAREHOUSE_MEMBER }),
    (0, swagger_1.ApiOperation)({ summary: "Move a pallet to a new location" }),
    (0, swagger_1.ApiParam)({ name: "id", type: String, description: "Pallet ID" }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true })),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, move_pallet_dto_1.MovePalletDto, Object]),
    __metadata("design:returntype", Promise)
], PalletsController.prototype, "movePallet", null);
__decorate([
    (0, common_1.Get)(":id/audit-logs"),
    (0, warehouse_permission_decorator_1.RequirePalletAccess)(),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], PalletsController.prototype, "getAuditLogs", null);
exports.PalletsController = PalletsController = PalletsController_1 = __decorate([
    (0, swagger_1.ApiTags)("Pallets"),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)("pallets"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, warehouse_permission_guard_1.WarehousePermissionGuard),
    (0, common_1.UseInterceptors)(audit_log_interceptor_1.AuditLogInterceptor),
    __metadata("design:paramtypes", [pallets_service_1.PalletsService])
], PalletsController);
//# sourceMappingURL=pallets.controller.js.map