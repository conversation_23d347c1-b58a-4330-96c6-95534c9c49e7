import { PrismaService } from '../prisma/prisma.service';
import { OnboardingState, OnboardingStep } from '@quildora/types';
export interface OnboardingSession {
    id: string;
    userId?: string;
    tenantId?: string;
    currentStep: OnboardingStep;
    data: OnboardingState;
    completedAt?: Date;
    expiresAt: Date;
    createdAt: Date;
    updatedAt: Date;
    warehouseId?: string;
}
export declare class SessionService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    createSession(data: Partial<OnboardingState>): Promise<OnboardingSession>;
    getSession(sessionId: string): Promise<OnboardingSession | null>;
    updateSession(sessionId: string, updates: Partial<OnboardingState & {
        userId?: string;
        tenantId?: string;
        warehouseId?: string;
    }>): Promise<OnboardingSession>;
    completeSession(sessionId: string): Promise<void>;
    deleteSession(sessionId: string): Promise<void>;
    cleanupExpiredSessions(): Promise<number>;
    getSessionsByUser(userId: string): Promise<OnboardingSession[]>;
    getSessionsByTenant(tenantId: string): Promise<OnboardingSession[]>;
    extendSession(sessionId: string, additionalHours?: number): Promise<OnboardingSession>;
    getSessionStats(): Promise<{
        total: number;
        active: number;
        completed: number;
        expired: number;
    }>;
    private mapPrismaSessionToSession;
}
