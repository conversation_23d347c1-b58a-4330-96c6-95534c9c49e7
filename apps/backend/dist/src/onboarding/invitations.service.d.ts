import { PrismaService } from "../prisma/prisma.service";
import { AuthService } from "../auth/auth.service";
import { CreateInvitationDto, AcceptInvitationDto, CreateInvitationResponseDto, ValidateInvitationResponseDto, AcceptInvitationResponseDto } from "./dto/invitations.dto";
export declare class InvitationsService {
    private readonly prisma;
    private readonly authService;
    constructor(prisma: PrismaService, authService: AuthService);
    createInvitation(invitedByUserId: string, dto: CreateInvitationDto): Promise<CreateInvitationResponseDto>;
    validateInvitation(code: string): Promise<ValidateInvitationResponseDto>;
    acceptInvitation(code: string, dto: AcceptInvitationDto): Promise<AcceptInvitationResponseDto>;
    listInvitations(tenantId: string): Promise<{
        id: string;
        email: string;
        role: import("@prisma/client").$Enums.Role;
        warehouseIds: string[];
        invitationCode: string;
        expiresAt: Date;
        acceptedAt: Date;
        createdAt: Date;
        invitedBy: {
            name: string;
            id: string;
            email: string;
        };
        acceptedByUser: {
            name: string;
            id: string;
            email: string;
        };
        status: string;
    }[]>;
    private generateInvitationCode;
    private getInvitationStatus;
}
