import { InvitationsService } from './invitations.service';
import { EnhancedUserPayload } from '../auth/types';
import { CreateInvitationDto, AcceptInvitationDto, CreateInvitationResponseDto, ValidateInvitationResponseDto, AcceptInvitationResponseDto } from './dto/invitations.dto';
export declare class InvitationsController {
    private readonly invitationsService;
    constructor(invitationsService: InvitationsService);
    createInvitation(req: {
        user: EnhancedUserPayload;
    }, dto: CreateInvitationDto): Promise<CreateInvitationResponseDto>;
    validateInvitation(code: string): Promise<ValidateInvitationResponseDto>;
    acceptInvitation(code: string, dto: AcceptInvitationDto): Promise<AcceptInvitationResponseDto>;
    listInvitations(req: {
        user: EnhancedUserPayload;
    }): Promise<{
        id: string;
        email: string;
        role: import("@prisma/client").$Enums.Role;
        warehouseIds: string[];
        invitationCode: string;
        expiresAt: Date;
        acceptedAt: Date;
        createdAt: Date;
        invitedBy: {
            name: string;
            id: string;
            email: string;
        };
        acceptedByUser: {
            name: string;
            id: string;
            email: string;
        };
        status: string;
    }[]>;
}
