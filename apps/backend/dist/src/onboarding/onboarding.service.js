"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnboardingService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
const session_service_1 = require("./session.service");
const warehouses_service_1 = require("../warehouses/warehouses.service");
const auth_service_1 = require("../auth/auth.service");
const invitations_service_1 = require("./invitations.service");
let OnboardingService = class OnboardingService {
    constructor(prisma, sessionService, warehousesService, authService, invitationsService) {
        this.prisma = prisma;
        this.sessionService = sessionService;
        this.warehousesService = warehousesService;
        this.authService = authService;
        this.invitationsService = invitationsService;
    }
    async startBusinessOnboarding(dto) {
        try {
            const session = await this.sessionService.createSession({
                step: "business_info",
                businessInfo: {
                    companyName: dto.companyName,
                    industry: dto.industry,
                    companySize: dto.companySize,
                    primaryUseCase: dto.primaryUseCase,
                },
            });
            return {
                sessionId: session.id,
                nextStep: "admin_account",
            };
        }
        catch (error) {
            console.error("Error starting business onboarding:", error);
            throw new common_1.InternalServerErrorException("Failed to start business onboarding process");
        }
    }
    async createAdminAccount(dto) {
        try {
            const session = await this.sessionService.getSession(dto.sessionId);
            if (!session) {
                throw new common_1.NotFoundException("Onboarding session not found");
            }
            if (session.currentStep !== "business_info") {
                throw new common_1.BadRequestException("Invalid step: admin account creation not allowed at this stage");
            }
            const supabaseUser = await this.authService.createSupabaseUser(dto.email, dto.password);
            const appUser = await this.prisma.user.create({
                data: {
                    email: dto.email,
                    name: dto.fullName,
                    authUserId: supabaseUser.id,
                    role: client_1.Role.TENANT_ADMIN,
                    status: "ACTIVE",
                },
            });
            await this.sessionService.updateSession(dto.sessionId, {
                step: "admin_account",
                adminAccount: {
                    fullName: dto.fullName,
                    email: dto.email,
                    password: dto.password,
                    phoneNumber: dto.phoneNumber,
                },
                userId: appUser.id,
            });
            return {
                userId: appUser.id,
                supabaseUser,
                nextStep: "warehouse_setup",
            };
        }
        catch (error) {
            console.error("Error creating admin account:", error);
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException("Failed to create admin account");
        }
    }
    async setupWarehouse(dto) {
        try {
            const session = await this.sessionService.getSession(dto.sessionId);
            if (!session) {
                throw new common_1.NotFoundException("Onboarding session not found");
            }
            if (session.currentStep !== "admin_account" || !session.userId) {
                throw new common_1.BadRequestException(`Invalid step: expected 'admin_account', got '${session.currentStep}'. Warehouse setup not allowed at this stage.`);
            }
            const sessionData = session.data;
            if (sessionData?.warehouseId) {
                return {
                    warehouseId: sessionData.warehouseId,
                    tenantId: sessionData.tenantId,
                    nextStep: "team_setup",
                };
            }
            const tenant = await this.prisma.tenant.create({
                data: {
                    name: session.data.businessInfo?.companyName || "New Company",
                },
            });
            await this.prisma.user.update({
                where: { id: session.userId },
                data: { tenantId: tenant.id },
            });
            const currentUser = {
                id: session.userId,
                email: session.data.adminAccount?.email || "",
                name: session.data.adminAccount?.fullName || "",
                role: client_1.Role.TENANT_ADMIN,
                tenantId: tenant.id,
                authUserId: null,
            };
            const warehouse = await this.warehousesService.create({
                name: dto.warehouseName,
                address: dto.address,
                tenantId: tenant.id,
            }, currentUser);
            await this.prisma.warehouseUser.upsert({
                where: {
                    userId_warehouseId: {
                        userId: session.userId,
                        warehouseId: warehouse.id,
                    },
                },
                update: {
                    role: client_1.Role.TENANT_ADMIN,
                },
                create: {
                    userId: session.userId,
                    warehouseId: warehouse.id,
                    role: client_1.Role.TENANT_ADMIN,
                },
            });
            await this.sessionService.updateSession(dto.sessionId, {
                step: "warehouse_setup",
                warehouseSetup: {
                    warehouseName: dto.warehouseName,
                    address: dto.address,
                    warehouseType: dto.warehouseType,
                    expectedVolume: dto.expectedVolume,
                },
                tenantId: tenant.id,
                warehouseId: warehouse.id,
            });
            return {
                warehouseId: warehouse.id,
                tenantId: tenant.id,
                nextStep: "team_setup",
            };
        }
        catch (error) {
            console.error("Error setting up warehouse:", error);
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException("Failed to setup warehouse");
        }
    }
    async completeOnboarding(dto) {
        try {
            const session = await this.sessionService.getSession(dto.sessionId);
            if (!session) {
                throw new common_1.NotFoundException("Onboarding session not found");
            }
            if (session.currentStep !== "warehouse_setup" ||
                !session.userId ||
                !session.tenantId) {
                throw new common_1.BadRequestException("Invalid step: onboarding completion not allowed at this stage");
            }
            const [user, tenant, warehouse] = await Promise.all([
                this.prisma.user.findUnique({
                    where: { id: session.userId },
                    include: { warehouseUsers: true },
                }),
                this.prisma.tenant.findUnique({
                    where: { id: session.tenantId },
                }),
                this.prisma.warehouse.findFirst({
                    where: { tenantId: session.tenantId },
                }),
            ]);
            if (!user || !tenant || !warehouse) {
                throw new common_1.InternalServerErrorException("Missing required data for completion");
            }
            const accessToken = await this.authService.generateAccessToken(user);
            let invitations = [];
            if (dto.teamSetup?.inviteEmails?.length > 0) {
                try {
                    for (const email of dto.teamSetup.inviteEmails) {
                        const invitation = await this.invitationsService.createInvitation(user.id, {
                            email,
                            role: dto.teamSetup.defaultRole === "WAREHOUSE_MANAGER"
                                ? client_1.Role.WAREHOUSE_MANAGER
                                : client_1.Role.WAREHOUSE_MEMBER,
                            warehouseIds: [warehouse.id],
                        });
                        invitations.push(invitation);
                    }
                }
                catch (error) {
                    console.error("Error creating invitations:", error);
                }
            }
            await this.sessionService.completeSession(dto.sessionId);
            return {
                accessToken,
                user: {
                    id: user.id,
                    email: user.email,
                    name: user.name,
                    role: user.role,
                    tenantId: user.tenantId,
                },
                warehouse: {
                    id: warehouse.id,
                    name: warehouse.name,
                    address: warehouse.address,
                },
                tenant: {
                    id: tenant.id,
                    name: tenant.name,
                },
                invitations: invitations.length > 0 ? invitations : undefined,
            };
        }
        catch (error) {
            console.error("Error completing onboarding:", error);
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException("Failed to complete onboarding");
        }
    }
    async completeProfile(userId, completeProfileDto) {
        const { companyName } = completeProfileDto;
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException("User not found.");
        }
        if (user.tenantId) {
            throw new common_1.ForbiddenException("User already has a tenant. Onboarding already completed or not applicable.");
        }
        try {
            const newTenant = await this.prisma.tenant.create({
                data: {
                    name: companyName,
                },
            });
            const updatedUser = await this.prisma.user.update({
                where: { id: userId },
                data: {
                    tenantId: newTenant.id,
                    role: client_1.Role.TENANT_ADMIN,
                    name: user.name || companyName,
                },
            });
            const { password, ...userWithoutPassword } = updatedUser;
            return userWithoutPassword;
        }
        catch (error) {
            console.error("Error during completeProfile:", error);
            throw new common_1.InternalServerErrorException("Failed to complete profile and create tenant.");
        }
    }
};
exports.OnboardingService = OnboardingService;
exports.OnboardingService = OnboardingService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        session_service_1.SessionService,
        warehouses_service_1.WarehousesService,
        auth_service_1.AuthService,
        invitations_service_1.InvitationsService])
], OnboardingService);
//# sourceMappingURL=onboarding.service.js.map