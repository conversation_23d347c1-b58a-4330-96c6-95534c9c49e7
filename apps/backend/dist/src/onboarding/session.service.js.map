{"version": 3, "file": "session.service.js", "sourceRoot": "", "sources": ["../../../src/onboarding/session.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA6F;AAC7F,6DAAyD;AACzD,2CAA+F;AAgBxF,IAAM,cAAc,GAApB,MAAM,cAAc;IACzB,YAA6B,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAEtD,KAAK,CAAC,aAAa,CAAC,IAA8B;QAChD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,mCAA2B,CAAC,CAAC;YAErE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;gBACzD,IAAI,EAAE;oBACJ,WAAW,EAAE,IAAI,CAAC,IAAI,IAAI,eAAe;oBACzC,IAAI,EAAE,IAAW;oBACjB,SAAS;iBACV;aACF,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,IAAI,qCAA4B,CAAC,qCAAqC,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,SAAiB;QAChC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC;gBAC7D,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;aACzB,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,IAAI,OAAO,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;gBACnC,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;gBACpC,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,IAAI,qCAA4B,CAAC,kCAAkC,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CACjB,SAAiB,EACjB,OAAgG;QAEhG,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACzD,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,MAAM,IAAI,0BAAiB,CAAC,8BAA8B,CAAC,CAAC;YAC9D,CAAC;YAGD,MAAM,WAAW,GAAG;gBAClB,GAAG,eAAe,CAAC,IAAI;gBACvB,GAAG,OAAO;aACX,CAAC;YAGF,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,WAAW,EAAE,GAAG,OAAO,CAAC;YAClE,MAAM,UAAU,GAAG;gBACjB,GAAG,eAAe,CAAC,IAAI;gBACvB,GAAG,WAAW;aACf,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;gBACzD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;gBACxB,IAAI,EAAE;oBACJ,WAAW,EAAE,OAAO,CAAC,IAAI,IAAI,eAAe,CAAC,WAAW;oBACxD,IAAI,EAAE,UAAiB;oBACvB,MAAM,EAAE,MAAM,IAAI,eAAe,CAAC,MAAM;oBACxC,QAAQ,EAAE,QAAQ,IAAI,eAAe,CAAC,QAAQ;oBAC9C,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;aACF,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;YAG9D,IAAI,WAAW,EAAE,CAAC;gBACf,aAAqB,CAAC,WAAW,GAAG,WAAW,CAAC;YACnD,CAAC;YAED,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC3D,IAAI,KAAK,YAAY,0BAAiB,EAAE,CAAC;gBACvC,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,qCAA4B,CAAC,qCAAqC,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,SAAiB;QACrC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACjD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,0BAAiB,CAAC,8BAA8B,CAAC,CAAC;YAC9D,CAAC;YAED,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;gBACzC,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;gBACxB,IAAI,EAAE;oBACJ,WAAW,EAAE,IAAI,IAAI,EAAE;oBACvB,WAAW,EAAE,YAAY;iBAC1B;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC7D,IAAI,KAAK,YAAY,0BAAiB,EAAE,CAAC;gBACvC,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,qCAA4B,CAAC,uCAAuC,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,SAAiB;QACnC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;gBACzC,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;aACzB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC3B,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;gBAC3D,MAAM,IAAI,qCAA4B,CAAC,qCAAqC,CAAC,CAAC;YAChF,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB;QAC1B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC;gBAC5D,KAAK,EAAE;oBACL,SAAS,EAAE;wBACT,EAAE,EAAE,IAAI,IAAI,EAAE;qBACf;oBACD,WAAW,EAAE,IAAI;iBAClB;aACF,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,cAAc,MAAM,CAAC,KAAK,8BAA8B,CAAC,CAAC;YACtE,OAAO,MAAM,CAAC,KAAK,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,IAAI,qCAA4B,CAAC,oCAAoC,CAAC,CAAC;QAC/E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAc;QACpC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;gBAC5D,KAAK,EAAE;oBACL,MAAM;oBACN,SAAS,EAAE;wBACT,EAAE,EAAE,IAAI,IAAI,EAAE;qBACf;iBACF;gBACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;aAC/B,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,CAAC;QAC1E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,IAAI,qCAA4B,CAAC,6BAA6B,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,QAAgB;QACxC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;gBAC5D,KAAK,EAAE;oBACL,QAAQ;oBACR,SAAS,EAAE;wBACT,EAAE,EAAE,IAAI,IAAI,EAAE;qBACf;iBACF;gBACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;aAC/B,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,CAAC;QAC1E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,IAAI,qCAA4B,CAAC,+BAA+B,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,SAAiB,EAAE,kBAA0B,EAAE;QACjE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACjD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,0BAAiB,CAAC,8BAA8B,CAAC,CAAC;YAC9D,CAAC;YAED,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,eAAe,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;YAEhG,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;gBAChE,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;gBACxB,IAAI,EAAE,EAAE,SAAS,EAAE,YAAY,EAAE;aAClC,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,CAAC;QACxD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC5D,IAAI,KAAK,YAAY,0BAAiB,EAAE,CAAC;gBACvC,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,qCAA4B,CAAC,qCAAqC,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe;QAMnB,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YAEvB,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC5D,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,EAAE;gBACrC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC;oBAClC,KAAK,EAAE;wBACL,SAAS,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;wBACtB,WAAW,EAAE,IAAI;qBAClB;iBACF,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC;oBAClC,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;iBACtC,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC;oBAClC,KAAK,EAAE;wBACL,SAAS,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;wBACtB,WAAW,EAAE,IAAI;qBAClB;iBACF,CAAC;aACH,CAAC,CAAC;YAEH,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,IAAI,qCAA4B,CAAC,kCAAkC,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAEO,yBAAyB,CAAC,aAAkB;QAClD,OAAO;YACL,EAAE,EAAE,aAAa,CAAC,EAAE;YACpB,MAAM,EAAE,aAAa,CAAC,MAAM;YAC5B,QAAQ,EAAE,aAAa,CAAC,QAAQ;YAChC,WAAW,EAAE,aAAa,CAAC,WAA6B;YACxD,IAAI,EAAE,aAAa,CAAC,IAAuB;YAC3C,WAAW,EAAE,aAAa,CAAC,WAAW;YACtC,SAAS,EAAE,aAAa,CAAC,SAAS;YAClC,SAAS,EAAE,aAAa,CAAC,SAAS;YAClC,SAAS,EAAE,aAAa,CAAC,SAAS;SACnC,CAAC;IACJ,CAAC;CACF,CAAA;AAtQY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;qCAE0B,8BAAa;GADvC,cAAc,CAsQ1B"}