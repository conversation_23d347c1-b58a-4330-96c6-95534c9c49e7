import { PrismaService } from "../prisma/prisma.service";
import { SessionService } from "./session.service";
import { WarehousesService } from "../warehouses/warehouses.service";
import { AuthService } from "../auth/auth.service";
import { InvitationsService } from "./invitations.service";
import { StartBusinessOnboardingDto, CreateAdminAccountDto, SetupWarehouseDto, CompleteOnboardingDto, StartBusinessOnboardingResponseDto, CreateAdminAccountResponseDto, SetupWarehouseResponseDto, CompleteOnboardingResponseDto, CompleteProfileDto } from "./dto";
export declare class OnboardingService {
    private readonly prisma;
    private readonly sessionService;
    private readonly warehousesService;
    private readonly authService;
    private readonly invitationsService;
    constructor(prisma: PrismaService, sessionService: SessionService, warehousesService: WarehousesService, authService: AuthService, invitationsService: InvitationsService);
    startBusinessOnboarding(dto: StartBusinessOnboardingDto): Promise<StartBusinessOnboardingResponseDto>;
    createAdminAccount(dto: CreateAdminAccountDto): Promise<CreateAdminAccountResponseDto>;
    setupWarehouse(dto: SetupWarehouseDto): Promise<SetupWarehouseResponseDto>;
    completeOnboarding(dto: CompleteOnboardingDto): Promise<CompleteOnboardingResponseDto>;
    completeProfile(userId: string, completeProfileDto: CompleteProfileDto): Promise<{
        name: string | null;
        status: string;
        id: string;
        email: string;
        authUserId: string | null;
        role: import("@prisma/client").$Enums.Role;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string | null;
    }>;
}
