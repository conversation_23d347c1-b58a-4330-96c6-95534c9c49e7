"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvitationsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const auth_service_1 = require("../auth/auth.service");
const client_1 = require("@prisma/client");
const types_1 = require("@quildora/types");
let InvitationsService = class InvitationsService {
    constructor(prisma, authService) {
        this.prisma = prisma;
        this.authService = authService;
    }
    async createInvitation(invitedByUserId, dto) {
        try {
            const invitingUser = await this.prisma.user.findUnique({
                where: { id: invitedByUserId },
                include: { warehouseUsers: true },
            });
            if (!invitingUser || !invitingUser.tenantId) {
                throw new common_1.ForbiddenException("User not authorized to send invitations");
            }
            const userWarehouseIds = invitingUser.warehouseUsers.map((wu) => wu.warehouseId);
            const hasAccessToAllWarehouses = dto.warehouseIds.every((id) => userWarehouseIds.includes(id));
            if (!hasAccessToAllWarehouses &&
                invitingUser.role !== client_1.Role.TENANT_ADMIN) {
                throw new common_1.ForbiddenException("Insufficient permissions for specified warehouses");
            }
            const [existingUser, existingInvitation] = await Promise.all([
                this.prisma.user.findUnique({ where: { email: dto.email } }),
                this.prisma.tenantInvitation.findFirst({
                    where: {
                        email: dto.email,
                        tenantId: invitingUser.tenantId,
                        acceptedAt: null,
                        expiresAt: { gt: new Date() },
                    },
                }),
            ]);
            if (existingUser) {
                throw new common_1.ConflictException("User with this email already exists");
            }
            if (existingInvitation) {
                throw new common_1.ConflictException("Active invitation already exists for this email");
            }
            const invitationCode = this.generateInvitationCode();
            const expiresAt = new Date(Date.now() + types_1.INVITATION_DURATION);
            const invitation = await this.prisma.tenantInvitation.create({
                data: {
                    tenantId: invitingUser.tenantId,
                    invitedByUserId,
                    email: dto.email,
                    role: dto.role,
                    warehouseIds: dto.warehouseIds,
                    invitationCode,
                    expiresAt,
                },
            });
            return {
                invitationId: invitation.id,
                invitationCode: invitation.invitationCode,
                expiresAt: invitation.expiresAt.toISOString(),
            };
        }
        catch (error) {
            console.error("Error creating invitation:", error);
            if (error instanceof common_1.ForbiddenException ||
                error instanceof common_1.ConflictException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException("Failed to create invitation");
        }
    }
    async validateInvitation(code) {
        try {
            const invitation = await this.prisma.tenantInvitation.findUnique({
                where: { invitationCode: code },
                include: {
                    tenant: true,
                    invitedBy: true,
                },
            });
            if (!invitation) {
                return { valid: false };
            }
            if (invitation.expiresAt < new Date()) {
                return { valid: false };
            }
            if (invitation.acceptedAt) {
                return { valid: false };
            }
            let warehouse = null;
            if (invitation.warehouseIds && invitation.warehouseIds.length > 0) {
                const warehouseData = await this.prisma.warehouse.findFirst({
                    where: { id: { in: invitation.warehouseIds } },
                });
                if (warehouseData) {
                    warehouse = {
                        id: warehouseData.id,
                        name: warehouseData.name,
                        address: warehouseData.address,
                    };
                }
            }
            return {
                valid: true,
                id: invitation.id,
                email: invitation.email,
                role: invitation.role,
                status: this.getInvitationStatus(invitation),
                expiresAt: invitation.expiresAt.toISOString(),
                tenant: {
                    id: invitation.tenant.id,
                    name: invitation.tenant.name,
                },
                warehouse,
                invitedBy: {
                    name: invitation.invitedBy.name,
                    email: invitation.invitedBy.email,
                },
            };
        }
        catch (error) {
            console.error("Error validating invitation:", error);
            throw new common_1.InternalServerErrorException("Failed to validate invitation");
        }
    }
    async acceptInvitation(code, dto) {
        try {
            const invitation = await this.prisma.tenantInvitation.findUnique({
                where: { invitationCode: code },
                include: { tenant: true },
            });
            if (!invitation) {
                throw new common_1.NotFoundException("Invitation not found");
            }
            if (invitation.expiresAt < new Date()) {
                throw new common_1.BadRequestException("Invitation has expired");
            }
            if (invitation.acceptedAt) {
                throw new common_1.BadRequestException("Invitation has already been accepted");
            }
            const existingUser = await this.prisma.user.findUnique({
                where: { email: invitation.email },
            });
            if (existingUser) {
                throw new common_1.ConflictException("User with this email already exists");
            }
            const supabaseUser = await this.authService.createSupabaseUser(invitation.email, dto.password);
            const user = await this.prisma.user.create({
                data: {
                    email: invitation.email,
                    name: dto.fullName,
                    authUserId: supabaseUser.id,
                    role: invitation.role,
                    tenantId: invitation.tenantId,
                    status: "ACTIVE",
                },
            });
            const warehouseAssignments = invitation.warehouseIds.map((warehouseId) => ({
                userId: user.id,
                warehouseId,
                role: invitation.role,
            }));
            await this.prisma.warehouseUser.createMany({
                data: warehouseAssignments,
            });
            await this.prisma.tenantInvitation.update({
                where: { id: invitation.id },
                data: {
                    acceptedAt: new Date(),
                    acceptedByUserId: user.id,
                },
            });
            const warehouses = await this.prisma.warehouse.findMany({
                where: { id: { in: invitation.warehouseIds } },
                select: { id: true, name: true, address: true },
            });
            const accessToken = await this.authService.generateAccessToken(user);
            return {
                accessToken,
                user: {
                    id: user.id,
                    email: user.email,
                    name: user.name,
                    role: user.role,
                    tenantId: user.tenantId,
                },
                warehouses,
                tenant: {
                    id: invitation.tenant.id,
                    name: invitation.tenant.name,
                },
            };
        }
        catch (error) {
            console.error("Error accepting invitation:", error);
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException ||
                error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException("Failed to accept invitation");
        }
    }
    async listInvitations(tenantId) {
        try {
            const invitations = await this.prisma.tenantInvitation.findMany({
                where: { tenantId },
                include: {
                    invitedBy: { select: { id: true, name: true, email: true } },
                    acceptedByUser: { select: { id: true, name: true, email: true } },
                },
                orderBy: { createdAt: "desc" },
            });
            return invitations.map((invitation) => ({
                id: invitation.id,
                email: invitation.email,
                role: invitation.role,
                warehouseIds: invitation.warehouseIds,
                invitationCode: invitation.invitationCode,
                expiresAt: invitation.expiresAt,
                acceptedAt: invitation.acceptedAt,
                createdAt: invitation.createdAt,
                invitedBy: invitation.invitedBy,
                acceptedByUser: invitation.acceptedByUser,
                status: this.getInvitationStatus(invitation),
            }));
        }
        catch (error) {
            console.error("Error listing invitations:", error);
            throw new common_1.InternalServerErrorException("Failed to list invitations");
        }
    }
    generateInvitationCode() {
        const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        let result = "INV-";
        for (let i = 0; i < 12; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    getInvitationStatus(invitation) {
        if (invitation.acceptedAt) {
            return "accepted";
        }
        if (invitation.expiresAt < new Date()) {
            return "expired";
        }
        return "pending";
    }
};
exports.InvitationsService = InvitationsService;
exports.InvitationsService = InvitationsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        auth_service_1.AuthService])
], InvitationsService);
//# sourceMappingURL=invitations.service.js.map