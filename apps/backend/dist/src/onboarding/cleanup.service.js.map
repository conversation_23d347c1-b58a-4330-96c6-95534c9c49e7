{"version": 3, "file": "cleanup.service.js", "sourceRoot": "", "sources": ["../../../src/onboarding/cleanup.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+CAAwD;AACxD,6DAAyD;AACzD,uDAAmD;AAG5C,IAAM,cAAc,sBAApB,MAAM,cAAc;IAGzB,YACmB,MAAqB,EACrB,cAA8B;QAD9B,WAAM,GAAN,MAAM,CAAe;QACrB,mBAAc,GAAd,cAAc,CAAgB;QAJhC,WAAM,GAAG,IAAI,eAAM,CAAC,gBAAc,CAAC,IAAI,CAAC,CAAC;IAKvD,CAAC;IAIE,AAAN,KAAK,CAAC,4BAA4B;QAChC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAEnE,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,sBAAsB,EAAE,CAAC;YACxE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,YAAY,8BAA8B,CAAC,CAAC;QAC5E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAIK,AAAN,KAAK,CAAC,+BAA+B;QACnC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QAE3D,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAC5D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,YAAY,sBAAsB,CAAC,CAAC;QACpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,yBAAyB;QAC7B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;gBAC3D,KAAK,EAAE;oBACL,SAAS,EAAE;wBACT,EAAE,EAAE,IAAI,IAAI,EAAE;qBACf;oBACD,UAAU,EAAE,IAAI;iBACjB;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,MAAM,CAAC,KAAK,sBAAsB,CAAC,CAAC;YAClE,OAAO,MAAM,CAAC,KAAK,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YACnE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,2BAA2B,CAAC,gBAAwB,EAAE;QAC1D,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;YAC9B,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,aAAa,CAAC,CAAC;YAEzD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC;gBAC5D,KAAK,EAAE;oBACL,WAAW,EAAE;wBACX,GAAG,EAAE,IAAI;wBACT,EAAE,EAAE,UAAU;qBACf;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,MAAM,CAAC,KAAK,oCAAoC,CAAC,CAAC;YAChF,OAAO,MAAM,CAAC,KAAK,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YACtE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe;QAKnB,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;YACjC,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;YAEpD,MAAM,CAAC,eAAe,EAAE,kBAAkB,EAAE,oBAAoB,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpF,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC;oBAClC,KAAK,EAAE;wBACL,SAAS,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;wBACtB,WAAW,EAAE,IAAI;qBAClB;iBACF,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC;oBACjC,KAAK,EAAE;wBACL,SAAS,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;wBACtB,UAAU,EAAE,IAAI;qBACjB;iBACF,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC;oBAClC,KAAK,EAAE;wBACL,WAAW,EAAE;4BACX,GAAG,EAAE,IAAI;4BACT,EAAE,EAAE,aAAa;yBAClB;qBACF;iBACF,CAAC;aACH,CAAC,CAAC;YAEH,OAAO;gBACL,eAAe;gBACf,kBAAkB;gBAClB,oBAAoB;aACrB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,cAAc;QAKlB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAEpD,IAAI,CAAC;YACH,MAAM,CAAC,eAAe,EAAE,kBAAkB,EAAE,oBAAoB,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpF,IAAI,CAAC,cAAc,CAAC,sBAAsB,EAAE;gBAC5C,IAAI,CAAC,yBAAyB,EAAE;gBAChC,IAAI,CAAC,2BAA2B,EAAE;aACnC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG;gBACd,eAAe;gBACf,kBAAkB;gBAClB,oBAAoB;aACrB,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC;YACpD,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AApJY,wCAAc;AAUnB;IADL,IAAA,eAAI,EAAC,yBAAc,CAAC,UAAU,CAAC;;;;kEAU/B;AAIK;IADL,IAAA,eAAI,EAAC,WAAW,CAAC;;;;qEAUjB;yBAhCU,cAAc;IAD1B,IAAA,mBAAU,GAAE;qCAKgB,8BAAa;QACL,gCAAc;GALtC,cAAc,CAoJ1B"}