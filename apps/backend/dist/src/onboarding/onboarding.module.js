"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnboardingModule = void 0;
const common_1 = require("@nestjs/common");
const onboarding_controller_1 = require("./onboarding.controller");
const onboarding_service_1 = require("./onboarding.service");
const session_service_1 = require("./session.service");
const invitations_controller_1 = require("./invitations.controller");
const invitations_service_1 = require("./invitations.service");
const cleanup_service_1 = require("./cleanup.service");
const prisma_module_1 = require("../prisma/prisma.module");
const auth_module_1 = require("../auth/auth.module");
const warehouses_module_1 = require("../warehouses/warehouses.module");
let OnboardingModule = class OnboardingModule {
};
exports.OnboardingModule = OnboardingModule;
exports.OnboardingModule = OnboardingModule = __decorate([
    (0, common_1.Module)({
        imports: [prisma_module_1.PrismaModule, auth_module_1.AuthModule, warehouses_module_1.WarehousesModule],
        controllers: [onboarding_controller_1.OnboardingController, invitations_controller_1.InvitationsController],
        providers: [
            onboarding_service_1.OnboardingService,
            session_service_1.SessionService,
            invitations_service_1.InvitationsService,
            cleanup_service_1.CleanupService,
        ],
        exports: [onboarding_service_1.OnboardingService, session_service_1.SessionService, invitations_service_1.InvitationsService],
    })
], OnboardingModule);
//# sourceMappingURL=onboarding.module.js.map