"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const types_1 = require("@quildora/types");
let SessionService = class SessionService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createSession(data) {
        try {
            const expiresAt = new Date(Date.now() + types_1.ONBOARDING_SESSION_DURATION);
            const session = await this.prisma.onboardingSession.create({
                data: {
                    currentStep: data.step || 'business_info',
                    data: data,
                    expiresAt,
                },
            });
            return this.mapPrismaSessionToSession(session);
        }
        catch (error) {
            console.error('Error creating onboarding session:', error);
            throw new common_1.InternalServerErrorException('Failed to create onboarding session');
        }
    }
    async getSession(sessionId) {
        try {
            const session = await this.prisma.onboardingSession.findUnique({
                where: { id: sessionId },
            });
            if (!session) {
                return null;
            }
            if (session.expiresAt < new Date()) {
                await this.deleteSession(sessionId);
                return null;
            }
            return this.mapPrismaSessionToSession(session);
        }
        catch (error) {
            console.error('Error getting onboarding session:', error);
            throw new common_1.InternalServerErrorException('Failed to get onboarding session');
        }
    }
    async updateSession(sessionId, updates) {
        try {
            const existingSession = await this.getSession(sessionId);
            if (!existingSession) {
                throw new common_1.NotFoundException('Onboarding session not found');
            }
            const updatedData = {
                ...existingSession.data,
                ...updates,
            };
            const { userId, tenantId, warehouseId, ...dataUpdates } = updates;
            const mergedData = {
                ...existingSession.data,
                ...dataUpdates,
            };
            const session = await this.prisma.onboardingSession.update({
                where: { id: sessionId },
                data: {
                    currentStep: updates.step || existingSession.currentStep,
                    data: mergedData,
                    userId: userId || existingSession.userId,
                    tenantId: tenantId || existingSession.tenantId,
                    updatedAt: new Date(),
                },
            });
            const mappedSession = this.mapPrismaSessionToSession(session);
            if (warehouseId) {
                mappedSession.warehouseId = warehouseId;
            }
            return mappedSession;
        }
        catch (error) {
            console.error('Error updating onboarding session:', error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to update onboarding session');
        }
    }
    async completeSession(sessionId) {
        try {
            const session = await this.getSession(sessionId);
            if (!session) {
                throw new common_1.NotFoundException('Onboarding session not found');
            }
            await this.prisma.onboardingSession.update({
                where: { id: sessionId },
                data: {
                    completedAt: new Date(),
                    currentStep: 'completion',
                },
            });
        }
        catch (error) {
            console.error('Error completing onboarding session:', error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to complete onboarding session');
        }
    }
    async deleteSession(sessionId) {
        try {
            await this.prisma.onboardingSession.delete({
                where: { id: sessionId },
            });
        }
        catch (error) {
            if (error.code !== 'P2025') {
                console.error('Error deleting onboarding session:', error);
                throw new common_1.InternalServerErrorException('Failed to delete onboarding session');
            }
        }
    }
    async cleanupExpiredSessions() {
        try {
            const result = await this.prisma.onboardingSession.deleteMany({
                where: {
                    expiresAt: {
                        lt: new Date(),
                    },
                    completedAt: null,
                },
            });
            console.log(`Cleaned up ${result.count} expired onboarding sessions`);
            return result.count;
        }
        catch (error) {
            console.error('Error cleaning up expired sessions:', error);
            throw new common_1.InternalServerErrorException('Failed to cleanup expired sessions');
        }
    }
    async getSessionsByUser(userId) {
        try {
            const sessions = await this.prisma.onboardingSession.findMany({
                where: {
                    userId,
                    expiresAt: {
                        gt: new Date(),
                    },
                },
                orderBy: { createdAt: 'desc' },
            });
            return sessions.map(session => this.mapPrismaSessionToSession(session));
        }
        catch (error) {
            console.error('Error getting sessions by user:', error);
            throw new common_1.InternalServerErrorException('Failed to get user sessions');
        }
    }
    async getSessionsByTenant(tenantId) {
        try {
            const sessions = await this.prisma.onboardingSession.findMany({
                where: {
                    tenantId,
                    expiresAt: {
                        gt: new Date(),
                    },
                },
                orderBy: { createdAt: 'desc' },
            });
            return sessions.map(session => this.mapPrismaSessionToSession(session));
        }
        catch (error) {
            console.error('Error getting sessions by tenant:', error);
            throw new common_1.InternalServerErrorException('Failed to get tenant sessions');
        }
    }
    async extendSession(sessionId, additionalHours = 24) {
        try {
            const session = await this.getSession(sessionId);
            if (!session) {
                throw new common_1.NotFoundException('Onboarding session not found');
            }
            const newExpiresAt = new Date(session.expiresAt.getTime() + (additionalHours * 60 * 60 * 1000));
            const updatedSession = await this.prisma.onboardingSession.update({
                where: { id: sessionId },
                data: { expiresAt: newExpiresAt },
            });
            return this.mapPrismaSessionToSession(updatedSession);
        }
        catch (error) {
            console.error('Error extending onboarding session:', error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to extend onboarding session');
        }
    }
    async getSessionStats() {
        try {
            const now = new Date();
            const [total, active, completed, expired] = await Promise.all([
                this.prisma.onboardingSession.count(),
                this.prisma.onboardingSession.count({
                    where: {
                        expiresAt: { gt: now },
                        completedAt: null,
                    },
                }),
                this.prisma.onboardingSession.count({
                    where: { completedAt: { not: null } },
                }),
                this.prisma.onboardingSession.count({
                    where: {
                        expiresAt: { lt: now },
                        completedAt: null,
                    },
                }),
            ]);
            return { total, active, completed, expired };
        }
        catch (error) {
            console.error('Error getting session stats:', error);
            throw new common_1.InternalServerErrorException('Failed to get session statistics');
        }
    }
    mapPrismaSessionToSession(prismaSession) {
        return {
            id: prismaSession.id,
            userId: prismaSession.userId,
            tenantId: prismaSession.tenantId,
            currentStep: prismaSession.currentStep,
            data: prismaSession.data,
            completedAt: prismaSession.completedAt,
            expiresAt: prismaSession.expiresAt,
            createdAt: prismaSession.createdAt,
            updatedAt: prismaSession.updatedAt,
        };
    }
};
exports.SessionService = SessionService;
exports.SessionService = SessionService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], SessionService);
//# sourceMappingURL=session.service.js.map