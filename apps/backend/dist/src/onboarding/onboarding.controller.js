"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnboardingController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const onboarding_service_1 = require("./onboarding.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const dto_1 = require("./dto");
let OnboardingController = class OnboardingController {
    constructor(onboardingService) {
        this.onboardingService = onboardingService;
    }
    async startBusinessOnboarding(dto) {
        return this.onboardingService.startBusinessOnboarding(dto);
    }
    async createAdminAccount(dto) {
        return this.onboardingService.createAdminAccount(dto);
    }
    async setupWarehouse(dto) {
        return this.onboardingService.setupWarehouse(dto);
    }
    async completeOnboarding(dto) {
        return this.onboardingService.completeOnboarding(dto);
    }
    async completeProfile(req, completeProfileDto) {
        const userId = req.user.id;
        return this.onboardingService.completeProfile(userId, completeProfileDto);
    }
};
exports.OnboardingController = OnboardingController;
__decorate([
    (0, common_1.Post)("business/start"),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: "Start business onboarding process" }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: "Onboarding session created successfully",
        type: dto_1.StartBusinessOnboardingResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: "Invalid business information" }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.StartBusinessOnboardingDto]),
    __metadata("design:returntype", Promise)
], OnboardingController.prototype, "startBusinessOnboarding", null);
__decorate([
    (0, common_1.Post)("business/admin-account"),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: "Create admin account during onboarding" }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: "Admin account created successfully",
        type: dto_1.CreateAdminAccountResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: "Invalid account information" }),
    (0, swagger_1.ApiResponse)({ status: 404, description: "Onboarding session not found" }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateAdminAccountDto]),
    __metadata("design:returntype", Promise)
], OnboardingController.prototype, "createAdminAccount", null);
__decorate([
    (0, common_1.Post)("business/warehouse"),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: "Setup warehouse during onboarding" }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: "Warehouse setup completed successfully",
        type: dto_1.SetupWarehouseResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: "Invalid warehouse information" }),
    (0, swagger_1.ApiResponse)({ status: 404, description: "Onboarding session not found" }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.SetupWarehouseDto]),
    __metadata("design:returntype", Promise)
], OnboardingController.prototype, "setupWarehouse", null);
__decorate([
    (0, common_1.Post)("business/complete"),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: "Complete business onboarding process" }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: "Onboarding completed successfully",
        type: dto_1.CompleteOnboardingResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: "Invalid completion data" }),
    (0, swagger_1.ApiResponse)({ status: 404, description: "Onboarding session not found" }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CompleteOnboardingDto]),
    __metadata("design:returntype", Promise)
], OnboardingController.prototype, "completeOnboarding", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)("complete-profile"),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: "Complete user profile (legacy)" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "Profile completed successfully" }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], OnboardingController.prototype, "completeProfile", null);
exports.OnboardingController = OnboardingController = __decorate([
    (0, swagger_1.ApiTags)("onboarding"),
    (0, common_1.Controller)("onboarding"),
    __metadata("design:paramtypes", [onboarding_service_1.OnboardingService])
], OnboardingController);
//# sourceMappingURL=onboarding.controller.js.map