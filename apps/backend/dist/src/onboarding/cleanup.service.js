"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CleanupService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CleanupService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const prisma_service_1 = require("../prisma/prisma.service");
const session_service_1 = require("./session.service");
let CleanupService = CleanupService_1 = class CleanupService {
    constructor(prisma, sessionService) {
        this.prisma = prisma;
        this.sessionService = sessionService;
        this.logger = new common_1.Logger(CleanupService_1.name);
    }
    async handleExpiredSessionsCleanup() {
        this.logger.log('Starting expired onboarding sessions cleanup...');
        try {
            const deletedCount = await this.sessionService.cleanupExpiredSessions();
            this.logger.log(`Cleaned up ${deletedCount} expired onboarding sessions`);
        }
        catch (error) {
            this.logger.error('Failed to cleanup expired onboarding sessions:', error);
        }
    }
    async handleExpiredInvitationsCleanup() {
        this.logger.log('Starting expired invitations cleanup...');
        try {
            const deletedCount = await this.cleanupExpiredInvitations();
            this.logger.log(`Cleaned up ${deletedCount} expired invitations`);
        }
        catch (error) {
            this.logger.error('Failed to cleanup expired invitations:', error);
        }
    }
    async cleanupExpiredInvitations() {
        try {
            const result = await this.prisma.tenantInvitation.deleteMany({
                where: {
                    expiresAt: {
                        lt: new Date(),
                    },
                    acceptedAt: null,
                },
            });
            this.logger.log(`Cleaned up ${result.count} expired invitations`);
            return result.count;
        }
        catch (error) {
            this.logger.error('Error cleaning up expired invitations:', error);
            throw error;
        }
    }
    async cleanupOldCompletedSessions(olderThanDays = 30) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
            const result = await this.prisma.onboardingSession.deleteMany({
                where: {
                    completedAt: {
                        not: null,
                        lt: cutoffDate,
                    },
                },
            });
            this.logger.log(`Cleaned up ${result.count} old completed onboarding sessions`);
            return result.count;
        }
        catch (error) {
            this.logger.error('Error cleaning up old completed sessions:', error);
            throw error;
        }
    }
    async getCleanupStats() {
        try {
            const now = new Date();
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            const [expiredSessions, expiredInvitations, oldCompletedSessions] = await Promise.all([
                this.prisma.onboardingSession.count({
                    where: {
                        expiresAt: { lt: now },
                        completedAt: null,
                    },
                }),
                this.prisma.tenantInvitation.count({
                    where: {
                        expiresAt: { lt: now },
                        acceptedAt: null,
                    },
                }),
                this.prisma.onboardingSession.count({
                    where: {
                        completedAt: {
                            not: null,
                            lt: thirtyDaysAgo,
                        },
                    },
                }),
            ]);
            return {
                expiredSessions,
                expiredInvitations,
                oldCompletedSessions,
            };
        }
        catch (error) {
            this.logger.error('Error getting cleanup stats:', error);
            throw error;
        }
    }
    async runFullCleanup() {
        this.logger.log('Starting full cleanup process...');
        try {
            const [expiredSessions, expiredInvitations, oldCompletedSessions] = await Promise.all([
                this.sessionService.cleanupExpiredSessions(),
                this.cleanupExpiredInvitations(),
                this.cleanupOldCompletedSessions(),
            ]);
            const results = {
                expiredSessions,
                expiredInvitations,
                oldCompletedSessions,
            };
            this.logger.log('Full cleanup completed:', results);
            return results;
        }
        catch (error) {
            this.logger.error('Error during full cleanup:', error);
            throw error;
        }
    }
};
exports.CleanupService = CleanupService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_HOUR),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CleanupService.prototype, "handleExpiredSessionsCleanup", null);
__decorate([
    (0, schedule_1.Cron)('0 2 * * *'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CleanupService.prototype, "handleExpiredInvitationsCleanup", null);
exports.CleanupService = CleanupService = CleanupService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        session_service_1.SessionService])
], CleanupService);
//# sourceMappingURL=cleanup.service.js.map