{"version": 3, "file": "onboarding.service.js", "sourceRoot": "", "sources": ["../../../src/onboarding/onboarding.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAMwB;AACxB,6DAAyD;AACzD,2CAAsC;AACtC,uDAAmD;AACnD,yEAAqE;AACrE,uDAAmD;AACnD,+DAA2D;AAcpD,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAC5B,YACmB,MAAqB,EACrB,cAA8B,EAC9B,iBAAoC,EACpC,WAAwB,EACxB,kBAAsC;QAJtC,WAAM,GAAN,MAAM,CAAe;QACrB,mBAAc,GAAd,cAAc,CAAgB;QAC9B,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,gBAAW,GAAX,WAAW,CAAa;QACxB,uBAAkB,GAAlB,kBAAkB,CAAoB;IACtD,CAAC;IAEJ,KAAK,CAAC,uBAAuB,CAC3B,GAA+B;QAE/B,IAAI,CAAC;YAEH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;gBACtD,IAAI,EAAE,eAAe;gBACrB,YAAY,EAAE;oBACZ,WAAW,EAAE,GAAG,CAAC,WAAW;oBAC5B,QAAQ,EAAE,GAAG,CAAC,QAAQ;oBACtB,WAAW,EAAE,GAAG,CAAC,WAAW;oBAC5B,cAAc,EAAE,GAAG,CAAC,cAAc;iBACnC;aACF,CAAC,CAAC;YAEH,OAAO;gBACL,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,QAAQ,EAAE,eAAe;aAC1B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,IAAI,qCAA4B,CACpC,6CAA6C,CAC9C,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CACtB,GAA0B;QAE1B,IAAI,CAAC;YAEH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACpE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,0BAAiB,CAAC,8BAA8B,CAAC,CAAC;YAC9D,CAAC;YAED,IAAI,OAAO,CAAC,WAAW,KAAK,eAAe,EAAE,CAAC;gBAC5C,MAAM,IAAI,4BAAmB,CAC3B,gEAAgE,CACjE,CAAC;YACJ,CAAC;YAGD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAC5D,GAAG,CAAC,KAAK,EACT,GAAG,CAAC,QAAQ,CACb,CAAC;YAGF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC5C,IAAI,EAAE;oBACJ,KAAK,EAAE,GAAG,CAAC,KAAK;oBAChB,IAAI,EAAE,GAAG,CAAC,QAAQ;oBAClB,UAAU,EAAE,YAAY,CAAC,EAAE;oBAC3B,IAAI,EAAE,aAAI,CAAC,YAAY;oBACvB,MAAM,EAAE,QAAQ;iBACjB;aACF,CAAC,CAAC;YAGH,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE;gBACrD,IAAI,EAAE,eAAe;gBACrB,YAAY,EAAE;oBACZ,QAAQ,EAAE,GAAG,CAAC,QAAQ;oBACtB,KAAK,EAAE,GAAG,CAAC,KAAK;oBAChB,QAAQ,EAAE,GAAG,CAAC,QAAQ;oBACtB,WAAW,EAAE,GAAG,CAAC,WAAW;iBAC7B;gBACD,MAAM,EAAE,OAAO,CAAC,EAAE;aACnB,CAAC,CAAC;YAEH,OAAO;gBACL,MAAM,EAAE,OAAO,CAAC,EAAE;gBAClB,YAAY;gBACZ,QAAQ,EAAE,iBAAiB;aAC5B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,IACE,KAAK,YAAY,0BAAiB;gBAClC,KAAK,YAAY,4BAAmB,EACpC,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,qCAA4B,CAAC,gCAAgC,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,GAAsB;QAEtB,IAAI,CAAC;YAEH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACpE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,0BAAiB,CAAC,8BAA8B,CAAC,CAAC;YAC9D,CAAC;YAED,IAAI,OAAO,CAAC,WAAW,KAAK,eAAe,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBAC/D,MAAM,IAAI,4BAAmB,CAC3B,gDAAgD,OAAO,CAAC,WAAW,+CAA+C,CACnH,CAAC;YACJ,CAAC;YAGD,MAAM,WAAW,GAAG,OAAO,CAAC,IAAW,CAAC;YACxC,IAAI,WAAW,EAAE,WAAW,EAAE,CAAC;gBAC7B,OAAO;oBACL,WAAW,EAAE,WAAW,CAAC,WAAW;oBACpC,QAAQ,EAAE,WAAW,CAAC,QAAQ;oBAC9B,QAAQ,EAAE,YAAqB;iBAChC,CAAC;YACJ,CAAC;YAGD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;gBAC7C,IAAI,EAAE;oBACJ,IAAI,EACD,OAAO,CAAC,IAAY,CAAC,YAAY,EAAE,WAAW,IAAI,aAAa;iBACnE;aACF,CAAC,CAAC;YAGH,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC5B,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE;gBAC7B,IAAI,EAAE,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE;aAC9B,CAAC,CAAC;YAGH,MAAM,WAAW,GAAG;gBAClB,EAAE,EAAE,OAAO,CAAC,MAAM;gBAClB,KAAK,EAAG,OAAO,CAAC,IAAY,CAAC,YAAY,EAAE,KAAK,IAAI,EAAE;gBACtD,IAAI,EAAG,OAAO,CAAC,IAAY,CAAC,YAAY,EAAE,QAAQ,IAAI,EAAE;gBACxD,IAAI,EAAE,aAAI,CAAC,YAAY;gBACvB,QAAQ,EAAE,MAAM,CAAC,EAAE;gBACnB,UAAU,EAAE,IAAI;aACjB,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CACnD;gBACE,IAAI,EAAE,GAAG,CAAC,aAAa;gBACvB,OAAO,EAAE,GAAG,CAAC,OAAO;gBACpB,QAAQ,EAAE,MAAM,CAAC,EAAE;aACpB,EACD,WAAW,CACZ,CAAC;YAGF,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;gBACrC,KAAK,EAAE;oBACL,kBAAkB,EAAE;wBAClB,MAAM,EAAE,OAAO,CAAC,MAAM;wBACtB,WAAW,EAAE,SAAS,CAAC,EAAE;qBAC1B;iBACF;gBACD,MAAM,EAAE;oBACN,IAAI,EAAE,aAAI,CAAC,YAAY;iBACxB;gBACD,MAAM,EAAE;oBACN,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,WAAW,EAAE,SAAS,CAAC,EAAE;oBACzB,IAAI,EAAE,aAAI,CAAC,YAAY;iBACxB;aACF,CAAC,CAAC;YAGH,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE;gBACrD,IAAI,EAAE,iBAAiB;gBACvB,cAAc,EAAE;oBACd,aAAa,EAAE,GAAG,CAAC,aAAa;oBAChC,OAAO,EAAE,GAAG,CAAC,OAAO;oBACpB,aAAa,EAAE,GAAG,CAAC,aAAa;oBAChC,cAAc,EAAE,GAAG,CAAC,cAAc;iBACnC;gBACD,QAAQ,EAAE,MAAM,CAAC,EAAE;gBACnB,WAAW,EAAE,SAAS,CAAC,EAAE;aAC1B,CAAC,CAAC;YAEH,OAAO;gBACL,WAAW,EAAE,SAAS,CAAC,EAAE;gBACzB,QAAQ,EAAE,MAAM,CAAC,EAAE;gBACnB,QAAQ,EAAE,YAAqB;aAChC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,IACE,KAAK,YAAY,0BAAiB;gBAClC,KAAK,YAAY,4BAAmB,EACpC,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,qCAA4B,CAAC,2BAA2B,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CACtB,GAA0B;QAE1B,IAAI,CAAC;YAEH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACpE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,0BAAiB,CAAC,8BAA8B,CAAC,CAAC;YAC9D,CAAC;YAED,IACE,OAAO,CAAC,WAAW,KAAK,iBAAiB;gBACzC,CAAC,OAAO,CAAC,MAAM;gBACf,CAAC,OAAO,CAAC,QAAQ,EACjB,CAAC;gBACD,MAAM,IAAI,4BAAmB,CAC3B,+DAA+D,CAChE,CAAC;YACJ,CAAC;YAGD,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAClD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBAC1B,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE;oBAC7B,OAAO,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE;iBAClC,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;oBAC5B,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,QAAQ,EAAE;iBAChC,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC;oBAC9B,KAAK,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE;iBACtC,CAAC;aACH,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnC,MAAM,IAAI,qCAA4B,CACpC,sCAAsC,CACvC,CAAC;YACJ,CAAC;YAGD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAGrE,IAAI,WAAW,GAAG,EAAE,CAAC;YACrB,IAAI,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5C,IAAI,CAAC;oBAEH,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;wBAC/C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAC/D,IAAI,CAAC,EAAE,EACP;4BACE,KAAK;4BACL,IAAI,EACF,GAAG,CAAC,SAAS,CAAC,WAAW,KAAK,mBAAmB;gCAC/C,CAAC,CAAC,aAAI,CAAC,iBAAiB;gCACxB,CAAC,CAAC,aAAI,CAAC,gBAAgB;4BAC3B,YAAY,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;yBAC7B,CACF,CAAC;wBACF,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBAC/B,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;gBAGtD,CAAC;YACH,CAAC;YAGD,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAEzD,OAAO;gBACL,WAAW;gBACX,IAAI,EAAE;oBACJ,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;iBACxB;gBACD,SAAS,EAAE;oBACT,EAAE,EAAE,SAAS,CAAC,EAAE;oBAChB,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,OAAO,EAAE,SAAS,CAAC,OAAO;iBAC3B;gBACD,MAAM,EAAE;oBACN,EAAE,EAAE,MAAM,CAAC,EAAE;oBACb,IAAI,EAAE,MAAM,CAAC,IAAI;iBAClB;gBACD,WAAW,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;aAC9D,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,IACE,KAAK,YAAY,0BAAiB;gBAClC,KAAK,YAAY,4BAAmB,EACpC,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,qCAA4B,CAAC,+BAA+B,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,eAAe,CACnB,MAAc,EACd,kBAAsC;QAEtC,MAAM,EAAE,WAAW,EAAE,GAAG,kBAAkB,CAAC;QAE3C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,0BAAiB,CAAC,iBAAiB,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,IAAI,2BAAkB,CAC1B,4EAA4E,CAC7E,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;gBAChD,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAElB;aACF,CAAC,CAAC;YAGH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBAChD,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;gBACrB,IAAI,EAAE;oBACJ,QAAQ,EAAE,SAAS,CAAC,EAAE;oBACtB,IAAI,EAAE,aAAI,CAAC,YAAY;oBACvB,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,WAAW;iBAC/B;aACF,CAAC,CAAC;YAGH,MAAM,EAAE,QAAQ,EAAE,GAAG,mBAAmB,EAAE,GAAG,WAAW,CAAC;YACzD,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YAEtD,MAAM,IAAI,qCAA4B,CACpC,+CAA+C,CAChD,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAxWY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;qCAGgB,8BAAa;QACL,gCAAc;QACX,sCAAiB;QACvB,0BAAW;QACJ,wCAAkB;GAN9C,iBAAiB,CAwW7B"}