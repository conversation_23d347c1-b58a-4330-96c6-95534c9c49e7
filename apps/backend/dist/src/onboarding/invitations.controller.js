"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvitationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const invitations_service_1 = require("./invitations.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const invitations_dto_1 = require("./dto/invitations.dto");
let InvitationsController = class InvitationsController {
    constructor(invitationsService) {
        this.invitationsService = invitationsService;
    }
    async createInvitation(req, dto) {
        return this.invitationsService.createInvitation(req.user.id, dto);
    }
    async validateInvitation(code) {
        return this.invitationsService.validateInvitation(code);
    }
    async acceptInvitation(code, dto) {
        return this.invitationsService.acceptInvitation(code, dto);
    }
    async listInvitations(req) {
        return this.invitationsService.listInvitations(req.user.tenantId);
    }
};
exports.InvitationsController = InvitationsController;
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('create'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create invitation for new team member' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Invitation created successfully',
        type: invitations_dto_1.CreateInvitationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid invitation data' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Insufficient permissions' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, invitations_dto_1.CreateInvitationDto]),
    __metadata("design:returntype", Promise)
], InvitationsController.prototype, "createInvitation", null);
__decorate([
    (0, common_1.Get)('validate/:code'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Validate invitation code' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Invitation validation result',
        type: invitations_dto_1.ValidateInvitationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Invitation not found or expired' }),
    __param(0, (0, common_1.Param)('code')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], InvitationsController.prototype, "validateInvitation", null);
__decorate([
    (0, common_1.Post)('accept/:code'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Accept invitation and create account' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Invitation accepted successfully',
        type: invitations_dto_1.AcceptInvitationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid account data' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Invitation not found or expired' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Email already exists' }),
    __param(0, (0, common_1.Param)('code')),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, invitations_dto_1.AcceptInvitationDto]),
    __metadata("design:returntype", Promise)
], InvitationsController.prototype, "acceptInvitation", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('list'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'List invitations for current tenant' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of invitations',
        type: [Object],
    }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], InvitationsController.prototype, "listInvitations", null);
exports.InvitationsController = InvitationsController = __decorate([
    (0, swagger_1.ApiTags)('invitations'),
    (0, common_1.Controller)('invitations'),
    __metadata("design:paramtypes", [invitations_service_1.InvitationsService])
], InvitationsController);
//# sourceMappingURL=invitations.controller.js.map