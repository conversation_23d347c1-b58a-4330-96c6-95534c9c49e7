export declare class CreateInvitationDto {
    email: string;
    role: "WAREHOUSE_MEMBER" | "WAREHOUSE_MANAGER";
    warehouseIds: string[];
}
export declare class AcceptInvitationDto {
    fullName: string;
    password: string;
}
export declare class CreateInvitationResponseDto {
    invitationId: string;
    invitationCode: string;
    expiresAt: string;
}
export declare class ValidateInvitationResponseDto {
    valid: boolean;
    id?: string;
    email?: string;
    role?: string;
    status?: string;
    expiresAt?: string;
    tenant?: {
        id: string;
        name: string;
    };
    warehouse?: {
        id: string;
        name: string;
        address?: string;
    };
    invitedBy?: {
        name: string;
        email: string;
    };
}
export declare class AcceptInvitationResponseDto {
    accessToken: string;
    user: any;
    warehouses: any[];
    tenant: any;
}
