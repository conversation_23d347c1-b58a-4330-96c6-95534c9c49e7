"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AcceptInvitationResponseDto = exports.ValidateInvitationResponseDto = exports.CreateInvitationResponseDto = exports.AcceptInvitationDto = exports.CreateInvitationDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const client_1 = require("@prisma/client");
class CreateInvitationDto {
}
exports.CreateInvitationDto = CreateInvitationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Email address to invite",
        example: "<EMAIL>",
    }),
    (0, class_validator_1.IsEmail)({}, { message: "Please enter a valid email address" }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateInvitationDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Role for the invited user",
        enum: ["WAREHOUSE_MEMBER", "WAREHOUSE_MANAGER"],
        example: "WAREHOUSE_MEMBER",
    }),
    (0, class_validator_1.IsEnum)(["WAREHOUSE_MEMBER", "WAREHOUSE_MANAGER"], {
        message: "Role must be WAREHOUSE_MEMBER or WAREHOUSE_MANAGER",
    }),
    __metadata("design:type", String)
], CreateInvitationDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Array of warehouse IDs the user should have access to",
        example: ["clp123abc456def789"],
        type: [String],
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateInvitationDto.prototype, "warehouseIds", void 0);
class AcceptInvitationDto {
}
exports.AcceptInvitationDto = AcceptInvitationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Full name of the user",
        example: "Jane Smith",
        minLength: 2,
        maxLength: 100,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MinLength)(2, { message: "Full name must be at least 2 characters" }),
    (0, class_validator_1.MaxLength)(100, { message: "Full name must be less than 100 characters" }),
    __metadata("design:type", String)
], AcceptInvitationDto.prototype, "fullName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Password for the new account",
        example: "SecurePassword123!",
        minLength: 8,
        maxLength: 128,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MinLength)(8, { message: "Password must be at least 8 characters" }),
    (0, class_validator_1.MaxLength)(128, { message: "Password must be less than 128 characters" }),
    (0, class_validator_1.Matches)(/[A-Z]/, {
        message: "Password must contain at least one uppercase letter",
    }),
    (0, class_validator_1.Matches)(/[a-z]/, {
        message: "Password must contain at least one lowercase letter",
    }),
    (0, class_validator_1.Matches)(/[0-9]/, { message: "Password must contain at least one number" }),
    __metadata("design:type", String)
], AcceptInvitationDto.prototype, "password", void 0);
class CreateInvitationResponseDto {
}
exports.CreateInvitationResponseDto = CreateInvitationResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Unique invitation ID",
        example: "clp123abc456def789",
    }),
    __metadata("design:type", String)
], CreateInvitationResponseDto.prototype, "invitationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Invitation code for sharing",
        example: "INV-ABC123DEF456",
    }),
    __metadata("design:type", String)
], CreateInvitationResponseDto.prototype, "invitationCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Invitation expiration date",
        example: "2024-12-22T10:30:00Z",
    }),
    __metadata("design:type", String)
], CreateInvitationResponseDto.prototype, "expiresAt", void 0);
class ValidateInvitationResponseDto {
}
exports.ValidateInvitationResponseDto = ValidateInvitationResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Whether the invitation is valid",
        example: true,
    }),
    __metadata("design:type", Boolean)
], ValidateInvitationResponseDto.prototype, "valid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Invitation ID",
        required: false,
        example: "clp123abc456def789",
    }),
    __metadata("design:type", String)
], ValidateInvitationResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Email address that was invited",
        required: false,
        example: "<EMAIL>",
    }),
    __metadata("design:type", String)
], ValidateInvitationResponseDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Role that will be assigned",
        enum: client_1.Role,
        required: false,
        example: client_1.Role.WAREHOUSE_MEMBER,
    }),
    __metadata("design:type", String)
], ValidateInvitationResponseDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Invitation status",
        required: false,
        example: "pending",
    }),
    __metadata("design:type", String)
], ValidateInvitationResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Invitation expiration date",
        required: false,
        example: "2024-12-22T10:30:00Z",
    }),
    __metadata("design:type", String)
], ValidateInvitationResponseDto.prototype, "expiresAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Tenant information",
        required: false,
        type: Object,
    }),
    __metadata("design:type", Object)
], ValidateInvitationResponseDto.prototype, "tenant", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Warehouse information",
        required: false,
        type: Object,
    }),
    __metadata("design:type", Object)
], ValidateInvitationResponseDto.prototype, "warehouse", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Information about who sent the invitation",
        required: false,
        type: Object,
    }),
    __metadata("design:type", Object)
], ValidateInvitationResponseDto.prototype, "invitedBy", void 0);
class AcceptInvitationResponseDto {
}
exports.AcceptInvitationResponseDto = AcceptInvitationResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "JWT access token for the new user",
        example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    }),
    __metadata("design:type", String)
], AcceptInvitationResponseDto.prototype, "accessToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Created user object",
        type: Object,
    }),
    __metadata("design:type", Object)
], AcceptInvitationResponseDto.prototype, "user", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Warehouses the user has access to",
        type: [Object],
    }),
    __metadata("design:type", Array)
], AcceptInvitationResponseDto.prototype, "warehouses", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Tenant information",
        type: Object,
    }),
    __metadata("design:type", Object)
], AcceptInvitationResponseDto.prototype, "tenant", void 0);
//# sourceMappingURL=invitations.dto.js.map