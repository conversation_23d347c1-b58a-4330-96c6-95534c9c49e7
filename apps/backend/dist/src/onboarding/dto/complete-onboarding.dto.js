"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompleteOnboardingDto = exports.TeamSetupDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
const types_1 = require("@quildora/types");
class TeamSetupDto {
}
exports.TeamSetupDto = TeamSetupDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Email addresses to invite to the team",
        example: ["<EMAIL>", "<EMAIL>"],
        type: [String],
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEmail)({}, { each: true, message: "Please enter valid email addresses" }),
    __metadata("design:type", Array)
], TeamSetupDto.prototype, "inviteEmails", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Default role for invited team members",
        enum: types_1.Role,
        example: types_1.Role.WAREHOUSE_MEMBER,
    }),
    (0, class_validator_1.IsEnum)(types_1.Role, { message: "Invalid role specified" }),
    __metadata("design:type", String)
], TeamSetupDto.prototype, "defaultRole", void 0);
class CompleteOnboardingDto {
}
exports.CompleteOnboardingDto = CompleteOnboardingDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Onboarding session ID",
        example: "clp123abc456def789",
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CompleteOnboardingDto.prototype, "sessionId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: "Team setup information (optional)",
        type: TeamSetupDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => TeamSetupDto),
    __metadata("design:type", TeamSetupDto)
], CompleteOnboardingDto.prototype, "teamSetup", void 0);
//# sourceMappingURL=complete-onboarding.dto.js.map