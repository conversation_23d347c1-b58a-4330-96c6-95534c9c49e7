"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SetupWarehouseDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class SetupWarehouseDto {
}
exports.SetupWarehouseDto = SetupWarehouseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Onboarding session ID',
        example: 'clp123abc456def789',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], SetupWarehouseDto.prototype, "sessionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Warehouse name',
        example: 'Main Distribution Center',
        minLength: 2,
        maxLength: 100,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MinLength)(2, { message: 'Warehouse name must be at least 2 characters' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Warehouse name must be less than 100 characters' }),
    __metadata("design:type", String)
], SetupWarehouseDto.prototype, "warehouseName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Warehouse address',
        example: '123 Industrial Blvd, Manufacturing City, ST 12345',
        maxLength: 500,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.MaxLength)(500, { message: 'Address must be less than 500 characters' }),
    __metadata("design:type", String)
], SetupWarehouseDto.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Type of warehouse',
        example: 'Distribution Center',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], SetupWarehouseDto.prototype, "warehouseType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Expected volume/throughput',
        example: 'Medium (1000-5000 pallets/month)',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], SetupWarehouseDto.prototype, "expectedVolume", void 0);
//# sourceMappingURL=setup-warehouse.dto.js.map