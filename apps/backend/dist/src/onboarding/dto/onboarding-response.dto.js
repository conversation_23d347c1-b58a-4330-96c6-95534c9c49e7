"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompleteOnboardingResponseDto = exports.SetupWarehouseResponseDto = exports.CreateAdminAccountResponseDto = exports.StartBusinessOnboardingResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class StartBusinessOnboardingResponseDto {
}
exports.StartBusinessOnboardingResponseDto = StartBusinessOnboardingResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Unique session ID for the onboarding process",
        example: "clp123abc456def789",
    }),
    __metadata("design:type", String)
], StartBusinessOnboardingResponseDto.prototype, "sessionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Next step in the onboarding process",
        enum: [
            "business_info",
            "admin_account",
            "warehouse_setup",
            "team_setup",
            "completion",
        ],
        example: "admin_account",
    }),
    __metadata("design:type", String)
], StartBusinessOnboardingResponseDto.prototype, "nextStep", void 0);
class CreateAdminAccountResponseDto {
}
exports.CreateAdminAccountResponseDto = CreateAdminAccountResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Created user ID",
        example: "clp456def789ghi012",
    }),
    __metadata("design:type", String)
], CreateAdminAccountResponseDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Supabase user object",
        type: Object,
    }),
    __metadata("design:type", Object)
], CreateAdminAccountResponseDto.prototype, "supabaseUser", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Next step in the onboarding process",
        enum: [
            "business_info",
            "admin_account",
            "warehouse_setup",
            "team_setup",
            "completion",
        ],
        example: "warehouse_setup",
    }),
    __metadata("design:type", String)
], CreateAdminAccountResponseDto.prototype, "nextStep", void 0);
class SetupWarehouseResponseDto {
}
exports.SetupWarehouseResponseDto = SetupWarehouseResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Created warehouse ID",
        example: "clp789ghi012jkl345",
    }),
    __metadata("design:type", String)
], SetupWarehouseResponseDto.prototype, "warehouseId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Created tenant ID",
        example: "clp012jkl345mno678",
    }),
    __metadata("design:type", String)
], SetupWarehouseResponseDto.prototype, "tenantId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Next step in the onboarding process",
        enum: [
            "business_info",
            "admin_account",
            "warehouse_setup",
            "team_setup",
            "completion",
        ],
        example: "team_setup",
    }),
    __metadata("design:type", String)
], SetupWarehouseResponseDto.prototype, "nextStep", void 0);
class CompleteOnboardingResponseDto {
}
exports.CompleteOnboardingResponseDto = CompleteOnboardingResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "JWT access token for the authenticated user",
        example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    }),
    __metadata("design:type", String)
], CompleteOnboardingResponseDto.prototype, "accessToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Complete user object",
        type: Object,
    }),
    __metadata("design:type", Object)
], CompleteOnboardingResponseDto.prototype, "user", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Created warehouse object",
        type: Object,
    }),
    __metadata("design:type", Object)
], CompleteOnboardingResponseDto.prototype, "warehouse", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Created tenant object",
        type: Object,
    }),
    __metadata("design:type", Object)
], CompleteOnboardingResponseDto.prototype, "tenant", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: "Sent invitations (if team setup was included)",
        type: "array",
        items: { type: "object" },
    }),
    __metadata("design:type", Array)
], CompleteOnboardingResponseDto.prototype, "invitations", void 0);
//# sourceMappingURL=onboarding-response.dto.js.map