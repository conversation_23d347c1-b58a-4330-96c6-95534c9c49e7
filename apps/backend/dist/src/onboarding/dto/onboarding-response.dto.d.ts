import { OnboardingStep } from "@quildora/types";
export declare class StartBusinessOnboardingResponseDto {
    sessionId: string;
    nextStep: OnboardingStep;
}
export declare class CreateAdminAccountResponseDto {
    userId: string;
    supabaseUser: any;
    nextStep: OnboardingStep;
}
export declare class SetupWarehouseResponseDto {
    warehouseId: string;
    tenantId: string;
    nextStep: OnboardingStep;
}
export declare class CompleteOnboardingResponseDto {
    accessToken: string;
    user: any;
    warehouse: any;
    tenant: any;
    invitations?: any[];
}
