import { PrismaService } from '../prisma/prisma.service';
import { SessionService } from './session.service';
export declare class CleanupService {
    private readonly prisma;
    private readonly sessionService;
    private readonly logger;
    constructor(prisma: PrismaService, sessionService: SessionService);
    handleExpiredSessionsCleanup(): Promise<void>;
    handleExpiredInvitationsCleanup(): Promise<void>;
    cleanupExpiredInvitations(): Promise<number>;
    cleanupOldCompletedSessions(olderThanDays?: number): Promise<number>;
    getCleanupStats(): Promise<{
        expiredSessions: number;
        expiredInvitations: number;
        oldCompletedSessions: number;
    }>;
    runFullCleanup(): Promise<{
        expiredSessions: number;
        expiredInvitations: number;
        oldCompletedSessions: number;
    }>;
}
