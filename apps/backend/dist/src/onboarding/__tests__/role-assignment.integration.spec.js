"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const prisma_service_1 = require("../../prisma/prisma.service");
const onboarding_service_1 = require("../onboarding.service");
const invitations_service_1 = require("../invitations.service");
const auth_service_1 = require("../../auth/auth.service");
const types_1 = require("@quildora/types");
describe("Role Assignment Integration Tests", () => {
    let app;
    let prisma;
    let onboardingService;
    let invitationsService;
    let authService;
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [],
        }).compile();
        app = moduleFixture.createNestApplication();
        await app.init();
        prisma = moduleFixture.get(prisma_service_1.PrismaService);
        onboardingService = moduleFixture.get(onboarding_service_1.OnboardingService);
        invitationsService =
            moduleFixture.get(invitations_service_1.InvitationsService);
        authService = moduleFixture.get(auth_service_1.AuthService);
    });
    afterAll(async () => {
        await app.close();
    });
    describe("Onboarding Role Assignment", () => {
        it("should assign TENANT_ADMIN role during warehouse setup", async () => {
            const sessionData = {
                sessionId: "test-session-123",
                userId: "test-user-123",
                step: "admin_account",
                businessInfo: {
                    companyName: "Test Company",
                    industry: "Technology",
                    companySize: "10-50",
                },
                adminAccount: {
                    email: "<EMAIL>",
                    fullName: "Test Admin",
                    password: "password123",
                },
            };
            jest.spyOn(onboardingService, "sessionService").mockReturnValue({
                getSession: jest.fn().mockResolvedValue(sessionData),
                updateSession: jest.fn().mockResolvedValue({}),
            });
            const warehouseSetupDto = {
                sessionId: "test-session-123",
                warehouseName: "Test Warehouse",
                warehouseType: "DISTRIBUTION",
                address: "123 Test St",
                city: "Test City",
                state: "TS",
                zipCode: "12345",
                country: "US",
            };
            const result = await onboardingService.setupWarehouse(warehouseSetupDto);
            const warehouseUser = await prisma.warehouseUser.findFirst({
                where: {
                    userId: sessionData.userId,
                    warehouseId: result.warehouseId,
                },
            });
            expect(warehouseUser).toBeDefined();
            expect(warehouseUser.role).toBe(types_1.Role.TENANT_ADMIN);
        });
        it("should assign correct roles during team invitation", async () => {
            const invitationDto = {
                email: "<EMAIL>",
                role: "WAREHOUSE_MEMBER",
                warehouseIds: ["test-warehouse-123"],
            };
            const invitation = await invitationsService.createInvitation("test-admin-user", invitationDto);
            expect(invitation.invitationCode).toBeDefined();
            const acceptDto = {
                fullName: "Test Member",
                password: "password123",
            };
            const acceptResult = await invitationsService.acceptInvitation(invitation.invitationCode, acceptDto);
            const user = await prisma.user.findUnique({
                where: { id: acceptResult.user.id },
            });
            expect(user.role).toBe(types_1.Role.WAREHOUSE_MEMBER);
            const warehouseUser = await prisma.warehouseUser.findFirst({
                where: {
                    userId: acceptResult.user.id,
                    warehouseId: invitationDto.warehouseIds[0],
                },
            });
            expect(warehouseUser).toBeDefined();
            expect(warehouseUser.role).toBe(types_1.Role.WAREHOUSE_MEMBER);
        });
    });
    describe("Warehouse Access Validation", () => {
        it("should validate user warehouse access correctly", async () => {
            const tenant = await prisma.tenant.create({
                data: {
                    name: "Test Tenant",
                },
            });
            const warehouse = await prisma.warehouse.create({
                data: {
                    name: "Test Warehouse",
                    tenantId: tenant.id,
                    status: "Active",
                },
            });
            const user = await prisma.user.create({
                data: {
                    email: "<EMAIL>",
                    name: "Test User",
                    role: types_1.Role.WAREHOUSE_MEMBER,
                    tenantId: tenant.id,
                    status: "Active",
                },
            });
            const warehouseUser = await prisma.warehouseUser.create({
                data: {
                    userId: user.id,
                    warehouseId: warehouse.id,
                    role: types_1.Role.WAREHOUSE_MEMBER,
                },
            });
            const hasAccess = await prisma.warehouseUser.findFirst({
                where: {
                    userId: user.id,
                    warehouseId: warehouse.id,
                },
            });
            expect(hasAccess).toBeDefined();
            expect(hasAccess.role).toBe(types_1.Role.WAREHOUSE_MEMBER);
            expect([
                types_1.Role.TENANT_ADMIN,
                types_1.Role.WAREHOUSE_MANAGER,
                types_1.Role.WAREHOUSE_MEMBER,
            ]).toContain(hasAccess.role);
        });
        it("should enforce role hierarchy correctly", async () => {
            const roleHierarchy = {
                [types_1.Role.TENANT_ADMIN]: 3,
                [types_1.Role.WAREHOUSE_MANAGER]: 2,
                [types_1.Role.WAREHOUSE_MEMBER]: 1,
            };
            expect(roleHierarchy[types_1.Role.TENANT_ADMIN]).toBeGreaterThan(roleHierarchy[types_1.Role.WAREHOUSE_MANAGER]);
            expect(roleHierarchy[types_1.Role.WAREHOUSE_MANAGER]).toBeGreaterThan(roleHierarchy[types_1.Role.WAREHOUSE_MEMBER]);
            const canManageWarehouse = (userRole) => {
                return [types_1.Role.TENANT_ADMIN, types_1.Role.WAREHOUSE_MANAGER].includes(userRole);
            };
            const canAccessWarehouse = (userRole) => {
                return [
                    types_1.Role.TENANT_ADMIN,
                    types_1.Role.WAREHOUSE_MANAGER,
                    types_1.Role.WAREHOUSE_MEMBER,
                ].includes(userRole);
            };
            expect(canManageWarehouse(types_1.Role.TENANT_ADMIN)).toBe(true);
            expect(canManageWarehouse(types_1.Role.WAREHOUSE_MANAGER)).toBe(true);
            expect(canManageWarehouse(types_1.Role.WAREHOUSE_MEMBER)).toBe(false);
            expect(canAccessWarehouse(types_1.Role.TENANT_ADMIN)).toBe(true);
            expect(canAccessWarehouse(types_1.Role.WAREHOUSE_MANAGER)).toBe(true);
            expect(canAccessWarehouse(types_1.Role.WAREHOUSE_MEMBER)).toBe(true);
        });
    });
    describe("Multi-Warehouse Access", () => {
        it("should handle multi-warehouse role assignments correctly", async () => {
            const tenant = await prisma.tenant.create({
                data: {
                    name: "Multi-Warehouse Tenant",
                },
            });
            const warehouse1 = await prisma.warehouse.create({
                data: {
                    name: "Warehouse 1",
                    tenantId: tenant.id,
                    status: "Active",
                },
            });
            const warehouse2 = await prisma.warehouse.create({
                data: {
                    name: "Warehouse 2",
                    tenantId: tenant.id,
                    status: "Active",
                },
            });
            const user = await prisma.user.create({
                data: {
                    email: "<EMAIL>",
                    name: "Multi Warehouse User",
                    role: types_1.Role.WAREHOUSE_MANAGER,
                    tenantId: tenant.id,
                    status: "Active",
                },
            });
            await prisma.warehouseUser.createMany({
                data: [
                    {
                        userId: user.id,
                        warehouseId: warehouse1.id,
                        role: types_1.Role.WAREHOUSE_MANAGER,
                    },
                    {
                        userId: user.id,
                        warehouseId: warehouse2.id,
                        role: types_1.Role.WAREHOUSE_MEMBER,
                    },
                ],
            });
            const warehouseAccess = await prisma.warehouseUser.findMany({
                where: {
                    userId: user.id,
                },
                include: {
                    warehouse: true,
                },
            });
            expect(warehouseAccess).toHaveLength(2);
            const warehouse1Access = warehouseAccess.find((wa) => wa.warehouseId === warehouse1.id);
            const warehouse2Access = warehouseAccess.find((wa) => wa.warehouseId === warehouse2.id);
            expect(warehouse1Access.role).toBe(types_1.Role.WAREHOUSE_MANAGER);
            expect(warehouse2Access.role).toBe(types_1.Role.WAREHOUSE_MEMBER);
        });
    });
    describe("Role Assignment Edge Cases", () => {
        it("should handle role updates correctly", async () => {
            const tenant = await prisma.tenant.create({
                data: {
                    name: "Role Update Tenant",
                },
            });
            const warehouse = await prisma.warehouse.create({
                data: {
                    name: "Role Update Warehouse",
                    tenantId: tenant.id,
                    status: "Active",
                },
            });
            const user = await prisma.user.create({
                data: {
                    email: "<EMAIL>",
                    name: "Role Update User",
                    role: types_1.Role.WAREHOUSE_MEMBER,
                    tenantId: tenant.id,
                    status: "Active",
                },
            });
            const warehouseUser = await prisma.warehouseUser.create({
                data: {
                    userId: user.id,
                    warehouseId: warehouse.id,
                    role: types_1.Role.WAREHOUSE_MEMBER,
                },
            });
            expect(warehouseUser.role).toBe(types_1.Role.WAREHOUSE_MEMBER);
            const updatedWarehouseUser = await prisma.warehouseUser.update({
                where: {
                    id: warehouseUser.id,
                },
                data: {
                    role: types_1.Role.WAREHOUSE_MANAGER,
                },
            });
            expect(updatedWarehouseUser.role).toBe(types_1.Role.WAREHOUSE_MANAGER);
        });
        it("should prevent duplicate warehouse assignments", async () => {
            const tenant = await prisma.tenant.create({
                data: {
                    name: "Duplicate Test Tenant",
                },
            });
            const warehouse = await prisma.warehouse.create({
                data: {
                    name: "Duplicate Test Warehouse",
                    tenantId: tenant.id,
                    status: "Active",
                },
            });
            const user = await prisma.user.create({
                data: {
                    email: "<EMAIL>",
                    name: "Duplicate Test User",
                    role: types_1.Role.WAREHOUSE_MEMBER,
                    tenantId: tenant.id,
                    status: "Active",
                },
            });
            await prisma.warehouseUser.create({
                data: {
                    userId: user.id,
                    warehouseId: warehouse.id,
                    role: types_1.Role.WAREHOUSE_MEMBER,
                },
            });
            await expect(prisma.warehouseUser.create({
                data: {
                    userId: user.id,
                    warehouseId: warehouse.id,
                    role: types_1.Role.WAREHOUSE_MANAGER,
                },
            })).rejects.toThrow();
        });
    });
});
//# sourceMappingURL=role-assignment.integration.spec.js.map