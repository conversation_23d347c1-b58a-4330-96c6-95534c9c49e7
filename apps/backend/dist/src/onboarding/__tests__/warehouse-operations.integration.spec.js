"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const supertest_1 = __importDefault(require("supertest"));
const prisma_service_1 = require("../../prisma/prisma.service");
const onboarding_service_1 = require("../onboarding.service");
const auth_service_1 = require("../../auth/auth.service");
const types_1 = require("@quildora/types");
describe("Warehouse Operations Integration Tests", () => {
    let app;
    let prisma;
    let onboardingService;
    let authService;
    let adminToken;
    let memberToken;
    let managerToken;
    let testTenant;
    let testWarehouse;
    beforeAll(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [],
        }).compile();
        app = moduleFixture.createNestApplication();
        await app.init();
        prisma = moduleFixture.get(prisma_service_1.PrismaService);
        onboardingService = moduleFixture.get(onboarding_service_1.OnboardingService);
        authService = moduleFixture.get(auth_service_1.AuthService);
        await setupTestData();
    });
    afterAll(async () => {
        await cleanupTestData();
        await app.close();
    });
    async function setupTestData() {
        testTenant = await prisma.tenant.create({
            data: {
                name: "Integration Test Tenant",
            },
        });
        testWarehouse = await prisma.warehouse.create({
            data: {
                name: "Integration Test Warehouse",
                tenantId: testTenant.id,
                status: "Active",
            },
        });
        const adminUser = await prisma.user.create({
            data: {
                email: "<EMAIL>",
                name: "Test Admin",
                role: types_1.Role.TENANT_ADMIN,
                tenantId: testTenant.id,
                status: "Active",
            },
        });
        const managerUser = await prisma.user.create({
            data: {
                email: "<EMAIL>",
                name: "Test Manager",
                role: types_1.Role.WAREHOUSE_MANAGER,
                tenantId: testTenant.id,
                status: "Active",
            },
        });
        const memberUser = await prisma.user.create({
            data: {
                email: "<EMAIL>",
                name: "Test Member",
                role: types_1.Role.WAREHOUSE_MEMBER,
                tenantId: testTenant.id,
                status: "Active",
            },
        });
        await prisma.warehouseUser.createMany({
            data: [
                {
                    userId: adminUser.id,
                    warehouseId: testWarehouse.id,
                    role: types_1.Role.TENANT_ADMIN,
                },
                {
                    userId: managerUser.id,
                    warehouseId: testWarehouse.id,
                    role: types_1.Role.WAREHOUSE_MANAGER,
                },
                {
                    userId: memberUser.id,
                    warehouseId: testWarehouse.id,
                    role: types_1.Role.WAREHOUSE_MEMBER,
                },
            ],
        });
        adminToken = "mock-admin-token";
        managerToken = "mock-manager-token";
        memberToken = "mock-member-token";
    }
    async function cleanupTestData() {
        await prisma.warehouseUser.deleteMany({
            where: { warehouseId: testWarehouse.id },
        });
        await prisma.user.deleteMany({
            where: { tenantId: testTenant.id },
        });
        await prisma.warehouse.deleteMany({
            where: { tenantId: testTenant.id },
        });
        await prisma.tenant.delete({
            where: { id: testTenant.id },
        });
    }
    describe("Pallet Management Operations", () => {
        it("should allow all roles to view pallets", async () => {
            const adminResponse = await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/pallets")
                .set("Authorization", `Bearer ${adminToken}`)
                .set("X-Warehouse-ID", testWarehouse.id)
                .expect(200);
            const managerResponse = await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/pallets")
                .set("Authorization", `Bearer ${managerToken}`)
                .set("X-Warehouse-ID", testWarehouse.id)
                .expect(200);
            const memberResponse = await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/pallets")
                .set("Authorization", `Bearer ${memberToken}`)
                .set("X-Warehouse-ID", testWarehouse.id)
                .expect(200);
            expect(adminResponse.body).toBeDefined();
            expect(managerResponse.body).toBeDefined();
            expect(memberResponse.body).toBeDefined();
        });
        it("should allow all roles to create pallets", async () => {
            const palletData = {
                barcode: "TEST-PALLET-001",
                locationId: "test-location-id",
                description: "Test pallet for integration",
            };
            await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/pallets")
                .set("Authorization", `Bearer ${adminToken}`)
                .set("X-Warehouse-ID", testWarehouse.id)
                .send(palletData)
                .expect(201);
            await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/pallets")
                .set("Authorization", `Bearer ${managerToken}`)
                .set("X-Warehouse-ID", testWarehouse.id)
                .send({ ...palletData, barcode: "TEST-PALLET-002" })
                .expect(201);
            await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/pallets")
                .set("Authorization", `Bearer ${memberToken}`)
                .set("X-Warehouse-ID", testWarehouse.id)
                .send({ ...palletData, barcode: "TEST-PALLET-003" })
                .expect(201);
        });
        it("should enforce warehouse context for pallet operations", async () => {
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/pallets")
                .set("Authorization", `Bearer ${adminToken}`)
                .expect(400);
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/pallets")
                .set("Authorization", `Bearer ${adminToken}`)
                .set("X-Warehouse-ID", "invalid-warehouse-id")
                .expect(403);
        });
    });
    describe("Receiving Operations", () => {
        it("should allow receiving operations for all roles", async () => {
            const purchaseOrder = await prisma.purchaseOrder.create({
                data: {
                    poNumber: "PO-TEST-001",
                    supplier: "Test Supplier",
                    warehouseId: testWarehouse.id,
                    status: "PROCESSING",
                    expectedDeliveryDate: new Date(),
                    tenantId: testTenant.id,
                },
            });
            const shipmentData = {
                referenceNumber: "SHIP-TEST-001",
                status: "PROCESSING",
                purchaseOrderId: purchaseOrder.id,
            };
            const adminShipment = await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/shipments")
                .set("Authorization", `Bearer ${adminToken}`)
                .set("X-Warehouse-ID", testWarehouse.id)
                .send(shipmentData)
                .expect(201);
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/shipments")
                .set("Authorization", `Bearer ${managerToken}`)
                .set("X-Warehouse-ID", testWarehouse.id)
                .expect(200);
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/shipments")
                .set("Authorization", `Bearer ${memberToken}`)
                .set("X-Warehouse-ID", testWarehouse.id)
                .expect(200);
            expect(adminShipment.body.id).toBeDefined();
        });
        it("should allow pallet receiving for all roles", async () => {
            const purchaseOrder = await prisma.purchaseOrder.create({
                data: {
                    poNumber: "PO-RECEIVE-001",
                    supplier: "Test Supplier",
                    warehouseId: testWarehouse.id,
                    status: "PROCESSING",
                    expectedDeliveryDate: new Date(),
                    tenantId: testTenant.id,
                },
            });
            const shipment = await prisma.shipment.create({
                data: {
                    referenceNumber: "SHIP-001",
                    status: "PROCESSING",
                    purchaseOrderId: purchaseOrder.id,
                    tenantId: testTenant.id,
                },
            });
            const receivingData = {
                shipmentId: shipment.id,
                pallets: [
                    {
                        barcode: "RECEIVE-PALLET-001",
                        locationId: "test-location-id",
                        description: "Received pallet",
                    },
                ],
            };
            await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/receiving/pallets")
                .set("Authorization", `Bearer ${memberToken}`)
                .set("X-Warehouse-ID", testWarehouse.id)
                .send(receivingData)
                .expect(201);
        });
    });
    describe("Location Management", () => {
        it("should allow location access based on role permissions", async () => {
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/locations")
                .set("Authorization", `Bearer ${adminToken}`)
                .set("X-Warehouse-ID", testWarehouse.id)
                .expect(200);
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/locations")
                .set("Authorization", `Bearer ${managerToken}`)
                .set("X-Warehouse-ID", testWarehouse.id)
                .expect(200);
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/locations")
                .set("Authorization", `Bearer ${memberToken}`)
                .set("X-Warehouse-ID", testWarehouse.id)
                .expect(200);
        });
        it("should restrict location creation to managers and admins", async () => {
            const locationData = {
                name: "Test Location",
                code: "TEST-LOC-001",
                type: "STORAGE",
            };
            await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/locations")
                .set("Authorization", `Bearer ${adminToken}`)
                .set("X-Warehouse-ID", testWarehouse.id)
                .send(locationData)
                .expect(201);
            await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/locations")
                .set("Authorization", `Bearer ${managerToken}`)
                .set("X-Warehouse-ID", testWarehouse.id)
                .send({ ...locationData, code: "TEST-LOC-002" })
                .expect(201);
            await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/locations")
                .set("Authorization", `Bearer ${memberToken}`)
                .set("X-Warehouse-ID", testWarehouse.id)
                .send({ ...locationData, code: "TEST-LOC-003" })
                .expect(403);
        });
    });
    describe("User Management Operations", () => {
        it("should restrict user management to tenant admins", async () => {
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/users")
                .set("Authorization", `Bearer ${adminToken}`)
                .expect(200);
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/users")
                .set("Authorization", `Bearer ${managerToken}`)
                .expect(403);
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/users")
                .set("Authorization", `Bearer ${memberToken}`)
                .expect(403);
        });
        it("should allow invitation management for tenant admins only", async () => {
            const invitationData = {
                email: "<EMAIL>",
                role: types_1.Role.WAREHOUSE_MEMBER,
                warehouseIds: [testWarehouse.id],
            };
            await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/invitations")
                .set("Authorization", `Bearer ${adminToken}`)
                .send(invitationData)
                .expect(201);
            await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/invitations")
                .set("Authorization", `Bearer ${managerToken}`)
                .send(invitationData)
                .expect(403);
            await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/invitations")
                .set("Authorization", `Bearer ${memberToken}`)
                .send(invitationData)
                .expect(403);
        });
    });
    describe("Cross-Warehouse Access Control", () => {
        it("should prevent access to unauthorized warehouses", async () => {
            const otherTenant = await prisma.tenant.create({
                data: {
                    name: "Other Tenant",
                },
            });
            const otherWarehouse = await prisma.warehouse.create({
                data: {
                    name: "Other Warehouse",
                    tenantId: otherTenant.id,
                    status: "Active",
                },
            });
            await (0, supertest_1.default)(app.getHttpServer())
                .get("/api/pallets")
                .set("Authorization", `Bearer ${adminToken}`)
                .set("X-Warehouse-ID", otherWarehouse.id)
                .expect(403);
            await prisma.warehouse.delete({ where: { id: otherWarehouse.id } });
            await prisma.tenant.delete({ where: { id: otherTenant.id } });
        });
    });
    describe("Audit Trail Integration", () => {
        it("should create audit logs for warehouse operations", async () => {
            const palletData = {
                barcode: "AUDIT-PALLET-001",
                locationId: "test-location-id",
                description: "Audit test pallet",
            };
            const response = await (0, supertest_1.default)(app.getHttpServer())
                .post("/api/pallets")
                .set("Authorization", `Bearer ${adminToken}`)
                .set("X-Warehouse-ID", testWarehouse.id)
                .send(palletData)
                .expect(201);
            const auditLogs = await prisma.auditLog.findMany({
                where: {
                    entityId: response.body.id,
                },
            });
            expect(auditLogs.length).toBeGreaterThan(0);
            expect(auditLogs[0].action).toBe("CREATE_PALLET");
            expect(auditLogs[0].userId).toBeDefined();
        });
    });
});
//# sourceMappingURL=warehouse-operations.integration.spec.js.map