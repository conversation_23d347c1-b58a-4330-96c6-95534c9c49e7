"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AllowWarehouseFiltering = exports.RequireTenantAdmin = exports.RequireWarehouseManager = exports.RequireWarehouseAccess_Basic = exports.WarehouseAuthGuard = exports.RequireWarehouseAccess = exports.WAREHOUSE_REQUIREMENTS_KEY = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const warehouse_validation_utils_1 = require("../utils/warehouse-validation.utils");
const client_1 = require("@prisma/client");
exports.WAREHOUSE_REQUIREMENTS_KEY = "warehouse_requirements";
const RequireWarehouseAccess = (requirements) => (0, common_1.SetMetadata)(exports.WAREHOUSE_REQUIREMENTS_KEY, requirements);
exports.RequireWarehouseAccess = RequireWarehouseAccess;
let WarehouseAuthGuard = class WarehouseAuthGuard {
    constructor(reflector, warehouseValidationUtils) {
        this.reflector = reflector;
        this.warehouseValidationUtils = warehouseValidationUtils;
    }
    async canActivate(context) {
        const requirements = this.reflector.get(exports.WAREHOUSE_REQUIREMENTS_KEY, context.getHandler());
        if (!requirements) {
            return true;
        }
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user) {
            throw new common_1.ForbiddenException("User not authenticated");
        }
        const warehouseId = this.extractWarehouseId(request);
        if (requirements.requireWarehouseId && !warehouseId) {
            throw new common_1.ForbiddenException("Warehouse ID is required for this operation");
        }
        if (warehouseId) {
            try {
                if (requirements.minimumRole) {
                    await this.warehouseValidationUtils.validateUserWarehouseRole(warehouseId, user, requirements.minimumRole);
                }
                else {
                    await this.warehouseValidationUtils.validateUserWarehouseAccess(warehouseId, user);
                }
            }
            catch (error) {
                throw new common_1.ForbiddenException(error.message);
            }
        }
        if (requirements.customValidation) {
            const isValid = await requirements.customValidation(warehouseId, user);
            if (!isValid) {
                throw new common_1.ForbiddenException("Custom warehouse validation failed");
            }
        }
        return true;
    }
    extractWarehouseId(request) {
        if (request.params?.warehouseId) {
            return request.params.warehouseId;
        }
        if (request.query?.warehouseId) {
            return request.query.warehouseId;
        }
        if (request.headers?.["x-warehouse-id"]) {
            return request.headers["x-warehouse-id"];
        }
        if (request.body?.warehouseId) {
            return request.body.warehouseId;
        }
        return undefined;
    }
};
exports.WarehouseAuthGuard = WarehouseAuthGuard;
exports.WarehouseAuthGuard = WarehouseAuthGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector,
        warehouse_validation_utils_1.WarehouseValidationUtils])
], WarehouseAuthGuard);
const RequireWarehouseAccess_Basic = () => (0, exports.RequireWarehouseAccess)({
    requireWarehouseId: true,
});
exports.RequireWarehouseAccess_Basic = RequireWarehouseAccess_Basic;
const RequireWarehouseManager = () => (0, exports.RequireWarehouseAccess)({
    requireWarehouseId: true,
    minimumRole: client_1.Role.WAREHOUSE_MANAGER,
});
exports.RequireWarehouseManager = RequireWarehouseManager;
const RequireTenantAdmin = () => (0, exports.RequireWarehouseAccess)({
    minimumRole: client_1.Role.TENANT_ADMIN,
});
exports.RequireTenantAdmin = RequireTenantAdmin;
const AllowWarehouseFiltering = () => (0, exports.RequireWarehouseAccess)({
    requireWarehouseId: false,
});
exports.AllowWarehouseFiltering = AllowWarehouseFiltering;
//# sourceMappingURL=warehouse-auth.guard.js.map