import { CanActivate, ExecutionContext } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { WarehouseValidationUtils } from "../utils/warehouse-validation.utils";
import { AuthenticatedUser } from "../../auth/types/authenticated-user.interface";
import { Role } from "@prisma/client";
export declare const WAREHOUSE_REQUIREMENTS_KEY = "warehouse_requirements";
export interface WarehouseRequirements {
    minimumRole?: Role;
    requireWarehouseId?: boolean;
    customValidation?: (warehouseId: string | undefined, user: AuthenticatedUser) => Promise<boolean>;
}
export declare const RequireWarehouseAccess: (requirements: WarehouseRequirements) => import("@nestjs/common").CustomDecorator<string>;
export declare class WarehouseAuthGuard implements CanActivate {
    private readonly reflector;
    private readonly warehouseValidationUtils;
    constructor(reflector: Reflector, warehouseValidationUtils: WarehouseValidationUtils);
    canActivate(context: ExecutionContext): Promise<boolean>;
    private extractWarehouseId;
}
export declare const RequireWarehouseAccess_Basic: () => import("@nestjs/common").CustomDecorator<string>;
export declare const RequireWarehouseManager: () => import("@nestjs/common").CustomDecorator<string>;
export declare const RequireTenantAdmin: () => import("@nestjs/common").CustomDecorator<string>;
export declare const AllowWarehouseFiltering: () => import("@nestjs/common").CustomDecorator<string>;
