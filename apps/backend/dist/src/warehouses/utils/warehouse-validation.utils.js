"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WarehouseValidationUtils = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const client_1 = require("@prisma/client");
let WarehouseValidationUtils = class WarehouseValidationUtils {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async validateUserWarehouseAccess(warehouseId, currentUser) {
        const warehouse = await this.prisma.warehouse.findFirst({
            where: {
                id: warehouseId,
                tenantId: currentUser.tenantId,
            },
        });
        if (!warehouse) {
            throw new common_1.NotFoundException(`Warehouse with ID "${warehouseId}" not found or not accessible.`);
        }
        if (currentUser.role === client_1.Role.TENANT_ADMIN) {
            return;
        }
        const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
        if (!userWarehouseIds.includes(warehouseId)) {
            throw new common_1.ForbiddenException("Access denied: You do not have permission to access this warehouse.");
        }
    }
    async validateUserWarehouseRole(warehouseId, currentUser, requiredRole) {
        await this.validateUserWarehouseAccess(warehouseId, currentUser);
        if (currentUser.role === client_1.Role.TENANT_ADMIN) {
            return;
        }
        const warehouseUser = currentUser.warehouseUsers?.find((wu) => wu.warehouseId === warehouseId);
        if (!warehouseUser) {
            throw new common_1.ForbiddenException("Access denied: You are not assigned to this warehouse.");
        }
        if (!this.hasRequiredRole(warehouseUser.role, requiredRole)) {
            throw new common_1.ForbiddenException(`Access denied: This action requires ${requiredRole} role or higher.`);
        }
    }
    async getUserAccessibleWarehouseIds(currentUser) {
        if (currentUser.role === client_1.Role.TENANT_ADMIN) {
            const warehouses = await this.prisma.warehouse.findMany({
                where: { tenantId: currentUser.tenantId },
                select: { id: true },
            });
            return warehouses.map((w) => w.id);
        }
        return currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
    }
    async validateWarehouseTenantRelationship(warehouseId, tenantId) {
        const warehouse = await this.prisma.warehouse.findFirst({
            where: {
                id: warehouseId,
                tenantId: tenantId,
            },
        });
        if (!warehouse) {
            throw new common_1.NotFoundException(`Warehouse with ID "${warehouseId}" not found in tenant "${tenantId}".`);
        }
    }
    async validateEntityWarehouseAccess(entityId, entityType, currentUser) {
        let warehouseId = null;
        switch (entityType) {
            case "pallet":
                const pallet = await this.prisma.pallet.findUnique({
                    where: { id: entityId },
                    include: { location: true },
                });
                if (!pallet || !pallet.location) {
                    throw new common_1.NotFoundException(`Pallet with ID "${entityId}" not found.`);
                }
                warehouseId = pallet.location.warehouseId;
                break;
            case "location":
                const location = await this.prisma.location.findUnique({
                    where: { id: entityId },
                });
                if (!location) {
                    throw new common_1.NotFoundException(`Location with ID "${entityId}" not found.`);
                }
                warehouseId = location.warehouseId;
                break;
            case "purchaseOrder":
                const purchaseOrder = await this.prisma.purchaseOrder.findUnique({
                    where: { id: entityId },
                });
                if (!purchaseOrder) {
                    throw new common_1.NotFoundException(`Purchase Order with ID "${entityId}" not found.`);
                }
                warehouseId = purchaseOrder.warehouseId;
                break;
            case "shipment":
                const shipment = await this.prisma.shipment.findUnique({
                    where: { id: entityId },
                    include: { purchaseOrder: true },
                });
                if (!shipment || !shipment.purchaseOrder) {
                    throw new common_1.NotFoundException(`Shipment with ID "${entityId}" not found.`);
                }
                warehouseId = shipment.purchaseOrder.warehouseId;
                break;
            default:
                throw new Error(`Unsupported entity type: ${entityType}`);
        }
        await this.validateUserWarehouseAccess(warehouseId, currentUser);
        return warehouseId;
    }
    hasRequiredRole(userRole, requiredRole) {
        const roleHierarchy = {
            [client_1.Role.WAREHOUSE_MEMBER]: 1,
            [client_1.Role.WAREHOUSE_MANAGER]: 2,
            [client_1.Role.TENANT_ADMIN]: 3,
        };
        return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
    }
    async buildWarehouseWhereClause(currentUser, warehouseId) {
        const whereClause = {};
        if (warehouseId) {
            await this.validateUserWarehouseAccess(warehouseId, currentUser);
            whereClause.warehouseId = warehouseId;
        }
        else if (currentUser.role !== client_1.Role.TENANT_ADMIN) {
            const accessibleWarehouseIds = await this.getUserAccessibleWarehouseIds(currentUser);
            if (accessibleWarehouseIds.length === 0) {
                whereClause.warehouseId = "impossible-warehouse-id";
            }
            else {
                whereClause.warehouseId = { in: accessibleWarehouseIds };
            }
        }
        return whereClause;
    }
    async buildLocationWarehouseWhereClause(currentUser, warehouseId) {
        const warehouseFilter = await this.buildWarehouseWhereClause(currentUser, warehouseId);
        return {
            location: {
                warehouse: {
                    tenantId: currentUser.tenantId,
                    ...warehouseFilter,
                },
            },
        };
    }
};
exports.WarehouseValidationUtils = WarehouseValidationUtils;
exports.WarehouseValidationUtils = WarehouseValidationUtils = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], WarehouseValidationUtils);
//# sourceMappingURL=warehouse-validation.utils.js.map