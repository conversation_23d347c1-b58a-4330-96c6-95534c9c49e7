import { PrismaService } from "../../prisma/prisma.service";
import { AuthenticatedUser } from "../../auth/types/authenticated-user.interface";
import { Role } from "@prisma/client";
export declare class WarehouseValidationUtils {
    private readonly prisma;
    constructor(prisma: PrismaService);
    validateUserWarehouseAccess(warehouseId: string, currentUser: AuthenticatedUser): Promise<void>;
    validateUserWarehouseRole(warehouseId: string, currentUser: AuthenticatedUser, requiredRole: Role): Promise<void>;
    getUserAccessibleWarehouseIds(currentUser: AuthenticatedUser): Promise<string[]>;
    validateWarehouseTenantRelationship(warehouseId: string, tenantId: string): Promise<void>;
    validateEntityWarehouseAccess(entityId: string, entityType: "pallet" | "location" | "purchaseOrder" | "shipment", currentUser: AuthenticatedUser): Promise<string>;
    private hasRequiredRole;
    buildWarehouseWhereClause(currentUser: AuthenticatedUser, warehouseId?: string): Promise<{
        warehouseId?: string | {
            in: string[];
        };
    }>;
    buildLocationWarehouseWhereClause(currentUser: AuthenticatedUser, warehouseId?: string): Promise<{
        location: {
            warehouse: {
                tenantId: string;
                id?: string | {
                    in: string[];
                };
            };
        };
    }>;
}
