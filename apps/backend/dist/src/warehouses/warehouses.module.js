"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WarehousesModule = void 0;
const common_1 = require("@nestjs/common");
const warehouses_service_1 = require("./warehouses.service");
const warehouses_controller_1 = require("./warehouses.controller");
const warehouse_validation_utils_1 = require("./utils/warehouse-validation.utils");
const warehouse_auth_guard_1 = require("./guards/warehouse-auth.guard");
let WarehousesModule = class WarehousesModule {
};
exports.WarehousesModule = WarehousesModule;
exports.WarehousesModule = WarehousesModule = __decorate([
    (0, common_1.Module)({
        providers: [warehouses_service_1.WarehousesService, warehouse_validation_utils_1.WarehouseValidationUtils, warehouse_auth_guard_1.WarehouseAuthGuard],
        controllers: [warehouses_controller_1.WarehousesController],
        exports: [warehouses_service_1.WarehousesService, warehouse_validation_utils_1.WarehouseValidationUtils, warehouse_auth_guard_1.WarehouseAuthGuard],
    })
], WarehousesModule);
//# sourceMappingURL=warehouses.module.js.map