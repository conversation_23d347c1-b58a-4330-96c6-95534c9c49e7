"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetRequiredWarehouseId = exports.GetWarehouseId = exports.GetWarehouseContext = void 0;
const common_1 = require("@nestjs/common");
exports.GetWarehouseContext = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    if (request.params?.warehouseId) {
        return {
            warehouseId: request.params.warehouseId,
            isWarehouseSpecific: true,
            source: "params",
        };
    }
    if (request.query?.warehouseId) {
        return {
            warehouseId: request.query.warehouseId,
            isWarehouseSpecific: true,
            source: "query",
        };
    }
    if (request.headers?.["x-warehouse-id"]) {
        return {
            warehouseId: request.headers["x-warehouse-id"],
            isWarehouseSpecific: true,
            source: "headers",
        };
    }
    if (request.body?.warehouseId) {
        return {
            warehouseId: request.body.warehouseId,
            isWarehouseSpecific: true,
            source: "body",
        };
    }
    return {
        warehouseId: undefined,
        isWarehouseSpecific: false,
        source: "none",
    };
});
exports.GetWarehouseId = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    if (request.params?.warehouseId) {
        return request.params.warehouseId;
    }
    if (request.query?.warehouseId) {
        return request.query.warehouseId;
    }
    if (request.headers?.["x-warehouse-id"]) {
        return request.headers["x-warehouse-id"];
    }
    if (request.body?.warehouseId) {
        return request.body.warehouseId;
    }
    return undefined;
});
exports.GetRequiredWarehouseId = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    if (request.params?.warehouseId) {
        return request.params.warehouseId;
    }
    if (request.query?.warehouseId) {
        return request.query.warehouseId;
    }
    if (request.headers?.["x-warehouse-id"]) {
        return request.headers["x-warehouse-id"];
    }
    if (request.body?.warehouseId) {
        return request.body.warehouseId;
    }
    throw new Error("Warehouse ID is required but not provided in request");
});
//# sourceMappingURL=warehouse-context.decorator.js.map