export interface WarehouseContext {
    warehouseId?: string;
    isWarehouseSpecific: boolean;
    source: "params" | "query" | "headers" | "body" | "none";
}
export declare const GetWarehouseContext: (...dataOrPipes: unknown[]) => ParameterDecorator;
export declare const GetWarehouseId: (...dataOrPipes: unknown[]) => ParameterDecorator;
export declare const GetRequiredWarehouseId: (...dataOrPipes: unknown[]) => ParameterDecorator;
