import { Role } from '@prisma/client';
export declare class WarehouseUserAccessDto {
    userId: string;
    userName: string;
    userEmail: string;
    role: Role;
}
export declare class WarehouseLocationSummaryDto {
    totalLocations: number;
    activeLocations: number;
    occupiedLocations: number;
}
export declare class WarehousePalletSummaryDto {
    totalPallets: number;
    storedPallets: number;
    receivingPallets: number;
    pickingPallets: number;
    shippingPallets: number;
}
export declare class WarehouseResponseDto {
    id: string;
    name: string;
    address?: string;
    createdAt: Date;
    updatedAt: Date;
    tenantId: string;
    locationSummary?: WarehouseLocationSummaryDto;
    palletSummary?: WarehousePalletSummaryDto;
    userAccess?: WarehouseUserAccessDto[];
    currentUserRole?: Role;
}
export declare class WarehouseListResponseDto {
    data: WarehouseResponseDto[];
    count: number;
    page?: number;
    limit?: number;
    totalPages?: number;
}
