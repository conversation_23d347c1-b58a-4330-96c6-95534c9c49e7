"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WarehouseListResponseDto = exports.WarehouseResponseDto = exports.WarehousePalletSummaryDto = exports.WarehouseLocationSummaryDto = exports.WarehouseUserAccessDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const client_1 = require("@prisma/client");
class WarehouseUserAccessDto {
}
exports.WarehouseUserAccessDto = WarehouseUserAccessDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User ID' }),
    __metadata("design:type", String)
], WarehouseUserAccessDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User name' }),
    __metadata("design:type", String)
], WarehouseUserAccessDto.prototype, "userName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User email' }),
    __metadata("design:type", String)
], WarehouseUserAccessDto.prototype, "userEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User role in this warehouse',
        enum: client_1.Role
    }),
    __metadata("design:type", String)
], WarehouseUserAccessDto.prototype, "role", void 0);
class WarehouseLocationSummaryDto {
}
exports.WarehouseLocationSummaryDto = WarehouseLocationSummaryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of locations' }),
    __metadata("design:type", Number)
], WarehouseLocationSummaryDto.prototype, "totalLocations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of active locations' }),
    __metadata("design:type", Number)
], WarehouseLocationSummaryDto.prototype, "activeLocations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of locations with pallets' }),
    __metadata("design:type", Number)
], WarehouseLocationSummaryDto.prototype, "occupiedLocations", void 0);
class WarehousePalletSummaryDto {
}
exports.WarehousePalletSummaryDto = WarehousePalletSummaryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of pallets' }),
    __metadata("design:type", Number)
], WarehousePalletSummaryDto.prototype, "totalPallets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of pallets in storage' }),
    __metadata("design:type", Number)
], WarehousePalletSummaryDto.prototype, "storedPallets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of pallets being received' }),
    __metadata("design:type", Number)
], WarehousePalletSummaryDto.prototype, "receivingPallets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of pallets being picked' }),
    __metadata("design:type", Number)
], WarehousePalletSummaryDto.prototype, "pickingPallets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of pallets ready for shipping' }),
    __metadata("design:type", Number)
], WarehousePalletSummaryDto.prototype, "shippingPallets", void 0);
class WarehouseResponseDto {
}
exports.WarehouseResponseDto = WarehouseResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Warehouse ID' }),
    __metadata("design:type", String)
], WarehouseResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Warehouse name' }),
    __metadata("design:type", String)
], WarehouseResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Warehouse address' }),
    __metadata("design:type", String)
], WarehouseResponseDto.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Creation timestamp' }),
    __metadata("design:type", Date)
], WarehouseResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last update timestamp' }),
    __metadata("design:type", Date)
], WarehouseResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tenant ID' }),
    __metadata("design:type", String)
], WarehouseResponseDto.prototype, "tenantId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Location summary statistics',
        type: WarehouseLocationSummaryDto
    }),
    __metadata("design:type", WarehouseLocationSummaryDto)
], WarehouseResponseDto.prototype, "locationSummary", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Pallet summary statistics',
        type: WarehousePalletSummaryDto
    }),
    __metadata("design:type", WarehousePalletSummaryDto)
], WarehouseResponseDto.prototype, "palletSummary", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Users with access to this warehouse',
        type: [WarehouseUserAccessDto]
    }),
    __metadata("design:type", Array)
], WarehouseResponseDto.prototype, "userAccess", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Current user role in this warehouse' }),
    __metadata("design:type", String)
], WarehouseResponseDto.prototype, "currentUserRole", void 0);
class WarehouseListResponseDto {
}
exports.WarehouseListResponseDto = WarehouseListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Array of warehouses',
        type: [WarehouseResponseDto]
    }),
    __metadata("design:type", Array)
], WarehouseListResponseDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of warehouses' }),
    __metadata("design:type", Number)
], WarehouseListResponseDto.prototype, "count", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Current page number' }),
    __metadata("design:type", Number)
], WarehouseListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of items per page' }),
    __metadata("design:type", Number)
], WarehouseListResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Total number of pages' }),
    __metadata("design:type", Number)
], WarehouseListResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=warehouse-response.dto.js.map