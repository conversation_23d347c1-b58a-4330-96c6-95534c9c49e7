import { WarehousesService } from "./warehouses.service";
import { CreateWarehouseDto } from "./dto/create-warehouse.dto";
import { UpdateWarehouseDto } from "./dto/update-warehouse.dto";
import { EnhancedUserPayload } from "../auth/types";
export declare class WarehousesController {
    private readonly warehousesService;
    constructor(warehousesService: WarehousesService);
    create(createWarehouseDto: CreateWarehouseDto, req: {
        user: EnhancedUserPayload;
    }): Promise<{
        name: string;
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
        address: string | null;
    }>;
    findAll(req: {
        user: EnhancedUserPayload;
    }): Promise<{
        name: string;
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
        address: string | null;
    }[]>;
    findOne(id: string, req: {
        user: EnhancedUserPayload;
    }): Promise<{
        name: string;
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
        address: string | null;
    }>;
    update(id: string, updateWarehouseDto: UpdateWarehouseDto, req: {
        user: EnhancedUserPayload;
    }): Promise<{
        name: string;
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
        address: string | null;
    }>;
    remove(id: string, req: {
        user: EnhancedUserPayload;
    }): Promise<{
        name: string;
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
        address: string | null;
    }>;
}
