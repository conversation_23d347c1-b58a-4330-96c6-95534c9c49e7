import { PrismaService } from "../prisma/prisma.service";
import { Warehouse } from "@prisma/client";
import { CreateWarehouseDto } from "./dto/create-warehouse.dto";
import { UpdateWarehouseDto } from "./dto/update-warehouse.dto";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";
export declare class WarehousesService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createWarehouseDto: CreateWarehouseDto, currentUser: AuthenticatedUser): Promise<Warehouse>;
    findAll(currentUser: AuthenticatedUser): Promise<Warehouse[]>;
    findOne(id: string, currentUser: AuthenticatedUser): Promise<Warehouse | null>;
    update(id: string, updateWarehouseDto: UpdateWarehouseDto, currentUser: AuthenticatedUser): Promise<Warehouse>;
    remove(id: string, currentUser: AuthenticatedUser): Promise<Warehouse>;
}
