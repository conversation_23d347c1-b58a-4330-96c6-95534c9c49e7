"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WarehousesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let WarehousesService = class WarehousesService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createWarehouseDto, currentUser) {
        const newWarehouse = await this.prisma.warehouse.create({
            data: {
                ...createWarehouseDto,
                tenantId: currentUser.tenantId,
            },
        });
        if (currentUser && currentUser.id && newWarehouse) {
            await this.prisma.warehouseUser.create({
                data: {
                    userId: currentUser.id,
                    warehouseId: newWarehouse.id,
                    role: currentUser.role,
                },
            });
        }
        return newWarehouse;
    }
    async findAll(currentUser) {
        const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
        if (currentUser.role !== "TENANT_ADMIN" && userWarehouseIds.length === 0) {
            return [];
        }
        const whereConditions = {
            tenantId: currentUser.tenantId,
        };
        if (currentUser.role !== "TENANT_ADMIN" && userWarehouseIds.length > 0) {
            whereConditions.id = { in: userWarehouseIds };
        }
        return this.prisma.warehouse.findMany({
            where: whereConditions,
            orderBy: { name: "asc" },
        });
    }
    async findOne(id, currentUser) {
        const warehouse = await this.prisma.warehouse.findUnique({
            where: {
                id,
                tenantId: currentUser.tenantId,
            },
        });
        if (!warehouse) {
            throw new common_1.NotFoundException(`Warehouse with ID \"${id}\" not found or not accessible within your tenant.`);
        }
        const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
        if (currentUser.role !== "TENANT_ADMIN" &&
            !userWarehouseIds.includes(warehouse.id)) {
            throw new common_1.NotFoundException(`Warehouse with ID \"${id}\" not found or not accessible.`);
        }
        return warehouse;
    }
    async update(id, updateWarehouseDto, currentUser) {
        const existingWarehouse = await this.prisma.warehouse.findUnique({
            where: { id, tenantId: currentUser.tenantId },
        });
        if (!existingWarehouse) {
            throw new common_1.NotFoundException(`Warehouse with ID \"${id}\" not found within your tenant.`);
        }
        return this.prisma.warehouse.update({
            where: {
                id,
                tenantId: currentUser.tenantId,
            },
            data: {
                name: updateWarehouseDto.name,
                address: updateWarehouseDto.address,
            },
        });
    }
    async remove(id, currentUser) {
        const existingWarehouse = await this.prisma.warehouse.findUnique({
            where: { id, tenantId: currentUser.tenantId },
        });
        if (!existingWarehouse) {
            throw new common_1.NotFoundException(`Warehouse with ID \"${id}\" not found within your tenant.`);
        }
        await this.prisma.warehouseUser.deleteMany({ where: { warehouseId: id } });
        return this.prisma.warehouse.delete({
            where: {
                id,
                tenantId: currentUser.tenantId,
            },
        });
    }
};
exports.WarehousesService = WarehousesService;
exports.WarehousesService = WarehousesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], WarehousesService);
//# sourceMappingURL=warehouses.service.js.map