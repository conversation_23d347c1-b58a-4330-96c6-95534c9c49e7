"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PalletItemsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
const audit_log_service_1 = require("../audit-log/audit-log.service");
let PalletItemsService = class PalletItemsService {
    constructor(prisma, auditLogService) {
        this.prisma = prisma;
        this.auditLogService = auditLogService;
    }
    async addItemToPallet(palletId, itemId, quantity, currentUser) {
        if (quantity <= 0) {
            throw new common_1.BadRequestException("Quantity must be greater than zero.");
        }
        const palletRecord = await this.prisma.pallet.findFirst({
            where: {
                id: palletId,
                location: {
                    warehouse: {
                        tenantId: currentUser.tenantId,
                    },
                },
            },
            include: { location: true },
        });
        const itemRecord = await this.prisma.item.findUnique({
            where: { id: itemId, tenantId: currentUser.tenantId },
        });
        if (!palletRecord) {
            throw new common_1.NotFoundException(`Pallet with ID "${palletId}" not found in your tenant.`);
        }
        if (!itemRecord) {
            throw new common_1.NotFoundException(`Item with ID "${itemId}" not found in your tenant.`);
        }
        if (palletRecord.location && palletRecord.location.warehouseId) {
            const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
            if (currentUser.role !== client_1.Role.TENANT_ADMIN &&
                !userWarehouseIds.includes(palletRecord.location.warehouseId)) {
                throw new common_1.NotFoundException(`You do not have access to the warehouse containing pallet "${palletId}".`);
            }
        }
        else if (palletRecord.location === null) {
        }
        const existingPalletItem = await this.prisma.palletItem.findUnique({
            where: {
                pallet_item_unique: {
                    palletId: palletId,
                    itemId: itemId,
                },
            },
        });
        if (existingPalletItem) {
            const updatedQuantity = existingPalletItem.quantity + quantity;
            const updatedPalletItem = await this.prisma.palletItem.update({
                where: {
                    pallet_item_unique: {
                        palletId: palletId,
                        itemId: itemId,
                    },
                },
                data: {
                    quantity: updatedQuantity,
                },
            });
            await this.auditLogService.create({
                userId: currentUser.id,
                userEmail: currentUser.email,
                action: "UPDATE_PALLET_ITEM_QUANTITY",
                entity: "PALLET",
                entityId: palletId,
                tenantId: currentUser.tenantId,
                details: {
                    itemId: itemId,
                    itemName: itemRecord.name,
                    previousQuantity: existingPalletItem.quantity,
                    addedQuantity: quantity,
                    newQuantity: updatedQuantity,
                    palletBarcode: palletRecord.barcode,
                },
            });
            return updatedPalletItem;
        }
        else {
            const newPalletItem = await this.prisma.palletItem.create({
                data: {
                    palletId: palletId,
                    itemId: itemId,
                    quantity: quantity,
                },
            });
            await this.auditLogService.create({
                userId: currentUser.id,
                userEmail: currentUser.email,
                action: "ADD_ITEM_TO_PALLET",
                entity: "PALLET",
                entityId: palletId,
                tenantId: currentUser.tenantId,
                details: {
                    itemId: itemId,
                    itemName: itemRecord.name,
                    quantity: quantity,
                    palletBarcode: palletRecord.barcode,
                },
            });
            return newPalletItem;
        }
    }
    async removeItemFromPallet(palletId, itemId, currentUser) {
        const palletRecord = await this.prisma.pallet.findFirst({
            where: {
                id: palletId,
                location: {
                    warehouse: {
                        tenantId: currentUser.tenantId,
                    },
                },
            },
            include: { location: true },
        });
        if (!palletRecord) {
            throw new common_1.NotFoundException(`Pallet with ID "${palletId}" not found in your tenant.`);
        }
        if (palletRecord.location && palletRecord.location.warehouseId) {
            const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
            if (currentUser.role !== client_1.Role.TENANT_ADMIN &&
                !userWarehouseIds.includes(palletRecord.location.warehouseId)) {
                throw new common_1.NotFoundException(`You do not have access to the warehouse containing pallet "${palletId}".`);
            }
        }
        const palletItemToDelete = await this.prisma.palletItem.findUnique({
            where: {
                pallet_item_unique: {
                    palletId,
                    itemId,
                },
            },
        });
        if (!palletItemToDelete) {
            throw new common_1.NotFoundException(`Item with ID "${itemId}" not found on pallet "${palletId}" within your tenant.`);
        }
        const itemRecord = await this.prisma.item.findUnique({
            where: { id: itemId, tenantId: currentUser.tenantId },
            select: { name: true },
        });
        const deletedPalletItem = await this.prisma.palletItem.delete({
            where: {
                pallet_item_unique: {
                    palletId,
                    itemId,
                },
            },
        });
        await this.auditLogService.create({
            userId: currentUser.id,
            userEmail: currentUser.email,
            action: "REMOVE_ITEM_FROM_PALLET",
            entity: "PALLET",
            entityId: palletId,
            tenantId: currentUser.tenantId,
            details: {
                itemId: itemId,
                itemName: itemRecord?.name || "Unknown Item",
                removedQuantity: palletItemToDelete.quantity,
                palletBarcode: palletRecord.barcode,
            },
        });
        return deletedPalletItem;
    }
    async updatePalletItemQuantity(palletId, itemId, newQuantity, currentUser) {
        if (newQuantity <= 0) {
            throw new common_1.BadRequestException("New quantity must be greater than zero.");
        }
        const palletRecord = await this.prisma.pallet.findFirst({
            where: {
                id: palletId,
                location: {
                    warehouse: {
                        tenantId: currentUser.tenantId,
                    },
                },
            },
            include: { location: true },
        });
        if (!palletRecord) {
            throw new common_1.NotFoundException(`Pallet with ID "${palletId}" not found in your tenant.`);
        }
        if (palletRecord.location && palletRecord.location.warehouseId) {
            const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
            if (currentUser.role !== client_1.Role.TENANT_ADMIN &&
                !userWarehouseIds.includes(palletRecord.location.warehouseId)) {
                throw new common_1.NotFoundException(`You do not have access to the warehouse containing pallet "${palletId}".`);
            }
        }
        const existingPalletItem = await this.prisma.palletItem.findUnique({
            where: {
                pallet_item_unique: {
                    palletId,
                    itemId,
                },
            },
        });
        if (!existingPalletItem) {
            throw new common_1.NotFoundException(`Item with ID "${itemId}" not found on pallet "${palletId}" within your tenant.`);
        }
        const itemRecord = await this.prisma.item.findUnique({
            where: { id: itemId, tenantId: currentUser.tenantId },
            select: { name: true },
        });
        const updatedPalletItem = await this.prisma.palletItem.update({
            where: {
                pallet_item_unique: {
                    palletId,
                    itemId,
                },
            },
            data: {
                quantity: newQuantity,
            },
        });
        await this.auditLogService.create({
            userId: currentUser.id,
            userEmail: currentUser.email,
            action: "UPDATE_PALLET_ITEM_QUANTITY",
            entity: "PALLET",
            entityId: palletId,
            tenantId: currentUser.tenantId,
            details: {
                itemId: itemId,
                itemName: itemRecord?.name || "Unknown Item",
                previousQuantity: existingPalletItem.quantity,
                newQuantity: newQuantity,
                quantityChange: newQuantity - existingPalletItem.quantity,
                palletBarcode: palletRecord.barcode,
            },
        });
        return updatedPalletItem;
    }
};
exports.PalletItemsService = PalletItemsService;
exports.PalletItemsService = PalletItemsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        audit_log_service_1.AuditLogService])
], PalletItemsService);
//# sourceMappingURL=pallet-items.service.js.map