"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PalletItemsController = void 0;
const common_1 = require("@nestjs/common");
const pallet_items_service_1 = require("./pallet-items.service");
const add_pallet_item_dto_1 = require("../pallets/dto/add-pallet-item.dto");
const update_pallet_item_dto_1 = require("../pallets/dto/update-pallet-item.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const warehouse_permission_guard_1 = require("../auth/guards/warehouse-permission.guard");
const warehouse_permission_decorator_1 = require("../auth/decorators/warehouse-permission.decorator");
const log_action_decorator_1 = require("../audit-log/decorators/log-action.decorator");
const audit_log_interceptor_1 = require("../audit-log/interceptors/audit-log.interceptor");
let PalletItemsController = class PalletItemsController {
    constructor(palletItemsService) {
        this.palletItemsService = palletItemsService;
    }
    async addItemToPallet(palletId, addPalletItemDto, req) {
        return this.palletItemsService.addItemToPallet(palletId, addPalletItemDto.itemId, addPalletItemDto.quantity, req.user);
    }
    async removeItemFromPallet(palletId, itemId, req) {
        await this.palletItemsService.removeItemFromPallet(palletId, itemId, req.user);
    }
    async updatePalletItemQuantity(palletId, itemId, updatePalletItemDto, req) {
        return this.palletItemsService.updatePalletItemQuantity(palletId, itemId, updatePalletItemDto.quantity, req.user);
    }
};
exports.PalletItemsController = PalletItemsController;
__decorate([
    (0, common_1.Post)(),
    (0, warehouse_permission_decorator_1.RequirePalletAccess)(),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true })),
    (0, log_action_decorator_1.LogAction)({
        action: "ADD_ITEM_TO_PALLET",
        entity: "Pallet",
        getEntityId: (context) => context.switchToHttp().getRequest().params.palletId,
    }),
    __param(0, (0, common_1.Param)("palletId")),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, add_pallet_item_dto_1.AddPalletItemDto, Object]),
    __metadata("design:returntype", Promise)
], PalletItemsController.prototype, "addItemToPallet", null);
__decorate([
    (0, common_1.Delete)(":itemId"),
    (0, warehouse_permission_decorator_1.RequirePalletAccess)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, log_action_decorator_1.LogAction)({
        action: "REMOVE_ITEM_FROM_PALLET",
        entity: "Pallet",
        getEntityId: (context) => context.switchToHttp().getRequest().params.palletId,
    }),
    __param(0, (0, common_1.Param)("palletId")),
    __param(1, (0, common_1.Param)("itemId")),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], PalletItemsController.prototype, "removeItemFromPallet", null);
__decorate([
    (0, common_1.Patch)(":itemId"),
    (0, warehouse_permission_decorator_1.RequirePalletAccess)(),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true })),
    (0, log_action_decorator_1.LogAction)({
        action: "UPDATE_PALLET_ITEM_QUANTITY",
        entity: "Pallet",
        getEntityId: (context) => context.switchToHttp().getRequest().params.palletId,
    }),
    __param(0, (0, common_1.Param)("palletId")),
    __param(1, (0, common_1.Param)("itemId")),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, update_pallet_item_dto_1.UpdatePalletItemDto, Object]),
    __metadata("design:returntype", Promise)
], PalletItemsController.prototype, "updatePalletItemQuantity", null);
exports.PalletItemsController = PalletItemsController = __decorate([
    (0, common_1.Controller)("pallets/:palletId/items"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, warehouse_permission_guard_1.WarehousePermissionGuard),
    (0, common_1.UseInterceptors)(audit_log_interceptor_1.AuditLogInterceptor),
    __metadata("design:paramtypes", [pallet_items_service_1.PalletItemsService])
], PalletItemsController);
//# sourceMappingURL=pallet-items.controller.js.map