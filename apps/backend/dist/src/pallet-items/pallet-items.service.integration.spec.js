"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const pallet_items_service_1 = require("./pallet-items.service");
const prisma_service_1 = require("../prisma/prisma.service");
const pallet_items_module_1 = require("./pallet-items.module");
const prisma_module_1 = require("../prisma/prisma.module");
const cleanup_database_1 = require("../util/cleanup-database");
const client_1 = require("@prisma/client");
const common_1 = require("@nestjs/common");
describe("PalletItemsService - Integration", () => {
    let service;
    let prisma;
    let testTenant;
    let mockAdminUser;
    let mockWarehouseManagerUser;
    let mockOtherTenant;
    let mockOtherTenantUser;
    let testWarehouse1;
    let testLocation1;
    let testItem1;
    let testItem2;
    let testPallet1;
    let testWarehouse2;
    let testLocation2;
    let testPallet2;
    beforeAll(async () => {
        const module = await testing_1.Test.createTestingModule({
            imports: [pallet_items_module_1.PalletItemsModule, prisma_module_1.PrismaModule],
        }).compile();
        service = module.get(pallet_items_service_1.PalletItemsService);
        prisma = module.get(prisma_service_1.PrismaService);
    });
    beforeEach(async () => {
        await (0, cleanup_database_1.cleanupDatabase)(prisma);
        testTenant = await prisma.tenant.create({
            data: { name: "Test Tenant PI" },
        });
        mockOtherTenant = await prisma.tenant.create({
            data: { name: "Other Tenant PI" },
        });
        mockAdminUser = {
            id: "admin-user-pi",
            email: "<EMAIL>",
            role: client_1.Role.TENANT_ADMIN,
            tenantId: testTenant.id,
            name: "Admin User PI",
            authUserId: "auth-admin-user-pi",
            warehouseUsers: [],
        };
        mockWarehouseManagerUser = {
            id: "whmanager-user-pi",
            email: "<EMAIL>",
            role: client_1.Role.WAREHOUSE_MANAGER,
            tenantId: testTenant.id,
            name: "Warehouse Manager PI",
            authUserId: "auth-whmanager-user-pi",
            warehouseUsers: [],
        };
        mockOtherTenantUser = {
            id: "other-tenant-user-pi",
            email: "<EMAIL>",
            role: client_1.Role.TENANT_ADMIN,
            tenantId: mockOtherTenant.id,
            name: "Other Tenant User PI",
            authUserId: "auth-other-tenant-user-pi",
            warehouseUsers: [],
        };
        await prisma.user.createMany({
            data: [
                {
                    id: mockAdminUser.id,
                    email: mockAdminUser.email,
                    role: mockAdminUser.role,
                    tenantId: mockAdminUser.tenantId,
                    name: mockAdminUser.name,
                    authUserId: mockAdminUser.authUserId,
                },
                {
                    id: mockWarehouseManagerUser.id,
                    email: mockWarehouseManagerUser.email,
                    role: mockWarehouseManagerUser.role,
                    tenantId: mockWarehouseManagerUser.tenantId,
                    name: mockWarehouseManagerUser.name,
                    authUserId: mockWarehouseManagerUser.authUserId,
                },
                {
                    id: mockOtherTenantUser.id,
                    email: mockOtherTenantUser.email,
                    role: mockOtherTenantUser.role,
                    tenantId: mockOtherTenantUser.tenantId,
                    name: mockOtherTenantUser.name,
                    authUserId: mockOtherTenantUser.authUserId,
                },
            ],
        });
        testWarehouse1 = await prisma.warehouse.create({
            data: { name: "Test Warehouse 1 PI", tenantId: testTenant.id },
        });
        testWarehouse2 = await prisma.warehouse.create({
            data: { name: "Test Warehouse 2 PI", tenantId: testTenant.id },
        });
        await prisma.warehouseUser.create({
            data: {
                userId: mockWarehouseManagerUser.id,
                warehouseId: testWarehouse1.id,
                role: client_1.Role.WAREHOUSE_MANAGER,
            },
        });
        mockWarehouseManagerUser.warehouseUsers = [
            { warehouseId: testWarehouse1.id, role: client_1.Role.WAREHOUSE_MANAGER },
        ];
        testLocation1 = await prisma.location.create({
            data: {
                name: "Test Location 1 PI",
                warehouseId: testWarehouse1.id,
                locationType: client_1.LocationType.ZONE,
            },
        });
        testLocation2 = await prisma.location.create({
            data: {
                name: "Test Location 2 PI",
                warehouseId: testWarehouse2.id,
                locationType: client_1.LocationType.ZONE,
            },
        });
        testItem1 = await prisma.item.create({
            data: {
                name: "Test Item 1 PI",
                sku: "PI-ITEM-1",
                tenantId: testTenant.id,
            },
        });
        testItem2 = await prisma.item.create({
            data: {
                name: "Test Item 2 PI",
                sku: "PI-ITEM-2",
                tenantId: testTenant.id,
            },
        });
        testPallet1 = await prisma.pallet.create({
            data: {
                label: "Test Pallet 1",
                locationId: testLocation1.id,
                barcode: `TEST-PALLET-1-${Date.now()}`,
                shipToDestination: "Test Destination 1",
            },
        });
        testPallet2 = await prisma.pallet.create({
            data: {
                label: "Test Pallet 2",
                locationId: testLocation2.id,
                barcode: `TEST-PALLET-2-${Date.now()}`,
                shipToDestination: "Test Destination 2",
            },
        });
    });
    afterAll(async () => {
        await prisma.$disconnect();
    });
    describe("addItemToPallet", () => {
        it("should add a new item to an empty pallet for TENANT_ADMIN", async () => {
            const quantityToAdd = 10;
            const palletItem = await service.addItemToPallet(testPallet1.id, testItem1.id, quantityToAdd, mockAdminUser);
            expect(palletItem).toBeDefined();
            expect(palletItem.palletId).toBe(testPallet1.id);
            expect(palletItem.itemId).toBe(testItem1.id);
            expect(palletItem.quantity).toBe(quantityToAdd);
            const dbItem = await prisma.palletItem.findUnique({
                where: {
                    pallet_item_unique: {
                        palletId: testPallet1.id,
                        itemId: testItem1.id,
                    },
                },
            });
            expect(dbItem).not.toBeNull();
            expect(dbItem?.quantity).toBe(quantityToAdd);
        });
        it("should add a new item to an empty pallet for WAREHOUSE_MANAGER with access", async () => {
            const quantityToAdd = 12;
            const palletItem = await service.addItemToPallet(testPallet1.id, testItem1.id, quantityToAdd, mockWarehouseManagerUser);
            expect(palletItem.quantity).toBe(quantityToAdd);
        });
        it("should update quantity for TENANT_ADMIN", async () => {
            const initialQuantity = 5;
            await prisma.palletItem.create({
                data: {
                    palletId: testPallet1.id,
                    itemId: testItem1.id,
                    quantity: initialQuantity,
                },
            });
            const quantityToAdd = 7;
            const palletItem = await service.addItemToPallet(testPallet1.id, testItem1.id, quantityToAdd, mockAdminUser);
            expect(palletItem.quantity).toBe(initialQuantity + quantityToAdd);
        });
        it("should throw NotFoundException if pallet does not exist in user's tenant", async () => {
            const nonExistentPalletId = "non-existent-pallet-id";
            await expect(service.addItemToPallet(nonExistentPalletId, testItem1.id, 10, mockAdminUser)).rejects.toThrow(new common_1.NotFoundException(`Pallet with ID "${nonExistentPalletId}" not found in your tenant.`));
        });
        it("should throw NotFoundException if item does not exist in user's tenant", async () => {
            const nonExistentItemId = "non-existent-item-id";
            await expect(service.addItemToPallet(testPallet1.id, nonExistentItemId, 10, mockAdminUser)).rejects.toThrow(new common_1.NotFoundException(`Item with ID "${nonExistentItemId}" not found in your tenant.`));
        });
        it("should throw NotFoundException for WAREHOUSE_MANAGER trying to access pallet in another permitted warehouse (if service logic restricts based on specific pallet location)", async () => {
            await expect(service.addItemToPallet(testPallet2.id, testItem1.id, 10, mockWarehouseManagerUser)).rejects.toThrow(new common_1.NotFoundException(`You do not have access to the warehouse containing pallet "${testPallet2.id}".`));
        });
        it("should throw NotFoundException if TENANT_ADMIN tries to access pallet from another tenant", async () => {
            const otherTenantWarehouse = await prisma.warehouse.create({
                data: {
                    name: "Other Tenant Warehouse",
                    tenantId: mockOtherTenant.id,
                },
            });
            const otherTenantLocation = await prisma.location.create({
                data: {
                    name: "Other Tenant Location",
                    warehouseId: otherTenantWarehouse.id,
                    locationType: client_1.LocationType.ZONE,
                },
            });
            const otherTenantPallet = await prisma.pallet.create({
                data: {
                    label: "Other Tenant Pallet",
                    locationId: otherTenantLocation.id,
                    barcode: `OTHER-TENANT-PALLET-${Date.now()}`,
                    shipToDestination: "Other Tenant Destination",
                },
            });
            await expect(service.addItemToPallet(otherTenantPallet.id, testItem1.id, 10, mockAdminUser)).rejects.toThrow(new common_1.NotFoundException(`Pallet with ID "${otherTenantPallet.id}" not found in your tenant.`));
        });
        it("should throw BadRequestException if quantity is zero", async () => {
            await expect(service.addItemToPallet(testPallet1.id, testItem1.id, 0, mockAdminUser)).rejects.toThrow(common_1.BadRequestException);
        });
        it("should throw BadRequestException if quantity is negative", async () => {
            await expect(service.addItemToPallet(testPallet1.id, testItem1.id, -5, mockAdminUser)).rejects.toThrow(common_1.BadRequestException);
        });
    });
    describe("removeItemFromPallet", () => {
        beforeEach(async () => {
            await prisma.palletItem.create({
                data: {
                    palletId: testPallet1.id,
                    itemId: testItem1.id,
                    quantity: 15,
                },
            });
        });
        it("should remove an existing item from the pallet for TENANT_ADMIN", async () => {
            const removedItem = await service.removeItemFromPallet(testPallet1.id, testItem1.id, mockAdminUser);
            expect(removedItem).toBeDefined();
            const dbItem = await prisma.palletItem.findUnique({
                where: {
                    pallet_item_unique: {
                        palletId: testPallet1.id,
                        itemId: testItem1.id,
                    },
                },
            });
            expect(dbItem).toBeNull();
        });
        it("should throw NotFoundException if WAREHOUSE_MANAGER tries to remove from pallet in non-permitted warehouse", async () => {
            await prisma.palletItem.create({
                data: {
                    palletId: testPallet2.id,
                    itemId: testItem1.id,
                    quantity: 5,
                },
            });
            await expect(service.removeItemFromPallet(testPallet2.id, testItem1.id, mockWarehouseManagerUser)).rejects.toThrow(new common_1.NotFoundException(`You do not have access to the warehouse containing pallet "${testPallet2.id}".`));
        });
        it("should throw NotFoundException if item is not on the pallet (TENANT_ADMIN)", async () => {
            await expect(service.removeItemFromPallet(testPallet1.id, testItem2.id, mockAdminUser)).rejects.toThrow(new common_1.NotFoundException(`Item with ID "${testItem2.id}" not found on pallet "${testPallet1.id}" within your tenant.`));
        });
        it("should throw NotFoundException if pallet does not exist in tenant (TENANT_ADMIN)", async () => {
            const nonExistentPalletId = "non-existent-pallet-id-remove";
            await expect(service.removeItemFromPallet(nonExistentPalletId, testItem1.id, mockAdminUser)).rejects.toThrow(new common_1.NotFoundException(`Pallet with ID "${nonExistentPalletId}" not found in your tenant.`));
        });
    });
    describe("updatePalletItemQuantity", () => {
        const initialQuantity = 10;
        const newQuantity = 25;
        beforeEach(async () => {
            await prisma.palletItem.upsert({
                where: {
                    pallet_item_unique: {
                        palletId: testPallet1.id,
                        itemId: testItem1.id,
                    },
                },
                update: { quantity: initialQuantity },
                create: {
                    palletId: testPallet1.id,
                    itemId: testItem1.id,
                    quantity: initialQuantity,
                },
            });
        });
        it("should update quantity for TENANT_ADMIN", async () => {
            const updatedItem = await service.updatePalletItemQuantity(testPallet1.id, testItem1.id, newQuantity, mockAdminUser);
            expect(updatedItem.quantity).toBe(newQuantity);
            const dbItem = await prisma.palletItem.findFirst({
                where: {
                    palletId: testPallet1.id,
                    itemId: testItem1.id,
                },
            });
            expect(dbItem?.quantity).toBe(newQuantity);
        });
        it("should throw NotFoundException for WAREHOUSE_MANAGER trying to update pallet in non-permitted warehouse", async () => {
            await prisma.palletItem.create({
                data: {
                    palletId: testPallet2.id,
                    itemId: testItem1.id,
                    quantity: 5,
                },
            });
            await expect(service.updatePalletItemQuantity(testPallet2.id, testItem1.id, newQuantity, mockWarehouseManagerUser)).rejects.toThrow(new common_1.NotFoundException(`You do not have access to the warehouse containing pallet "${testPallet2.id}".`));
        });
        it("should throw NotFoundException if item not on pallet (TENANT_ADMIN)", async () => {
            await expect(service.updatePalletItemQuantity(testPallet1.id, testItem2.id, newQuantity, mockAdminUser)).rejects.toThrow(new common_1.NotFoundException(`Item with ID "${testItem2.id}" not found on pallet "${testPallet1.id}" within your tenant.`));
        });
        it("should throw NotFoundException if pallet not in tenant (TENANT_ADMIN)", async () => {
            const otherTenantWarehouse2 = await prisma.warehouse.create({
                data: {
                    name: "Other Tenant Warehouse 2",
                    tenantId: mockOtherTenant.id,
                },
            });
            const otherTenantLocation2 = await prisma.location.create({
                data: {
                    name: "Other Tenant Location 2",
                    warehouseId: otherTenantWarehouse2.id,
                    locationType: client_1.LocationType.ZONE,
                },
            });
            const otherTenantPallet = await prisma.pallet.create({
                data: {
                    label: "Other Tenant Pallet For Contents",
                    locationId: otherTenantLocation2.id,
                    barcode: `OTHER-TENANT-PALLET-2-${Date.now()}`,
                    shipToDestination: "Other Tenant Destination 2",
                },
            });
            await expect(service.updatePalletItemQuantity(otherTenantPallet.id, testItem1.id, newQuantity, mockAdminUser)).rejects.toThrow(new common_1.NotFoundException(`Pallet with ID "${otherTenantPallet.id}" not found in your tenant.`));
        });
        it("should throw BadRequestException if new quantity is zero", async () => {
            await expect(service.updatePalletItemQuantity(testPallet1.id, testItem1.id, 0, mockAdminUser)).rejects.toThrow(common_1.BadRequestException);
        });
        it("should throw BadRequestException if new quantity is negative", async () => {
            await expect(service.updatePalletItemQuantity(testPallet1.id, testItem1.id, -5, mockAdminUser)).rejects.toThrow(common_1.BadRequestException);
        });
    });
});
//# sourceMappingURL=pallet-items.service.integration.spec.js.map