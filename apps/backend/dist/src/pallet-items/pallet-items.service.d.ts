import { PrismaService } from "../prisma/prisma.service";
import { PalletItem } from "@prisma/client";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";
import { AuditLogService } from "../audit-log/audit-log.service";
export declare class PalletItemsService {
    private prisma;
    private auditLogService;
    constructor(prisma: PrismaService, auditLogService: AuditLogService);
    addItemToPallet(palletId: string, itemId: string, quantity: number, currentUser: AuthenticatedUser): Promise<PalletItem>;
    removeItemFromPallet(palletId: string, itemId: string, currentUser: AuthenticatedUser): Promise<PalletItem>;
    updatePalletItemQuantity(palletId: string, itemId: string, newQuantity: number, currentUser: AuthenticatedUser): Promise<PalletItem>;
}
