"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PalletItemsModule = void 0;
const common_1 = require("@nestjs/common");
const pallet_items_service_1 = require("./pallet-items.service");
const pallet_items_controller_1 = require("./pallet-items.controller");
const prisma_module_1 = require("../prisma/prisma.module");
const audit_log_module_1 = require("../audit-log/audit-log.module");
const auth_module_1 = require("../auth/auth.module");
const warehouses_module_1 = require("../warehouses/warehouses.module");
let PalletItemsModule = class PalletItemsModule {
};
exports.PalletItemsModule = PalletItemsModule;
exports.PalletItemsModule = PalletItemsModule = __decorate([
    (0, common_1.Module)({
        imports: [prisma_module_1.PrismaModule, audit_log_module_1.AuditLogModule, auth_module_1.AuthModule, warehouses_module_1.WarehousesModule],
        controllers: [pallet_items_controller_1.PalletItemsController],
        providers: [pallet_items_service_1.PalletItemsService],
    })
], PalletItemsModule);
//# sourceMappingURL=pallet-items.module.js.map