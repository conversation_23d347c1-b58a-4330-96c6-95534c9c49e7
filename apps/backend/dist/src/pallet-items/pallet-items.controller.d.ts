import { PalletItemsService } from "./pallet-items.service";
import { AddPalletItemDto } from "../pallets/dto/add-pallet-item.dto";
import { UpdatePalletItemDto } from "../pallets/dto/update-pallet-item.dto";
import { PalletItem } from "@prisma/client";
import { RequestWithWarehouseContext } from "../auth/decorators/warehouse-permission.decorator";
import { EnhancedUserPayload } from "../auth/types";
export declare class PalletItemsController {
    private readonly palletItemsService;
    constructor(palletItemsService: PalletItemsService);
    addItemToPallet(palletId: string, addPalletItemDto: AddPalletItemDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<PalletItem>;
    removeItemFromPallet(palletId: string, itemId: string, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<void>;
    updatePalletItemQuantity(palletId: string, itemId: string, updatePalletItemDto: UpdatePalletItemDto, req: RequestWithWarehouseContext<{
        user: EnhancedUserPayload;
    }>): Promise<PalletItem>;
}
