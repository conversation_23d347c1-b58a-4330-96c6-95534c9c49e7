"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShipmentsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let ShipmentsService = class ShipmentsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getShipmentSummary(id, currentUser, warehouseId) {
        const whereClause = {
            id: id,
            purchaseOrder: {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
            },
        };
        if (currentUser.role !== client_1.Role.TENANT_ADMIN) {
            const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
            if (userWarehouseIds.length === 0) {
                throw new common_1.NotFoundException(`Shipment with ID ${id} not found`);
            }
            whereClause.purchaseOrder.warehouseId = { in: userWarehouseIds };
        }
        if (warehouseId) {
            whereClause.purchaseOrder.warehouseId = warehouseId;
        }
        const shipment = await this.prisma.shipment.findFirst({
            where: whereClause,
            include: {
                purchaseOrder: true,
                pallets: {
                    include: {
                        location: true,
                        palletItems: {
                            include: {
                                item: true,
                            },
                        },
                    },
                },
            },
        });
        if (!shipment) {
            throw new common_1.NotFoundException(`Shipment with ID ${id} not found`);
        }
        const palletsByDestination = shipment.pallets.reduce((acc, pallet) => {
            const dest = pallet.shipToDestination || "Unassigned";
            if (!acc[dest]) {
                acc[dest] = [];
            }
            acc[dest].push(pallet);
            return acc;
        }, {});
        return {
            shipmentId: shipment.id,
            purchaseOrderNumber: shipment.purchaseOrder.poNumber,
            status: shipment.status,
            palletsByDestination,
        };
    }
    async findByPoNumber(poNumber, currentUser, warehouseId) {
        console.log(`[ShipmentsService] Searching for PO #${poNumber} for user ${currentUser.id}`);
        const whereClause = {
            poNumber: poNumber,
            warehouse: {
                tenantId: currentUser.tenantId,
            },
        };
        if (currentUser.role !== client_1.Role.TENANT_ADMIN) {
            const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
            if (userWarehouseIds.length === 0) {
                throw new common_1.NotFoundException(`Shipment with PO Number #${poNumber} not found`);
            }
            whereClause.warehouseId = { in: userWarehouseIds };
        }
        if (warehouseId) {
            whereClause.warehouseId = warehouseId;
        }
        const purchaseOrder = await this.prisma.purchaseOrder.findFirst({
            where: whereClause,
            include: {
                shipments: true,
            },
        });
        console.log("[ShipmentsService] Found PO:", JSON.stringify(purchaseOrder, null, 2));
        if (!purchaseOrder ||
            !purchaseOrder.shipments ||
            purchaseOrder.shipments.length === 0) {
            console.error("[ShipmentsService] PO found, but no shipment associated, or PO not found.");
            throw new common_1.NotFoundException(`Shipment with PO Number #${poNumber} not found`);
        }
        return purchaseOrder.shipments[0];
    }
    async findAll(currentUser, queryDto, warehouseId) {
        const { page = 1, limit = 10, status, sortBy = "createdAt", sortOrder = "desc", } = queryDto;
        const skip = (page - 1) * limit;
        const whereClause = {
            purchaseOrder: {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
            },
        };
        if (currentUser.role !== client_1.Role.TENANT_ADMIN) {
            const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
            if (userWarehouseIds.length === 0) {
                return { data: [], count: 0 };
            }
            whereClause.purchaseOrder.warehouseId = { in: userWarehouseIds };
        }
        if (warehouseId) {
            whereClause.purchaseOrder.warehouseId = warehouseId;
        }
        if (status) {
            whereClause.status = status;
        }
        const shipments = await this.prisma.shipment.findMany({
            where: whereClause,
            include: {
                purchaseOrder: true,
                _count: {
                    select: {
                        pallets: true,
                    },
                },
            },
            orderBy: {
                [sortBy]: sortOrder,
            },
            skip: skip,
            take: limit,
        });
        const totalCount = await this.prisma.shipment.count({
            where: whereClause,
        });
        const data = shipments.map((shipment) => ({
            id: shipment.id,
            referenceNumber: shipment.referenceNumber,
            status: shipment.status,
            createdAt: shipment.createdAt,
            updatedAt: shipment.updatedAt,
            poNumber: shipment.purchaseOrder.poNumber,
            palletCount: shipment._count.pallets,
            supplier: shipment.purchaseOrder.supplier,
        }));
        return {
            data,
            count: totalCount,
        };
    }
    async updateShipment(id, currentUser, updateShipmentDto, warehouseId) {
        const whereClause = {
            id: id,
            purchaseOrder: {
                warehouse: {
                    tenantId: currentUser.tenantId,
                },
            },
        };
        if (currentUser.role !== client_1.Role.TENANT_ADMIN) {
            const userWarehouseIds = currentUser.warehouseUsers?.map((wu) => wu.warehouseId) || [];
            if (userWarehouseIds.length === 0) {
                throw new common_1.NotFoundException(`Shipment with ID ${id} not found`);
            }
            whereClause.purchaseOrder.warehouseId = { in: userWarehouseIds };
        }
        if (warehouseId) {
            whereClause.purchaseOrder.warehouseId = warehouseId;
        }
        const existingShipment = await this.prisma.shipment.findFirst({
            where: whereClause,
        });
        if (!existingShipment) {
            throw new common_1.NotFoundException(`Shipment with ID ${id} not found`);
        }
        return this.prisma.shipment.update({
            where: { id },
            data: updateShipmentDto,
            include: {
                purchaseOrder: true,
            },
        });
    }
};
exports.ShipmentsService = ShipmentsService;
exports.ShipmentsService = ShipmentsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ShipmentsService);
//# sourceMappingURL=shipments.service.js.map