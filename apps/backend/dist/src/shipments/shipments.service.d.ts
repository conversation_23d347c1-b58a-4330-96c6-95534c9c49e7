import { PrismaService } from "../prisma/prisma.service";
import { Item, Location, PalletItem } from "@prisma/client";
import { QueryShipmentDto } from "./dto/query-shipment.dto";
import { ShipmentListItemDto } from "./dto/shipment-list-response.dto";
import { UpdateShipmentDto } from "./dto/update-shipment.dto";
import { AuthenticatedUser } from "../auth/types/authenticated-user.interface";
export declare class ShipmentsService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    getShipmentSummary(id: string, currentUser: AuthenticatedUser, warehouseId?: string): Promise<{
        shipmentId: string;
        purchaseOrderNumber: string;
        status: string;
        palletsByDestination: Record<string, ({
            description: string | null;
            status: string;
            id: string;
            label: string;
            barcode: string | null;
            shipToDestination: string | null;
            destinationCode: string | null;
            dateCreated: Date;
            lastMovedDate: Date;
            locationId: string | null;
            shipmentId: string | null;
        } & {
            location: Location | null;
            palletItems: (PalletItem & {
                item: Item;
            })[];
        })[]>;
    }>;
    findByPoNumber(poNumber: string, currentUser: AuthenticatedUser, warehouseId?: string): Promise<{
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
        referenceNumber: string | null;
        purchaseOrderId: string;
    }>;
    findAll(currentUser: AuthenticatedUser, queryDto: QueryShipmentDto, warehouseId?: string): Promise<{
        data: ShipmentListItemDto[];
        count: number;
    }>;
    updateShipment(id: string, currentUser: AuthenticatedUser, updateShipmentDto: UpdateShipmentDto, warehouseId?: string): Promise<{
        purchaseOrder: {
            status: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            tenantId: string;
            warehouseId: string | null;
            poNumber: string;
            supplier: string | null;
            notes: string | null;
            orderDate: Date;
            expectedDeliveryDate: Date | null;
        };
    } & {
        status: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
        referenceNumber: string | null;
        purchaseOrderId: string;
    }>;
}
