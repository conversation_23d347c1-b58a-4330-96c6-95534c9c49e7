"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShipmentsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const warehouse_permission_guard_1 = require("../auth/guards/warehouse-permission.guard");
const warehouse_permission_decorator_1 = require("../auth/decorators/warehouse-permission.decorator");
const shipments_service_1 = require("./shipments.service");
const shipment_summary_dto_1 = require("../purchase-orders/dto/shipment-summary.dto");
const query_shipment_dto_1 = require("./dto/query-shipment.dto");
const shipment_list_response_dto_1 = require("./dto/shipment-list-response.dto");
const update_shipment_dto_1 = require("./dto/update-shipment.dto");
let ShipmentsController = class ShipmentsController {
    constructor(shipmentsService) {
        this.shipmentsService = shipmentsService;
    }
    async findAll(req, queryDto, warehouseId) {
        const result = await this.shipmentsService.findAll(req.user, queryDto, warehouseId);
        const { page = 1, limit = 10 } = queryDto;
        const totalPages = Math.ceil(result.count / limit);
        return {
            ...result,
            page,
            limit,
            totalPages,
        };
    }
    getSummary(id, req, warehouseId) {
        return this.shipmentsService.getShipmentSummary(id, req.user, warehouseId);
    }
    findByPoNumber(poNumber, req, warehouseId) {
        return this.shipmentsService.findByPoNumber(poNumber, req.user, warehouseId);
    }
    updateShipment(id, updateShipmentDto, req, warehouseId) {
        return this.shipmentsService.updateShipment(id, req.user, updateShipmentDto, warehouseId);
    }
};
exports.ShipmentsController = ShipmentsController;
__decorate([
    (0, common_1.Get)(),
    (0, warehouse_permission_decorator_1.AllowWarehouseFiltering)(),
    (0, swagger_1.ApiOperation)({ summary: "Get all shipments for the tenant" }),
    (0, swagger_1.ApiQuery)({
        name: "page",
        required: false,
        type: Number,
        description: "Page number for pagination",
    }),
    (0, swagger_1.ApiQuery)({
        name: "limit",
        required: false,
        type: Number,
        description: "Number of items per page",
    }),
    (0, swagger_1.ApiQuery)({
        name: "status",
        required: false,
        type: String,
        description: "Filter by shipment status",
    }),
    (0, swagger_1.ApiQuery)({
        name: "sortBy",
        required: false,
        type: String,
        description: "Field to sort by (e.g., createdAt)",
    }),
    (0, swagger_1.ApiQuery)({
        name: "sortOrder",
        required: false,
        enum: ["asc", "desc"],
        description: "Sort order (asc or desc)",
    }),
    (0, swagger_1.ApiQuery)({
        name: "warehouseId",
        required: false,
        type: String,
        description: "Filter by warehouse ID",
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: "List of shipments.",
        type: shipment_list_response_dto_1.ShipmentListResponseDto,
    }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Query)("warehouseId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, query_shipment_dto_1.QueryShipmentDto, String]),
    __metadata("design:returntype", Promise)
], ShipmentsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)("summary/:id"),
    (0, warehouse_permission_decorator_1.RequireShipmentAccess)({
        extractFromEntity: { entityType: "shipment", paramName: "id" },
    }),
    (0, swagger_1.ApiOperation)({ summary: "Get a summary of a shipment" }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: "The shipment summary.",
        type: shipment_summary_dto_1.ShipmentSummaryResponseDto,
    }),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Query)("warehouseId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String]),
    __metadata("design:returntype", void 0)
], ShipmentsController.prototype, "getSummary", null);
__decorate([
    (0, common_1.Get)("by-po/:poNumber"),
    (0, warehouse_permission_decorator_1.RequireWarehouseAccess)(),
    (0, swagger_1.ApiOperation)({ summary: "Find a shipment by its Purchase Order number" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "The shipment record." }),
    (0, swagger_1.ApiResponse)({ status: 404, description: "Shipment not found." }),
    __param(0, (0, common_1.Param)("poNumber")),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Query)("warehouseId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String]),
    __metadata("design:returntype", void 0)
], ShipmentsController.prototype, "findByPoNumber", null);
__decorate([
    (0, common_1.Patch)(":id"),
    (0, warehouse_permission_decorator_1.RequireShipmentAccess)(),
    (0, swagger_1.ApiOperation)({ summary: "Update a shipment" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "The updated shipment." }),
    (0, swagger_1.ApiResponse)({ status: 404, description: "Shipment not found." }),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __param(3, (0, common_1.Query)("warehouseId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_shipment_dto_1.UpdateShipmentDto, Object, String]),
    __metadata("design:returntype", void 0)
], ShipmentsController.prototype, "updateShipment", null);
exports.ShipmentsController = ShipmentsController = __decorate([
    (0, swagger_1.ApiTags)("Shipments"),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, warehouse_permission_guard_1.WarehousePermissionGuard),
    (0, common_1.Controller)("shipments"),
    __metadata("design:paramtypes", [shipments_service_1.ShipmentsService])
], ShipmentsController);
//# sourceMappingURL=shipments.controller.js.map