"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnboardingController = void 0;
const common_1 = require("@nestjs/common");
const onboarding_service_1 = require("./onboarding.service");
const complete_profile_dto_1 = require("./dto/complete-profile.dto");
const jwt_auth_guard_1 = require("@/auth/guards/jwt-auth.guard");
const types_1 = require("@/auth/types");
let OnboardingController = exports.OnboardingController = class OnboardingController {
    constructor(onboardingService) {
        this.onboardingService = onboardingService;
    }
    async completeProfile(req, completeProfileDto) {
        const userId = req.user.id;
        return this.onboardingService.completeProfile(userId, completeProfileDto);
    }
};
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('complete-profile'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof types_1.AuthenticatedRequest !== "undefined" && types_1.AuthenticatedRequest) === "function" ? _b : Object, typeof (_c = typeof complete_profile_dto_1.CompleteProfileDto !== "undefined" && complete_profile_dto_1.CompleteProfileDto) === "function" ? _c : Object]),
    __metadata("design:returntype", Promise)
], OnboardingController.prototype, "completeProfile", null);
exports.OnboardingController = OnboardingController = __decorate([
    (0, common_1.Controller)('onboarding'),
    __metadata("design:paramtypes", [typeof (_a = typeof onboarding_service_1.OnboardingService !== "undefined" && onboarding_service_1.OnboardingService) === "function" ? _a : Object])
], OnboardingController);
//# sourceMappingURL=temp_onboarding.controller.js.map