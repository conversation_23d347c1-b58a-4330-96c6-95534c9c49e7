"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cleanupDatabase = cleanupDatabase;
const client_1 = require("@prisma/client");
async function cleanupDatabase(prisma) {
    const client = prisma instanceof client_1.PrismaClient ? prisma : prisma;
    const models = [
        "palletItem",
        "pallet",
        "location",
        "warehouse",
        "item",
        "user",
    ];
    console.log("Cleaning test database...");
    try {
        for (const model of models) {
            console.log(`Deleting ${model}...`);
            await client[model].deleteMany();
        }
        console.log("Test database cleaned.");
    }
    catch (error) {
        console.error(`Error cleaning database during ${models.join(", ")}:`, error);
        throw error;
    }
}
//# sourceMappingURL=db-cleanup.js.map