-- Grant permissions to the postgres role
GRANT USAGE ON SCHEMA public TO postgres;
GRANT SELECT ON TABLE public."User" TO postgres;

-- Create the custom JWT claims hook function
CREATE OR REPLACE FUNCTION public.custom_jwt_claims_hook(event json)
RETURNS json
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
  claims json;
  user_role public."Role";
  user_tenant_id TEXT;
  auth_user_id TEXT;
BEGIN
  -- Get the user's ID from the event
  auth_user_id := event->>'sub';

  -- Fetch the user's role and tenant ID from the public.User table
  SELECT
    "role",
    "tenantId"
  INTO
    user_role,
    user_tenant_id
  FROM
    public."User"
  WHERE
    "authUserId" = auth_user_id;

  -- Create the claims to be added to the JWT
  claims := json_build_object(
    'role', user_role,
    'tenant_id', user_tenant_id
  );

  -- Return the custom claims
  RETURN claims;
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.custom_jwt_claims_hook(json) TO authenticated;
GRANT EXECUTE ON FUNCTION public.custom_jwt_claims_hook(json) TO postgres;