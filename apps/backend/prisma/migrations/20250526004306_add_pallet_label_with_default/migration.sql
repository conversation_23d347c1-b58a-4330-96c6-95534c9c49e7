/*
  Warnings:

  - Added the required column `label` to the `Pallet` table without a default value. This is not possible if the table is not empty.

*/
-- Step 1: Add the column as nullable
ALTER TABLE "Pallet" ADD COLUMN "label" TEXT;

-- Step 2: Update existing rows to set a default value for label (e.g., copy from 'id')
-- Ensure the 'id' column is of a type that can be cast or directly used as TEXT. CUIDs are strings.
UPDATE "Pallet" SET "label" = "id" WHERE "label" IS NULL;

-- Step 3: Alter the column to be NOT NULL
ALTER TABLE "Pallet" ALTER COLUMN "label" SET NOT NULL;
