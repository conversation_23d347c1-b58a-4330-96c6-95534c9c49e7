/*
  Warnings:

  - The values [Other] on the enum `LocationCategory` will be removed. If these variants are still used in the database, this will fail.
  - The values [AISLE,STAGING_AREA] on the enum `LocationType` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `tenantId` on the `Location` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `Pallet` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `PalletItem` table. All the data in the column will be lost.
  - The primary key for the `Tenant` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The `status` column on the `User` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The primary key for the `WarehouseUser` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - A unique constraint covering the columns `[locationId,barcode]` on the table `Pallet` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[palletId,itemId]` on the table `PalletItem` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[userId,warehouseId]` on the table `WarehouseUser` will be added. If there are existing duplicate values, this will fail.
  - The required column `id` was added to the `WarehouseUser` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - Added the required column `role` to the `WarehouseUser` table without a default value. This is not possible if the table is not empty.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "LocationCategory_new" AS ENUM ('Receiving', 'Storage', 'Picking', 'Shipping', 'Returns');
ALTER TABLE "Location" ALTER COLUMN "category" DROP DEFAULT;
ALTER TABLE "Location" ALTER COLUMN "category" TYPE "LocationCategory_new" USING ("category"::text::"LocationCategory_new");
ALTER TYPE "LocationCategory" RENAME TO "LocationCategory_old";
ALTER TYPE "LocationCategory_new" RENAME TO "LocationCategory";
DROP TYPE "LocationCategory_old";
ALTER TABLE "Location" ALTER COLUMN "category" SET DEFAULT 'Storage';
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "LocationType_new" AS ENUM ('FLOOR', 'RACK', 'SHELF', 'BIN', 'DOCK', 'STAGING', 'OFFICE', 'ZONE', 'OTHER');
ALTER TABLE "Location" ALTER COLUMN "locationType" TYPE "LocationType_new" USING ("locationType"::text::"LocationType_new");
ALTER TYPE "LocationType" RENAME TO "LocationType_old";
ALTER TYPE "LocationType_new" RENAME TO "LocationType";
DROP TYPE "LocationType_old";
COMMIT;

-- DropForeignKey
ALTER TABLE "AuditLog" DROP CONSTRAINT "AuditLog_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "Item" DROP CONSTRAINT "Item_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "Location" DROP CONSTRAINT "Location_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "Pallet" DROP CONSTRAINT "Pallet_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "PalletItem" DROP CONSTRAINT "PalletItem_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "PurchaseOrder" DROP CONSTRAINT "PurchaseOrder_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "PurchaseOrderItem" DROP CONSTRAINT "PurchaseOrderItem_purchaseOrderId_fkey";

-- DropForeignKey
ALTER TABLE "Shipment" DROP CONSTRAINT "Shipment_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "User" DROP CONSTRAINT "User_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "Warehouse" DROP CONSTRAINT "Warehouse_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "WarehouseItem" DROP CONSTRAINT "WarehouseItem_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "WarehouseUser" DROP CONSTRAINT "WarehouseUser_userId_fkey";

-- DropForeignKey
ALTER TABLE "WarehouseUser" DROP CONSTRAINT "WarehouseUser_warehouseId_fkey";

-- DropIndex
DROP INDEX "Pallet_tenantId_barcode_key";

-- DropIndex
DROP INDEX "PalletItem_tenantId_palletId_itemId_key";

-- DropIndex
DROP INDEX "PurchaseOrderItem_purchaseOrderId_itemId_key";

-- AlterTable
ALTER TABLE "AuditLog" ALTER COLUMN "tenantId" SET DATA TYPE TEXT;

-- AlterTable
ALTER TABLE "Item" ALTER COLUMN "tenantId" SET DATA TYPE TEXT;

-- AlterTable
ALTER TABLE "Location" DROP COLUMN "tenantId";

-- AlterTable
ALTER TABLE "Pallet" DROP COLUMN "tenantId",
ADD COLUMN     "destinationCode" TEXT;

-- AlterTable
ALTER TABLE "PalletItem" DROP COLUMN "tenantId";

-- AlterTable
ALTER TABLE "PurchaseOrder" ALTER COLUMN "tenantId" SET DATA TYPE TEXT;

-- AlterTable
ALTER TABLE "Shipment" ALTER COLUMN "status" SET DEFAULT 'Pending',
ALTER COLUMN "tenantId" SET DATA TYPE TEXT;

-- AlterTable
ALTER TABLE "Tenant" DROP CONSTRAINT "Tenant_pkey",
ALTER COLUMN "id" SET DATA TYPE TEXT,
ADD CONSTRAINT "Tenant_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "User" ALTER COLUMN "tenantId" SET DATA TYPE TEXT,
DROP COLUMN "status",
ADD COLUMN     "status" TEXT NOT NULL DEFAULT 'Active';

-- AlterTable
ALTER TABLE "Warehouse" ALTER COLUMN "tenantId" SET DATA TYPE TEXT;

-- AlterTable
ALTER TABLE "WarehouseItem" ADD COLUMN     "currentStock" INTEGER NOT NULL DEFAULT 0,
ALTER COLUMN "tenantId" SET DATA TYPE TEXT;

-- AlterTable
ALTER TABLE "WarehouseUser" DROP CONSTRAINT "WarehouseUser_pkey",
ADD COLUMN     "assignedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "id" TEXT NOT NULL,
ADD COLUMN     "role" "Role" NOT NULL,
ADD CONSTRAINT "WarehouseUser_pkey" PRIMARY KEY ("id");

-- DropEnum
DROP TYPE "UserStatus";

-- CreateIndex
CREATE INDEX "Location_warehouseId_category_idx" ON "Location"("warehouseId", "category");

-- CreateIndex
CREATE INDEX "Location_warehouseId_status_idx" ON "Location"("warehouseId", "status");

-- CreateIndex
CREATE INDEX "Location_warehouseId_name_idx" ON "Location"("warehouseId", "name");

-- CreateIndex
CREATE INDEX "Pallet_locationId_status_idx" ON "Pallet"("locationId", "status");

-- CreateIndex
CREATE INDEX "Pallet_locationId_shipToDestination_idx" ON "Pallet"("locationId", "shipToDestination");

-- CreateIndex
CREATE INDEX "Pallet_locationId_dateCreated_idx" ON "Pallet"("locationId", "dateCreated");

-- CreateIndex
CREATE INDEX "Pallet_destinationCode_idx" ON "Pallet"("destinationCode");

-- CreateIndex
CREATE INDEX "Pallet_shipToDestination_destinationCode_idx" ON "Pallet"("shipToDestination", "destinationCode");

-- CreateIndex
CREATE UNIQUE INDEX "Pallet_locationId_barcode_key" ON "Pallet"("locationId", "barcode");

-- CreateIndex
CREATE UNIQUE INDEX "PalletItem_palletId_itemId_key" ON "PalletItem"("palletId", "itemId");

-- CreateIndex
CREATE INDEX "Warehouse_id_tenantId_idx" ON "Warehouse"("id", "tenantId");

-- CreateIndex
CREATE INDEX "WarehouseUser_warehouseId_userId_idx" ON "WarehouseUser"("warehouseId", "userId");

-- CreateIndex
CREATE UNIQUE INDEX "WarehouseUser_userId_warehouseId_key" ON "WarehouseUser"("userId", "warehouseId");

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Warehouse" ADD CONSTRAINT "Warehouse_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Item" ADD CONSTRAINT "Item_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrder" ADD CONSTRAINT "PurchaseOrder_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrderItem" ADD CONSTRAINT "PurchaseOrderItem_purchaseOrderId_fkey" FOREIGN KEY ("purchaseOrderId") REFERENCES "PurchaseOrder"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuditLog" ADD CONSTRAINT "AuditLog_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WarehouseUser" ADD CONSTRAINT "WarehouseUser_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WarehouseUser" ADD CONSTRAINT "WarehouseUser_warehouseId_fkey" FOREIGN KEY ("warehouseId") REFERENCES "Warehouse"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WarehouseItem" ADD CONSTRAINT "WarehouseItem_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Shipment" ADD CONSTRAINT "Shipment_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
