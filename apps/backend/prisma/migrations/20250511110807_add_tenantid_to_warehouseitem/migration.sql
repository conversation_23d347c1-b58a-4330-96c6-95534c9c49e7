/*
  Warnings:

  - A unique constraint covering the columns `[tenantId,warehouseId,itemId]` on the table `WarehouseItem` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `tenantId` to the `WarehouseItem` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "WarehouseItem_warehouseId_itemId_key";

-- AlterTable
ALTER TABLE "WarehouseItem" ADD COLUMN     "tenantId" UUID NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "WarehouseItem_tenantId_warehouseId_itemId_key" ON "WarehouseItem"("tenantId", "warehouseId", "itemId");

-- AddForeignKey
ALTER TABLE "WarehouseItem" ADD CONSTRAINT "WarehouseItem_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
