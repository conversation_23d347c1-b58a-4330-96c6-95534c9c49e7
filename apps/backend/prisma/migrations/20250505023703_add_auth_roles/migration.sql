/*
  Warnings:

  - The `role` column on the `User` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- CreateEnum
CREATE TYPE "Role" AS ENUM ('ADMIN', 'WAREHOUSE_ADMIN', 'WAREHOUSE_MEMBER');

-- AlterTable
ALTER TABLE "User" DROP COLUMN "role",
ADD COLUMN     "role" "Role" NOT NULL DEFAULT 'WAREHOUSE_MEMBER';

-- CreateTable
CREATE TABLE "WarehouseUser" (
    "userId" TEXT NOT NULL,
    "warehouseId" TEXT NOT NULL,

    CONSTRAINT "WarehouseUser_pkey" PRIMARY KEY ("userId","warehouseId")
);

-- AddForeignKey
ALTER TABLE "WarehouseUser" ADD CONSTRAINT "WarehouseUser_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WarehouseUser" ADD CONSTRAINT "WarehouseUser_warehouseId_fkey" FOREIGN KEY ("warehouseId") REFERENCES "Warehouse"("id") ON DELETE CASCADE ON UPDATE CASCADE;
