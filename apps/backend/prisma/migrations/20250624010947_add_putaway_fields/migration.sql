-- CreateEnum
CREATE TYPE "LocationCategory" AS ENUM ('Receiving', 'Storage', 'Shipping', 'Other');

-- AlterTable
ALTER TABLE "Location" ADD COLUMN     "category" "LocationCategory" NOT NULL DEFAULT 'Storage';

-- AlterTable
ALTER TABLE "Pallet" ADD COLUMN     "purchaseOrderId" TEXT;

-- CreateIndex
CREATE INDEX "Pallet_purchaseOrderId_idx" ON "Pallet"("purchaseOrderId");

-- AddForeignKey
ALTER TABLE "Pallet" ADD CONSTRAINT "Pallet_purchaseOrderId_fkey" FOREIGN KEY ("purchaseOrderId") REFERENCES "PurchaseOrder"("id") ON DELETE SET NULL ON UPDATE CASCADE;
