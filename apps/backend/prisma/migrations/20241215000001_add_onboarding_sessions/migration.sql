-- SAFE MIGRATION: Only adds new table, no existing data changes
-- CreateTable: onboarding_sessions
CREATE TABLE "onboarding_sessions" (
    "id" TEXT NOT NULL,
    "user_id" TEXT,
    "tenant_id" TEXT,
    "current_step" TEXT NOT NULL DEFAULT 'business_info',
    "data" JSONB NOT NULL DEFAULT '{}',
    "completed_at" TIMESTAMP(3),
    "expires_at" TIMESTAMP(3) NOT NULL DEFAULT (NOW() + INTERVAL '24 hours'),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "onboarding_sessions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "onboarding_sessions_user_id_idx" ON "onboarding_sessions"("user_id");

-- CreateIndex  
CREATE INDEX "onboarding_sessions_expires_at_idx" ON "onboarding_sessions"("expires_at");

-- CreateIndex
CREATE INDEX "onboarding_sessions_tenant_id_idx" ON "onboarding_sessions"("tenant_id");

-- AddForeignKey
ALTER TABLE "onboarding_sessions" ADD CONSTRAINT "onboarding_sessions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "onboarding_sessions" ADD CONSTRAINT "onboarding_sessions_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;
