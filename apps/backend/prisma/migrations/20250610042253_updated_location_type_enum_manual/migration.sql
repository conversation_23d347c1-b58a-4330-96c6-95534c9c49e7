-- CreateEnum
CREATE TYPE "LocationType" AS ENUM ('<PERSON><PERSON><PERSON>', 'RACK', 'SHELF', 'BIN', 'ZONE', 'DOCK', 'STAGING_AREA', 'OFFICE', 'OTHER');

-- Update existing string values to match the new enum definitions before changing the column type.
-- This prevents data loss by ensuring all current values can be cast to the new enum.
-- Mapping old string values to new enum values.
UPDATE "Location" SET "locationType" = 'DOCK' WHERE "locationType" IN ('RECEIVING_DOCK', 'Receiving');
UPDATE "Location" SET "locationType" = 'ZONE' WHERE "locationType" = 'Storage';
UPDATE "Location" SET "locationType" = 'STAGING_AREA' WHERE "locationType" IN ('Picking', 'Shipping');

-- Alter the column type from String to the new "LocationType" enum.
-- The USING clause tells PostgreSQL how to cast the existing text values to the new enum type.
-- This operation is now safe because the previous UPDATE statements aligned the data.
ALTER TABLE "Location"
ALTER COLUMN "locationType" TYPE "LocationType"
USING ("locationType"::text::"LocationType");
