import { PrismaClient, <PERSON>risma, Tenant, Warehouse, Item } from "@prisma/client";

const prisma = new PrismaClient();

const TARGET_TENANT_ID = "7f1d511e-d14a-47d8-a2c9-e06369d3ff98";
const PRIMARY_WAREHOUSE_NAME = "Main Test Warehouse";

async function main() {
  console.log(`Start seeding for tenant ID: ${TARGET_TENANT_ID} ...`);

  // --- Ensure Target Tenant Exists ---
  let tenant: Tenant | null = await prisma.tenant.findUnique({
    where: { id: TARGET_TENANT_ID },
  });

  if (!tenant) {
    console.log(`Tenant with ID ${TARGET_TENANT_ID} not found. Creating it...`);
    try {
      tenant = await prisma.tenant.create({
        data: {
          id: TARGET_TENANT_ID,
          name: "Primary Test Tenant (Seed)",
          // Add any other required fields for Tenant model
        },
      });
      console.log(`Created tenant with id: ${tenant.id}`);
    } catch (error) {
      console.error(`Failed to create tenant ${TARGET_TENANT_ID}:`, error);
      // If tenant creation fails, we cannot proceed with tenant-specific data.
      throw new Error(
        `Critical error: Could not find or create tenant ${TARGET_TENANT_ID}. Aborting seed.`
      );
    }
  } else {
    console.log(`Tenant ${TARGET_TENANT_ID} already exists.`);
  }

  // --- Create Test Warehouse for the Target Tenant ---
  let primaryWarehouse: Warehouse | undefined;
  try {
    const existingWarehouse = await prisma.warehouse.findFirst({
      where: { name: PRIMARY_WAREHOUSE_NAME, tenantId: TARGET_TENANT_ID },
    });
    if (!existingWarehouse) {
      primaryWarehouse = await prisma.warehouse.create({
        data: {
          name: PRIMARY_WAREHOUSE_NAME,
          address: "123 Seed Street, Testville",
          tenantId: TARGET_TENANT_ID,
        },
      });
      console.log(
        `Created warehouse "${primaryWarehouse.name}" with id: ${primaryWarehouse.id} for tenant ${TARGET_TENANT_ID}`
      );
    } else {
      primaryWarehouse = existingWarehouse;
      console.log(
        `Warehouse "${PRIMARY_WAREHOUSE_NAME}" for tenant ${TARGET_TENANT_ID} already exists. Skipping creation.`
      );
    }
  } catch (error) {
    console.error(
      `Failed to create or find warehouse ${PRIMARY_WAREHOUSE_NAME} for tenant ${TARGET_TENANT_ID}:`,
      error
    );
    // If warehouse creation fails, we might not be able to link items properly.
    // Depending on requirements, you might want to throw an error or continue.
    // For now, we will continue, but warehouseItem creation might fail or be incomplete.
  }

  // --- Create Items for the Target Tenant ---
  const itemsToCreate = [
    {
      name: "Standard Widget",
      sku: "WDGT-STD-001",
      description: "A standard widget for everyday use.",
      unitOfMeasure: "Each",
      lowStockThreshold: 10,
      defaultCost: 5.99,
      status: "ACTIVE",
    },
    {
      name: "Heavy-Duty Widget",
      sku: "WDGT-HD-001",
      description: "A heavy-duty widget for industrial applications.",
      unitOfMeasure: "Each",
      lowStockThreshold: 5,
      defaultCost: 15.75,
      status: "ACTIVE",
    },
    {
      name: "Small Component Alpha",
      sku: "COMP-S-A-T1", // Make SKU unique if necessary, e.g. by tenant
      description: "Alpha-type small component for primary tenant.",
      unitOfMeasure: "Case",
      lowStockThreshold: 50,
      defaultCost: 2.1,
      status: "ACTIVE",
    },
    {
      name: "Large Assembly Beta",
      sku: "ASSY-L-B-T1",
      description: "Beta-type large assembly for primary tenant.",
      unitOfMeasure: "Each",
      lowStockThreshold: 2,
      defaultCost: 120.5,
      status: "ACTIVE",
    },
    {
      name: "Bulk Material Gamma",
      description:
        "Gamma-type bulk material for primary tenant, sold by weight.",
      unitOfMeasure: "KG",
      lowStockThreshold: 100,
      defaultCost: 0.75,
      status: "ACTIVE",
      // No SKU example
    },
    {
      name: "Obsolete Gadget",
      sku: "GADGET-OBS-004",
      description: "An old gadget, no longer in production.",
      unitOfMeasure: "Each",
      lowStockThreshold: 0,
      defaultCost: 1.0,
      status: "INACTIVE",
    },
  ];

  const createdItems: Item[] = [];

  for (const itemData of itemsToCreate) {
    try {
      // Add tenantId to each item
      const dataWithTenant = { ...itemData, tenantId: TARGET_TENANT_ID };
      const item = await prisma.item.create({
        data: dataWithTenant as Prisma.ItemUncheckedCreateInput,
      });
      console.log(
        `Created item "${item.name}" (SKU: ${item.sku || "N/A"}) with id: ${
          item.id
        } for tenant ${TARGET_TENANT_ID}`
      );
      createdItems.push(item);
    } catch (error: any) {
      if (error.code === "P2002") {
        const target = (error.meta as any)?.target;
        console.log(
          `Item with conflicting unique field (e.g., SKU: ${
            itemData.sku
          } on target: ${
            target ? target.join(", ") : "unknown"
          }) for tenant ${TARGET_TENANT_ID} already exists. Skipping.`
        );
      } else {
        console.error(
          `Failed to create item "${itemData.name}" for tenant ${TARGET_TENANT_ID}:`,
          error
        );
      }
    }
  }

  // --- Create WarehouseItem entries to link items to the primary warehouse ---
  if (primaryWarehouse && createdItems.length > 0) {
    console.log(
      `Linking ${createdItems.length} created items to warehouse "${primaryWarehouse.name}" (ID: ${primaryWarehouse.id})...`
    );
    const warehouseItemData = createdItems.map((item) => ({
      warehouseId: primaryWarehouse!.id, // Use non-null assertion as we checked primaryWarehouse
      itemId: item.id,
      tenantId: TARGET_TENANT_ID,
      // Add initial stock details if your model supports it (e.g., quantity, locationInWarehouse)
      // For example:
      // quantity: Math.floor(Math.random() * 100) + 10, // Random initial stock
      // locationInWarehouse: `Shelf ${String.fromCharCode(65 + Math.floor(Math.random() * 5))}-${Math.floor(Math.random()*10)+1}`
    }));

    try {
      const creationResult = await prisma.warehouseItem.createMany({
        data: warehouseItemData,
        skipDuplicates: true, // In case some links already exist
      });
      console.log(
        `Created ${creationResult.count} WarehouseItem links for warehouse "${primaryWarehouse.name}".`
      );
    } catch (error) {
      console.error(
        `Failed to create WarehouseItem links for warehouse "${primaryWarehouse.name}":`,
        error
      );
    }
  } else {
    if (!primaryWarehouse) {
      console.warn(
        "Primary warehouse was not created or found. Skipping WarehouseItem linking."
      );
    }
    if (createdItems.length === 0) {
      console.warn(
        "No items were successfully created. Skipping WarehouseItem linking."
      );
    }
  }

  console.log(`Seeding finished for tenant ID: ${TARGET_TENANT_ID}.`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
