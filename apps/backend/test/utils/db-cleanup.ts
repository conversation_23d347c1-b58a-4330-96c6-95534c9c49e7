import { PrismaClient } from "@prisma/client";
import { PrismaService } from "../../src/prisma/prisma.service"; // Import PrismaService if needed for type

// Remove the locally instantiated client
// const prisma = new PrismaClient();

// Accept PrismaService instance as an argument
export async function cleanupDatabase(prisma: PrismaService | PrismaClient) {
  // Ensure we have the actual client methods
  const client = prisma instanceof PrismaClient ? prisma : (prisma as any);

  // Correct Order: Delete dependents first!
  const models = [
    "palletItem",
    "pallet",
    "location", // Locations depend on Warehouses
    "warehouse",
    "item",
    "user",
  ];

  console.log("Cleaning test database...");
  try {
    // Iterate in the specified order (dependents first)
    for (const model of models) {
      console.log(`Deleting ${model}...`);
      await (client[model] as any).deleteMany();
    }
    console.log("Test database cleaned.");
  } catch (error) {
    console.error(
      `Error cleaning database during ${models.join(", ")}:`,
      error
    );
    throw error;
  }
  // No need to disconnect here, the client passed in will be managed by the test suite
  // finally {
  //   await prisma.$disconnect();
  // }
}

// Optional: Add functions to seed specific data if needed for tests
