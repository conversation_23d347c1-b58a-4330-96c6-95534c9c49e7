import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication, ValidationPipe } from "@nestjs/common";
import request from "supertest";
import { JwtService } from "@nestjs/jwt";
// import { ConfigService } from '@nestjs/config'; // ConfigService not needed for secret handling
import { Role, User } from "@prisma/client";

import { AppModule } from "../src/app.module";
import { PrismaService } from "../src/prisma/prisma.service";
import { cleanupDatabase } from "../src/util/cleanup-database";

// Use a fixed secret for testing E2E
const TEST_JWT_SECRET = "test-secret-key-for-auth-e2e";

describe("AuthController (e2e)", () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let jwtService: JwtService;
  let adminUser: User;
  let memberUser: User;
  let testTenant: any;
  const adminSupabaseId = "supabase|admin123";
  const memberSupabaseId = "supabase|member456";

  beforeAll(async () => {
    // Set environment variables for test JWT configuration in AuthModule
    process.env.NODE_ENV = "test";
    process.env.TEST_JWT_SECRET = TEST_JWT_SECRET;

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      // ConfigService override not needed for secret
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(
      new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true })
    );
    app.setGlobalPrefix("api");
    await app.init();

    prisma = moduleFixture.get<PrismaService>(PrismaService);
    jwtService = moduleFixture.get<JwtService>(JwtService);

    // Clean DB before tests
    await cleanupDatabase(prisma);

    // Create a tenant first
    testTenant = await prisma.tenant.create({
      data: {
        name: "E2E Test Tenant",
      },
    });

    // Seed users with different roles, associated with the test tenant
    [adminUser, memberUser] = await prisma.$transaction([
      prisma.user.create({
        data: {
          email: "<EMAIL>",
          authUserId: adminSupabaseId,
          role: Role.TENANT_ADMIN,
          name: "Admin E2E User",
          tenantId: testTenant.id, // Associate with the created tenant
        },
      }),
      prisma.user.create({
        data: {
          email: "<EMAIL>",
          authUserId: memberSupabaseId,
          role: Role.WAREHOUSE_MEMBER,
          name: "Member E2E User",
          tenantId: testTenant.id, // Associate with the created tenant
        },
      }),
    ]);
  });

  afterAll(async () => {
    await cleanupDatabase(prisma);
    await app.close();
    // Clean up env vars if needed, though usually not necessary for tests
    delete process.env.NODE_ENV;
    delete process.env.TEST_JWT_SECRET;
  });

  // Helper to generate mock Supabase token (Uses jwtService from moduleFixture)
  const generateMockSupabaseToken = async (
    payload: object,
    secret: string = TEST_JWT_SECRET,
    expiresIn: string = "15m"
  ): Promise<string> => {
    return jwtService.signAsync(payload, { secret, expiresIn });
  };

  // --- /api/auth/login Tests ---

  it("/api/auth/login (POST) - should return accessToken with valid Supabase token", async () => {
    const validSupabaseToken = await generateMockSupabaseToken({
      sub: memberSupabaseId,
      email: memberUser.email,
    });

    const response = await request(app.getHttpServer())
      .post("/api/auth/login")
      .send({ supabaseToken: validSupabaseToken })
      .expect(200);

    expect(response.body).toHaveProperty("accessToken");
    const tokenPayload = await jwtService.verifyAsync(
      response.body.accessToken,
      { secret: TEST_JWT_SECRET }
    );
    expect(tokenPayload.userId).toEqual(memberUser.id);
    expect(tokenPayload.role).toEqual(Role.WAREHOUSE_MEMBER);
  });

  it("/api/auth/login (POST) - should return 401 with invalid Supabase token (bad signature)", async () => {
    const invalidToken = await generateMockSupabaseToken(
      { sub: memberSupabaseId, email: memberUser.email },
      "wrong-secret"
    );
    return request(app.getHttpServer())
      .post("/api/auth/login")
      .send({ supabaseToken: invalidToken })
      .expect(401);
  });

  it("/api/auth/login (POST) - should return 401 with expired Supabase token", async () => {
    const expiredToken = await generateMockSupabaseToken(
      { sub: memberSupabaseId, email: memberUser.email },
      TEST_JWT_SECRET,
      "-1s"
    );
    return request(app.getHttpServer())
      .post("/api/auth/login")
      .send({ supabaseToken: expiredToken })
      .expect(401);
  });

  it("/api/auth/login (POST) - should return 401 if user not found locally", async () => {
    const tokenForUnknown = await generateMockSupabaseToken({
      sub: "supabase|unknown",
      email: "<EMAIL>",
    });
    return request(app.getHttpServer())
      .post("/api/auth/login")
      .send({ supabaseToken: tokenForUnknown })
      .expect(401);
  });

  it("/api/auth/login (POST) - should return 400 for missing supabaseToken in body", async () => {
    return request(app.getHttpServer())
      .post("/api/auth/login")
      .send({}) // Empty body
      .expect(400);
  });

  // --- Guard Tests (using /api/warehouses) ---
  let adminAppToken: string;
  let memberAppToken: string;

  // Helper to get app token via login endpoint
  const getAppToken = async (
    supabaseId: string,
    email: string
  ): Promise<string> => {
    const supabaseToken = await generateMockSupabaseToken(
      { sub: supabaseId, email },
      TEST_JWT_SECRET
    );
    const response = await request(app.getHttpServer())
      .post("/api/auth/login")
      .send({ supabaseToken });
    if (!response.body || !response.body.accessToken) {
      console.error(
        "Login failed in getAppToken:",
        response.status,
        response.body
      );
      throw new Error(
        `Failed to get app token for ${email}. Status: ${response.status}`
      );
    }
    return response.body.accessToken;
  };

  beforeAll(async () => {
    // Get tokens for seeded users
    adminAppToken = await getAppToken(adminSupabaseId, adminUser.email);
    memberAppToken = await getAppToken(memberSupabaseId, memberUser.email);
  });

  it("/api/warehouses (GET) - should return 401 Unauthorized without token", () => {
    return request(app.getHttpServer()).get("/api/warehouses").expect(401);
  });

  it("/api/warehouses (GET) - should return 401 Unauthorized with invalid token", () => {
    return request(app.getHttpServer())
      .get("/api/warehouses")
      .set("Authorization", "Bearer invalidtoken")
      .expect(401);
  });

  it("/api/warehouses (GET) - should return 200 OK with valid token (Member role)", () => {
    return request(app.getHttpServer())
      .get("/api/warehouses")
      .set("Authorization", `Bearer ${memberAppToken}`)
      .expect(200);
  });

  it("/api/warehouses (GET) - should return 200 OK with valid token (Admin role)", () => {
    return request(app.getHttpServer())
      .get("/api/warehouses")
      .set("Authorization", `Bearer ${adminAppToken}`)
      .expect(200);
  });

  // Test POST (requires Admin role)
  const warehousePostBody = {
    name: "E2E Test Warehouse",
    address: "123 Test St",
  };

  it("/api/warehouses (POST) - should return 401 Unauthorized without token", () => {
    return request(app.getHttpServer())
      .post("/api/warehouses")
      .send(warehousePostBody)
      .expect(401);
  });

  it("/api/warehouses (POST) - should return 403 Forbidden with valid token but insufficient role (Member)", () => {
    return request(app.getHttpServer())
      .post("/api/warehouses")
      .set("Authorization", `Bearer ${memberAppToken}`)
      .send(warehousePostBody)
      .expect(403);
  });

  it("/api/warehouses (POST) - should return 201 Created with valid token and sufficient role (Admin)", () => {
    return request(app.getHttpServer())
      .post("/api/warehouses")
      .set("Authorization", `Bearer ${adminAppToken}`)
      .send(warehousePostBody)
      .expect(201)
      .then((response) => {
        expect(response.body.name).toEqual(warehousePostBody.name);
      });
  });
});
