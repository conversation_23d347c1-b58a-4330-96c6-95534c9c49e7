import { Test, TestingModule } from "@nestjs/testing";
import { AppModule } from "../src/app.module"; // Import main AppModule
import { PrismaService } from "../src/prisma/prisma.service";
import { ReceivingService } from "../src/receiving.service";
import { ReceivingModule } from "../src/receiving.module"; // Import the feature module
import { cleanupDatabase } from "./utils/db-cleanup";
import { CreateWarehouseDto } from "../src/warehouses/dto/create-warehouse.dto";
import { WarehousesService } from "../src/warehouses/warehouses.service";
import { CreateLocationDto } from "../src/locations/dto/create-location.dto";
import { LocationsService } from "../src/locations/locations.service";
import { CreateItemDto } from "../src/items/dto/create-item.dto";
import { ItemsService } from "../src/items/items.service";
import { ReceiveItemsDto } from "../src/receiving/dto/receive-items.dto";
import {
  BadRequestException,
  INestApplication,
  NotFoundException,
  ValidationPipe,
} from "@nestjs/common";
import request from "supertest";

describe("Receiving API (E2E)", () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let itemsService: ItemsService;
  let locationsService: LocationsService;
  let warehousesService: WarehousesService;
  let createdWarehouseId: string;
  let receivingLocationId: string;
  let storageLocationId: string;
  let item1Id: string;
  let item2Id: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
      })
    );
    await app.init();

    prisma = app.get<PrismaService>(PrismaService);
    itemsService = app.get<ItemsService>(ItemsService);
    locationsService = app.get<LocationsService>(LocationsService);
    warehousesService = app.get<WarehousesService>(WarehousesService);
  });

  beforeEach(async () => {
    await cleanupDatabase(prisma);

    const warehouseDto: CreateWarehouseDto = { name: "Test Warehouse" };
    const warehouse = await warehousesService.create(warehouseDto);
    createdWarehouseId = warehouse.id;

    const receivingLocationDto: CreateLocationDto = {
      name: "REC-01",
      locationType: "Receiving",
      warehouseId: createdWarehouseId,
    };
    const receivingLocation = await locationsService.create(
      receivingLocationDto
    );
    receivingLocationId = receivingLocation.id;

    const storageLocationDto: CreateLocationDto = {
      name: "STO-A1",
      locationType: "Storage",
      warehouseId: createdWarehouseId,
    };
    const storageLocation = await locationsService.create(storageLocationDto);
    storageLocationId = storageLocation.id;

    const item1Dto: CreateItemDto = { name: "Test Item 1", sku: "ITEM001" };
    const item1 = await itemsService.create(item1Dto);
    item1Id = item1.id;

    const item2Dto: CreateItemDto = { name: "Test Item 2", sku: "ITEM002" };
    const item2 = await itemsService.create(item2Dto);
    item2Id = item2.id;
  });

  afterAll(async () => {
    await app.close();
  });

  it("POST /receiving - should successfully receive items", async () => {
    const receiveDto: ReceiveItemsDto = {
      receivingLocationId: receivingLocationId,
      items: [
        { itemId: item1Id, quantity: 10 },
        { itemId: item2Id, quantity: 5 },
      ],
    };

    const response = await request(app.getHttpServer())
      .post("/receiving")
      .send(receiveDto)
      .expect(201);

    const resultPallet = response.body;

    expect(resultPallet).toBeDefined();
    expect(resultPallet.status).toEqual("RECEIVED");
    expect(resultPallet.locationId).toEqual(receivingLocationId);

    const palletItems = await prisma.palletItem.findMany({
      where: { palletId: resultPallet.id },
      orderBy: { itemId: "asc" },
    });
    expect(palletItems).toHaveLength(2);
    expect(palletItems[0].itemId).toEqual(item1Id);
    expect(palletItems[0].quantity).toEqual(10);
    expect(palletItems[1].itemId).toEqual(item2Id);
    expect(palletItems[1].quantity).toEqual(5);
  });

  it("POST /receiving - should return 404 if receiving location does not exist", async () => {
    const receiveDto: ReceiveItemsDto = {
      receivingLocationId: "invalid-location-id",
      items: [{ itemId: item1Id, quantity: 10 }],
    };

    const response = await request(app.getHttpServer())
      .post("/receiving")
      .send(receiveDto)
      .expect(404);

    expect(response.body.message).toContain(
      'Receiving location with ID "invalid-location-id" not found.'
    );
  });

  it("POST /receiving - should return 400 if location is not of type 'Receiving'", async () => {
    const receiveDto: ReceiveItemsDto = {
      receivingLocationId: storageLocationId,
      items: [{ itemId: item1Id, quantity: 10 }],
    };

    const response = await request(app.getHttpServer())
      .post("/receiving")
      .send(receiveDto)
      .expect(400);

    expect(response.body.message).toContain(
      'Location "STO-A1" is not a receiving location.'
    );
  });

  it("POST /receiving - should return 404 if any item ID does not exist", async () => {
    const receiveDto: ReceiveItemsDto = {
      receivingLocationId: receivingLocationId,
      items: [
        { itemId: item1Id, quantity: 10 },
        { itemId: "invalid-item-id", quantity: 5 },
      ],
    };

    const response = await request(app.getHttpServer())
      .post("/receiving")
      .send(receiveDto)
      .expect(404);

    expect(response.body.message).toContain(
      "Items with IDs not found: invalid-item-id"
    );
  });

  it("POST /receiving - should return 400 if quantity is invalid", async () => {
    const receiveDto: ReceiveItemsDto = {
      receivingLocationId: receivingLocationId,
      items: [
        { itemId: item1Id, quantity: 10 },
        { itemId: item2Id, quantity: -5 },
      ],
    };

    const response = await request(app.getHttpServer())
      .post("/receiving")
      .send(receiveDto)
      .expect(400);

    expect(response.body.message).toBeDefined();
    expect(response.body.message).toContain(
      "items.1.quantity must not be less than 1"
    );
    expect(response.body.error).toEqual("Bad Request");

    const pallets = await prisma.pallet.findMany();
    expect(pallets).toHaveLength(0);
    const palletItems = await prisma.palletItem.findMany();
    expect(palletItems).toHaveLength(0);
  });

  it("POST /receiving - should receive a single item successfully", async () => {
    const receiveDto: ReceiveItemsDto = {
      receivingLocationId: receivingLocationId,
      items: [{ itemId: item1Id, quantity: 15 }],
    };

    const response = await request(app.getHttpServer())
      .post("/receiving")
      .send(receiveDto)
      .expect(201);

    const resultPallet = response.body;
    expect(resultPallet).toBeDefined();
    expect(resultPallet.status).toEqual("RECEIVED");
    expect(resultPallet.locationId).toEqual(receivingLocationId);

    const palletItems = await prisma.palletItem.findMany({
      where: { palletId: resultPallet.id },
    });
    expect(palletItems).toHaveLength(1);
    expect(palletItems[0].itemId).toEqual(item1Id);
    expect(palletItems[0].quantity).toEqual(15);
  });
});
