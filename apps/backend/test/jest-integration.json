{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "../src", "testRegex": ".*\\.integration\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage-integration", "testEnvironment": "node", "modulePaths": ["<rootDir>/../node_modules"], "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1", "^@/util/(.*)$": "<rootDir>/util/$1", "^@/auth/(.*)$": "<rootDir>/auth/$1", "^@/prisma/(.*)$": "<rootDir>/prisma/$1"}}