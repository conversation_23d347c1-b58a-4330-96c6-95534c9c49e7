import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication, ValidationPipe } from "@nestjs/common";
import request from "supertest";
import { AppModule } from "../src/app.module";
import { PrismaService } from "../src/prisma/prisma.service";
import { JwtService } from "@nestjs/jwt";
import { Role, User } from "@prisma/client";
import { cleanupDatabase } from "../src/util/cleanup-database";

const TEST_JWT_SECRET = "test-secret-key-for-e2e-tests";

describe("ShipmentsController (e2e)", () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let jwtService: JwtService;
  let adminUser: User;
  let testTenant: any;
  let adminAppToken: string;
  const adminSupabaseId = "supabase|admin123";

  beforeAll(async () => {
    // Set environment variables for test JWT configuration
    process.env.NODE_ENV = "test";
    process.env.TEST_JWT_SECRET = TEST_JWT_SECRET;

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(
      new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true })
    );
    app.setGlobalPrefix("api");
    await app.init();

    prisma = moduleFixture.get<PrismaService>(PrismaService);
    jwtService = moduleFixture.get<JwtService>(JwtService);

    // Clean DB before tests
    await cleanupDatabase(prisma);

    // Create test tenant
    testTenant = await prisma.tenant.create({
      data: {
        name: "Shipments Test Tenant",
      },
    });

    // Create admin user
    adminUser = await prisma.user.create({
      data: {
        email: "<EMAIL>",
        name: "Admin User",
        authUserId: adminSupabaseId,
        role: Role.TENANT_ADMIN,
        tenantId: testTenant.id,
      },
    });

    // Get app token for admin user
    const getAppToken = async (
      supabaseId: string,
      email: string
    ): Promise<string> => {
      const supabaseToken = await generateMockSupabaseToken(
        { sub: supabaseId, email },
        TEST_JWT_SECRET
      );
      const response = await request(app.getHttpServer())
        .post("/api/auth/login")
        .send({ supabaseToken });
      if (!response.body || !response.body.accessToken) {
        throw new Error(
          `Failed to get app token for ${email}. Status: ${response.status}`
        );
      }
      return response.body.accessToken;
    };

    adminAppToken = await getAppToken(adminSupabaseId, adminUser.email);
  });

  // Helper to generate mock Supabase token
  const generateMockSupabaseToken = async (
    payload: object,
    secret: string = TEST_JWT_SECRET,
    expiresIn: string = "15m"
  ): Promise<string> => {
    return jwtService.signAsync(payload, { secret, expiresIn });
  };

  afterAll(async () => {
    await cleanupDatabase(prisma);
    await app.close();
    delete process.env.NODE_ENV;
    delete process.env.TEST_JWT_SECRET;
  });

  describe("/api/shipments (GET)", () => {
    it("should return 401 Unauthorized without token", () => {
      return request(app.getHttpServer()).get("/api/shipments").expect(401);
    });

    it("should return 401 Unauthorized with invalid token", () => {
      return request(app.getHttpServer())
        .get("/api/shipments")
        .set("Authorization", "Bearer invalidtoken")
        .expect(401);
    });

    it("should return 200 OK with valid token and proper response structure", async () => {
      const response = await request(app.getHttpServer())
        .get("/api/shipments")
        .set("Authorization", `Bearer ${adminAppToken}`)
        .expect(200);

      // Verify response structure
      expect(response.body).toHaveProperty("data");
      expect(response.body).toHaveProperty("count");
      expect(response.body).toHaveProperty("page");
      expect(response.body).toHaveProperty("limit");
      expect(response.body).toHaveProperty("totalPages");
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it("should accept pagination parameters", async () => {
      const response = await request(app.getHttpServer())
        .get("/api/shipments?page=1&limit=5")
        .set("Authorization", `Bearer ${adminAppToken}`)
        .expect(200);

      expect(response.body.page).toBe(1);
      expect(response.body.limit).toBe(5);
    });

    it("should accept sorting parameters", async () => {
      const response = await request(app.getHttpServer())
        .get("/api/shipments?sortBy=createdAt&sortOrder=desc")
        .set("Authorization", `Bearer ${adminAppToken}`)
        .expect(200);

      expect(response.body).toHaveProperty("data");
    });
  });
});
