{"name": "backend", "version": "1.0.0", "description": "backend is the ultimate backend solution", "main": "dist/main.js", "scripts": {"setup": "npm install && npm update", "tsc": "tsc", "build": "nest build", "start": "node dist/src/main", "dev": "nest start --watch", "prod": "node dist/src/main", "prisma:test:deploy": "dotenv -e .env.test -- pnpm exec prisma migrate deploy", "test": "pnpm run prisma:test:deploy && pnpm exec prisma generate && dotenv -e .env.test -- jest --runInBand", "test:watch": "pnpm run prisma:test:deploy && pnpm exec prisma generate && dotenv -e .env.test -- jest --watch --runInBand", "test:cov": "pnpm run prisma:test:deploy && pnpm exec prisma generate && dotenv -e .env.test -- jest --coverage --runInBand", "test:debug": "pnpm run prisma:test:deploy && pnpm exec prisma generate && dotenv -e .env.test -- node --inspect-brk -r tsconfig-paths/register -r ts-node/register ../../node_modules/.bin/jest --runInBand", "test:e2e": "pnpm run prisma:test:deploy && pnpm exec prisma generate && jest --config ./test/jest-e2e.json --runInBand --forceExit", "test:integration": "pnpm run prisma:test:deploy && pnpm exec prisma generate && dotenv -e .env.test -- jest --config ./test/jest-integration.json --runInBand"}, "keywords": ["express", "backend", "backend"], "license": "UNLICENSED", "dependencies": {"@nestjs/cli": "^11.0.0", "@nestjs/common": "^11.0.20", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.20", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.0", "@nestjs/schedule": "^6.0.0", "@prisma/client": "^6.11.1", "@quildora/types": "workspace:*", "@supabase/supabase-js": "^2.50.0", "@types/bcryptjs": "^3.0.0", "@types/express": "^4.17.1", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "bwip-js": "^4.6.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.17.1", "express-custom-error": "^1.8.4", "has-keys": "^0.1.0", "helmet": "^3.21.2", "http-status": "^1.3.2", "mandatoryenv": "^1.1.1", "module-alias": "^2.2.2", "morgan": "^1.9.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pdf-lib": "^1.17.1", "pg": "^7.12.1", "pg-hstore": "^2.3.3", "reflect-metadata": "^0.2.2", "rotating-file-stream": "^1.4.6", "rxjs": "^7.8.2", "sequelize": "^5.19.2", "ts-node-dev": "^1.0.0-pre.43"}, "devDependencies": {"@nestjs/swagger": "^11.2.0", "@nestjs/testing": "^10.3.10", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "dotenv-cli": "^8.0.0", "jest": "^29.7.0", "jest-mock-extended": "^4.0.0", "prisma": "^6.11.1", "supertest": "^7.0.0", "swagger-ui-express": "^5.0.1", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5.1.6"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}