"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = process.env, DB_HOST = _a.DB_HOST, DB_DATABASE = _a.DB_DATABASE, DB_USER = _a.DB_USER, DB_PASSWORD = _a.DB_PASSWORD;
var sequelize_1 = __importDefault(require("sequelize"));
var db = new sequelize_1.default(DB_DATABASE, DB_USER, DB_PASSWORD, {
    host: DB_HOST,
    pool: {
        max: 10,
        min: 0,
        acquire: 30000,
        idle: 10000
    },
    dialect: 'postgres',
    dialectOptions: {
        options: {
            useUTC: false,
            dateFirst: 1,
        }
    },
    define: {
        timestamps: false
    }
});
exports.default = db;
