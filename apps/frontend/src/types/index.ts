// /Users/<USER>/Dev/quildora/apps/frontend/src/types/index.ts



// All primary domain interfaces (User, Item, WarehouseBasic, Location, PalletItem, Pallet)
// have been moved to @quildora/types.
// This file can be used for any truly frontend-specific types or re-exports if needed in the future.

// Example of re-exporting if a different alias is desired locally (optional):
// export type { Item as LocalItem } from '@quildora/types';

// Consider defining enums for locationTypes and locationStatuses here if shared
// and if they are not suitable for the main @quildora/types package.
// export const locationTypes = [...] as const;
// export const locationStatuses = [...] as const;
