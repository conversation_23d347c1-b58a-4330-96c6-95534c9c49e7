/* print.css */

/* The @page rule is the most important part. It instructs the browser/printer
  on the physical page's characteristics BEFORE any content is rendered.
  Setting the size and margins here is the most reliable method.
*/
@page {
  size: 11in 8.5in; /* US Letter landscape */
  margin: 0.5in;
}

@media print {
  /*
    Apply border-box sizing to all printed elements. This ensures that
    padding and borders are included in the element's total width and height,
    which prevents unexpected overflow.
  */
  * {
    box-sizing: border-box;
  }

  body {
    /* Hide scrollbars that might appear during print rendering */
    overflow: hidden;
  }

  /* Hide everything in the body by default */
  body * {
    visibility: hidden;
  }

  /*
    Make ONLY the placard area and its children visible.
    This element should wrap your <Placard /> component in the page that handles printing.
  */
  #placard-print-area, #placard-print-area * {
    visibility: visible;
  }

  /*
    Position the placard area to fill the entire printable area of the page,
    respecting the margins set in the @page rule.
  */
  #placard-print-area {
    display: block; /* Ensure it behaves like a block-level element */
    position: absolute;
    top: 0;
    left: 0;
    width: 10in; /* 11in total width - 2 * 0.5in margin */
    height: 7.5in; /* 8.5in total height - 2 * 0.5in margin */
  }
}