/**
 * Utility functions for pallet-related operations
 */

/**
 * Get the appropriate CSS classes for pallet status badges
 * @param status - The pallet status
 * @returns CSS classes for styling the status badge
 */
export const getStatusColor = (status: string): string => {
  switch (status) {
    case "Stored":
      return "bg-green-100 text-green-800";
    case "Picking":
      return "bg-yellow-100 text-yellow-800";
    case "Released":
      return "bg-gray-100 text-gray-800";
    default:
      return "bg-slate-100 text-slate-800";
  }
};

/**
 * Format a date string for display in tables
 * @param dateString - ISO date string or null/undefined
 * @returns Formatted date string or "-" if no date
 */
export const formatTableDate = (dateString: string | null | undefined): string => {
  if (!dateString) return "-";
  return new Date(dateString).toLocaleDateString();
};

/**
 * Calculate the number of table columns based on user permissions
 * @param hasActions - Whether the user has permission to see action column
 * @returns Number of columns in the table
 */
export const getTableColumnCount = (hasActions: boolean): number => {
  // Base columns: Barcode, Status, Ship To Destination, Destination Code, Location, Description, Created At, Last Moved
  const baseColumns = 8;
  return hasActions ? baseColumns + 1 : baseColumns;
};
