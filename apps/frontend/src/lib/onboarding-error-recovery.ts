'use client';

import { OnboardingState, OnboardingStep } from '@quildora/types';

// Error types for onboarding
export enum OnboardingErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  SERVER_ERROR = 'SERVER_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export interface OnboardingError {
  type: OnboardingErrorType;
  message: string;
  step?: OnboardingStep;
  field?: string;
  retryable: boolean;
  timestamp: number;
}

// Recovery strategies
export enum RecoveryStrategy {
  RETRY = 'RETRY',
  RELOAD_PAGE = 'RELOAD_PAGE',
  RESTART_ONBOARDING = 'RESTART_ONBOARDING',
  CONTACT_SUPPORT = 'CONTACT_SUPPORT',
  MANUAL_INTERVENTION = 'MANUAL_INTERVENTION',
}

export interface RecoveryAction {
  strategy: RecoveryStrategy;
  description: string;
  action: () => Promise<void> | void;
  priority: number; // Lower number = higher priority
}

// Local storage keys
const STORAGE_KEYS = {
  ONBOARDING_BACKUP: 'quildora_onboarding_backup',
  ERROR_LOG: 'quildora_onboarding_errors',
  RETRY_COUNT: 'quildora_onboarding_retry_count',
  LAST_SUCCESSFUL_STEP: 'quildora_last_successful_step',
} as const;

export class OnboardingErrorRecovery {
  private static readonly MAX_RETRY_ATTEMPTS = 3;
  private static readonly RETRY_DELAY_BASE = 1000; // 1 second
  private static readonly MAX_RETRY_DELAY = 10000; // 10 seconds
  private static readonly ERROR_LOG_MAX_SIZE = 50;

  // Save onboarding progress to localStorage
  static async saveProgressToLocalStorage(data: OnboardingState): Promise<void> {
    try {
      if (typeof window === 'undefined') return;
      
      const backup = {
        data,
        timestamp: Date.now(),
        version: '1.0',
      };
      
      localStorage.setItem(STORAGE_KEYS.ONBOARDING_BACKUP, JSON.stringify(backup));
      
      // Also save the last successful step
      if (data.step) {
        localStorage.setItem(STORAGE_KEYS.LAST_SUCCESSFUL_STEP, data.step);
      }
    } catch (error) {
      console.warn('Failed to save onboarding progress to localStorage:', error);
    }
  }

  // Load onboarding progress from localStorage
  static loadProgressFromLocalStorage(): OnboardingState | null {
    try {
      if (typeof window === 'undefined') return null;
      
      const saved = localStorage.getItem(STORAGE_KEYS.ONBOARDING_BACKUP);
      if (!saved) return null;

      const backup = JSON.parse(saved);
      
      // Validate backup structure
      if (!backup.data || !backup.timestamp || !backup.version) {
        console.warn('Invalid backup structure, clearing localStorage');
        this.clearProgressFromLocalStorage();
        return null;
      }

      // Check if backup is too old (older than 24 hours)
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      if (Date.now() - backup.timestamp > maxAge) {
        console.warn('Backup is too old, clearing localStorage');
        this.clearProgressFromLocalStorage();
        return null;
      }

      return backup.data;
    } catch (error) {
      console.warn('Failed to load onboarding progress from localStorage:', error);
      this.clearProgressFromLocalStorage();
      return null;
    }
  }

  // Clear onboarding progress from localStorage
  static clearProgressFromLocalStorage(): void {
    try {
      if (typeof window === 'undefined') return;
      
      localStorage.removeItem(STORAGE_KEYS.ONBOARDING_BACKUP);
      localStorage.removeItem(STORAGE_KEYS.LAST_SUCCESSFUL_STEP);
      localStorage.removeItem(STORAGE_KEYS.RETRY_COUNT);
    } catch (error) {
      console.warn('Failed to clear onboarding progress from localStorage:', error);
    }
  }

  // Log error for debugging and analytics
  static logError(error: OnboardingError): void {
    try {
      if (typeof window === 'undefined') return;
      
      const existingLogs = this.getErrorLogs();
      const newLogs = [error, ...existingLogs].slice(0, this.ERROR_LOG_MAX_SIZE);
      
      localStorage.setItem(STORAGE_KEYS.ERROR_LOG, JSON.stringify(newLogs));
    } catch (storageError) {
      console.warn('Failed to log error to localStorage:', storageError);
    }
    
    // Also log to console for development
    console.error('Onboarding Error:', error);
  }

  // Get error logs from localStorage
  static getErrorLogs(): OnboardingError[] {
    try {
      if (typeof window === 'undefined') return [];
      
      const logs = localStorage.getItem(STORAGE_KEYS.ERROR_LOG);
      return logs ? JSON.parse(logs) : [];
    } catch (error) {
      console.warn('Failed to get error logs from localStorage:', error);
      return [];
    }
  }

  // Clear error logs
  static clearErrorLogs(): void {
    try {
      if (typeof window === 'undefined') return;
      localStorage.removeItem(STORAGE_KEYS.ERROR_LOG);
    } catch (error) {
      console.warn('Failed to clear error logs:', error);
    }
  }

  // Get retry count for current session
  static getRetryCount(): number {
    try {
      if (typeof window === 'undefined') return 0;
      
      const count = localStorage.getItem(STORAGE_KEYS.RETRY_COUNT);
      return count ? parseInt(count, 10) : 0;
    } catch (error) {
      return 0;
    }
  }

  // Increment retry count
  static incrementRetryCount(): number {
    try {
      if (typeof window === 'undefined') return 0;
      
      const currentCount = this.getRetryCount();
      const newCount = currentCount + 1;
      localStorage.setItem(STORAGE_KEYS.RETRY_COUNT, newCount.toString());
      return newCount;
    } catch (error) {
      return 0;
    }
  }

  // Reset retry count
  static resetRetryCount(): void {
    try {
      if (typeof window === 'undefined') return;
      localStorage.removeItem(STORAGE_KEYS.RETRY_COUNT);
    } catch (error) {
      console.warn('Failed to reset retry count:', error);
    }
  }

  // Classify error type
  static classifyError(error: any): OnboardingErrorType {
    if (!error) return OnboardingErrorType.UNKNOWN_ERROR;
    
    const message = error.message?.toLowerCase() || '';
    
    if (error.name === 'TypeError' && message.includes('fetch')) {
      return OnboardingErrorType.NETWORK_ERROR;
    }
    
    if (message.includes('validation') || message.includes('invalid')) {
      return OnboardingErrorType.VALIDATION_ERROR;
    }
    
    if (message.includes('session') || message.includes('expired')) {
      return OnboardingErrorType.SESSION_EXPIRED;
    }
    
    if (message.includes('unauthorized') || message.includes('authentication')) {
      return OnboardingErrorType.AUTHENTICATION_ERROR;
    }
    
    if (error.status >= 500) {
      return OnboardingErrorType.SERVER_ERROR;
    }
    
    if (error.status >= 400) {
      return OnboardingErrorType.VALIDATION_ERROR;
    }
    
    return OnboardingErrorType.UNKNOWN_ERROR;
  }

  // Create OnboardingError from any error
  static createOnboardingError(
    error: any, 
    step?: OnboardingStep, 
    field?: string
  ): OnboardingError {
    const type = this.classifyError(error);
    const message = error.message || 'An unknown error occurred';
    
    const retryable = [
      OnboardingErrorType.NETWORK_ERROR,
      OnboardingErrorType.SERVER_ERROR,
    ].includes(type);

    return {
      type,
      message,
      step,
      field,
      retryable,
      timestamp: Date.now(),
    };
  }

  // Get recovery actions for an error
  static getRecoveryActions(
    error: OnboardingError,
    context: {
      retryCallback?: () => Promise<void>;
      restartCallback?: () => void;
      reloadCallback?: () => void;
    }
  ): RecoveryAction[] {
    const actions: RecoveryAction[] = [];

    switch (error.type) {
      case OnboardingErrorType.NETWORK_ERROR:
        if (context.retryCallback && this.getRetryCount() < this.MAX_RETRY_ATTEMPTS) {
          actions.push({
            strategy: RecoveryStrategy.RETRY,
            description: 'Retry the request',
            action: context.retryCallback,
            priority: 1,
          });
        }
        actions.push({
          strategy: RecoveryStrategy.RELOAD_PAGE,
          description: 'Reload the page',
          action: context.reloadCallback || (() => window.location.reload()),
          priority: 2,
        });
        break;

      case OnboardingErrorType.SESSION_EXPIRED:
        actions.push({
          strategy: RecoveryStrategy.RESTART_ONBOARDING,
          description: 'Restart onboarding process',
          action: context.restartCallback || (() => {}),
          priority: 1,
        });
        break;

      case OnboardingErrorType.VALIDATION_ERROR:
        actions.push({
          strategy: RecoveryStrategy.MANUAL_INTERVENTION,
          description: 'Please correct the form and try again',
          action: () => {},
          priority: 1,
        });
        break;

      case OnboardingErrorType.SERVER_ERROR:
        if (context.retryCallback && this.getRetryCount() < this.MAX_RETRY_ATTEMPTS) {
          actions.push({
            strategy: RecoveryStrategy.RETRY,
            description: 'Retry the request',
            action: context.retryCallback,
            priority: 1,
          });
        }
        actions.push({
          strategy: RecoveryStrategy.CONTACT_SUPPORT,
          description: 'Contact support if the problem persists',
          action: () => {},
          priority: 3,
        });
        break;

      default:
        actions.push({
          strategy: RecoveryStrategy.RELOAD_PAGE,
          description: 'Reload the page',
          action: context.reloadCallback || (() => window.location.reload()),
          priority: 2,
        });
        actions.push({
          strategy: RecoveryStrategy.CONTACT_SUPPORT,
          description: 'Contact support',
          action: () => {},
          priority: 3,
        });
    }

    return actions.sort((a, b) => a.priority - b.priority);
  }

  // Retry with exponential backoff
  static async withRetry<T>(
    operation: () => Promise<T>,
    context: string,
    step?: OnboardingStep
  ): Promise<T> {
    let lastError: Error;
    const maxAttempts = this.MAX_RETRY_ATTEMPTS;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const result = await operation();
        
        // Reset retry count on success
        if (attempt > 1) {
          this.resetRetryCount();
        }
        
        return result;
      } catch (error) {
        lastError = error as Error;
        
        const onboardingError = this.createOnboardingError(error, step);
        this.logError(onboardingError);
        
        if (attempt < maxAttempts && onboardingError.retryable) {
          const delay = Math.min(
            this.RETRY_DELAY_BASE * Math.pow(2, attempt - 1),
            this.MAX_RETRY_DELAY
          );
          
          console.log(`Retrying ${context} in ${delay}ms (attempt ${attempt}/${maxAttempts})`);
          await this.delay(delay);
          this.incrementRetryCount();
          continue;
        }
        
        // Log final failure
        console.error(`${context} failed after ${attempt} attempts:`, error);
        throw error;
      }
    }

    throw lastError!;
  }

  // Utility delay function
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Check if recovery is possible
  static canRecover(error: OnboardingError): boolean {
    return error.retryable && this.getRetryCount() < this.MAX_RETRY_ATTEMPTS;
  }

  // Get user-friendly error message
  static getUserFriendlyMessage(error: OnboardingError): string {
    switch (error.type) {
      case OnboardingErrorType.NETWORK_ERROR:
        return 'Network connection issue. Please check your internet connection and try again.';
      case OnboardingErrorType.VALIDATION_ERROR:
        return error.message || 'Please check your input and try again.';
      case OnboardingErrorType.SESSION_EXPIRED:
        return 'Your session has expired. Please restart the onboarding process.';
      case OnboardingErrorType.SERVER_ERROR:
        return 'Server error occurred. Please try again in a moment.';
      case OnboardingErrorType.AUTHENTICATION_ERROR:
        return 'Authentication error. Please sign in again.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }
}
