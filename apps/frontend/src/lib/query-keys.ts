/**
 * Centralized query key factory for React Query
 * Implements hierarchical caching structure for optimal cache management
 */

// Base query key types
type QueryKeyBase = readonly unknown[];

// User-related query keys
export const userKeys = {
  all: ["user"] as const,
  user: (userId: string) => [...userKeys.all, userId] as const,
  warehouses: (userId: string) => [...userKeys.user(userId), "warehouses"] as const,
  profile: (userId: string) => [...userKeys.user(userId), "profile"] as const,
} as const;

// Warehouse-related query keys
export const warehouseKeys = {
  all: ["warehouse"] as const,
  warehouse: (warehouseId: string) => [...warehouseKeys.all, warehouseId] as const,
  
  // Warehouse permissions and validation
  permissions: (warehouseId: string) => [...warehouseKeys.warehouse(warehouseId), "permissions"] as const,
  validation: (warehouseId: string, userId: string) => [...warehouseKeys.warehouse(warehouseId), "validation", userId] as const,
  
  // Warehouse data
  data: (warehouseId: string) => [...warehouseKeys.warehouse(warehouseId), "data"] as const,
  pallets: (warehouseId: string) => [...warehouseKeys.data(warehouseId), "pallets"] as const,
  locations: (warehouseId: string) => [...warehouseKeys.data(warehouseId), "locations"] as const,
  items: (warehouseId: string) => [...warehouseKeys.data(warehouseId), "items"] as const,
  
  // Warehouse operations
  operations: (warehouseId: string) => [...warehouseKeys.warehouse(warehouseId), "operations"] as const,
  shipments: (warehouseId: string) => [...warehouseKeys.operations(warehouseId), "shipments"] as const,
  purchaseOrders: (warehouseId: string) => [...warehouseKeys.operations(warehouseId), "purchase-orders"] as const,
  
  // Specific pallet queries
  pallet: (warehouseId: string, palletId: string) => [...warehouseKeys.pallets(warehouseId), palletId] as const,
  palletItems: (warehouseId: string, palletId: string) => [...warehouseKeys.pallet(warehouseId, palletId), "items"] as const,
  
  // Specific location queries
  location: (warehouseId: string, locationId: string) => [...warehouseKeys.locations(warehouseId), locationId] as const,
  locationPallets: (warehouseId: string, locationId: string) => [...warehouseKeys.location(warehouseId, locationId), "pallets"] as const,
} as const;

// Shipment-related query keys
export const shipmentKeys = {
  all: ["shipment"] as const,
  shipment: (shipmentId: string) => [...shipmentKeys.all, shipmentId] as const,
  summary: (shipmentId: string) => [...shipmentKeys.shipment(shipmentId), "summary"] as const,
  pallets: (shipmentId: string) => [...shipmentKeys.shipment(shipmentId), "pallets"] as const,
  
  // Shipment by PO
  byPo: (poNumber: string) => [...shipmentKeys.all, "by-po", poNumber] as const,
} as const;

// Purchase Order related query keys
export const purchaseOrderKeys = {
  all: ["purchase-order"] as const,
  purchaseOrder: (poId: string) => [...purchaseOrderKeys.all, poId] as const,
  byNumber: (poNumber: string) => [...purchaseOrderKeys.all, "by-number", poNumber] as const,
  shipments: (poId: string) => [...purchaseOrderKeys.purchaseOrder(poId), "shipments"] as const,
} as const;

// Item-related query keys
export const itemKeys = {
  all: ["item"] as const,
  item: (itemId: string) => [...itemKeys.all, itemId] as const,
  search: (query: string) => [...itemKeys.all, "search", query] as const,
  autocomplete: (query: string) => [...itemKeys.all, "autocomplete", query] as const,
} as const;

// Location-related query keys
export const locationKeys = {
  all: ["location"] as const,
  location: (locationId: string) => [...locationKeys.all, locationId] as const,
  search: (query: string) => [...locationKeys.all, "search", query] as const,
  autocomplete: (query: string) => [...locationKeys.all, "autocomplete", query] as const,
} as const;

// Picking-related query keys
export const pickingKeys = {
  all: ["picking"] as const,
  picklist: (picklistId: string) => [...pickingKeys.all, "picklist", picklistId] as const,
  destination: (destinationId: string) => [...pickingKeys.all, "destination", destinationId] as const,
  pallets: (destinationId: string) => [...pickingKeys.destination(destinationId), "pallets"] as const,
} as const;

// Audit log related query keys
export const auditKeys = {
  all: ["audit"] as const,
  logs: (filters?: Record<string, unknown>) => [...auditKeys.all, "logs", filters] as const,
  pallet: (palletId: string) => [...auditKeys.all, "pallet", palletId] as const,
  warehouse: (warehouseId: string) => [...auditKeys.all, "warehouse", warehouseId] as const,
} as const;

// Batch operations query keys
export const batchKeys = {
  all: ["batch"] as const,
  validation: (type: string, ids: string[]) => [...batchKeys.all, "validation", type, ids] as const,
  warehouseValidation: (warehouseIds: string[], userId: string) => 
    [...batchKeys.validation("warehouse", warehouseIds), userId] as const,
} as const;

// Query key utilities
export const queryKeyUtils = {
  /**
   * Get all query keys for a specific warehouse
   */
  getWarehouseKeys: (warehouseId: string) => warehouseKeys.warehouse(warehouseId),
  
  /**
   * Get all query keys for a specific user
   */
  getUserKeys: (userId: string) => userKeys.user(userId),
  
  /**
   * Get all data-related keys for a warehouse
   */
  getWarehouseDataKeys: (warehouseId: string) => warehouseKeys.data(warehouseId),
  
  /**
   * Get all operation-related keys for a warehouse
   */
  getWarehouseOperationKeys: (warehouseId: string) => warehouseKeys.operations(warehouseId),
  
  /**
   * Invalidate all warehouse-related queries
   */
  invalidateWarehouse: (queryClient: any, warehouseId: string) => {
    queryClient.invalidateQueries({ queryKey: warehouseKeys.warehouse(warehouseId) });
  },
  
  /**
   * Invalidate all user-related queries
   */
  invalidateUser: (queryClient: any, userId: string) => {
    queryClient.invalidateQueries({ queryKey: userKeys.user(userId) });
  },
  
  /**
   * Remove all warehouse-related queries from cache
   */
  removeWarehouse: (queryClient: any, warehouseId: string) => {
    queryClient.removeQueries({ queryKey: warehouseKeys.warehouse(warehouseId) });
  },
  
  /**
   * Remove all user-related queries from cache
   */
  removeUser: (queryClient: any, userId: string) => {
    queryClient.removeQueries({ queryKey: userKeys.user(userId) });
  },
  
  /**
   * Prefetch common warehouse data
   */
  prefetchWarehouseData: async (queryClient: any, warehouseId: string, token: string) => {
    const prefetchPromises = [
      queryClient.prefetchQuery({
        queryKey: warehouseKeys.locations(warehouseId),
        queryFn: () => fetch(`/api/warehouses/${warehouseId}/locations`, {
          headers: { Authorization: `Bearer ${token}` }
        }).then(res => res.json()),
        staleTime: 5 * 60 * 1000, // 5 minutes
      }),
      queryClient.prefetchQuery({
        queryKey: warehouseKeys.items(warehouseId),
        queryFn: () => fetch(`/api/warehouses/${warehouseId}/items`, {
          headers: { Authorization: `Bearer ${token}` }
        }).then(res => res.json()),
        staleTime: 5 * 60 * 1000, // 5 minutes
      }),
    ];
    
    await Promise.allSettled(prefetchPromises);
  },
} as const;

// Export all query key factories
// export {
//   userKeys,
//   warehouseKeys,
//   shipmentKeys,
//   purchaseOrderKeys,
//   itemKeys,
//   locationKeys,
//   pickingKeys,
//   auditKeys,
//   batchKeys,
// };

// Type exports for better TypeScript support
export type UserQueryKeys = typeof userKeys;
export type WarehouseQueryKeys = typeof warehouseKeys;
export type ShipmentQueryKeys = typeof shipmentKeys;
export type PurchaseOrderQueryKeys = typeof purchaseOrderKeys;
export type ItemQueryKeys = typeof itemKeys;
export type LocationQueryKeys = typeof locationKeys;
export type PickingQueryKeys = typeof pickingKeys;
export type AuditQueryKeys = typeof auditKeys;
export type BatchQueryKeys = typeof batchKeys;
