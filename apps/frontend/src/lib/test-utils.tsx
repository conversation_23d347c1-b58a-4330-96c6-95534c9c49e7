import React, { ReactElement } from "react";
import { render, RenderOptions } from "@testing-library/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AuthContext } from "@/components/providers/auth-provider";
import { SupabaseClient } from "@supabase/supabase-js";
import { vi } from "vitest";
import { Role } from "@quildora/types";

// Create a new QueryClient instance for each test to ensure isolation
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false, // Disable retries for faster tests
      },
      mutations: {
        retry: false, // Disable retries for mutations as well
      },
    },
  });

// Mock AuthContext value
const mockAuthContextValue = {
  supabase: {} as SupabaseClient, // Mock Supabase client as needed
  supabaseSession: null,
  supabaseUser: null,
  appUser: {
    id: "user-id-123",
    email: "<EMAIL>",
    role: Role.TENANT_ADMIN,
    name: "Test User",
    authUserId: "supabase-id-123",
    tenantId: "tenant-id-123",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  appToken: "mock-app-token",
  isLoading: false,
  onboardingStatus: "complete",

  // New onboarding properties
  isOnboarding: false,
  onboardingStep: null,
  onboardingData: null,

  // Enhanced methods
  startBusinessOnboarding: vi.fn(),
  joinTenantWithInvitation: vi.fn(),
  completeOnboarding: vi.fn(),

  // Existing methods
  loginWithSupabaseToken: vi.fn(),
  logout: vi.fn(),
};

const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const testQueryClient = createTestQueryClient();
  return (
    <QueryClientProvider client={testQueryClient}>
      <AuthContext.Provider value={mockAuthContextValue}>
        {children}
      </AuthContext.Provider>
    </QueryClientProvider>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, "wrapper">
) => render(ui, { wrapper: AllTheProviders, ...options });

// Re-export everything from testing-library
export * from "@testing-library/react";

// Override the render method
export { customRender as render };
