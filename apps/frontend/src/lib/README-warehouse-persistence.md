# Warehouse State Persistence System

A comprehensive system for managing warehouse selection persistence across browser sessions with user preferences, validation, and error handling.

## Overview

The warehouse persistence system provides:
- **Configurable storage options** (localStorage vs sessionStorage)
- **User preferences** for auto-selection and persistence behavior
- **Validation and error handling** for storage operations
- **Session management** and restoration utilities
- **Migration support** for legacy storage formats

## Core Components

### WarehousePersistence Class
Singleton class that manages all warehouse persistence operations.

```typescript
import { warehousePersistence } from '@/lib/warehouse-persistence';

// Get saved warehouse ID
const savedId = warehousePersistence.getSavedWarehouseId();

// Save warehouse selection
warehousePersistence.saveWarehouseSelection(warehouse);

// Find saved warehouse in available list
const savedWarehouse = warehousePersistence.findSavedWarehouse(warehouses);

// Get default warehouse based on preferences
const defaultWarehouse = warehousePersistence.getDefaultWarehouse(warehouses);
```

### Warehouse Preferences
User-configurable preferences for persistence behavior.

```typescript
interface WarehousePreferences {
  lastSelectedWarehouseId: string | null;
  autoSelectLastWarehouse: boolean;      // Auto-select on login
  rememberWarehouseSelection: boolean;   // Enable persistence
  sessionOnly: boolean;                  // Use sessionStorage vs localStorage
}
```

### Storage Keys
Organized storage key management:

```typescript
const STORAGE_KEYS = {
  CURRENT_WAREHOUSE: 'quildora-current-warehouse',
  WAREHOUSE_PREFERENCES: 'quildora-warehouse-preferences',
  SESSION_WAREHOUSE: 'quildora-session-warehouse',
};
```

## Usage Examples

### Basic Persistence
```typescript
import { saveWarehouseSelection, getSavedWarehouseId } from '@/lib/warehouse-persistence';

// Save warehouse selection
saveWarehouseSelection(selectedWarehouse);

// Retrieve saved warehouse ID
const savedId = getSavedWarehouseId();
```

### Preference Management
```typescript
import { getWarehousePreferences, saveWarehousePreferences } from '@/lib/warehouse-persistence';

// Get current preferences
const preferences = getWarehousePreferences();

// Update preferences
saveWarehousePreferences({
  autoSelectLastWarehouse: true,
  sessionOnly: false,
});
```

### Warehouse Provider Integration
The warehouse provider automatically uses the persistence system:

```typescript
// Enhanced warehouse provider with persistence
export function WarehouseProvider({ children }) {
  // Initialization
  useEffect(() => {
    warehousePersistence.migrateOldStorage();
  }, []);

  // Auto-select warehouse based on preferences
  const defaultWarehouse = getDefaultWarehouse(warehouses);
  
  // Save selections automatically
  const setCurrentWarehouse = useCallback((warehouse) => {
    setCurrentWarehouseState(warehouse);
    saveWarehouseSelection(warehouse);
  }, []);
}
```

## React Components

### WarehousePreferences Component
User interface for configuring persistence preferences.

```typescript
import { WarehousePreferences } from '@/components/warehouses/WarehousePreferences';

<WarehousePreferences className="max-w-2xl" />
```

**Features:**
- Toggle warehouse selection persistence
- Configure auto-selection behavior
- Choose storage type (persistent vs session)
- Reset to defaults
- Real-time preference validation

### WarehouseRestoration Component
Interface for managing warehouse session and restoration.

```typescript
import { WarehouseRestoration } from '@/components/warehouses/WarehouseRestoration';

<WarehouseRestoration 
  autoRestore={true}
  showSessionInfo={true}
  className="max-w-md"
/>
```

**Features:**
- Display current warehouse status
- Show session duration and statistics
- Provide restoration suggestions
- Manual restore and clear actions
- Auto-restoration capabilities

## Hooks

### useWarehouseSession Hook
Comprehensive session management utilities.

```typescript
import { useWarehouseSession } from '@/hooks/useWarehouseSession';

function MyComponent() {
  const {
    getSessionStats,
    validateWarehouseSelection,
    restoreWarehouseSelection,
    switchToWarehouse,
    getWarehouseSuggestions,
  } = useWarehouseSession();

  // Get comprehensive session information
  const stats = getSessionStats();
  
  // Validate current selection
  const validation = validateWarehouseSelection();
  
  // Restore from persistence
  const restored = restoreWarehouseSelection();
  
  // Get smart suggestions
  const suggestions = getWarehouseSuggestions();
}
```

## Storage Strategy

### Persistent Storage (Default)
- Uses `localStorage` for cross-session persistence
- Survives browser restarts and tab closures
- Ideal for regular users who want consistent experience

### Session Storage
- Uses `sessionStorage` for session-only persistence
- Cleared when browser tab is closed
- Ideal for shared computers or privacy-conscious users

### Storage Validation
- Automatic fallback when storage is unavailable
- Error handling for quota exceeded scenarios
- Graceful degradation when storage is disabled

## Error Handling

### Storage Availability
```typescript
// Automatic storage availability checking
private isStorageAvailable(type: 'localStorage' | 'sessionStorage'): boolean {
  try {
    const storage = window[type];
    const test = '__storage_test__';
    storage.setItem(test, test);
    storage.removeItem(test);
    return true;
  } catch {
    return false;
  }
}
```

### Validation and Cleanup
```typescript
// Validate saved warehouse against available warehouses
findSavedWarehouse(availableWarehouses: WarehouseBasic[]): WarehouseBasic | null {
  const savedWarehouseId = this.getSavedWarehouseId();
  
  if (!savedWarehouseId || availableWarehouses.length === 0) {
    return null;
  }

  const savedWarehouse = availableWarehouses.find(w => w.id === savedWarehouseId);
  
  if (!savedWarehouse) {
    // Clean up invalid saved warehouse
    this.clearSavedWarehouse();
    return null;
  }

  return savedWarehouse;
}
```

## Migration Support

### Legacy Storage Migration
```typescript
// Migrate from old storage formats
migrateOldStorage(): void {
  // Ensures preferences exist and migrates old formats
  const preferences = this.getPreferences();
  if (preferences === DEFAULT_PREFERENCES) {
    this.savePreferences(DEFAULT_PREFERENCES);
  }
}
```

## Security and Privacy

### Local Storage Only
- All data stored locally in user's browser
- No server-side storage of warehouse preferences
- User has full control over their data

### Data Cleanup
- Automatic cleanup on user logout
- Manual clear options available
- No persistent tracking across sessions (when session-only mode enabled)

### Privacy Notice
Components include privacy notices explaining data storage:
> "Warehouse preferences are stored locally in your browser and are not shared with other devices or users."

## Integration with Existing Systems

### Warehouse Provider
The persistence system integrates seamlessly with the existing warehouse provider:

```typescript
// In WarehouseProvider
const loadWarehouses = useCallback(async () => {
  // ... load warehouses from API
  
  // Auto-select based on persistence preferences
  if (!currentWarehouse && warehouses.length > 0) {
    const defaultWarehouse = getDefaultWarehouse(warehouses);
    if (defaultWarehouse) {
      setCurrentWarehouseState(defaultWarehouse);
    }
  }
}, []);
```

### Navigation Components
Navigation components automatically respect warehouse persistence:

```typescript
// PrimaryNav shows warnings when warehouse required but not selected
const showWarehouseWarning = !isLoadingWarehouses && !currentWarehouse;
```

## Best Practices

### Initialization
Always initialize the persistence system early in the app lifecycle:

```typescript
useEffect(() => {
  warehousePersistence.migrateOldStorage();
}, []);
```

### Error Handling
Always handle storage errors gracefully:

```typescript
try {
  saveWarehouseSelection(warehouse);
} catch (error) {
  console.error('Failed to save warehouse selection:', error);
  // Continue without persistence
}
```

### User Control
Provide users with clear controls over their persistence preferences:

```typescript
<WarehousePreferences />
<WarehouseRestoration />
```

### Validation
Always validate saved warehouses against current access:

```typescript
const validation = validateWarehouseSelection();
if (!validation.isValid) {
  // Handle invalid selection
}
```
