/**
 * Optimized React Query configurations for different data types
 * Prevents unnecessary re-renders and improves performance
 */

// Base configuration for all queries
const baseQueryConfig = {
  refetchOnWindowFocus: false,
  refetchOnMount: false,
  refetchOnReconnect: true,
  refetchInterval: false,
  refetchIntervalInBackground: false,
  retry: 2,
} as const;

// Configuration for different data types based on their characteristics
export const queryConfigs = {
  // Static/rarely changing data (warehouses, locations, items)
  static: {
    ...baseQueryConfig,
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 3, // More retries for critical data
  },

  // Dynamic data that changes frequently (pallets, shipments)
  dynamic: {
    ...baseQueryConfig,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  },

  // Real-time data (audit logs, status updates)
  realtime: {
    ...baseQueryConfig,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  },

  // Search/autocomplete data
  search: {
    ...baseQueryConfig,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 1,
    refetchOnMount: false, // Important for search to prevent re-fetching
  },

  // User session data
  session: {
    ...baseQueryConfig,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
    retry: 3,
  },

  // Background data (less critical)
  background: {
    ...baseQueryConfig,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 20 * 60 * 1000, // 20 minutes
    retry: 1,
  },
} as const;

// Helper function to get optimized config for specific data types
export function getQueryConfig(dataType: keyof typeof queryConfigs) {
  return queryConfigs[dataType];
}

// Specific configurations for warehouse data
export const warehouseQueryConfigs = {
  // Warehouse list (rarely changes)
  warehouses: queryConfigs.static,

  // Locations (change infrequently)
  locations: queryConfigs.static,

  // Items (change infrequently)
  items: queryConfigs.static,

  // Pallets (change frequently)
  pallets: queryConfigs.dynamic,

  // Individual pallet (may change during operations)
  pallet: queryConfigs.dynamic,

  // Shipments (change during receiving/shipping)
  shipments: queryConfigs.dynamic,

  // Purchase orders (change during receiving)
  purchaseOrders: queryConfigs.dynamic,

  // Audit logs (real-time)
  auditLogs: queryConfigs.realtime,

  // Destination search
  destinations: queryConfigs.search,

  // User warehouse access
  userWarehouses: queryConfigs.session,
} as const;

// Helper to get warehouse-specific query config
export function getWarehouseQueryConfig(
  queryType: keyof typeof warehouseQueryConfigs
) {
  return warehouseQueryConfigs[queryType];
}

// Performance monitoring helpers
export const performanceConfig = {
  // Enable performance monitoring in development
  enableDevtools: process.env.NODE_ENV === "development",

  // Log slow queries (over 2 seconds)
  slowQueryThreshold: 2000,

  // Maximum cache size (number of queries)
  maxCacheSize: 100,
} as const;
