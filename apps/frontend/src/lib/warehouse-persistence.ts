/**
 * Warehouse state persistence utilities
 * Handles localStorage/sessionStorage for warehouse selection with validation and error handling
 */

import { WarehouseBasic } from "@quildora/types";

// Storage keys
const STORAGE_KEYS = {
  CURRENT_WAREHOUSE: "quildora-current-warehouse",
  WAREHOUSE_PREFERENCES: "quildora-warehouse-preferences",
  SESSION_WAREHOUSE: "quildora-session-warehouse",
} as const;

// Warehouse preferences interface
interface WarehousePreferences {
  lastSelectedWarehouseId: string | null;
  autoSelectLastWarehouse: boolean;
  rememberWarehouseSelection: boolean;
  sessionOnly: boolean;
}

// Default preferences
const DEFAULT_PREFERENCES: WarehousePreferences = {
  lastSelectedWarehouseId: null,
  autoSelectLastWarehouse: true,
  rememberWarehouseSelection: true,
  sessionOnly: false,
};

/**
 * Warehouse persistence manager
 */
export class WarehousePersistence {
  private static instance: WarehousePersistence;

  static getInstance(): WarehousePersistence {
    if (!WarehousePersistence.instance) {
      WarehousePersistence.instance = new WarehousePersistence();
    }
    return WarehousePersistence.instance;
  }

  /**
   * Check if storage is available
   */
  private isStorageAvailable(type: "localStorage" | "sessionStorage"): boolean {
    try {
      const storage = window[type];
      const test = "__storage_test__";
      storage.setItem(test, test);
      storage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get warehouse preferences
   */
  getPreferences(): WarehousePreferences {
    if (!this.isStorageAvailable("localStorage")) {
      return DEFAULT_PREFERENCES;
    }

    try {
      const stored = localStorage.getItem(STORAGE_KEYS.WAREHOUSE_PREFERENCES);
      if (stored) {
        const parsed = JSON.parse(stored);
        return { ...DEFAULT_PREFERENCES, ...parsed };
      }
    } catch (error) {
      console.warn("Failed to parse warehouse preferences:", error);
    }

    return DEFAULT_PREFERENCES;
  }

  /**
   * Save warehouse preferences
   */
  savePreferences(preferences: Partial<WarehousePreferences>): void {
    if (!this.isStorageAvailable("localStorage")) {
      console.warn("localStorage not available, preferences not saved");
      return;
    }

    try {
      const current = this.getPreferences();
      const updated = { ...current, ...preferences };
      localStorage.setItem(
        STORAGE_KEYS.WAREHOUSE_PREFERENCES,
        JSON.stringify(updated)
      );
    } catch (error) {
      console.error("Failed to save warehouse preferences:", error);
    }
  }

  /**
   * Get saved warehouse ID
   */
  getSavedWarehouseId(): string | null {
    const preferences = this.getPreferences();

    if (!preferences.rememberWarehouseSelection) {
      return null;
    }

    // Check session storage first if session-only mode
    if (preferences.sessionOnly && this.isStorageAvailable("sessionStorage")) {
      try {
        return sessionStorage.getItem(STORAGE_KEYS.SESSION_WAREHOUSE);
      } catch (error) {
        console.warn("Failed to read from sessionStorage:", error);
      }
    }

    // Check localStorage
    if (this.isStorageAvailable("localStorage")) {
      try {
        return localStorage.getItem(STORAGE_KEYS.CURRENT_WAREHOUSE);
      } catch (error) {
        console.warn("Failed to read from localStorage:", error);
      }
    }

    return null;
  }

  /**
   * Save warehouse selection
   */
  saveWarehouseSelection(warehouse: WarehouseBasic | null): void {
    const preferences = this.getPreferences();

    if (!preferences.rememberWarehouseSelection) {
      return;
    }

    const warehouseId = warehouse?.id || null;

    // Update preferences with last selected warehouse
    this.savePreferences({
      lastSelectedWarehouseId: warehouseId,
    });

    // Save to appropriate storage
    if (preferences.sessionOnly && this.isStorageAvailable("sessionStorage")) {
      try {
        if (warehouseId) {
          sessionStorage.setItem(STORAGE_KEYS.SESSION_WAREHOUSE, warehouseId);
        } else {
          sessionStorage.removeItem(STORAGE_KEYS.SESSION_WAREHOUSE);
        }
      } catch (error) {
        console.error("Failed to save to sessionStorage:", error);
      }
    } else if (this.isStorageAvailable("localStorage")) {
      try {
        if (warehouseId) {
          localStorage.setItem(STORAGE_KEYS.CURRENT_WAREHOUSE, warehouseId);
        } else {
          localStorage.removeItem(STORAGE_KEYS.CURRENT_WAREHOUSE);
        }
      } catch (error) {
        console.error("Failed to save to localStorage:", error);
      }
    }
  }

  /**
   * Find and validate saved warehouse from available warehouses
   */
  findSavedWarehouse(
    availableWarehouses: WarehouseBasic[]
  ): WarehouseBasic | null {
    const savedWarehouseId = this.getSavedWarehouseId();

    if (!savedWarehouseId || availableWarehouses.length === 0) {
      return null;
    }

    // Find the saved warehouse in available warehouses
    const savedWarehouse = availableWarehouses.find(
      (w) => w.id === savedWarehouseId
    );

    if (!savedWarehouse) {
      console.warn(
        `Saved warehouse ${savedWarehouseId} not found in available warehouses`
      );
      // Clean up invalid saved warehouse
      this.clearSavedWarehouse();
      return null;
    }

    return savedWarehouse;
  }

  /**
   * Get default warehouse selection based on preferences
   */
  getDefaultWarehouse(
    availableWarehouses: WarehouseBasic[]
  ): WarehouseBasic | null {
    if (availableWarehouses.length === 0) {
      return null;
    }

    const preferences = this.getPreferences();

    // Try to restore saved warehouse if auto-select is enabled
    if (preferences.autoSelectLastWarehouse) {
      const savedWarehouse = this.findSavedWarehouse(availableWarehouses);
      if (savedWarehouse) {
        return savedWarehouse;
      }
    }

    // Default to first available warehouse
    return availableWarehouses[0];
  }

  /**
   * Clear saved warehouse data
   */
  clearSavedWarehouse(): void {
    if (this.isStorageAvailable("localStorage")) {
      try {
        localStorage.removeItem(STORAGE_KEYS.CURRENT_WAREHOUSE);
      } catch (error) {
        console.error("Failed to clear localStorage:", error);
      }
    }

    if (this.isStorageAvailable("sessionStorage")) {
      try {
        sessionStorage.removeItem(STORAGE_KEYS.SESSION_WAREHOUSE);
      } catch (error) {
        console.error("Failed to clear sessionStorage:", error);
      }
    }
  }

  /**
   * Clear all warehouse-related data (for logout)
   */
  clearAllWarehouseData(): void {
    this.clearSavedWarehouse();

    if (this.isStorageAvailable("localStorage")) {
      try {
        localStorage.removeItem(STORAGE_KEYS.WAREHOUSE_PREFERENCES);
      } catch (error) {
        console.error("Failed to clear warehouse preferences:", error);
      }
    }
  }

  /**
   * Migrate old storage format if needed
   */
  migrateOldStorage(): void {
    // This can be used to migrate from old storage formats
    // Currently just ensures preferences exist
    const preferences = this.getPreferences();
    if (preferences === DEFAULT_PREFERENCES) {
      this.savePreferences(DEFAULT_PREFERENCES);
    }
  }
}

// Export singleton instance
export const warehousePersistence = WarehousePersistence.getInstance();

// Export convenience functions
export const getWarehousePreferences = () =>
  warehousePersistence.getPreferences();
export const saveWarehousePreferences = (
  preferences: Partial<WarehousePreferences>
) => warehousePersistence.savePreferences(preferences);
export const getSavedWarehouseId = () =>
  warehousePersistence.getSavedWarehouseId();
export const saveWarehouseSelection = (warehouse: WarehouseBasic | null) =>
  warehousePersistence.saveWarehouseSelection(warehouse);
export const findSavedWarehouse = (warehouses: WarehouseBasic[]) =>
  warehousePersistence.findSavedWarehouse(warehouses);
export const getDefaultWarehouse = (warehouses: WarehouseBasic[]) =>
  warehousePersistence.getDefaultWarehouse(warehouses);
export const clearWarehouseData = () =>
  warehousePersistence.clearAllWarehouseData();

// Export types
export type { WarehousePreferences };
