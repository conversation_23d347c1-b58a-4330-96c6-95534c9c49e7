/**
 * Warehouse validation cache utilities
 * Handles caching of warehouse validation results to avoid redundant API calls
 */

import { Role } from "@quildora/types";

// Cache keys
const CACHE_KEYS = {
  WAREHOUSE_VALIDATION: "quildora-warehouse-validation",
  VALIDATION_TIMESTAMP: "quildora-validation-timestamp",
} as const;

// Cache duration (1 minute)
const VALIDATION_CACHE_DURATION = 60 * 1000; // 1 minute in milliseconds

// Warehouse validation result interface
interface WarehouseValidationResult {
  warehouseId: string;
  userId: string;
  hasAccess: boolean;
  userRole: Role | null;
  isManager: boolean;
  isAdmin: boolean;
  timestamp: number;
}

// Validation cache interface
interface ValidationCache {
  [key: string]: WarehouseValidationResult;
}

class WarehouseValidationCache {
  private isStorageAvailable(type: "localStorage" | "sessionStorage"): boolean {
    try {
      const storage = window[type];
      const test = "__storage_test__";
      storage.setItem(test, test);
      storage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Generate cache key for warehouse validation
   */
  private getCacheKey(warehouseId: string, userId: string): string {
    return `${warehouseId}-${userId}`;
  }

  /**
   * Get validation cache from sessionStorage
   */
  private getValidationCache(): ValidationCache {
    if (!this.isStorageAvailable("sessionStorage")) {
      return {};
    }

    try {
      const cached = sessionStorage.getItem(CACHE_KEYS.WAREHOUSE_VALIDATION);
      return cached ? JSON.parse(cached) : {};
    } catch (error) {
      console.warn("Failed to parse warehouse validation cache:", error);
      return {};
    }
  }

  /**
   * Save validation cache to sessionStorage
   */
  private saveValidationCache(cache: ValidationCache): void {
    if (!this.isStorageAvailable("sessionStorage")) {
      return;
    }

    try {
      sessionStorage.setItem(
        CACHE_KEYS.WAREHOUSE_VALIDATION,
        JSON.stringify(cache)
      );
    } catch (error) {
      console.warn("Failed to save warehouse validation cache:", error);
    }
  }

  /**
   * Check if validation result is still valid (within cache duration)
   */
  private isValidationFresh(timestamp: number): boolean {
    return Date.now() - timestamp < VALIDATION_CACHE_DURATION;
  }

  /**
   * Get cached validation result if available and fresh
   */
  getCachedValidation(
    warehouseId: string,
    userId: string
  ): WarehouseValidationResult | null {
    const cache = this.getValidationCache();
    const cacheKey = this.getCacheKey(warehouseId, userId);
    const cached = cache[cacheKey];

    if (cached && this.isValidationFresh(cached.timestamp)) {
      return cached;
    }

    return null;
  }

  /**
   * Cache warehouse validation result
   */
  cacheValidation(
    warehouseId: string,
    userId: string,
    hasAccess: boolean,
    userRole: Role | null,
    isManager: boolean,
    isAdmin: boolean
  ): void {
    const cache = this.getValidationCache();
    const cacheKey = this.getCacheKey(warehouseId, userId);

    const validationResult: WarehouseValidationResult = {
      warehouseId,
      userId,
      hasAccess,
      userRole,
      isManager,
      isAdmin,
      timestamp: Date.now(),
    };

    cache[cacheKey] = validationResult;
    this.saveValidationCache(cache);
  }

  /**
   * Clear validation cache for a specific warehouse and user
   */
  clearValidation(warehouseId: string, userId: string): void {
    const cache = this.getValidationCache();
    const cacheKey = this.getCacheKey(warehouseId, userId);
    delete cache[cacheKey];
    this.saveValidationCache(cache);
  }

  /**
   * Clear all validation cache
   */
  clearAllValidation(): void {
    if (this.isStorageAvailable("sessionStorage")) {
      sessionStorage.removeItem(CACHE_KEYS.WAREHOUSE_VALIDATION);
    }
  }

  /**
   * Clean up expired validation entries
   */
  cleanupExpiredValidation(): void {
    const cache = this.getValidationCache();
    const now = Date.now();
    let hasChanges = false;

    for (const [key, validation] of Object.entries(cache)) {
      if (!this.isValidationFresh(validation.timestamp)) {
        delete cache[key];
        hasChanges = true;
      }
    }

    if (hasChanges) {
      this.saveValidationCache(cache);
    }
  }

  /**
   * Get validation statistics for debugging
   */
  getValidationStats(): {
    totalEntries: number;
    freshEntries: number;
    expiredEntries: number;
  } {
    const cache = this.getValidationCache();
    const entries = Object.values(cache);
    const freshEntries = entries.filter((v) =>
      this.isValidationFresh(v.timestamp)
    );

    return {
      totalEntries: entries.length,
      freshEntries: freshEntries.length,
      expiredEntries: entries.length - freshEntries.length,
    };
  }
}

// Export singleton instance
export const warehouseValidationCache = new WarehouseValidationCache();

// Export convenience functions
export const getCachedWarehouseValidation = (
  warehouseId: string,
  userId: string
) => warehouseValidationCache.getCachedValidation(warehouseId, userId);

export const cacheWarehouseValidation = (
  warehouseId: string,
  userId: string,
  hasAccess: boolean,
  userRole: Role | null,
  isManager: boolean,
  isAdmin: boolean
) =>
  warehouseValidationCache.cacheValidation(
    warehouseId,
    userId,
    hasAccess,
    userRole,
    isManager,
    isAdmin
  );

export const clearWarehouseValidationCache = (
  warehouseId: string,
  userId: string
) => warehouseValidationCache.clearValidation(warehouseId, userId);

export const clearAllWarehouseValidationCache = () =>
  warehouseValidationCache.clearAllValidation();

export const cleanupExpiredWarehouseValidation = () =>
  warehouseValidationCache.cleanupExpiredValidation();
