interface FetchOptions extends RequestInit {
  token?: string | null;
}

/**
 * Custom fetch wrapper to automatically include the Authorization header
 * if a token is provided. Automatically prepends the API base URL for relative paths.
 */
export async function fetchWithAuth(
  endpoint: string,
  options: FetchOptions = {}
) {
  const { token, ...fetchOptions } = options;
  const headers = new Headers(fetchOptions.headers || {});

  if (token) {
    headers.append("Authorization", `Bearer ${token}`);
  }

  // If a body exists and Content-Type is not already set, assume JSON.
  if (fetchOptions.body && !headers.has("Content-Type")) {
    headers.append("Content-Type", "application/json");
  }

  // Automatically prepend base URL for relative paths
  const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
  const fullUrl = endpoint.startsWith("http")
    ? endpoint
    : `${baseUrl}${endpoint}`;

  const response = await fetch(fullUrl, {
    ...fetchOptions,
    headers,
  });

  if (!response.ok) {
    let errorToThrow;
    try {
      const errorData = await response.json();
      errorToThrow = new Error(
        errorData.message ||
          `API Error: ${response.status} ${response.statusText}`
      );
    } catch (jsonParseError) {
      // This catch is specifically for errors during response.json()
      console.error(
        "fetchWithAuth failed to parse error JSON:",
        jsonParseError
      );
      errorToThrow = new Error(
        `API Error: ${response.status} ${response.statusText} (Could not parse error details)`
      );
    }
    throw errorToThrow; // Throw the determined error
  }

  // Handle 204 No Content specifically
  if (response.status === 204) {
    return null; // Or an empty object, or a specific success indicator
  }

  // Attempt to parse JSON for other successful responses
  try {
    return await response.json();
  } catch (e) {
    console.error("Failed to parse API JSON response:", e);
    throw new Error("Received invalid data format from API.");
  }
}

// API function to fetch shipments with pagination and filtering
export async function fetchShipments(
  appToken: string | null,
  params: {
    page?: number;
    limit?: number;
    status?: string;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  } = {}
) {
  if (!appToken) {
    throw new Error("Authentication token not available.");
  }

  const searchParams = new URLSearchParams();
  if (params.page) searchParams.append("page", params.page.toString());
  if (params.limit) searchParams.append("limit", params.limit.toString());
  if (params.status) searchParams.append("status", params.status);
  if (params.sortBy) searchParams.append("sortBy", params.sortBy);
  if (params.sortOrder) searchParams.append("sortOrder", params.sortOrder);

  const url = `/api/shipments${
    searchParams.toString() ? `?${searchParams.toString()}` : ""
  }`;

  return fetchWithAuth(url, { token: appToken });
}

// API Functions
import { AuditLog } from "@quildora/types";

/**
 * Fetch audit logs for a specific pallet
 */
export async function fetchPalletAuditLogs(
  palletId: string,
  warehouseId: string,
  token: string | null
): Promise<AuditLog[]> {
  if (!token) {
    throw new Error("Authentication token is required");
  }

  return fetchWithAuth(
    `/api/pallets/${palletId}/audit-logs?warehouseId=${warehouseId}`,
    { token }
  );
}

// Example of how a component would use this:
/*
import { useAuth } from '@/components/providers/auth-provider';
import { fetchWithAuth } from '@/lib/api';

function MyComponent() {
  const { appToken } = useAuth();

  const fetchData = async () => {
    try {
      const data = await fetchWithAuth('/api/protected-resource', { token: appToken });
      console.log(data);
    } catch (error) {
      console.error(error);
    }
  };
  // ...
}
*/
