import { describe, it, expect, vi, beforeEach } from "vitest";
import {
  OnboardingErrorRecovery,
  OnboardingErrorType,
  OnboardingError,
} from "../onboarding-error-recovery";

describe("OnboardingErrorRecovery", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Clear any stored errors
    (OnboardingErrorRecovery as any).errors = [];
  });

  describe("logError", () => {
    it("should log an error", () => {
      const error: OnboardingError = {
        type: OnboardingErrorType.NETWORK_ERROR,
        message: "Network error occurred",
        step: "business_info",
        retryable: true,
        timestamp: Date.now(),
      };

      OnboardingErrorRecovery.logError(error);

      expect((OnboardingErrorRecovery as any).errors).toHaveLength(1);
      expect((OnboardingErrorRecovery as any).errors[0]).toEqual(error);
    });

    it("should limit the number of stored errors", () => {
      // Add max errors + 1
      const maxErrors = (OnboardingErrorRecovery as any).MAX_STORED_ERRORS;

      for (let i = 0; i < maxErrors + 5; i++) {
        OnboardingErrorRecovery.logError({
          type: OnboardingErrorType.NETWORK_ERROR,
          message: `Error ${i}`,
          step: "business_info",
          retryable: true,
          timestamp: Date.now() + i,
        });
      }

      expect((OnboardingErrorRecovery as any).errors).toHaveLength(maxErrors);
      // Should keep the most recent errors
      expect((OnboardingErrorRecovery as any).errors[0].message).toBe(
        `Error ${5}`
      );
    });
  });

  describe("getRecoveryActions", () => {
    it("should return retry action for network errors", () => {
      const error: OnboardingError = {
        type: OnboardingErrorType.NETWORK_ERROR,
        message: "Network error occurred",
        step: "business_info",
        retryable: true,
        timestamp: Date.now(),
      };

      const mockContext = {
        retryCallback: vi.fn(),
        reloadCallback: vi.fn(),
        restartCallback: vi.fn(),
      };

      const actions = OnboardingErrorRecovery.getRecoveryActions(
        error,
        mockContext
      );

      expect(actions).toHaveLength(2);
      expect(actions[0].strategy).toBe("RETRY");
      expect(actions[1].strategy).toBe("RELOAD");

      // Execute the actions
      actions[0].action();
      expect(mockContext.retryCallback).toHaveBeenCalled();

      actions[1].action();
      expect(mockContext.reloadCallback).toHaveBeenCalled();
    });

    it("should return restart action for session expired errors", () => {
      const error: OnboardingError = {
        type: OnboardingErrorType.SESSION_EXPIRED,
        message: "Session expired",
        step: "admin_account",
        retryable: false,
        timestamp: Date.now(),
      };

      const mockContext = {
        retryCallback: vi.fn(),
        reloadCallback: vi.fn(),
        restartCallback: vi.fn(),
      };

      const actions = OnboardingErrorRecovery.getRecoveryActions(
        error,
        mockContext
      );

      expect(actions).toHaveLength(1);
      expect(actions[0].strategy).toBe("RESTART");

      // Execute the action
      actions[0].action();
      expect(mockContext.restartCallback).toHaveBeenCalled();
    });

    it("should return appropriate actions for validation errors", () => {
      const error: OnboardingError = {
        type: OnboardingErrorType.VALIDATION_ERROR,
        message: "Invalid input",
        step: "warehouse_setup",
        field: "warehouseName",
        retryable: false,
        timestamp: Date.now(),
      };

      const mockContext = {
        retryCallback: vi.fn(),
        reloadCallback: vi.fn(),
        restartCallback: vi.fn(),
      };

      const actions = OnboardingErrorRecovery.getRecoveryActions(
        error,
        mockContext
      );

      expect(actions).toHaveLength(1);
      expect(actions[0].strategy).toBe(RecoveryStrategy.RETRY);
      expect(actions[0].description).toContain("retry");
    });

    it("should return default actions when no context provided", () => {
      const error: OnboardingError = {
        type: OnboardingErrorType.NETWORK_ERROR,
        message: "Network error occurred",
        step: "business_info",
        retryable: true,
        timestamp: Date.now(),
      };

      const actions = OnboardingErrorRecovery.getRecoveryActions(error, {});

      expect(actions).toHaveLength(2);
      expect(actions[0].strategy).toBe("RETRY");
      expect(actions[1].strategy).toBe("RELOAD");

      // Actions should be no-ops without context
      actions[0].action();
      actions[1].action();
    });
  });

  describe("getErrorSummary", () => {
    it("should return error summary", () => {
      // Log multiple errors
      OnboardingErrorRecovery.logError({
        type: OnboardingErrorType.NETWORK_ERROR,
        message: "Network error 1",
        step: "business_info",
        retryable: true,
        timestamp: Date.now() - 5000,
      });

      OnboardingErrorRecovery.logError({
        type: OnboardingErrorType.VALIDATION_ERROR,
        message: "Invalid input",
        step: "admin_account",
        field: "email",
        retryable: false,
        timestamp: Date.now() - 3000,
      });

      OnboardingErrorRecovery.logError({
        type: OnboardingErrorType.SERVER_ERROR,
        message: "Server error",
        step: "warehouse_setup",
        retryable: true,
        timestamp: Date.now() - 1000,
      });

      const logs = OnboardingErrorRecovery.getErrorLogs();

      expect(logs).toHaveLength(3);
      expect(
        logs.some((log) => log.type === OnboardingErrorType.NETWORK_ERROR)
      ).toBe(true);
      expect(
        logs.some((log) => log.type === OnboardingErrorType.VALIDATION_ERROR)
      ).toBe(true);
      expect(
        logs.some((log) => log.type === OnboardingErrorType.SERVER_ERROR)
      ).toBe(true);
      expect(logs.some((log) => log.step === "admin_account")).toBe(true);
      expect(logs.some((log) => log.step === "warehouse_setup")).toBe(true);
    });

    it("should return empty logs when no errors", () => {
      const logs = OnboardingErrorRecovery.getErrorLogs();

      expect(logs).toHaveLength(0);
    });
  });

  describe("clearErrors", () => {
    it("should clear all errors", () => {
      // Log some errors
      OnboardingErrorRecovery.logError({
        type: OnboardingErrorType.NETWORK_ERROR,
        message: "Network error",
        step: "business_info",
        retryable: true,
        timestamp: Date.now(),
      });

      OnboardingErrorRecovery.logError({
        type: OnboardingErrorType.VALIDATION_ERROR,
        message: "Invalid input",
        step: "admin_account",
        field: "email",
        retryable: false,
        timestamp: Date.now(),
      });

      expect((OnboardingErrorRecovery as any).errors).toHaveLength(2);

      OnboardingErrorRecovery.clearErrorLogs();

      expect((OnboardingErrorRecovery as any).errors).toHaveLength(0);
    });
  });

  describe("getErrorsByStep", () => {
    it("should return errors for a specific step", () => {
      // Log errors for different steps
      OnboardingErrorRecovery.logError({
        type: OnboardingErrorType.NETWORK_ERROR,
        message: "Network error 1",
        step: "business_info",
        retryable: true,
        timestamp: Date.now() - 5000,
      });

      OnboardingErrorRecovery.logError({
        type: OnboardingErrorType.VALIDATION_ERROR,
        message: "Invalid input",
        step: "admin_account",
        field: "email",
        retryable: false,
        timestamp: Date.now() - 3000,
      });

      OnboardingErrorRecovery.logError({
        type: OnboardingErrorType.SERVER_ERROR,
        message: "Server error",
        step: "business_info",
        retryable: true,
        timestamp: Date.now() - 1000,
      });

      const logs = OnboardingErrorRecovery.getErrorLogs();
      const businessInfoErrors = logs.filter(
        (log) => log.step === "business_info"
      );
      const adminAccountErrors = logs.filter(
        (log) => log.step === "admin_account"
      );
      const warehouseSetupErrors = logs.filter(
        (log) => log.step === "warehouse_setup"
      );

      expect(businessInfoErrors).toHaveLength(2);
      expect(adminAccountErrors).toHaveLength(1);
      expect(warehouseSetupErrors).toHaveLength(0);
    });
  });
});
