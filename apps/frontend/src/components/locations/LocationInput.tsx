"use client";

import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { Check, MapPin, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { cn } from "@/lib/utils";
import { useAuth } from "@/components/providers/auth-provider";
import { Location } from "@quildora/types";
import { useLocations } from "@/hooks/api";

interface LocationInputProps {
  value?: string;
  onLocationSelect: (locationId: string, location: Location) => void;
  placeholder?: string;
  label?: string;
  disabled?: boolean;
  className?: string;
  filterCategory?: "Storage" | "Receiving" | "Shipping";
}

function LocationInput({
  value,
  onLocationSelect,
  placeholder = "Select location...",
  label = "Location",
  disabled = false,
  className,
  filterCategory,
}: LocationInputProps) {
  const { appToken } = useAuth();
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(
    null
  );

  const { data: locations = [], isLoading } = useLocations({
    category: filterCategory as any, // Cast to match the expected type
  });

  // Find the selected location when value changes
  useEffect(() => {
    if (value && locations.length > 0) {
      const location = locations.find((loc) => loc.id === value);
      setSelectedLocation(location || null);
    } else {
      setSelectedLocation(null);
    }
  }, [value, locations]);

  const handleLocationSelect = useCallback(
    (location: Location) => {
      setSelectedLocation(location);
      onLocationSelect(location.id, location);
      setOpen(false);
      setSearchValue("");
    },
    [onLocationSelect]
  );

  const handleBarcodeInput = useCallback(
    (inputValue: string) => {
      // Check if the input matches a location barcode or name
      const matchedLocation = locations.find(
        (loc) => loc.name.toLowerCase() === inputValue.toLowerCase()
      );

      if (matchedLocation) {
        handleLocationSelect(matchedLocation);
      } else {
        setSearchValue(inputValue);
      }
    },
    [locations, handleLocationSelect]
  );

  const filteredLocations = useMemo(
    () =>
      locations.filter((location) =>
        location.name.toLowerCase().includes(searchValue.toLowerCase())
      ),
    [locations, searchValue]
  );

  return (
    <div className={cn("space-y-2", className)}>
      {label && <Label>{label}</Label>}
      <div className="flex space-x-2">
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              className="flex-1 justify-between"
              disabled={disabled}
            >
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4" />
                <span className="truncate">
                  {selectedLocation ? selectedLocation.name : placeholder}
                </span>
              </div>
              <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[400px] p-0">
            <Command>
              <CommandInput
                placeholder="Search locations or scan barcode..."
                value={searchValue}
                onValueChange={setSearchValue}
              />
              <CommandList>
                <CommandEmpty>
                  {isLoading ? "Loading locations..." : "No locations found."}
                </CommandEmpty>
                <CommandGroup>
                  {filteredLocations.map((location) => (
                    <CommandItem
                      key={location.id}
                      value={location.name}
                      onSelect={() => handleLocationSelect(location)}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          selectedLocation?.id === location.id
                            ? "opacity-100"
                            : "opacity-0"
                        )}
                      />
                      <div className="flex flex-col">
                        <span className="font-medium">{location.name}</span>
                        <span className="text-sm text-muted-foreground">
                          {location.category} • {location.warehouse?.name}
                        </span>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        {/* Direct barcode input */}
        <Input
          placeholder="Scan barcode"
          className="w-32"
          disabled={disabled}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              e.preventDefault();
              handleBarcodeInput(e.currentTarget.value);
              e.currentTarget.value = "";
            }
          }}
        />
      </div>

      {selectedLocation && (
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <Check className="h-4 w-4 text-green-600" />
          <span>
            Selected: {selectedLocation.name} ({selectedLocation.category})
          </span>
        </div>
      )}
    </div>
  );
}

// Memoize the component to prevent unnecessary re-renders
// Only re-render when props actually change
export const MemoizedLocationInput = React.memo(
  LocationInput,
  (prevProps, nextProps) => {
    return (
      prevProps.value === nextProps.value &&
      prevProps.placeholder === nextProps.placeholder &&
      prevProps.label === nextProps.label &&
      prevProps.disabled === nextProps.disabled &&
      prevProps.className === nextProps.className &&
      prevProps.filterCategory === nextProps.filterCategory &&
      prevProps.onLocationSelect === nextProps.onLocationSelect
    );
  }
);

// Export both the memoized and non-memoized versions
export { LocationInput, MemoizedLocationInput as default };
