"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { useAuth } from "@/components/providers/auth-provider";
import { fetchWithAuth } from "@/lib/api";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Location } from '@quildora/types';

// Define the expected structure for a Warehouse (for the dropdown)
interface Warehouse {
  id: string;
  name: string;
}

// Define valid location types based on backend requirements
const locationTypes = [
  "AISLE", "RACK", "SHELF", "BIN", "ZONE",
  "DOCK", "STAGING_AREA", "OFFICE", "OTHER"
] as const;
const locationCategories = ["Receiving", "Storage", "Shipping", "Other"] as const;

// Zod schema including new fields
const formSchema = z.object({
  name: z.string().trim().min(1, { message: "Location name is required." }),
  locationType: z.enum(locationTypes, {
    errorMap: () => ({ message: "Please select a valid location type." }),
  }),
  warehouseId: z.string().min(1, { message: "Please select a warehouse." }),
  category: z.enum(locationCategories, {
    errorMap: () => ({ message: "Please select a valid category." }),
  }),
});

type LocationFormData = z.infer<typeof formSchema>;

// Define the expected shape of data returned by the API
interface CreateLocationResponse extends Location {}

// API function to fetch warehouses
async function fetchWarehouses(appToken: string | null): Promise<Warehouse[]> {
  if (!appToken) {
    console.warn("fetchWarehouses called without an appToken.");
    throw new Error("Authentication token not available to fetch warehouses.");
  }
  return fetchWithAuth("/api/warehouses", { token: appToken });
}

// API function to create a location
async function createLocation(
  locationData: LocationFormData,
  appToken: string | null
): Promise<CreateLocationResponse> {
  if (!appToken) {
    console.warn("createLocation called without an appToken.");
    throw new Error("Authentication token not available to create location.");
  }
  const payload = {
    ...locationData,
  };

  return fetchWithAuth("/api/locations", {
    method: "POST",
    body: JSON.stringify(payload),
    token: appToken,
  });
}

export interface CreateLocationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: (newLocation: CreateLocationResponse) => void;
}

export function CreateLocationDialog({
  open,
  onOpenChange,
  onSuccess,
}: CreateLocationDialogProps) {
  const queryClient = useQueryClient();
  const { appToken } = useAuth(); // Get appToken

  // Fetch warehouses for the dropdown
  const { data: warehouses, isLoading: isLoadingWarehouses, error: warehousesError } = useQuery<
    Warehouse[],
    Error
  >({
    queryKey: ["warehouses", appToken], // Add appToken to queryKey if data depends on it (or for refetch on token change)
    queryFn: () => fetchWarehouses(appToken),
    enabled: open && !!appToken, // Only fetch when the dialog is open and token is available
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  });

  const form = useForm<LocationFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      locationType: undefined,
      warehouseId: undefined,
      category: undefined,
    },
  });

  const mutation = useMutation<CreateLocationResponse, Error, LocationFormData>(
    {
      mutationFn: (data) => createLocation(data, appToken),
      onSuccess: (data) => {
        toast.success(`Location "${data.name}" created successfully.`);
        queryClient.invalidateQueries({ queryKey: ["locations"] });
        onSuccess?.(data);
        onOpenChange(false);
        form.reset();
      },
      onError: (error) => {
        toast.error(error.message || "Failed to create location.");
      },
    }
  );

  function onSubmit(values: LocationFormData) {
    mutation.mutate(values);
  }

  React.useEffect(() => {
    if (open) {
      form.reset(); // Reset form fields when dialog opens
      // Optionally refetch warehouses if dialog is opened and there was an error previously or token changed
      if (warehousesError && appToken) {
        queryClient.invalidateQueries({ queryKey: ['warehouses', appToken] });
      }
    }
  }, [open, form, appToken, warehousesError, queryClient]); // Added appToken, warehousesError, queryClient to dependency array

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Location</DialogTitle>
          <DialogDescription>
            Enter the details for the new warehouse location.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Warehouse Select */}
            <FormField
              control={form.control}
              name="warehouseId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Warehouse</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isLoadingWarehouses || !warehouses}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={
                            isLoadingWarehouses
                              ? "Loading..."
                              : "Select a warehouse"
                          }
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {warehouses?.map((warehouse) => (
                        <SelectItem key={warehouse.id} value={warehouse.id}>
                          {warehouse.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Location Name Input */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., A1-R2-S3" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Location Type Select */}
            <FormField
              control={form.control}
              name="locationType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a location type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {locationTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Location Category Select */}
            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {locationCategories.map((cat) => (
                        <SelectItem key={cat} value={cat}>
                          {cat}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={mutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={mutation.isPending || isLoadingWarehouses}
              >
                {mutation.isPending ? "Creating..." : "Create Location"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
