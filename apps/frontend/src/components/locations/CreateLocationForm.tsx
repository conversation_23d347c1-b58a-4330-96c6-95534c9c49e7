"use client";

import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useRouter } from "next/navigation";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import {
  LocationType,
  LocationStatus,
  LocationCategory,
} from "@quildora/types";

// Use the shared enums for validation
const formSchema = z.object({
  name: z
    .string()
    .min(2, { message: "Location name must be at least 2 characters." })
    .max(100),
  locationType: z
    .string()
    .min(1, { message: "Please select a location type." }),
  category: z.nativeEnum(LocationCategory).optional(), // Optional, defaults to Storage on backend
  status: z.nativeEnum(LocationStatus).optional(), // Optional, defaults to ACTIVE on backend if not sent
  warehouseId: z
    .string()
    .cuid({ message: "Invalid warehouse ID format. Expected a CUID." }), // Use .cuid() to match the database schema
});

type LocationFormValues = z.infer<typeof formSchema>;

interface CreateLocationFormProps {
  warehouseId: string;
  onLocationCreated?: () => void; // Optional callback
}

export function CreateLocationForm({
  warehouseId,
  onLocationCreated,
}: CreateLocationFormProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { appToken } = useAuth();

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      locationType: "",
      category: LocationCategory.Storage, // Default to Storage
      status: LocationStatus.ACTIVE,
      warehouseId: warehouseId,
    },
  });

  // This effect ensures that if the warehouseId prop updates after the initial render
  // (which can happen with Next.js routing), the form value is updated to match.
  useEffect(() => {
    if (warehouseId) {
      form.setValue("warehouseId", warehouseId);
    }
  }, [warehouseId, form.setValue]);

  const createLocationMutation = useMutation({
    mutationFn: async (data: LocationFormValues) => {
      if (!appToken) throw new Error("Not authenticated");
      return fetchWithAuth("/api/locations", {
        method: "POST",
        token: appToken,
        body: JSON.stringify(data),
      });
    },
    onSuccess: () => {
      toast.success("Location created successfully!");
      queryClient.invalidateQueries({ queryKey: ["locations", warehouseId] });
      if (onLocationCreated) {
        onLocationCreated();
      } else {
        router.push(`/settings/warehouses/${warehouseId}/locations`);
      }
      form.reset();
    },
    onError: (error: any) => {
      const errorMsg =
        error.response?.data?.message ||
        error.message ||
        "Failed to create location.";
      toast.error(errorMsg);
      console.error("Create location error:", error);
    },
  });

  function onSubmit(data: LocationFormValues) {
    console.log("Submitting location data:", data);
    createLocationMutation.mutate(data);
  }

  function onInvalid(errors: any) {
    console.error("Form validation failed:", errors);
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit, onInvalid)}
        className="space-y-6"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Location Name</FormLabel>
              <FormControl>
                <Input
                  placeholder="e.g., Aisle 1, Rack A-01, Bin 17"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="category"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Category</FormLabel>
              <Select onValueChange={field.onChange} value={field.value || ""}>
                <FormControl>
                  <SelectTrigger className="min-h-[44px]">
                    {" "}
                    {/* Larger touch target for mobile */}
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.values(LocationCategory).map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="locationType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Location Type</FormLabel>
              <Select onValueChange={field.onChange} value={field.value || ""}>
                <FormControl>
                  <SelectTrigger className="min-h-[44px]">
                    {" "}
                    {/* Larger touch target for mobile */}
                    <SelectValue placeholder="Select a location type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.values(LocationType).map((type) => (
                    <SelectItem key={type} value={type}>
                      {type.replace("_", " ")}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Status (Optional)</FormLabel>
              <Select onValueChange={field.onChange} value={field.value || ""}>
                <FormControl>
                  <SelectTrigger className="min-h-[44px]">
                    {" "}
                    {/* Larger touch target for mobile */}
                    <SelectValue placeholder="Select a status (defaults to ACTIVE)" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.values(LocationStatus).map((status) => (
                    <SelectItem key={status} value={status}>
                      {status.replace("_", " ")}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* warehouseId is hidden but part of the form data */}
        <FormField
          control={form.control}
          name="warehouseId"
          render={() => <FormItem />}
        />

        <Button
          type="submit"
          disabled={createLocationMutation.isPending}
          className="min-h-[44px] w-full md:w-auto" // Larger touch target and full width on mobile
        >
          {createLocationMutation.isPending ? "Creating..." : "Create Location"}
        </Button>
      </form>
    </Form>
  );
}
