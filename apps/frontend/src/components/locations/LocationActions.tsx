"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { CreateLocationDialog } from "./CreateLocationDialog";

export function LocationActions() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  return (
    <>
      <div className="mb-4">
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          Create New Location
        </Button>
      </div>
      <CreateLocationDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
      />
    </>
  );
}
