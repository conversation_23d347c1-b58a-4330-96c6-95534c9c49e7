"use client";

import React from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import {
  LocationType,
  LocationStatus,
  LocationCategory,
} from "@quildora/types";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Plus, Trash2, Package } from "lucide-react";

const locationSchema = z.object({
  name: z
    .string()
    .min(2, { message: "Location name must be at least 2 characters." })
    .max(100),
  locationType: z
    .string()
    .min(1, { message: "Please select a location type." }),
  category: z.nativeEnum(LocationCategory).optional(),
  status: z.nativeEnum(LocationStatus).optional(),
});

const bulkFormSchema = z.object({
  warehouseId: z.string().cuid({ message: "Invalid warehouse ID format." }),
  locations: z
    .array(locationSchema)
    .min(1, { message: "At least one location is required." }),
});

type BulkLocationFormData = z.infer<typeof bulkFormSchema>;

interface BulkCreateLocationsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  warehouseId: string;
  warehouseName?: string;
  onSuccess?: () => void;
}

export function BulkCreateLocationsDialog({
  open,
  onOpenChange,
  warehouseId,
  warehouseName,
  onSuccess,
}: BulkCreateLocationsDialogProps) {
  const queryClient = useQueryClient();
  const { appToken } = useAuth();

  const form = useForm<BulkLocationFormData>({
    resolver: zodResolver(bulkFormSchema),
    defaultValues: {
      warehouseId,
      locations: [
        {
          name: "",
          locationType: "",
          category: LocationCategory.Storage,
          status: LocationStatus.ACTIVE,
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "locations",
  });

  const createLocationsMutation = useMutation({
    mutationFn: async (data: BulkLocationFormData) => {
      if (!appToken) throw new Error("Not authenticated");

      // Create locations one by one (could be optimized with a bulk endpoint)
      const results = [];
      for (const location of data.locations) {
        const result = await fetchWithAuth("/api/locations", {
          method: "POST",
          token: appToken,
          body: JSON.stringify({
            ...location,
            warehouseId: data.warehouseId,
          }),
        });
        results.push(result);
      }
      return results;
    },
    onSuccess: (results) => {
      toast.success(`Successfully created ${results.length} location(s)!`);
      queryClient.invalidateQueries({ queryKey: ["locations", warehouseId] });
      if (onSuccess) {
        onSuccess();
      }
      onOpenChange(false);
      form.reset();
    },
    onError: (error: any) => {
      const errorMsg = error.message || "Failed to create locations.";
      toast.error(errorMsg);
    },
  });

  const onSubmit = (data: BulkLocationFormData) => {
    createLocationsMutation.mutate(data);
  };

  const addLocation = () => {
    append({
      name: "",
      locationType: "",
      category: LocationCategory.Storage,
      status: LocationStatus.ACTIVE,
    });
  };

  const removeLocation = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Package className="mr-2 h-5 w-5" />
            Bulk Create Locations
          </DialogTitle>
          <DialogDescription>
            Create multiple locations at once for warehouse "
            {warehouseName || "Unknown Warehouse"}".
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-4">
              {fields.map((field, index) => (
                <Card key={field.id} className="relative">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm flex items-center justify-between">
                      Location {index + 1}
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeLocation(index)}
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name={`locations.${index}.name`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Location Name</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="e.g., Aisle 1, Rack A-01, Bin 17"
                              {...field}
                              className="text-base md:text-sm" // Larger text for mobile
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name={`locations.${index}.category`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Category</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value || ""}
                            >
                              <FormControl>
                                <SelectTrigger className="min-h-[44px]">
                                  <SelectValue placeholder="Select category" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {Object.values(LocationCategory).map(
                                  (category) => (
                                    <SelectItem key={category} value={category}>
                                      {category}
                                    </SelectItem>
                                  )
                                )}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`locations.${index}.locationType`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Type</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value || ""}
                            >
                              <FormControl>
                                <SelectTrigger className="min-h-[44px]">
                                  <SelectValue placeholder="Select type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {Object.values(LocationType).map((type) => (
                                  <SelectItem key={type} value={type}>
                                    {type.replace("_", " ")}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <Button
              type="button"
              variant="outline"
              onClick={addLocation}
              className="w-full min-h-[44px]"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Another Location
            </Button>

            <DialogFooter className="flex flex-col sm:flex-row gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={createLocationsMutation.isPending}
                className="min-h-[44px]"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createLocationsMutation.isPending}
                className="min-h-[44px]"
              >
                {createLocationsMutation.isPending
                  ? `Creating ${fields.length} location(s)...`
                  : `Create ${fields.length} Location(s)`}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
