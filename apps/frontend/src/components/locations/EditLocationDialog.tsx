"use client";

import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Location } from "@quildora/types"; // Added for standardized Location type

// Corresponds to backend CreateLocationDto and Prisma Location model enums
const locationTypes = [
  "AISLE",
  "RACK",
  "SHELF",
  "BIN",
  "ZONE",
  "DOCK",
  "STAGING_AREA",
  "OFFICE",
  "OTHER",
] as const;
const locationStatuses = ["ACTIVE", "INACTIVE", "MAINTENANCE"] as const;
const locationCategories = [
  "Receiving",
  "Storage",
  "Shipping",
  "Other",
] as const;

// Zod schema for form validation
const formSchema = z.object({
  name: z
    .string()
    .trim()
    .min(2, { message: "Location name must be at least 2 characters." })
    .max(100),
  locationType: z.enum(locationTypes, {
    errorMap: () => ({ message: "Please select a valid location type." }),
  }),
  status: z.enum(locationStatuses).optional(),
  category: z.enum(locationCategories).optional(),
});

type LocationFormData = z.infer<typeof formSchema>;

// Define the expected shape of data returned by the API after update
interface UpdateLocationResponse extends Location {}

// API function to update a location
async function updateLocation(
  {
    id,
    locationData,
    warehouseId,
  }: {
    id: string;
    locationData: LocationFormData;
    warehouseId: string;
  },
  appToken: string | null
): Promise<UpdateLocationResponse> {
  if (!appToken) {
    throw new Error(
      "Authentication token not available for updating location."
    );
  }
  const payload = {
    ...locationData,
  };
  return fetchWithAuth(`/api/locations/${id}?warehouseId=${warehouseId}`, {
    method: "PATCH",
    body: JSON.stringify(payload),
    token: appToken,
  });
}

export interface EditLocationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  location: Location | null; // Location to edit
  onSuccess?: (updatedLocation: UpdateLocationResponse) => void;
}

export function EditLocationDialog({
  open,
  onOpenChange,
  location,
  onSuccess,
}: EditLocationDialogProps) {
  const queryClient = useQueryClient();
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  const form = useForm<LocationFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      locationType: undefined,
      status: "ACTIVE", // Default status
    },
  });

  // Update form default values when the location prop changes
  useEffect(() => {
    if (location) {
      form.reset({
        name: location.name,
        locationType: location.locationType as (typeof locationTypes)[number],
        status:
          (location.status as (typeof locationStatuses)[number]) || "ACTIVE",
        category: location.category as
          | (typeof locationCategories)[number]
          | undefined,
      });
    } else {
      form.reset(); // Reset to default if no location
    }
  }, [location, form]);

  const mutation = useMutation<
    UpdateLocationResponse,
    Error,
    { id: string; locationData: LocationFormData }
  >({
    mutationFn: (params) =>
      updateLocation(
        { ...params, warehouseId: currentWarehouse?.id || "" },
        appToken
      ),
    onSuccess: (updatedLocationData) => {
      toast.success("Location updated successfully!");
      // Invalidate queries for the list of locations for the specific warehouse
      if (location?.warehouseId) {
        // Ensure location and warehouseId are defined
        queryClient.invalidateQueries({
          queryKey: ["locations", location.warehouseId],
        });
      }
      // Invalidate query for this specific location if one exists
      if (location?.id) {
        // Ensure location and id are defined
        queryClient.invalidateQueries({ queryKey: ["location", location.id] });
      }
      if (onSuccess) {
        onSuccess(updatedLocationData);
      }
      onOpenChange(false); // Close dialog on success
    },
    onError: (error) => {
      toast.error(error.message || "Failed to update location.");
    },
  });

  function onSubmit(values: LocationFormData) {
    if (!location) return;
    mutation.mutate({ id: location.id, locationData: values });
  }

  const handleOpenChange = (isOpen: boolean) => {
    onOpenChange(isOpen);
  };

  return (
    <Dialog open={open && !!location} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        {location && (
          <>
            <DialogHeader>
              <DialogTitle>Edit Location</DialogTitle>
              <DialogDescription>
                Update the details for location "{location.name}" in warehouse "
                {location.warehouse?.name ?? "N/A"}".
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                {/* Location Name */}
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g., Aisle 1, Rack 2, Shelf 3"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Location Category Select */}
                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {locationCategories.map((cat) => (
                            <SelectItem key={cat} value={cat}>
                              {cat}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Location Type Select */}
                <FormField
                  control={form.control}
                  name="locationType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {locationTypes.map((type) => (
                            <SelectItem key={type} value={type}>
                              {type}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => handleOpenChange(false)}
                    disabled={mutation.isPending}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={mutation.isPending}>
                    {mutation.isPending ? "Saving..." : "Save Changes"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
