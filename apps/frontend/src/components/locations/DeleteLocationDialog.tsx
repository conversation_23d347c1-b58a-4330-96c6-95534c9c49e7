"use client";

import React from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Location } from "@quildora/types"; // Added for standardized Location type

// Define the expected shape of data returned by the API after delete
interface DeleteLocationResponse {
  id?: string;
}

export interface DeleteLocationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  location: Location | null; // Location to delete
  onSuccess?: () => void;
}

export function DeleteLocationDialog({
  open,
  onOpenChange,
  location,
  onSuccess,
}: DeleteLocationDialogProps) {
  const queryClient = useQueryClient();
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  const mutation = useMutation<DeleteLocationResponse | null, Error, string>({
    mutationFn: async (locationId: string) => {
      if (!appToken) {
        toast.error("Authentication token not found. Please log in.");
        throw new Error("Authentication token not found.");
      }
      return fetchWithAuth(
        `/api/locations/${locationId}?warehouseId=${
          currentWarehouse?.id || ""
        }`,
        {
          method: "DELETE",
          token: appToken,
        }
      );
    },
    onSuccess: (_data, deletedLocationId) => {
      toast.success(
        `Location "${
          location?.name ?? deletedLocationId
        }" deleted successfully.`
      );
      if (location?.warehouseId) {
        queryClient.invalidateQueries({
          queryKey: ["locations", location.warehouseId],
        });
      } else {
        queryClient.invalidateQueries({ queryKey: ["locations"] }); // Fallback to broader invalidation
      }
      onSuccess?.();
      onOpenChange(false);
    },
    onError: (error) => {
      toast.error(error.message || "Failed to delete location.");
    },
  });

  const handleDelete = () => {
    if (!location) return;
    mutation.mutate(location.id);
  };

  return (
    <AlertDialog open={open && !!location} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        {location && (
          <>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the
                location "<strong>{location.name}</strong>". Associated pallets
                might prevent deletion.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel
                onClick={() => onOpenChange(false)}
                disabled={mutation.isPending}
              >
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                disabled={mutation.isPending}
                className="bg-red-600 hover:bg-red-700"
              >
                {mutation.isPending ? "Deleting..." : "Delete"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </>
        )}
      </AlertDialogContent>
    </AlertDialog>
  );
}
