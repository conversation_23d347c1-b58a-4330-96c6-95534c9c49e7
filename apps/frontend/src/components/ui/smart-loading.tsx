/**
 * Smart loading components that provide context-aware loading states
 * Optimized for warehouse operations with minimal loading interruptions
 */

import { ReactNode } from "react";
import { Loader2, Building2, Package, MapPin, Users } from "lucide-react";
import { cn } from "@/lib/utils";

interface SmartLoadingProps {
  isLoading: boolean;
  hasCache?: boolean;
  loadingType?: "initial" | "refresh" | "switch" | "operation";
  context?: "warehouse" | "pallet" | "location" | "user" | "general";
  size?: "sm" | "md" | "lg" | "xl";
  showText?: boolean;
  className?: string;
  children?: ReactNode;
}

const LOADING_MESSAGES = {
  warehouse: {
    initial: "Loading workspace...",
    refresh: "Refreshing warehouse data...",
    switch: "Switching warehouse...",
    operation: "Processing...",
  },
  pallet: {
    initial: "Loading pallets...",
    refresh: "Refreshing pallet data...",
    switch: "Loading pallet details...",
    operation: "Processing pallet operation...",
  },
  location: {
    initial: "Loading locations...",
    refresh: "Refreshing location data...",
    switch: "Loading location details...",
    operation: "Processing location operation...",
  },
  user: {
    initial: "Authenticating...",
    refresh: "Refreshing user data...",
    switch: "Switching user context...",
    operation: "Processing user operation...",
  },
  general: {
    initial: "Loading...",
    refresh: "Refreshing...",
    switch: "Loading...",
    operation: "Processing...",
  },
} as const;

const CONTEXT_ICONS = {
  warehouse: Building2,
  pallet: Package,
  location: MapPin,
  user: Users,
  general: Loader2,
} as const;

const SIZE_CLASSES = {
  sm: "h-4 w-4",
  md: "h-6 w-6",
  lg: "h-8 w-8",
  xl: "h-12 w-12",
} as const;

/**
 * Smart loading spinner that adapts based on context and cache state
 */
export function SmartLoading({
  isLoading,
  hasCache = false,
  loadingType = "initial",
  context = "general",
  size = "md",
  showText = true,
  className,
  children,
}: SmartLoadingProps) {
  // Don't show loading if we have cache and it's just a refresh
  if (!isLoading || (hasCache && loadingType === "refresh")) {
    return <>{children}</>;
  }

  const Icon = CONTEXT_ICONS[context];
  const message = LOADING_MESSAGES[context][loadingType];
  const sizeClass = SIZE_CLASSES[size];

  return (
    <div className={cn("flex items-center justify-center", className)}>
      <div className="text-center space-y-2">
        <Icon className={cn("animate-spin mx-auto text-blue-600", sizeClass)} />
        {showText && (
          <p className="text-sm text-muted-foreground">{message}</p>
        )}
      </div>
    </div>
  );
}

/**
 * Full-screen smart loading overlay
 */
export function SmartLoadingOverlay({
  isLoading,
  hasCache = false,
  loadingType = "initial",
  context = "general",
  className,
}: Omit<SmartLoadingProps, "children" | "size">) {
  if (!isLoading || (hasCache && loadingType === "refresh")) {
    return null;
  }

  return (
    <div className={cn("fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center", className)}>
      <SmartLoading
        isLoading={true}
        loadingType={loadingType}
        context={context}
        size="lg"
        showText={true}
      />
    </div>
  );
}

/**
 * Inline smart loading for smaller components
 */
export function InlineSmartLoading({
  isLoading,
  hasCache = false,
  loadingType = "operation",
  context = "general",
  fallback,
  children,
}: SmartLoadingProps & { fallback?: ReactNode }) {
  if (!isLoading || (hasCache && loadingType === "refresh")) {
    return <>{children}</>;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  return (
    <div className="flex items-center justify-center py-4">
      <SmartLoading
        isLoading={true}
        loadingType={loadingType}
        context={context}
        size="sm"
        showText={false}
      />
    </div>
  );
}

/**
 * Skeleton loading for warehouse data
 */
export function WarehouseDataSkeleton({ type }: { type: "pallets" | "locations" | "items" }) {
  const skeletonCount = type === "pallets" ? 6 : type === "locations" ? 8 : 10;
  
  return (
    <div className="space-y-4">
      <div className="h-8 bg-muted rounded w-1/3 animate-pulse" />
      <div className="grid gap-4">
        {Array.from({ length: skeletonCount }).map((_, i) => (
          <div key={i} className="border rounded-lg p-4 space-y-3">
            <div className="flex items-center space-x-3">
              <div className="h-10 w-10 bg-muted rounded animate-pulse" />
              <div className="space-y-2 flex-1">
                <div className="h-4 bg-muted rounded w-3/4 animate-pulse" />
                <div className="h-3 bg-muted rounded w-1/2 animate-pulse" />
              </div>
            </div>
            {type === "pallets" && (
              <div className="space-y-2">
                <div className="h-3 bg-muted rounded w-full animate-pulse" />
                <div className="h-3 bg-muted rounded w-2/3 animate-pulse" />
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

/**
 * Smart loading wrapper for warehouse operations
 */
export function WarehouseOperationLoading({
  isLoading,
  operation,
  children,
}: {
  isLoading: boolean;
  operation: "receiving" | "picking" | "moving" | "creating";
  children: ReactNode;
}) {
  const operationMessages = {
    receiving: "Processing receipt...",
    picking: "Processing pick...",
    moving: "Moving pallet...",
    creating: "Creating pallet...",
  };

  if (!isLoading) {
    return <>{children}</>;
  }

  return (
    <div className="flex items-center justify-center py-8">
      <div className="text-center space-y-4">
        <Package className="h-8 w-8 animate-spin mx-auto text-blue-600" />
        <p className="text-sm text-muted-foreground">
          {operationMessages[operation]}
        </p>
      </div>
    </div>
  );
}

/**
 * Optimistic loading wrapper - shows content immediately with background loading
 */
export function OptimisticLoading({
  isLoading,
  showOptimistic = true,
  loadingFallback,
  children,
}: {
  isLoading: boolean;
  showOptimistic?: boolean;
  loadingFallback?: ReactNode;
  children: ReactNode;
}) {
  // Show content immediately if optimistic loading is enabled
  if (showOptimistic) {
    return (
      <div className="relative">
        {children}
        {isLoading && (
          <div className="absolute top-2 right-2">
            <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
          </div>
        )}
      </div>
    );
  }

  // Fall back to traditional loading
  if (isLoading) {
    return <>{loadingFallback || <SmartLoading isLoading={true} />}</>;
  }

  return <>{children}</>;
}

/**
 * Context-aware loading hook
 */
export function useSmartLoading(
  isLoading: boolean,
  context: SmartLoadingProps["context"] = "general",
  hasCache = false
) {
  const shouldShowLoading = isLoading && !hasCache;
  
  return {
    shouldShowLoading,
    LoadingComponent: ({ loadingType = "initial", size = "md" }: Partial<SmartLoadingProps>) => (
      <SmartLoading
        isLoading={shouldShowLoading}
        hasCache={hasCache}
        loadingType={loadingType}
        context={context}
        size={size}
      />
    ),
  };
}
