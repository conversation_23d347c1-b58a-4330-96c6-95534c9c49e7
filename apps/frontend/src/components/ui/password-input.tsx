'use client';

import React, { useState, forwardRef } from 'react';
import { Eye, EyeOff, Check, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface PasswordStrength {
  score: number;
  label: string;
  color: string;
  requirements: {
    length: boolean;
    lowercase: boolean;
    uppercase: boolean;
    number: boolean;
    special: boolean;
  };
}

interface PasswordInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  showStrengthIndicator?: boolean;
  showRequirements?: boolean;
  minLength?: number;
  requireUppercase?: boolean;
  requireLowercase?: boolean;
  requireNumber?: boolean;
  requireSpecial?: boolean;
  onStrengthChange?: (strength: PasswordStrength) => void;
}

const PasswordInput = forwardRef<HTMLInputElement, PasswordInputProps>(
  (
    {
      className,
      showStrengthIndicator = false,
      showRequirements = false,
      minLength = 8,
      requireUppercase = true,
      requireLowercase = true,
      requireNumber = true,
      requireSpecial = false,
      onStrengthChange,
      value,
      onChange,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false);
    const passwordValue = (value as string) || '';

    // Calculate password strength
    const getPasswordStrength = (password: string): PasswordStrength => {
      const requirements = {
        length: password.length >= minLength,
        lowercase: requireLowercase ? /[a-z]/.test(password) : true,
        uppercase: requireUppercase ? /[A-Z]/.test(password) : true,
        number: requireNumber ? /\d/.test(password) : true,
        special: requireSpecial ? /[^a-zA-Z\d]/.test(password) : true,
      };

      const metRequirements = Object.values(requirements).filter(Boolean).length;
      const totalRequirements = Object.values({
        length: minLength > 0,
        lowercase: requireLowercase,
        uppercase: requireUppercase,
        number: requireNumber,
        special: requireSpecial,
      }).filter(Boolean).length;

      let score = 0;
      let label = '';
      let color = '';

      if (!password) {
        score = 0;
        label = '';
        color = '';
      } else if (metRequirements < totalRequirements * 0.5) {
        score = 1;
        label = 'Weak';
        color = 'bg-red-500';
      } else if (metRequirements < totalRequirements * 0.75) {
        score = 2;
        label = 'Fair';
        color = 'bg-yellow-500';
      } else if (metRequirements < totalRequirements) {
        score = 3;
        label = 'Good';
        color = 'bg-blue-500';
      } else {
        score = 4;
        label = 'Strong';
        color = 'bg-green-500';
      }

      return { score, label, color, requirements };
    };

    const strength = getPasswordStrength(passwordValue);

    // Notify parent of strength changes
    React.useEffect(() => {
      if (onStrengthChange) {
        onStrengthChange(strength);
      }
    }, [passwordValue, onStrengthChange]);

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    return (
      <div className="space-y-2">
        {/* Password Input with Toggle */}
        <div className="relative">
          <Input
            {...props}
            ref={ref}
            type={showPassword ? 'text' : 'password'}
            value={value}
            onChange={onChange}
            className={cn('pr-12', className)}
            autoComplete="new-password"
          />
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
            onClick={togglePasswordVisibility}
            tabIndex={-1}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4 text-gray-400" />
            ) : (
              <Eye className="h-4 w-4 text-gray-400" />
            )}
            <span className="sr-only">
              {showPassword ? 'Hide password' : 'Show password'}
            </span>
          </Button>
        </div>

        {/* Strength Indicator */}
        {showStrengthIndicator && passwordValue && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div
                  className={cn(
                    'h-2 rounded-full transition-all duration-300',
                    strength.color
                  )}
                  style={{ width: `${(strength.score / 4) * 100}%` }}
                />
              </div>
              <span className="text-xs text-gray-600 min-w-[3rem]">
                {strength.label}
              </span>
            </div>
          </div>
        )}

        {/* Requirements List */}
        {showRequirements && passwordValue && (
          <div className="space-y-1">
            <p className="text-xs font-medium text-gray-700">Password must contain:</p>
            <div className="grid grid-cols-1 gap-1 text-xs">
              {minLength > 0 && (
                <RequirementItem
                  met={strength.requirements.length}
                  text={`At least ${minLength} characters`}
                />
              )}
              {requireLowercase && (
                <RequirementItem
                  met={strength.requirements.lowercase}
                  text="One lowercase letter"
                />
              )}
              {requireUppercase && (
                <RequirementItem
                  met={strength.requirements.uppercase}
                  text="One uppercase letter"
                />
              )}
              {requireNumber && (
                <RequirementItem
                  met={strength.requirements.number}
                  text="One number"
                />
              )}
              {requireSpecial && (
                <RequirementItem
                  met={strength.requirements.special}
                  text="One special character"
                />
              )}
            </div>
          </div>
        )}
      </div>
    );
  }
);

PasswordInput.displayName = 'PasswordInput';

// Requirement item component
interface RequirementItemProps {
  met: boolean;
  text: string;
}

function RequirementItem({ met, text }: RequirementItemProps) {
  return (
    <div className="flex items-center gap-2">
      {met ? (
        <Check className="h-3 w-3 text-green-600" />
      ) : (
        <X className="h-3 w-3 text-gray-400" />
      )}
      <span className={cn(
        'text-xs',
        met ? 'text-green-600' : 'text-gray-500'
      )}>
        {text}
      </span>
    </div>
  );
}

export { PasswordInput };
export type { PasswordInputProps, PasswordStrength };
