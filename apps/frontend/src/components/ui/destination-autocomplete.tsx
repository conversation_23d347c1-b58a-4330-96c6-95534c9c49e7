"use client";

import * as React from "react";
import { useState, useEffect, useMemo } from "react";
import { Check, ChevronsUpDown } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/components/providers/auth-provider";
import {
  useDestinations,
  useDestinationsWithCodes,
  useDestinationsByName,
} from "@/hooks/api";
import { fetchWithAuth } from "@/lib/api";
import { DestinationResponse } from "@quildora/types";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface DestinationAutocompleteProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  priorityDestinations?: string[]; // For ReceiveItemsForm to prioritize PO destinations
  className?: string;
  supportCodes?: boolean; // Whether to show destination codes and enable code-based search
  searchType?: "name" | "code"; // Primary search type when supportCodes is true
  onDestinationSelect?: (destination: DestinationResponse | null) => void; // Callback when a destination with code is selected
}

// Note: fetchDestinations function removed - now using useDestinations hook for warehouse-aware data

export function DestinationAutocomplete({
  value = "",
  onValueChange,
  placeholder = "Select or enter destination...",
  disabled = false,
  priorityDestinations = [],
  className,
  supportCodes = false,
  searchType = "name",
  onDestinationSelect,
}: DestinationAutocompleteProps) {
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState(value);
  const [highlightedValue, setHighlightedValue] = useState<string>("");

  // Use warehouse-aware destinations hooks
  const { data: destinations = [], isLoading: isLoadingDestinations } =
    useDestinations();

  const { data: destinationsWithCodes = [], isLoading: isLoadingWithCodes } =
    useDestinationsWithCodes();

  const { data: searchResults = [], isLoading: isSearching } =
    useDestinationsByName(inputValue, 300);

  // Update input value when external value changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // Organize destinations with priority and codes support
  const organizedDestinations = useMemo(() => {
    if (supportCodes) {
      // Use destinations with codes when code support is enabled
      const allDestinations = [...destinationsWithCodes];
      const priority = allDestinations.filter((dest) =>
        priorityDestinations.includes(dest.name)
      );
      const others = allDestinations.filter(
        (dest) => !priorityDestinations.includes(dest.name)
      );

      return {
        priority: priority.sort((a, b) => a.name.localeCompare(b.name)),
        others: others.sort((a, b) => a.name.localeCompare(b.name)),
      };
    } else {
      // Use simple string destinations for backward compatibility
      const allDestinations = [...destinations];
      const priority = priorityDestinations.filter(
        (dest) => dest && allDestinations.includes(dest)
      );
      const others = allDestinations.filter(
        (dest) => !priorityDestinations.includes(dest)
      );

      return {
        priority: priority.sort(),
        others: others.sort(),
      };
    }
  }, [destinations, destinationsWithCodes, priorityDestinations, supportCodes]);

  // Filter destinations based on input
  const filteredDestinations = useMemo(() => {
    if (!inputValue.trim()) {
      return organizedDestinations;
    }

    const searchTerm = inputValue.toLowerCase();

    if (supportCodes) {
      // Filter destinations with codes
      const filterFn = (
        dest: string | DestinationResponse
      ): dest is DestinationResponse => {
        if (typeof dest === "string") return false;
        const nameMatch = dest.name.toLowerCase().includes(searchTerm);
        const codeMatch = dest.code?.toLowerCase().includes(searchTerm);
        const displayMatch = dest.displayName
          .toLowerCase()
          .includes(searchTerm);
        return nameMatch || codeMatch || displayMatch;
      };

      return {
        priority: organizedDestinations.priority.filter(filterFn),
        others: organizedDestinations.others.filter(filterFn),
      };
    } else {
      // Filter simple string destinations
      const filterFn = (dest: string | DestinationResponse): dest is string => {
        if (typeof dest !== "string") return false;
        return dest.toLowerCase().includes(searchTerm);
      };

      return {
        priority: organizedDestinations.priority.filter(filterFn),
        others: organizedDestinations.others.filter(filterFn),
      };
    }
  }, [organizedDestinations, inputValue, supportCodes]);

  const handleSelect = (selectedValue: string) => {
    setInputValue(selectedValue);
    onValueChange(selectedValue);
    setOpen(false);
    setHighlightedValue(""); // Reset highlighted value
  };

  // Handle selection of destinations with codes for auto-population
  const handleDestinationSelect = (destination: DestinationResponse) => {
    setInputValue(destination.name);
    onValueChange(destination.name);
    setOpen(false);
    setHighlightedValue("");

    // Call the callback to auto-populate destination code
    if (onDestinationSelect) {
      onDestinationSelect(destination);
    }
  };

  const handleInputChange = (newValue: string) => {
    setInputValue(newValue);
    onValueChange(newValue);
    setHighlightedValue(""); // Reset highlighted value when typing
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      event.preventDefault();

      // If there's a highlighted value from arrow key navigation, use that
      if (highlightedValue) {
        handleSelect(highlightedValue);
      }
      // Otherwise, if there's input text, use that as a new destination
      else if (inputValue.trim()) {
        handleSelect(inputValue.trim());
      }
    }
  };

  // Handle when Command component highlights an item via arrow keys
  const handleValueChange = (newHighlightedValue: string) => {
    setHighlightedValue(newHighlightedValue);
  };

  const displayValue = inputValue || placeholder;
  const hasResults =
    filteredDestinations.priority.length > 0 ||
    filteredDestinations.others.length > 0;

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      setHighlightedValue(""); // Reset highlighted value when closing
    }
  };

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between", className)}
          disabled={disabled}
        >
          <span
            className={cn("truncate", !inputValue && "text-muted-foreground")}
          >
            {displayValue}
          </span>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command
          shouldFilter={false}
          value={highlightedValue}
          onValueChange={handleValueChange}
        >
          <CommandInput
            placeholder="Search or type new destination..."
            value={inputValue}
            onValueChange={handleInputChange}
            onKeyDown={handleKeyDown}
          />
          <CommandList>
            {isLoadingDestinations || (supportCodes && isLoadingWithCodes) ? (
              <CommandEmpty>Loading destinations...</CommandEmpty>
            ) : !hasResults ? (
              <CommandEmpty>
                {inputValue.trim() ? (
                  <div className="text-center py-2">
                    <p className="text-sm text-muted-foreground mb-2">
                      No matching destinations found
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Press Enter to use "{inputValue.trim()}"
                    </p>
                  </div>
                ) : (
                  "No destinations found"
                )}
              </CommandEmpty>
            ) : (
              <>
                {filteredDestinations.priority.length > 0 && (
                  <CommandGroup heading="Current PO Destinations">
                    {filteredDestinations.priority.map((destination) => {
                      if (supportCodes) {
                        const dest = destination as DestinationResponse;
                        return (
                          <CommandItem
                            key={`priority-${dest.name}-${
                              dest.code || "no-code"
                            }`}
                            value={dest.name}
                            onSelect={() => handleDestinationSelect(dest)}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                value === dest.name
                                  ? "opacity-100"
                                  : "opacity-0"
                              )}
                            />
                            <div className="flex flex-col">
                              <span>{dest.name}</span>
                              {dest.code && (
                                <span className="text-xs text-muted-foreground">
                                  Code: {dest.code}
                                </span>
                              )}
                            </div>
                          </CommandItem>
                        );
                      } else {
                        const dest = destination as string;
                        return (
                          <CommandItem
                            key={`priority-${dest}`}
                            value={dest}
                            onSelect={() => handleSelect(dest)}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                value === dest ? "opacity-100" : "opacity-0"
                              )}
                            />
                            {dest}
                          </CommandItem>
                        );
                      }
                    })}
                  </CommandGroup>
                )}
                {filteredDestinations.others.length > 0 && (
                  <CommandGroup
                    heading={
                      filteredDestinations.priority.length > 0
                        ? "Other Destinations"
                        : "Destinations"
                    }
                  >
                    {filteredDestinations.others.map((destination) => {
                      if (supportCodes) {
                        const dest = destination as DestinationResponse;
                        return (
                          <CommandItem
                            key={`other-${dest.name}-${dest.code || "no-code"}`}
                            value={dest.name}
                            onSelect={() => handleDestinationSelect(dest)}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                value === dest.name
                                  ? "opacity-100"
                                  : "opacity-0"
                              )}
                            />
                            <div className="flex flex-col">
                              <span>{dest.name}</span>
                              {dest.code && (
                                <span className="text-xs text-muted-foreground">
                                  Code: {dest.code}
                                </span>
                              )}
                            </div>
                          </CommandItem>
                        );
                      } else {
                        const dest = destination as string;
                        return (
                          <CommandItem
                            key={`other-${dest}`}
                            value={dest}
                            onSelect={() => handleSelect(dest)}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                value === dest ? "opacity-100" : "opacity-0"
                              )}
                            />
                            {dest}
                          </CommandItem>
                        );
                      }
                    })}
                  </CommandGroup>
                )}
                {inputValue.trim() &&
                  !destinations.includes(inputValue.trim()) && (
                    <CommandGroup heading="Create New">
                      <CommandItem
                        value={inputValue.trim()}
                        onSelect={() => handleSelect(inputValue.trim())}
                      >
                        <Check className="mr-2 h-4 w-4 opacity-0" />
                        Create "{inputValue.trim()}"
                      </CommandItem>
                    </CommandGroup>
                  )}
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
