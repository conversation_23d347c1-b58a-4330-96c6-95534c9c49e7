"use client"

import { useTheme } from "next-themes"
import { Toaster as Son<PERSON>, ToasterProps } from "sonner"

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      style={{
        // Default (normal) toast styles
        "--normal-bg": "var(--popover)",
        "--normal-text": "var(--popover-foreground)",
        "--normal-border": "var(--border)",
        // Success toast styles
        "--success-bg": "var(--accent)",
        "--success-text": "var(--accent-foreground)",
        "--success-border": "var(--border)", // Or a slightly darker accent like emerald-600 if preferred
        // Error toast styles
        "--error-bg": "var(--destructive)",
        "--error-text": "var(--destructive-foreground)",
        "--error-border": "var(--border)", // Or a slightly darker destructive like red-600 if preferred
        // Warning toast styles
        "--warning-bg": "var(--warning)",
        "--warning-text": "var(--warning-foreground)",
        "--warning-border": "var(--border)", // Or a slightly darker warning like amber-500 if preferred
        // TODO: Add styles for --info-bg, --info-text, --info-border if info toasts are used
      } as React.CSSProperties}
      {...props}
    />
  )
}

export { Toaster }
