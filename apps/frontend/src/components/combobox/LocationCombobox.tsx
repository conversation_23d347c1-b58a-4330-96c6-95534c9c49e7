"use client";

import * as React from "react";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/components/providers/auth-provider";
import { fetchWithAuth } from "@/lib/api";
import { Location } from "@quildora/types";
import { useLocations } from "@/hooks/api";

interface LocationComboboxProps {
  onLocationSelected: (location: Location | null) => void;
  warehouseId?: string; // To fetch locations for a specific warehouse
  initialLocationId?: string;
  locations?: Location[]; // To pass locations directly
  isLoading?: boolean; // To indicate loading state when locations are passed directly
  className?: string; // For custom styling
}

export function LocationCombobox({
  onLocationSelected,
  warehouseId,
  initialLocationId,
  locations: locationsProp,
  isLoading: isLoadingProp,
  className,
}: LocationComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [selectedLocationId, setSelectedLocationId] = React.useState<
    string | undefined
  >(initialLocationId);

  const { appToken } = useAuth();

  const {
    data: fetchedLocations,
    isLoading: isFetching,
    error,
  } = useQuery<Location[], Error>({
    queryKey: ["locations", warehouseId],
    queryFn: () =>
      fetchWithAuth(`/api/locations?warehouseId=${warehouseId}`, {
        token: appToken,
      }),
    // Only fetch if locations are NOT passed in as a prop AND we have a warehouseId to fetch for.
    enabled: !!appToken && !!warehouseId && locationsProp === undefined,
  });

  // Use passed locations if available, otherwise use fetched locations
  const locations = locationsProp || fetchedLocations;
  const isLoading = isLoadingProp !== undefined ? isLoadingProp : isFetching;

  React.useEffect(() => {
    if (initialLocationId) {
      setSelectedLocationId(initialLocationId);
    } else {
      setSelectedLocationId(undefined);
    }
  }, [initialLocationId]);

  const handleSelect = (location: Location) => {
    if (selectedLocationId === location.id) {
      setSelectedLocationId(undefined);
      onLocationSelected(null);
    } else {
      setSelectedLocationId(location.id);
      onLocationSelected(location);
    }
    setOpen(false);
  };

  const selectedLocationDisplay =
    locations?.find((loc) => loc.id === selectedLocationId)?.name ||
    "Select a location...";

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
            disabled={isLoading || !locations}
          >
            {isLoading ? "Loading..." : selectedLocationDisplay}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
          <Command>
            <CommandInput placeholder="Search location..." />
            <CommandList>
              <CommandEmpty>No location found.</CommandEmpty>
              <CommandGroup>
                {locations?.map((location) => (
                  <CommandItem
                    key={location.id}
                    value={location.name} // Use name for searchability
                    onSelect={() => handleSelect(location)} // Pass the full object on select
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedLocationId === location.id
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                    {location.name}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
