# Warehouse-Aware Layout Components

This directory contains layout components that are warehouse-aware and provide consistent navigation and context throughout the application.

## Components Overview

### AppHeader
Enhanced header component that displays warehouse context and supports breadcrumbs.

**Features:**
- Shows current warehouse name below page title
- Integrates WarehouseSelector for warehouse switching
- Supports optional breadcrumb component
- Responsive design with proper text truncation
- Back button functionality

**Props:**
```typescript
interface AppHeaderProps {
  showBackButton?: boolean; // Control back button visibility
  showWarehouseContext?: boolean; // Show warehouse name in header (default: true)
  breadcrumbComponent?: React.ReactNode; // Optional breadcrumb component
}
```

**Usage:**
```typescript
import AppHeader from '@/components/layout/AppHeader';

// Basic usage (handled automatically by ConditionalLayout)
<AppHeader />

// With custom breadcrumbs
<AppHeader 
  breadcrumbComponent={<WarehouseBreadcrumb items={breadcrumbItems} />}
/>

// Without warehouse context (for settings pages)
<AppHeader showWarehouseContext={false} />
```

### PrimaryNav
Bottom navigation with warehouse-aware state management.

**Features:**
- Disables warehouse-dependent navigation when no warehouse selected
- Shows warning banner when warehouse selection required
- Visual feedback for disabled states
- Responsive icon and text layout

**Warehouse-dependent pages:**
- Dashboard (/)
- Pallets (/pallets)
- Move (/move)
- Picking (/picking)
- Items (/items)
- Receiving (/receiving)

**Always accessible:**
- Settings (/settings)

### WarehouseBreadcrumb
Warehouse-aware breadcrumb component with automatic warehouse context.

**Features:**
- Automatically includes warehouse context
- Flexible breadcrumb item configuration
- Consistent styling with design system
- Optional warehouse display

**Props:**
```typescript
interface WarehouseBreadcrumbProps {
  items: BreadcrumbItemType[];
  showWarehouse?: boolean; // Default: true
  className?: string;
}

interface BreadcrumbItemType {
  label: string;
  href?: string;
  isCurrentPage?: boolean;
}
```

**Usage:**
```typescript
import { WarehouseBreadcrumb } from '@/components/layout/WarehouseBreadcrumb';

// Basic entity list page
<WarehouseBreadcrumb 
  items={[
    { label: "Pallets", isCurrentPage: true }
  ]}
/>

// Entity detail page
<WarehouseBreadcrumb 
  items={[
    { label: "Pallets", href: "/pallets" },
    { label: "Pallet ABC123", isCurrentPage: true }
  ]}
/>

// Settings page (no warehouse context)
<WarehouseBreadcrumb 
  items={[
    { label: "Settings", href: "/settings" },
    { label: "Warehouses", isCurrentPage: true }
  ]}
  showWarehouse={false}
/>
```

### useWarehouseBreadcrumbs Hook
Helper hook for generating common breadcrumb patterns.

**Available Patterns:**
```typescript
const { createBreadcrumbs } = useWarehouseBreadcrumbs();

// Entity list pages
createBreadcrumbs.entityList("Pallets", "/pallets")

// Entity detail pages  
createBreadcrumbs.entityDetail("Pallets", "/pallets", "Pallet ABC123")

// Nested pages
createBreadcrumbs.nested("Settings", "/settings", "Warehouses", "/settings/warehouses")

// Workflow pages
createBreadcrumbs.workflow("Receiving", "/receiving", "Put Away", "PO123")

// Operation pages
createBreadcrumbs.operation("Move Pallet", "Pallets", "ABC123")
```

**Predefined Configurations:**
```typescript
import { BREADCRUMB_CONFIGS } from '@/components/layout/WarehouseBreadcrumb';

// Available configurations
BREADCRUMB_CONFIGS.PALLETS
BREADCRUMB_CONFIGS.LOCATIONS  
BREADCRUMB_CONFIGS.ITEMS
BREADCRUMB_CONFIGS.RECEIVING
BREADCRUMB_CONFIGS.PICKING
BREADCRUMB_CONFIGS.MOVE
BREADCRUMB_CONFIGS.SETTINGS
BREADCRUMB_CONFIGS.WAREHOUSES
```

## Implementation Examples

### Entity List Page Layout
```typescript
// apps/frontend/src/app/pallets/layout.tsx
"use client";

import { WarehouseBreadcrumb, useWarehouseBreadcrumbs, BREADCRUMB_CONFIGS } from "@/components/layout/WarehouseBreadcrumb";

export default function PalletsLayout({ children }) {
  const { createBreadcrumbs } = useWarehouseBreadcrumbs();
  
  const breadcrumbItems = createBreadcrumbs.entityList(
    BREADCRUMB_CONFIGS.PALLETS.entityName,
    BREADCRUMB_CONFIGS.PALLETS.entityPath
  );

  return (
    <div className="container mx-auto max-w-7xl py-6 px-4 md:px-6">
      <WarehouseBreadcrumb items={breadcrumbItems} className="mb-6" />
      {children}
    </div>
  );
}
```

### Entity Detail Page with Server Component Layout
```typescript
// apps/frontend/src/app/pallets/[palletId]/PalletDetailBreadcrumb.tsx
"use client";

import { useParams } from "next/navigation";
import { WarehouseBreadcrumb, useWarehouseBreadcrumbs, BREADCRUMB_CONFIGS } from "@/components/layout/WarehouseBreadcrumb";

export function PalletDetailBreadcrumb() {
  const params = useParams();
  const palletId = params.palletId as string;
  const { createBreadcrumbs } = useWarehouseBreadcrumbs();
  
  const breadcrumbItems = createBreadcrumbs.entityDetail(
    BREADCRUMB_CONFIGS.PALLETS.entityName,
    BREADCRUMB_CONFIGS.PALLETS.entityPath,
    `Pallet ${palletId}`
  );

  return (
    <div className="container mx-auto max-w-7xl py-6 px-4 md:px-6">
      <WarehouseBreadcrumb items={breadcrumbItems} className="mb-6" />
    </div>
  );
}

// apps/frontend/src/app/pallets/[palletId]/layout.tsx
import { PalletDetailBreadcrumb } from "./PalletDetailBreadcrumb";

export default function PalletDetailLayout({ children }) {
  return (
    <>
      <PalletDetailBreadcrumb />
      {children}
    </>
  );
}
```

### Settings Page (No Warehouse Context)
```typescript
<WarehouseBreadcrumb 
  items={[
    { label: "Settings", href: "/settings" },
    { label: "Warehouse Management", isCurrentPage: true }
  ]}
  showWarehouse={false}
  className="mb-8"
/>
```

## Design Patterns

### Warehouse Context Display
- **Header**: Shows warehouse name below page title when warehouse is selected
- **Breadcrumbs**: Includes warehouse context between Dashboard and page-specific breadcrumbs
- **Navigation**: Disables warehouse-dependent items when no warehouse selected

### Responsive Behavior
- **Header**: Text truncation prevents overflow on mobile
- **Navigation**: Warning banner appears above navigation when warehouse required
- **Breadcrumbs**: Responsive text sizing and spacing

### Accessibility
- **Navigation**: Proper ARIA labels and disabled states
- **Breadcrumbs**: Semantic navigation structure with proper roles
- **Header**: Screen reader friendly warehouse context

## Migration Guide

### From Old Breadcrumbs
```typescript
// Before
<Breadcrumb>
  <BreadcrumbList>
    <BreadcrumbItem>
      <BreadcrumbLink href="/settings">Settings</BreadcrumbLink>
    </BreadcrumbItem>
    <BreadcrumbSeparator />
    <BreadcrumbItem>
      <BreadcrumbPage>Warehouses</BreadcrumbPage>
    </BreadcrumbItem>
  </BreadcrumbList>
</Breadcrumb>

// After
<WarehouseBreadcrumb 
  items={[
    { label: "Settings", href: "/settings" },
    { label: "Warehouses", isCurrentPage: true }
  ]}
  showWarehouse={false}
/>
```

### Layout Integration
1. Convert page layouts to client components if using breadcrumbs
2. Add breadcrumb components to layout files
3. Use predefined configurations for consistency
4. Consider warehouse context requirements for each page
