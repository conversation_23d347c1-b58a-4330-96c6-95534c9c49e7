"use client";

import { usePathname } from "next/navigation";
import AppHeader from "@/components/layout/AppHeader";
import PrimaryNav from "@/components/layout/PrimaryNav";

export default function ConditionalLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Hide navigation on auth and onboarding pages
  const hideNavigation =
    pathname.startsWith("/auth") || pathname.startsWith("/onboarding");

  return (
    <div className="flex flex-col h-screen">
      {!hideNavigation && <AppHeader />}
      <main
        className={`flex-1 overflow-y-auto ${!hideNavigation ? "pb-16" : ""}`}
      >
        {children}
      </main>
      {!hideNavigation && <PrimaryNav />}
    </div>
  );
}
