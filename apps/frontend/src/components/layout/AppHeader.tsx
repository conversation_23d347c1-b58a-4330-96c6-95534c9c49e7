"use client";

import React from "react";
import { ChevronLeft, Building2 } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import { usePageTitle } from "@/components/providers/PageTitleContext";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { WarehouseSelector } from "@/components/warehouses/WarehouseSelector";

interface AppHeaderProps {
  // title prop is no longer needed as it comes from context
  showBackButton?: boolean; // Prop to control back button visibility
  showWarehouseContext?: boolean; // Show warehouse name in header
  breadcrumbComponent?: React.ReactNode; // Optional breadcrumb component
}

export default function AppHeader({
  showBackButton,
  showWarehouseContext = true,
  breadcrumbComponent,
}: AppHeaderProps) {
  const { title } = usePageTitle();
  const { currentWarehouse } = useWarehouse();
  const pathname = usePathname();
  const router = useRouter();

  // Determine if back button should be shown based on prop or path
  const displayBackButton =
    showBackButton !== undefined ? showBackButton : pathname !== "/";

  return (
    <header className="sticky top-0 z-40 bg-white border-b border-slate-200">
      {/* Main header row */}
      <div className="h-12 px-4 flex items-center justify-between">
        <div className="flex items-center min-w-0 flex-1">
          {displayBackButton && (
            <button
              onClick={() => router.back()}
              className="mr-2 p-1 rounded-md hover:bg-slate-100 active:bg-slate-200 transition-colors flex-shrink-0"
              aria-label="Go back"
            >
              <ChevronLeft className="size-5 text-slate-700" />
            </button>
          )}

          <div className="min-w-0 flex-1">
            <h1 className="text-lg font-medium text-slate-800 truncate">
              {title}
            </h1>

            {/* Show warehouse context if enabled and warehouse is selected */}
            {showWarehouseContext && currentWarehouse && (
              <div className="flex items-center gap-1 text-xs text-slate-500 mt-0.5">
                <Building2 className="h-3 w-3" />
                <span className="truncate">{currentWarehouse.name}</span>
              </div>
            )}
          </div>
        </div>

        {/* Warehouse selector on the right */}
        <div className="flex items-center flex-shrink-0 ml-4">
          <WarehouseSelector variant="header" />
        </div>
      </div>

      {/* Optional breadcrumb row */}
      {breadcrumbComponent && (
        <div className="px-4 py-2 border-t border-slate-100 bg-slate-50/50">
          {breadcrumbComponent}
        </div>
      )}
    </header>
  );
}
