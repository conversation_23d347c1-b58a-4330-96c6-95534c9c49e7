"use client";

import React from "react";
import Link from "next/link";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";
import { Building2 } from "lucide-react";

interface BreadcrumbItemType {
  label: string;
  href?: string;
  isCurrentPage?: boolean;
}

interface WarehouseBreadcrumbProps {
  items: BreadcrumbItemType[];
  showWarehouse?: boolean;
  className?: string;
}

/**
 * Warehouse-aware breadcrumb component that automatically includes warehouse context
 */
export function WarehouseBreadcrumb({ 
  items, 
  showWarehouse = true, 
  className 
}: WarehouseBreadcrumbProps) {
  const { currentWarehouse } = useWarehouse();

  // Don't show warehouse context if no warehouse is selected or showWarehouse is false
  const shouldShowWarehouse = showWarehouse && currentWarehouse;

  return (
    <Breadcrumb className={className}>
      <BreadcrumbList>
        {/* Always start with Dashboard */}
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link href="/">Dashboard</Link>
          </BreadcrumbLink>
        </BreadcrumbItem>

        {/* Show warehouse context if enabled and warehouse is selected */}
        {shouldShowWarehouse && (
          <>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <div className="flex items-center gap-1.5 text-muted-foreground">
                <Building2 className="h-3.5 w-3.5" />
                <span className="font-medium">{currentWarehouse.name}</span>
              </div>
            </BreadcrumbItem>
          </>
        )}

        {/* Render provided breadcrumb items */}
        {items.map((item, index) => (
          <React.Fragment key={index}>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              {item.isCurrentPage ? (
                <BreadcrumbPage>{item.label}</BreadcrumbPage>
              ) : item.href ? (
                <BreadcrumbLink asChild>
                  <Link href={item.href}>{item.label}</Link>
                </BreadcrumbLink>
              ) : (
                <span className="text-muted-foreground">{item.label}</span>
              )}
            </BreadcrumbItem>
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}

/**
 * Hook to generate common breadcrumb patterns for warehouse-scoped pages
 */
export function useWarehouseBreadcrumbs() {
  const { currentWarehouse } = useWarehouse();

  const createBreadcrumbs = {
    // For main entity pages (pallets, locations, items)
    entityList: (entityName: string, entityPath: string): BreadcrumbItemType[] => [
      { label: entityName, href: entityPath, isCurrentPage: true }
    ],

    // For entity detail pages
    entityDetail: (
      entityName: string, 
      entityPath: string, 
      entityLabel: string
    ): BreadcrumbItemType[] => [
      { label: entityName, href: entityPath },
      { label: entityLabel, isCurrentPage: true }
    ],

    // For nested pages (e.g., settings > warehouses)
    nested: (
      parentName: string,
      parentPath: string,
      childName: string,
      childPath?: string
    ): BreadcrumbItemType[] => [
      { label: parentName, href: parentPath },
      { 
        label: childName, 
        href: childPath,
        isCurrentPage: !childPath 
      }
    ],

    // For workflow pages (e.g., receiving > PO123 > put-away)
    workflow: (
      workflowName: string,
      workflowPath: string,
      stepName: string,
      stepIdentifier?: string
    ): BreadcrumbItemType[] => {
      const items: BreadcrumbItemType[] = [
        { label: workflowName, href: workflowPath }
      ];

      if (stepIdentifier) {
        items.push(
          { label: stepIdentifier, href: `${workflowPath}/${stepIdentifier}` },
          { label: stepName, isCurrentPage: true }
        );
      } else {
        items.push({ label: stepName, isCurrentPage: true });
      }

      return items;
    },

    // For operation pages (e.g., move pallet, picking)
    operation: (
      operationName: string,
      targetEntity?: string,
      targetLabel?: string
    ): BreadcrumbItemType[] => {
      const items: BreadcrumbItemType[] = [];

      if (targetEntity && targetLabel) {
        items.push(
          { label: targetEntity, href: `/${targetEntity.toLowerCase()}` },
          { label: targetLabel, href: `/${targetEntity.toLowerCase()}/${targetLabel}` },
          { label: operationName, isCurrentPage: true }
        );
      } else {
        items.push({ label: operationName, isCurrentPage: true });
      }

      return items;
    }
  };

  return {
    createBreadcrumbs,
    currentWarehouse,
    hasWarehouse: !!currentWarehouse
  };
}

/**
 * Common breadcrumb configurations for different page types
 */
export const BREADCRUMB_CONFIGS = {
  // Main entity pages
  PALLETS: { entityName: "Pallets", entityPath: "/pallets" },
  LOCATIONS: { entityName: "Locations", entityPath: "/locations" },
  ITEMS: { entityName: "Items", entityPath: "/items" },
  
  // Workflow pages
  RECEIVING: { workflowName: "Receiving", workflowPath: "/receiving" },
  PICKING: { workflowName: "Picking", workflowPath: "/picking" },
  MOVE: { workflowName: "Move", workflowPath: "/move" },
  
  // Settings pages
  SETTINGS: { parentName: "Settings", parentPath: "/settings" },
  WAREHOUSES: { childName: "Warehouses", childPath: "/settings/warehouses" },
} as const;
