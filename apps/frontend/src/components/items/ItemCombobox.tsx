"use client";

import * as React from "react";
import { useState, useCallback, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { Check, ChevronsUpDown } from "lucide-react";

import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Item } from "@quildora/types"; // Assuming Item type is available globally or adjust path

// --- Component Props --- //

export interface ItemComboboxProps {
  selectedItemId: string | null;
  onSelect: (itemId: string | null) => void;
  disabled?: boolean;
}

// --- Component --- //

function ItemCombobox({
  selectedItemId,
  onSelect,
  disabled,
}: ItemComboboxProps) {
  const [open, setOpen] = useState(false);
  const { appToken, isLoading: isAuthLoading } = useAuth(); // Get appToken

  const { data: items, isLoading } = useQuery<Item[], Error>({
    queryKey: ["items", appToken], // Add appToken to queryKey for re-fetching on token change
    queryFn: async () => {
      if (!appToken && !isAuthLoading) {
        // If auth is loaded and still no token, throw error or return empty/handle appropriately
        // This prevents attempting a fetch without a token.
        // Consider if you want to throw an error or return an empty array.
        // For a combobox, returning empty and potentially showing a message might be better UX.
        console.warn("ItemCombobox: No appToken available to fetch items.");
        return [];
      }
      if (isAuthLoading) {
        // Still waiting for auth state to resolve, don't fetch yet
        return []; // Or throw a specific error if you prefer useQuery's error handling for this
      }
      // Ensure fetchWithAuth is called only when appToken is available
      return fetchWithAuth("/api/items", { token: appToken });
    },
    enabled: !isAuthLoading && !!appToken, // Only enable if auth has loaded and token exists
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  });

  const currentItem = useMemo(
    () => items?.find((item) => item.id === selectedItemId),
    [items, selectedItemId]
  );

  const handleSelect = useCallback(
    (currentValue: string) => {
      onSelect(currentValue);
      setOpen(false);
    },
    [onSelect]
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          disabled={isLoading || disabled}
          className={cn(
            "w-full justify-between",
            !selectedItemId && "text-muted-foreground"
          )}
        >
          {selectedItemId
            ? currentItem?.name
            : isLoading
            ? "Loading items..."
            : "Select item"}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] max-h-[--radix-popover-content-available-height] p-0">
        <Command>
          <CommandInput placeholder="Search item..." />
          <CommandList>
            <CommandEmpty>
              {isLoading ? "Loading..." : "No item found."}
            </CommandEmpty>
            <CommandGroup>
              {items?.map((item) => (
                <CommandItem
                  key={item.id}
                  value={item.id} // Use ID as the value for selection
                  onSelect={handleSelect}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      selectedItemId === item.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <div>
                    <div>{item.name}</div>
                    {item.sku && (
                      <div className="text-xs text-muted-foreground">
                        SKU: {item.sku}
                      </div>
                    )}
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

// Memoize the component to prevent unnecessary re-renders
const MemoizedItemCombobox = React.memo(
  ItemCombobox,
  (prevProps, nextProps) => {
    return (
      prevProps.selectedItemId === nextProps.selectedItemId &&
      prevProps.disabled === nextProps.disabled &&
      prevProps.onSelect === nextProps.onSelect
    );
  }
);

// Export both named and default for compatibility
export { ItemCombobox, MemoizedItemCombobox };
export default MemoizedItemCombobox;
