"use client";

import React from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { But<PERSON> } from "@/components/ui/button"; // Keep Button if needed for trigger, though often trigger is external

// Define the expected shape of the item data passed to the dialog
interface Item {
  id: string;
  name: string;
  // Add other relevant fields if needed for display or context
}

// Define the expected shape of data returned by the API after delete
// Often, DELETE endpoints return 204 No Content or the deleted object
// Adjust based on your actual backend response
interface DeleteItemResponse {
  // Could be empty or contain the deleted item's data, or be null for 204
  id?: string;
}

export interface DeleteItemDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  item: Item | null; // Item to delete
  onSuccess?: () => void; // Simple success callback
}

export function DeleteItemDialog({
  open,
  onOpenChange,
  item,
  onSuccess,
}: DeleteItemDialogProps) {
  const queryClient = useQueryClient();
  const { appToken } = useAuth(); // Get appToken
  const { currentWarehouse } = useWarehouse();

  const mutation = useMutation<
    DeleteItemResponse | null, // fetchWithAuth can return null for 204
    Error,
    string // Expects item ID as input
  >({
    mutationFn: async (itemId: string) => {
      if (!appToken) {
        // This should ideally prevent the mutation from even being called
        // e.g., by disabling the button if appToken is not available.
        toast.error("Authentication token not found. Please log in.");
        throw new Error("Authentication token not found.");
      }
      // fetchWithAuth handles 204 No Content by returning null
      return fetchWithAuth(
        `/api/items/${itemId}?warehouseId=${currentWarehouse?.id || ""}`,
        {
          method: "DELETE",
          token: appToken,
        }
      );
    },
    onSuccess: (_data, deletedItemId) => {
      // Pass deletedItemId here (already correct)
      toast.success(
        `Item "${item?.name ?? deletedItemId}" deleted successfully.`
      );
      queryClient.invalidateQueries({ queryKey: ["items"] });
      onSuccess?.();
      onOpenChange(false); // Close dialog on success
    },
    onError: (error) => {
      toast.error(error.message || "Failed to delete item.");
    },
  });

  const handleDelete = () => {
    if (!item) return;
    mutation.mutate(item.id);
  };

  return (
    // Ensure item is not null before rendering DialogContent
    <AlertDialog open={open && !!item} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        {item && (
          <>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the
                item "<strong>{item.name}</strong>".
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel
                onClick={() => onOpenChange(false)}
                disabled={mutation.isPending}
              >
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                disabled={mutation.isPending}
                // Optional: Add specific styling for destructive action
                className="bg-red-600 hover:bg-red-700"
              >
                {mutation.isPending ? "Deleting..." : "Delete"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </>
        )}
      </AlertDialogContent>
    </AlertDialog>
  );
}
