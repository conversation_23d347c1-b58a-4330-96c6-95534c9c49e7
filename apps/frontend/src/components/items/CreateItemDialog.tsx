import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Package, RefreshCw } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { Role } from "@quildora/types";
import { useQuery } from "@tanstack/react-query";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";

// Zod schema for form validation
const formSchema = z.object({
  name: z.string().min(1, { message: "Name is required." }),
  sku: z.string().optional(),
  description: z.string().optional(),
  unitOfMeasure: z.string().optional(),
  defaultCost: z.coerce.number().optional(),
  lowStockThreshold: z.coerce
    .number()
    .int("Low stock threshold must be a whole number")
    .optional(),
  status: z.string(),
  warehouseIds: z.array(z.string()).optional(),
});

type ItemFormData = z.infer<typeof formSchema>;

// Define the expected shape of data returned by the API (adjust as needed)
interface CreateItemResponse {
  id: string;
  name: string;
  // ... other fields returned by backend
}

// API function to create an item
async function createItemApi(
  itemData: ItemFormData,
  appToken: string | null
): Promise<CreateItemResponse> {
  console.log(
    "[CreateItemDialog] createItemApi - itemData received:",
    JSON.stringify(itemData)
  ); // LOG #2
  if (!appToken) {
    throw new Error("Authentication token not found.");
  }
  const requestBody = JSON.stringify(itemData);
  console.log(
    "[CreateItemDialog] createItemApi - requestBody to be sent:",
    requestBody
  ); // LOG #3
  return fetchWithAuth("/api/items", {
    method: "POST",
    body: requestBody,
    token: appToken,
  });
}

export interface CreateItemDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: (newItem: CreateItemResponse) => void;
}

export function CreateItemDialog({
  open,
  onOpenChange,
  onSuccess,
}: CreateItemDialogProps) {
  const queryClient = useQueryClient();
  const { appToken, appUser } = useAuth();

  // Fetch associated warehouses for the ADMIN user
  const { data: warehouses, isLoading: isLoadingWarehouses } = useQuery<
    { id: string; name: string }[]
  >({
    queryKey: ["warehouses", "associated", appUser?.id],
    queryFn: async () => {
      if (!appToken) throw new Error("Not authenticated");
      return fetchWithAuth("/api/warehouses", { token: appToken });
    },
    enabled: !!appToken && !!appUser && open,
  });

  const form = useForm<ItemFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      sku: "",
      description: "",
      unitOfMeasure: "",
      defaultCost: undefined,
      lowStockThreshold: undefined,
      status: "ACTIVE",
      warehouseIds: [],
    },
  });

  const mutation = useMutation<CreateItemResponse, Error, ItemFormData>({
    mutationFn: (data) => createItemApi(data, appToken),
    onSuccess: (data) => {
      toast.success("Item created successfully.");
      queryClient.invalidateQueries({ queryKey: ["items"] });
      onSuccess?.(data);
      onOpenChange(false);
      form.reset();
    },
    onError: (error) => {
      toast.error(error.message || "Failed to create item.");
    },
  });

  function onSubmit(values: ItemFormData) {
    console.log(
      "[CreateItemDialog] onSubmit - form values:",
      JSON.stringify(values)
    ); // LOG #1
    mutation.mutate(values);
  }

  // Reset form when dialog reopens
  React.useEffect(() => {
    if (open) {
      form.reset();
    }
  }, [open, form]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Create New Item
          </DialogTitle>
          <DialogDescription>
            Fill in the details for the new item. All fields except name are
            optional.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-semibold">
                    Item Name *
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Widget Type A" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="sku"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>SKU (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., WIDGET-A-001" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the item..."
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="unitOfMeasure"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Unit of Measure (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., kg, lbs, each" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="defaultCost"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Default Cost (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="e.g., 10.99"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lowStockThreshold"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Low Stock Threshold (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="0"
                      placeholder="e.g., 5"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., ACTIVE, INACTIVE" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {appUser?.role === Role.TENANT_ADMIN && (
              <FormItem>
                <FormLabel className="text-base font-semibold">
                  Associate with Warehouses (Optional)
                </FormLabel>
                <ScrollArea className="h-32 w-full rounded-md border p-3">
                  {isLoadingWarehouses ? (
                    <p className="text-sm text-muted-foreground">
                      Loading warehouses...
                    </p>
                  ) : warehouses && warehouses.length > 0 ? (
                    warehouses.map((warehouse) => (
                      <FormField
                        key={warehouse.id}
                        control={form.control}
                        name="warehouseIds"
                        render={({ field }) => {
                          return (
                            <FormItem
                              key={warehouse.id}
                              className="flex flex-row items-start space-x-3 space-y-0 my-3"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(warehouse.id)}
                                  onCheckedChange={(checked: boolean) => {
                                    return checked
                                      ? field.onChange([
                                          ...(field.value || []),
                                          warehouse.id,
                                        ])
                                      : field.onChange(
                                          (field.value || []).filter(
                                            (value) => value !== warehouse.id
                                          )
                                        );
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="font-normal text-sm">
                                {warehouse.name}
                              </FormLabel>
                            </FormItem>
                          );
                        }}
                      />
                    ))
                  ) : (
                    <p className="text-sm text-muted-foreground">
                      No warehouses associated with your account, or unable to
                      load them.
                    </p>
                  )}
                </ScrollArea>
                <FormMessage />
              </FormItem>
            )}
            <DialogFooter className="gap-2 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={mutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={mutation.isPending}
                className="min-w-[120px]"
              >
                {mutation.isPending ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Package className="mr-2 h-4 w-4" />
                    Create Item
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
