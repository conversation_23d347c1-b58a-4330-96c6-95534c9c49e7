"use client"; // Required for hooks like useState, useForm, useMutation

import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { useQuery } from "@tanstack/react-query";
import { ScrollArea } from "@/components/ui/scroll-area";
// Checkbox might not be needed if warehouseIds is removed

// Define the expected shape of the item data passed to the dialog
interface Item {
  id: string;
  name: string;
  sku?: string | null;
  description?: string | null;
  unitOfMeasure?: string | null;
  defaultCost?: number | null;
  lowStockThreshold?: number | null;
  status?: string | null;
  createdAt?: string; // Added for compatibility
  updatedAt?: string; // Added for compatibility
  // warehouseIds?: string[]; // Removed for now
}

// Zod schema for form validation
const formSchema = z.object({
  name: z.string().min(1, { message: "Name is required." }),
  sku: z.string().optional().nullable(),
  description: z.string().optional().nullable(),
  unitOfMeasure: z.string().optional().nullable(),
  // Rely on onChange in Input to convert empty string to null for numeric fields
  defaultCost: z
    .number()
    .positive({ message: "Cost must be a positive number." })
    .nullable()
    .optional(),
  lowStockThreshold: z
    .number()
    .int()
    .nonnegative({ message: "Threshold must be a non-negative integer." })
    .nullable()
    .optional(),
  status: z.string().optional().nullable(), // TODO: Consider z.enum if statuses are fixed
  // warehouseIds: z.array(z.string()).optional(), // Removed for now
});

type ItemFormData = z.infer<typeof formSchema>;

// Define the expected shape of data returned by the API after update
interface UpdateItemResponse extends Item {}

// API function to update an item
async function updateItemApi(
  {
    id,
    itemData,
    warehouseId,
  }: {
    id: string;
    itemData: ItemFormData;
    warehouseId: string;
  },
  appToken: string | null
): Promise<UpdateItemResponse> {
  if (!appToken) {
    throw new Error("Authentication token not found.");
  }
  return fetchWithAuth(`/api/items/${id}?warehouseId=${warehouseId}`, {
    method: "PATCH",
    body: JSON.stringify(itemData),
    token: appToken,
  });
}

export interface EditItemDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  item: Item | null; // Item to edit, null if none selected
  onSuccess?: (updatedItem: UpdateItemResponse) => void;
}

export function EditItemDialog({
  open,
  onOpenChange,
  item,
  onSuccess,
}: EditItemDialogProps) {
  const queryClient = useQueryClient();
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();
  // Removed warehouse fetching logic

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    mode: "all",
    defaultValues: {
      name: item?.name ?? "",
      sku: item?.sku ?? "",
      description: item?.description ?? "",
      unitOfMeasure: item?.unitOfMeasure ?? "",
      defaultCost: item?.defaultCost ?? undefined,
      lowStockThreshold: item?.lowStockThreshold ?? undefined,
      status: item?.status ?? "",
      // warehouseIds: [], // Removed
    },
  });

  // Update form default values when the item prop changes
  useEffect(() => {
    if (item) {
      form.reset({
        name: item.name,
        sku: item.sku ?? "",
        description: item.description ?? "",
        unitOfMeasure: item.unitOfMeasure ?? "",
        defaultCost: item.defaultCost ?? undefined,
        lowStockThreshold: item.lowStockThreshold ?? undefined,
        status: item.status ?? "",
        // warehouseIds: item.warehouseIds ?? [], // Removed
      });
    } else {
      // Optionally reset if dialog opens without an item (e.g., error case)
      form.reset({
        name: "",
        sku: "",
        description: "",
        unitOfMeasure: "",
        defaultCost: undefined,
        lowStockThreshold: undefined,
        status: "",
        // warehouseIds: [], // Removed
      });
    }
  }, [item, form]);

  const mutation = useMutation<
    UpdateItemResponse,
    Error,
    { id: string; itemData: ItemFormData } // Pass id and data
  >({
    mutationFn: (data) =>
      updateItemApi(
        { ...data, warehouseId: currentWarehouse?.id || "" },
        appToken
      ),
    onSuccess: (data) => {
      toast.success("Item updated successfully.");
      // Invalidate cache for the specific item AND the list
      queryClient.invalidateQueries({ queryKey: ["items", data.id] });
      queryClient.invalidateQueries({ queryKey: ["items"] });
      onSuccess?.(data);
      onOpenChange(false); // Close dialog on success
    },
    onError: (error) => {
      toast.error(error.message || "Failed to update item.");
    },
  });

  function onSubmit(values: ItemFormData) {
    if (!item) return; // Should not happen if dialog is open with an item
    mutation.mutate({ id: item.id, itemData: values });
  }

  // Handle closing the dialog cleanly
  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      // Reset form state if closing? Optional, depends on desired UX
      // form.reset(); // Resetting here might clear fields momentarily before closing animation finishes
    }
    onOpenChange(isOpen);
  };

  return (
    // Ensure item is not null before rendering DialogContent to prevent errors during initial render or if item becomes null
    <Dialog open={open && !!item} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        {item && ( // Conditionally render content based on item existence
          <>
            <DialogHeader>
              <DialogTitle>Edit Item</DialogTitle>
              <DialogDescription>
                Update the details for "{item.name}".
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Widget Type A" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="sku"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SKU (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g., WGT-001"
                          {...field}
                          value={field.value ?? ""} // Handle null/undefined for controlled input
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Detailed description of the item"
                          {...field}
                          value={field.value ?? ""} // Handle null/undefined
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="unitOfMeasure"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Unit of Measure (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g., PCS, KG, BOX"
                          {...field}
                          value={field.value ?? ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="defaultCost"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Default Cost (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="e.g., 10.99"
                          {...field}
                          value={
                            field.value !== null && field.value !== undefined
                              ? String(field.value)
                              : ""
                          }
                          onChange={(event) =>
                            field.onChange(
                              event.target.value === ""
                                ? null
                                : Number(event.target.value)
                            )
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="lowStockThreshold"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Low Stock Threshold (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="e.g., 10"
                          {...field}
                          value={
                            field.value !== null && field.value !== undefined
                              ? String(field.value)
                              : ""
                          }
                          onChange={(event) =>
                            field.onChange(
                              event.target.value === ""
                                ? null
                                : Number(event.target.value)
                            )
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status (Optional)</FormLabel>
                      {/* TODO: Replace with a Select component if statuses are predefined */}
                      <FormControl>
                        <Input
                          placeholder="e.g., Active, Discontinued"
                          {...field}
                          value={field.value ?? ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => handleOpenChange(false)}
                    disabled={mutation.isPending}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={mutation.isPending}>
                    {mutation.isPending ? "Saving..." : "Save Changes"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
