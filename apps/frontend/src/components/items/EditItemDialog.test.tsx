import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@/lib/test-utils';
import { EditItemDialog } from './EditItemDialog';
import * as api from '@/lib/api';
import { toast } from 'sonner';

// Mock dependencies
vi.mock('@/lib/api');
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

const mockedFetchWithAuth = vi.mocked(api.fetchWithAuth);
const mockedToast = vi.mocked(toast);

const mockItem = {
  id: 'item123',
  name: 'Test Item',
  sku: 'SKU123',
  description: 'A test item description.',
  unitOfMeasure: 'PCS',
  defaultCost: 10.99,
  lowStockThreshold: 5,
  status: 'Active',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

describe('EditItemDialog', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with item data and allows editing', () => {
    render(
      <EditItemDialog
        open={true}
        onOpenChange={() => {}}
        item={mockItem}
        onSuccess={() => {}}
      />
    );

    // Check pre-filled values
    expect(screen.getByLabelText(/name/i)).toHaveValue(mockItem.name);
    expect(screen.getByLabelText(/sku/i)).toHaveValue(mockItem.sku!);
    expect(screen.getByLabelText(/description/i)).toHaveValue(mockItem.description!);
    expect(screen.getByLabelText(/unit of measure/i)).toHaveValue(mockItem.unitOfMeasure!);
    expect(screen.getByLabelText(/default cost/i)).toHaveValue(String(mockItem.defaultCost));
    expect(screen.getByLabelText(/low stock threshold/i)).toHaveValue(String(mockItem.lowStockThreshold));

    // Edit a field
    const nameInput = screen.getByLabelText(/name/i);
    fireEvent.change(nameInput, { target: { value: 'Updated Item Name' } });
    expect(nameInput).toHaveValue('Updated Item Name');
  });

  it('submits the form and calls onSuccess', async () => {
    mockedFetchWithAuth.mockResolvedValue({ ok: true, json: () => Promise.resolve({}) } as Response);
    const onSuccess = vi.fn();

    render(
      <EditItemDialog
        open={true}
        onOpenChange={() => {}}
        item={mockItem}
        onSuccess={onSuccess}
      />
    );

    // Change a value and submit
    fireEvent.change(screen.getByLabelText(/name/i), { target: { value: 'New Name' } });
    fireEvent.click(screen.getByRole('button', { name: /save changes/i }));

    await waitFor(() => {
      expect(mockedFetchWithAuth).toHaveBeenCalledWith(`/api/items/${mockItem.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...mockItem,
          name: 'New Name',
          // Ensure numeric values are numbers, not strings from the form
          defaultCost: 10.99,
          lowStockThreshold: 5,
        }),
      });
      expect(onSuccess).toHaveBeenCalledTimes(1);
      expect(mockedToast.success).toHaveBeenCalledWith('Item Updated',
        { description: 'The item details have been successfully updated.' });
    });
  });

  it('shows an error toast on submission failure', async () => {
    mockedFetchWithAuth.mockRejectedValue(new Error('API Error'));
    const onSuccess = vi.fn();

    render(
      <EditItemDialog
        open={true}
        onOpenChange={() => {}}
        item={mockItem}
        onSuccess={onSuccess}
      />
    );

    fireEvent.click(screen.getByRole('button', { name: /save changes/i }));

    await waitFor(() => {
      expect(mockedFetchWithAuth).toHaveBeenCalledTimes(1);
      expect(onSuccess).not.toHaveBeenCalled();
      expect(mockedToast.error).toHaveBeenCalledWith('API Error');
    });
  });

  it('shows validation error for empty name', async () => {
    render(
      <EditItemDialog
        open={true}
        onOpenChange={() => {}}
        item={mockItem}
        onSuccess={() => {}}
      />
    );
    const nameInput = screen.getByLabelText(/name/i);
    fireEvent.change(nameInput, { target: { value: '' } });
    fireEvent.blur(nameInput); // Trigger validation

    expect(await screen.findByText('Name is required.')).toBeInTheDocument();
  });
});
