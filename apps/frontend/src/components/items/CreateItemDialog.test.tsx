import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@/lib/test-utils';
import { CreateItemDialog } from './CreateItemDialog';
import * as api from '@/lib/api';

// Mock the fetchWithAuth API call
vi.mock('@/lib/api');

describe('CreateItemDialog', () => {
  it('should render the dialog and submit the form', async () => {
    const handleSuccess = vi.fn();
    const mockedFetchWithAuth = vi.mocked(api.fetchWithAuth);
    // Mock a successful response for the POST request
    mockedFetchWithAuth.mockResolvedValue({ ok: true, json: () => Promise.resolve({}) } as Response);

    render(
      <CreateItemDialog
        open={true}
        onOpenChange={() => {}}
        onSuccess={handleSuccess}
      />
    );

    // Check if essential elements are present
    expect(screen.getByRole('heading', { name: /create new item/i })).toBeInTheDocument();
    const nameInput = screen.getByLabelText(/name/i);
    const skuInput = screen.getByLabelText(/sku/i);
    const descriptionInput = screen.getByLabelText(/description/i);
    const submitButton = screen.getByRole('button', { name: /create item/i });

    // Simulate user input
    fireEvent.change(nameInput, { target: { value: 'Test Item Name' } });
    fireEvent.change(skuInput, { target: { value: 'TEST-SKU-123' } });
    fireEvent.change(descriptionInput, {
      target: { value: 'This is a test description.' },
    });

    // Simulate form submission
    fireEvent.click(submitButton);

    // Wait for the API call
    await waitFor(() => {
      expect(mockedFetchWithAuth).toHaveBeenCalledTimes(1);
    });

    // Check if the API was called with the correct data
    expect(mockedFetchWithAuth).toHaveBeenCalledWith('/api/items', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'Test Item Name',
        sku: 'TEST-SKU-123',
        description: 'This is a test description.',
        defaultCost: 0,
        lowStockThreshold: 0,
        unitOfMeasure: '',
        status: 'ACTIVE',
      }),
    });

    // Check if the onSuccess callback was called
    await waitFor(() => {
      expect(handleSuccess).toHaveBeenCalledTimes(1);
    });
  });

  // Add more tests:
  // - Test validation (e.g., required name field)
  // - Test closing the dialog
  // - Test error handling from API
});
