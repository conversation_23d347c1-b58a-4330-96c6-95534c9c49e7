"use client";

import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Building2, 
  Loader2, 
  Alert<PERSON>riangle, 
  Refresh<PERSON>w,
  Shield,
  CheckCircle
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useAppLoading } from "@/hooks/useAppLoading";

interface AppLoadingScreenProps {
  className?: string;
  showProgress?: boolean;
  showDetails?: boolean;
}

/**
 * Unified loading screen that handles both auth and warehouse loading
 * Replaces sequential "Authenticating" → "Loading warehouses" with single smooth experience
 */
export function AppLoadingScreen({ 
  className,
  showProgress = true,
  showDetails = false
}: AppLoadingScreenProps) {
  const {
    isLoading,
    isReady,
    loadingMessage,
    progress,
    hasError,
    error,
    canRetry,
    retry,
  } = useAppLoading();

  // Don't render if app is ready
  if (isReady) {
    return null;
  }

  // Error state
  if (hasError && error) {
    return (
      <div className={cn("flex items-center justify-center min-h-[400px]", className)}>
        <Card className="w-full max-w-md mx-4">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="rounded-full bg-destructive/10 p-3">
                <AlertTriangle className="h-8 w-8 text-destructive" />
              </div>
            </div>
            <CardTitle>Failed to Load Application</CardTitle>
            <CardDescription>
              There was an error loading your workspace
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            
            {canRetry && (
              <Button onClick={retry} className="w-full">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className={cn("flex items-center justify-center min-h-[400px]", className)}>
        <Card className="w-full max-w-md mx-4">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="relative">
                {progress < 50 ? (
                  <Shield className="h-12 w-12 text-primary" />
                ) : (
                  <Building2 className="h-12 w-12 text-primary" />
                )}
                <Loader2 className="h-6 w-6 text-primary animate-spin absolute -bottom-1 -right-1" />
              </div>
            </div>
            <CardTitle>Loading Quildora</CardTitle>
            <CardDescription>
              {loadingMessage}
            </CardDescription>
          </CardHeader>
          
          {showProgress && (
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>{loadingMessage}</span>
                  <span>{Math.round(progress)}%</span>
                </div>
                <div className="w-full bg-secondary rounded-full h-2">
                  <div 
                    className="bg-primary h-2 rounded-full transition-all duration-500 ease-out"
                    style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
                  />
                </div>
              </div>
              
              {showDetails && (
                <div className="space-y-2 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    {progress >= 50 ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    )}
                    <span>Authentication</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {progress >= 100 ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : progress >= 50 ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <div className="h-4 w-4 rounded-full border-2 border-muted" />
                    )}
                    <span>Workspace Setup</span>
                  </div>
                </div>
              )}
            </CardContent>
          )}
        </Card>
      </div>
    );
  }

  return null;
}

/**
 * Compact loading indicator for inline use
 */
export function AppLoadingIndicator({ 
  className 
}: { 
  className?: string 
}) {
  const { isLoading, loadingMessage, progress } = useAppLoading();

  if (!isLoading) {
    return null;
  }

  return (
    <div className={cn("flex items-center gap-2 text-sm text-muted-foreground", className)}>
      <Loader2 className="h-4 w-4 animate-spin" />
      <span>{loadingMessage}</span>
      <span className="text-xs">({Math.round(progress)}%)</span>
    </div>
  );
}
