import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle } from "lucide-react";

interface PutAwayCompletionProps {
  isComplete: boolean;
  totalPallets: number;
}

/**
 * Reusable component for displaying put-away completion status
 * Shows celebration message when all pallets are successfully moved
 */
export const PutAwayCompletion: React.FC<PutAwayCompletionProps> = ({
  isComplete,
  totalPallets,
}) => {
  if (!isComplete || totalPallets === 0) {
    return null;
  }

  return (
    <Card className="border-green-200 bg-green-50">
      <CardContent className="flex items-center justify-center py-8">
        <div className="text-center">
          <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-green-800">
            Put-Away Complete!
          </h3>
          <p className="text-green-600">
            All pallets have been successfully moved to their storage locations.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
