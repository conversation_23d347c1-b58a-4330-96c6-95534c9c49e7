import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

interface PutAwayOverviewProps {
  purchaseOrderNumber: string;
  totalPallets: number;
  pendingPallets: number;
  completedPallets: number;
  onBackToSummary: () => void;
  onCompleteShipment?: () => void;
  canCompleteShipment?: boolean;
  isCompletingShipment?: boolean;
}

/**
 * Reusable component for displaying put-away workflow overview
 * Shows progress statistics and workflow control buttons
 */
export const PutAwayOverview: React.FC<PutAwayOverviewProps> = ({
  purchaseOrderNumber,
  totalPallets,
  pendingPallets,
  completedPallets,
  onBackToSummary,
  onCompleteShipment,
  canCompleteShipment = false,
  isCompletingShipment = false,
}) => {
  return (
    <>
      {/* Header with navigation and actions */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Put-Away Workflow
          </h1>
          <p className="text-muted-foreground">PO: {purchaseOrderNumber}</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onBackToSummary}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Summary
          </Button>
          {canCompleteShipment && onCompleteShipment && (
            <Button
              onClick={onCompleteShipment}
              disabled={isCompletingShipment}
            >
              {isCompletingShipment ? "Completing..." : "Complete Shipment"}
            </Button>
          )}
        </div>
      </div>

      {/* Progress Overview Card */}
      <Card>
        <CardHeader>
          <CardTitle>Progress Overview</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-3">
          <div>
            <p className="text-sm font-medium text-muted-foreground">
              Total Pallets
            </p>
            <p className="text-2xl font-bold">{totalPallets}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-muted-foreground">Pending</p>
            <p className="text-2xl font-bold text-orange-600">
              {pendingPallets}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-muted-foreground">
              Completed
            </p>
            <p className="text-2xl font-bold text-green-600">
              {completedPallets}
            </p>
          </div>
        </CardContent>
      </Card>
    </>
  );
};
