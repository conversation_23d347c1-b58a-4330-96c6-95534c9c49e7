import React from "react";
import { PalletMoveCard } from "./PalletMoveCard";
import { Location } from "@quildora/types";

// Define the pallet type for put-away operations
type PutAwayPallet = {
  id: string;
  label?: string | null;
  barcode?: string | null;
  status: string;
  shipToDestination?: string | null;
  location?: Location | null;
};

interface PutAwaySectionProps {
  title: string;
  pallets: PutAwayPallet[];
  onMovePallet: (palletId: string, hasLocation: boolean) => void;
  movingPallets: Set<string>;
  variant?: "pending" | "completed";
  titleColor?: string;
}

/**
 * Reusable component for displaying sections of pallets in put-away workflow
 * Shows grouped pallets with consistent styling and interactions
 */
export const PutAwaySection: React.FC<PutAwaySectionProps> = ({
  title,
  pallets,
  onMovePallet,
  movingPallets,
  variant = "pending",
  titleColor,
}) => {
  if (pallets.length === 0) {
    return null;
  }

  const titleClassName = titleColor 
    ? `text-2xl font-bold tracking-tight mb-4 ${titleColor}`
    : "text-2xl font-bold tracking-tight mb-4";

  return (
    <div>
      <h2 className={titleClassName}>
        {title} ({pallets.length})
      </h2>
      <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
        {pallets.map((pallet) => (
          <PalletMoveCard
            key={pallet.id}
            pallet={pallet}
            onMovePallet={onMovePallet}
            isMoving={movingPallets.has(pallet.id)}
            variant={variant}
          />
        ))}
      </div>
    </div>
  );
};
