import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Loader2, Package, CheckCircle } from "lucide-react";
import { Location } from "@quildora/types";

// Define the pallet type for put-away operations
type PutAwayPallet = {
  id: string;
  label?: string | null;
  barcode?: string | null;
  status: string;
  shipToDestination?: string | null;
  location?: Location | null;
};

interface PalletMoveCardProps {
  pallet: PutAwayPallet;
  onMovePallet: (palletId: string, hasLocation: boolean) => void;
  isMoving: boolean;
  isCompleted?: boolean;
  variant?: "pending" | "completed";
}

/**
 * Reusable component for displaying individual pallet move operations
 * Shows pallet information and move/completion status
 */
export const PalletMoveCard: React.FC<PalletMoveCardProps> = ({
  pallet,
  onMovePallet,
  isMoving,
  isCompleted = false,
  variant = "pending",
}) => {
  const getBadgeVariant = (
    status: string
  ): "default" | "secondary" | "destructive" | "outline" => {
    switch (status.toLowerCase()) {
      case "received":
      case "stored":
        return "default";
      case "receiving":
      case "processing":
        return "secondary";
      case "damaged":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const hasStorageLocation = pallet.location?.category === "Storage";

  const cardClassName = variant === "completed" 
    ? "border-green-200 bg-green-50" 
    : "border-orange-200";

  return (
    <Card className={cardClassName}>
      <CardHeader>
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg">
            {pallet.label || `Pallet ${pallet.barcode}`}
          </CardTitle>
          {variant === "completed" ? (
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <Badge variant="default">Stored</Badge>
            </div>
          ) : (
            <Badge variant={getBadgeVariant(pallet.status)}>
              {pallet.status}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <p className="text-sm font-medium text-muted-foreground">
            Destination
          </p>
          <p className="text-sm">
            {pallet.shipToDestination || "Unknown"}
          </p>
        </div>

        <div>
          <p className="text-sm font-medium text-muted-foreground">
            {variant === "completed" ? "Final Location" : "Assigned Location"}
          </p>
          <p className="text-sm">
            {pallet.location?.name || 
             (variant === "completed" ? "Location updated" : "No location assigned")}
          </p>
        </div>

        {variant === "pending" && (
          <Button
            onClick={() => onMovePallet(pallet.id, hasStorageLocation)}
            disabled={isMoving || !hasStorageLocation}
            className="w-full"
          >
            {isMoving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Moving...
              </>
            ) : hasStorageLocation ? (
              <>
                <Package className="h-4 w-4 mr-2" />
                Confirm Move
              </>
            ) : (
              "Assign Location First"
            )}
          </Button>
        )}
      </CardContent>
    </Card>
  );
};
