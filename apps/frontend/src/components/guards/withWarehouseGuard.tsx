"use client";

import { ComponentType, ReactNode } from "react";
import { WarehouseRouteGuard } from "./WarehouseRouteGuard";
import { Role } from "@quildora/types";

interface WithWarehouseGuardOptions {
  requireWarehouse?: boolean;
  requiredRole?: Role;
  allowedRoles?: Role[];
  redirectTo?: string;
  fallbackComponent?: ReactNode;
}

/**
 * Higher-order component that wraps a component with warehouse route protection
 */
export function withWarehouseGuard<P extends object>(
  WrappedComponent: ComponentType<P>,
  options: WithWarehouseGuardOptions = {}
) {
  const GuardedComponent = (props: P) => {
    return (
      <WarehouseRouteGuard {...options}>
        <WrappedComponent {...props} />
      </WarehouseRouteGuard>
    );
  };

  // Set display name for debugging
  GuardedComponent.displayName = `withWarehouseGuard(${
    WrappedComponent.displayName || WrappedComponent.name || "Component"
  })`;

  return GuardedComponent;
}

/**
 * HOC for pages that require warehouse selection
 */
export function withWarehouseRequired<P extends object>(
  WrappedComponent: ComponentType<P>
) {
  return withWarehouseGuard(WrappedComponent, { requireWarehouse: true });
}

/**
 * HOC for admin-only pages
 */
export function withAdminOnly<P extends object>(
  WrappedComponent: ComponentType<P>
) {
  return withWarehouseGuard(WrappedComponent, { 
    requiredRole: Role.TENANT_ADMIN 
  });
}

/**
 * HOC for manager-only pages
 */
export function withManagerOnly<P extends object>(
  WrappedComponent: ComponentType<P>
) {
  return withWarehouseGuard(WrappedComponent, { 
    requiredRole: Role.WAREHOUSE_MANAGER 
  });
}

/**
 * HOC for warehouse member pages (requires warehouse + member role)
 */
export function withWarehouseMember<P extends object>(
  WrappedComponent: ComponentType<P>
) {
  return withWarehouseGuard(WrappedComponent, { 
    requireWarehouse: true,
    requiredRole: Role.WAREHOUSE_MEMBER 
  });
}

/**
 * HOC for warehouse manager pages (requires warehouse + manager role)
 */
export function withWarehouseManager<P extends object>(
  WrappedComponent: ComponentType<P>
) {
  return withWarehouseGuard(WrappedComponent, { 
    requireWarehouse: true,
    requiredRole: Role.WAREHOUSE_MANAGER 
  });
}

/**
 * HOC for pages that allow multiple roles
 */
export function withRoles<P extends object>(
  WrappedComponent: ComponentType<P>,
  allowedRoles: Role[],
  requireWarehouse = false
) {
  return withWarehouseGuard(WrappedComponent, { 
    allowedRoles,
    requireWarehouse 
  });
}
