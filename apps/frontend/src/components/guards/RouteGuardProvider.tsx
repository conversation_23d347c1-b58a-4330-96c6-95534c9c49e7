"use client";

import { ReactNode } from "react";
import { WarehouseRouteGuard } from "./WarehouseRouteGuard";

interface RouteGuardProviderProps {
  children: ReactNode;
}

/**
 * Provider component that automatically applies route guards based on the current pathname
 * This component should be placed in the layout to protect all routes automatically
 */
export function RouteGuardProvider({ children }: RouteGuardProviderProps) {
  // The WarehouseRouteGuard component automatically determines requirements
  // based on the pathname, so we just need to wrap all routes with it
  return <WarehouseRouteGuard>{children}</WarehouseRouteGuard>;
}
