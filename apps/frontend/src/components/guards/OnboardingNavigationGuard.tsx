"use client";

import React, { useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import { OnboardingStep, ONBOARDING_ROUTES } from "@quildora/types";
import { useOnboarding } from "@/components/providers/onboarding-provider";
import { toast } from "sonner";

interface OnboardingNavigationGuardProps {
  children: React.ReactNode;
  allowedSteps?: OnboardingStep[];
  requiredStep?: OnboardingStep;
  redirectOnInvalid?: string;
}

/**
 * Navigation guard specifically for onboarding flow
 * Prevents users from accessing onboarding pages in invalid states
 */
export function OnboardingNavigationGuard({
  children,
  allowedSteps,
  requiredStep,
  redirectOnInvalid = "/auth/welcome",
}: OnboardingNavigationGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { currentStep, sessionData, error, recoveryAvailable } =
    useOnboarding();
  const [isValidating, setIsValidating] = useState(true);
  const [isValid, setIsValid] = useState(false);

  useEffect(() => {
    const validateNavigation = () => {
      setIsValidating(true);

      // Find the current route configuration
      const currentRoute = ONBOARDING_ROUTES.find(
        (route) => pathname === route.path
      );

      if (!currentRoute) {
        // Not an onboarding route, allow access
        setIsValid(true);
        setIsValidating(false);
        return;
      }

      // Check if user has an active onboarding session
      if (!sessionData.sessionId) {
        console.warn("No active onboarding session found");
        toast.error("Please start the onboarding process from the beginning");
        router.push("/auth/welcome");
        return;
      }

      // Check if there's an unrecoverable error
      if (error && !recoveryAvailable) {
        console.warn("Unrecoverable onboarding error:", error);
        toast.error(
          "There was an issue with your onboarding. Please start over."
        );
        router.push("/auth/welcome");
        return;
      }

      // Check step-specific requirements
      if (requiredStep && currentStep !== requiredStep) {
        console.warn(
          `Invalid step access. Required: ${requiredStep}, Current: ${currentStep}`
        );
        redirectToCorrectStep(currentStep);
        return;
      }

      // Check allowed steps
      if (allowedSteps && !allowedSteps.includes(currentStep)) {
        console.warn(
          `Step not allowed. Allowed: ${allowedSteps.join(
            ", "
          )}, Current: ${currentStep}`
        );
        redirectToCorrectStep(currentStep);
        return;
      }

      // Check if current step allows access to this route
      if (currentRoute.step !== currentStep) {
        // Check if user is trying to access a future step
        const currentRouteIndex = ONBOARDING_ROUTES.findIndex(
          (r) => r.step === currentStep
        );
        const targetRouteIndex = ONBOARDING_ROUTES.findIndex(
          (r) => r.step === currentRoute.step
        );

        if (targetRouteIndex > currentRouteIndex) {
          console.warn(
            `Cannot access future step. Current: ${currentStep}, Target: ${currentRoute.step}`
          );
          toast.error("Please complete the current step before proceeding");
          redirectToCorrectStep(currentStep);
          return;
        }

        // Check if the route allows access from current step
        if (!currentRoute.allowedFromSteps.includes(currentStep)) {
          console.warn(
            `Route does not allow access from current step. Current: ${currentStep}, Route: ${currentRoute.step}`
          );
          redirectToCorrectStep(currentStep);
          return;
        }
      }

      // Validate required data for current step
      const validationResult = validateStepData(currentRoute.step);
      if (!validationResult.isValid) {
        console.warn(
          `Missing required data for step ${currentRoute.step}:`,
          validationResult.missingData
        );
        toast.error(
          `Missing required information: ${validationResult.missingData.join(
            ", "
          )}`
        );
        redirectToCorrectStep(findEarliestIncompleteStep());
        return;
      }

      // All checks passed
      setIsValid(true);
      setIsValidating(false);
    };

    validateNavigation();
  }, [
    pathname,
    currentStep,
    sessionData,
    error,
    recoveryAvailable,
    requiredStep,
    allowedSteps,
    router,
  ]);

  const redirectToCorrectStep = (step: OnboardingStep) => {
    const targetRoute = ONBOARDING_ROUTES.find((route) => route.step === step);
    if (targetRoute) {
      router.push(targetRoute.path);
    } else {
      router.push(redirectOnInvalid);
    }
  };

  const validateStepData = (
    step: OnboardingStep
  ): { isValid: boolean; missingData: string[] } => {
    const missingData: string[] = [];

    switch (step) {
      case "business_info":
        // No prerequisites for first step
        break;

      case "admin_account":
        if (!sessionData.businessInfo?.companyName)
          missingData.push("Company name");
        if (!sessionData.businessInfo?.industry) missingData.push("Industry");
        break;

      case "warehouse_setup":
        if (!sessionData.adminAccount?.email) missingData.push("Admin email");
        if (!sessionData.adminAccount?.fullName)
          missingData.push("Admin full name");
        break;

      case "team_setup":
        if (!sessionData.warehouseSetup?.warehouseName)
          missingData.push("Warehouse name");
        if (!sessionData.warehouseSetup?.warehouseType)
          missingData.push("Warehouse type");
        break;

      case "completion":
        // Team setup is optional, so no validation needed
        break;

      default:
        console.warn(`Unknown onboarding step: ${step}`);
    }

    return {
      isValid: missingData.length === 0,
      missingData,
    };
  };

  const findEarliestIncompleteStep = (): OnboardingStep => {
    // Check each step in order to find the first incomplete one
    if (!sessionData.businessInfo?.companyName) return "business_info";
    if (!sessionData.adminAccount?.email) return "admin_account";
    if (!sessionData.warehouseSetup?.warehouseName) return "warehouse_setup";
    return "team_setup";
  };

  // Show loading state while validating
  if (isValidating) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Validating navigation...</p>
        </div>
      </div>
    );
  }

  // Show error state if navigation is invalid
  if (!isValid) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-8 h-8 text-red-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Navigation Error
          </h2>
          <p className="text-gray-600 mb-4">
            There was an issue with your onboarding progress. You're being
            redirected to the correct step.
          </p>
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-600 mx-auto"></div>
        </div>
      </div>
    );
  }

  // Render children if navigation is valid
  return <>{children}</>;
}

/**
 * Higher-order component for onboarding navigation protection
 */
export function withOnboardingGuard<P extends object>(
  Component: React.ComponentType<P>,
  guardProps?: Omit<OnboardingNavigationGuardProps, "children">
) {
  return function GuardedComponent(props: P) {
    return (
      <OnboardingNavigationGuard {...guardProps}>
        <Component {...props} />
      </OnboardingNavigationGuard>
    );
  };
}

/**
 * Hook for checking onboarding navigation validity
 */
export function useOnboardingNavigation() {
  const { currentStep, sessionData, error, recoveryAvailable } =
    useOnboarding();
  const pathname = usePathname();

  const canAccessStep = (step: OnboardingStep): boolean => {
    const route = ONBOARDING_ROUTES.find((r) => r.step === step);
    if (!route) return false;

    // Check if current step allows access
    return (
      route.allowedFromSteps.includes(currentStep) || route.step === currentStep
    );
  };

  const getNextAllowedStep = (): OnboardingStep | null => {
    const currentIndex = ONBOARDING_ROUTES.findIndex(
      (r) => r.step === currentStep
    );
    if (currentIndex === -1 || currentIndex === ONBOARDING_ROUTES.length - 1)
      return null;

    return ONBOARDING_ROUTES[currentIndex + 1].step;
  };

  const getPreviousAllowedStep = (): OnboardingStep | null => {
    const currentIndex = ONBOARDING_ROUTES.findIndex(
      (r) => r.step === currentStep
    );
    if (currentIndex <= 0) return null;

    return ONBOARDING_ROUTES[currentIndex - 1].step;
  };

  return {
    currentStep,
    canAccessStep,
    getNextAllowedStep,
    getPreviousAllowedStep,
    hasActiveSession: !!sessionData.sessionId,
    hasRecoverableError: !!error && recoveryAvailable,
    hasUnrecoverableError: !!error && !recoveryAvailable,
  };
}
