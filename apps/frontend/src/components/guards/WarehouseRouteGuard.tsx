"use client";

import {
  useEffect,
  useState,
  ReactNode,
  useCallback,
  Suspense,
  useMemo,
} from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { Role } from "@quildora/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertTriangle, Building2, ShieldAlert } from "lucide-react";

interface WarehouseRouteGuardProps {
  children: ReactNode;
  requireWarehouse?: boolean;
  requiredRole?: Role;
  allowedRoles?: Role[];
  redirectTo?: string;
  fallbackComponent?: ReactNode;
}

// Define route configurations
const ROUTE_CONFIGS = {
  // Public routes - no authentication required
  public: ["/auth", "/auth/welcome", "/auth/signin", "/auth/signup", "/signup"],

  // Onboarding routes - authenticated but no warehouse required
  onboarding: ["/onboarding", "/auth/signup/business"],

  // Warehouse-dependent routes - require warehouse selection
  warehouseDependent: [
    "/",
    "/pallets",
    "/locations",
    "/move",
    "/picking",
    "/receiving",
    "/items",
  ],

  // Admin-only routes - require TENANT_ADMIN role
  adminOnly: [
    "/warehouses",
    "/settings/warehouses",
    "/settings/users",
    "/settings/billing",
  ],

  // Manager routes - require WAREHOUSE_MANAGER or higher
  managerRoutes: ["/settings/company"],
} as const;

export function WarehouseRouteGuard({
  children,
  requireWarehouse = false,
  requiredRole,
  allowedRoles,
  redirectTo,
  fallbackComponent,
}: WarehouseRouteGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { appUser, isLoading: authLoading, onboardingStatus } = useAuth();
  const {
    currentWarehouse,
    accessibleWarehouses,
    isLoadingWarehouses,
    warehouseError,
    setCurrentWarehouse,
  } = useWarehouse();

  const [isChecking, setIsChecking] = useState(true);
  const [accessDenied, setAccessDenied] = useState(false);
  const [denialReason, setDenialReason] = useState<string>("");
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [hasValidWarehouseCache, setHasValidWarehouseCache] = useState(false);

  // Determine route requirements based on pathname
  const getRouteRequirements = (path: string) => {
    // Check if it's a public route
    if (ROUTE_CONFIGS.public.some((route) => path.startsWith(route))) {
      return {
        requiresAuth: false,
        requiresWarehouse: false,
        requiredRole: null,
      };
    }

    // Check if it's an onboarding route
    if (ROUTE_CONFIGS.onboarding.some((route) => path.startsWith(route))) {
      return {
        requiresAuth: true,
        requiresWarehouse: false,
        requiredRole: null,
      };
    }

    // Check if it's an admin-only route
    if (ROUTE_CONFIGS.adminOnly.some((route) => path.startsWith(route))) {
      return {
        requiresAuth: true,
        requiresWarehouse: false,
        requiredRole: Role.TENANT_ADMIN,
      };
    }

    // Check if it's a manager route
    if (ROUTE_CONFIGS.managerRoutes.some((route) => path.startsWith(route))) {
      return {
        requiresAuth: true,
        requiresWarehouse: false,
        requiredRole: Role.WAREHOUSE_MANAGER,
      };
    }

    // Check if it's a warehouse-dependent route
    if (
      ROUTE_CONFIGS.warehouseDependent.some(
        (route) => path === route || path.startsWith(`${route}/`)
      )
    ) {
      return {
        requiresAuth: true,
        requiresWarehouse: true,
        requiredRole: Role.WAREHOUSE_MEMBER,
      };
    }

    // Default for other routes
    return { requiresAuth: true, requiresWarehouse: false, requiredRole: null };
  };

  // Check user role access
  const hasRoleAccess = (userRole: Role, requiredRole: Role): boolean => {
    const roleHierarchy = {
      [Role.TENANT_ADMIN]: 3,
      [Role.WAREHOUSE_MANAGER]: 2,
      [Role.WAREHOUSE_MEMBER]: 1,
    };

    return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
  };

  // Check warehouse-specific role access
  const hasWarehouseRoleAccess = useCallback(
    (warehouseId: string, requiredRole: Role): boolean => {
      if (!appUser || !currentWarehouse) return false;

      // Tenant admins have access to all warehouses
      if (appUser.role === Role.TENANT_ADMIN) return true;

      // Check if user has access to this warehouse
      const hasAccess = accessibleWarehouses.some((w) => w.id === warehouseId);
      if (!hasAccess) return false;

      // For warehouse-specific roles, check the user's role in this warehouse
      if (appUser.warehouseUsers && appUser.warehouseUsers.length > 0) {
        const warehouseUser = appUser.warehouseUsers.find(
          (wu) => wu.warehouseId === warehouseId
        );
        if (warehouseUser) {
          return hasRoleAccess(warehouseUser.role, requiredRole);
        }
      }

      // Fallback to global role
      return hasRoleAccess(appUser.role, requiredRole);
    },
    [appUser, currentWarehouse, accessibleWarehouses]
  );

  // Track initial load state and warehouse cache validity
  useEffect(() => {
    // Check if we have valid warehouse data from cache
    if (accessibleWarehouses.length > 0 && !isLoadingWarehouses) {
      setHasValidWarehouseCache(true);
      setIsInitialLoad(false);
    }
  }, [accessibleWarehouses, isLoadingWarehouses]);

  // Reset initial load state when user changes
  useEffect(() => {
    if (appUser) {
      setIsInitialLoad(true);
      setHasValidWarehouseCache(false);
    }
  }, [appUser?.id]);

  // Memoize permission check results to prevent unnecessary re-runs
  const permissionCheckKey = useMemo(() => {
    return `${pathname}-${appUser?.id}-${currentWarehouse?.id}-${appUser?.role}`;
  }, [pathname, appUser?.id, currentWarehouse?.id, appUser?.role]);

  const [lastPermissionCheck, setLastPermissionCheck] = useState<string>("");
  const [permissionCheckResult, setPermissionCheckResult] = useState<{
    isValid: boolean;
    accessDenied: boolean;
    denialReason: string;
  } | null>(null);

  useEffect(() => {
    const checkAccess = async () => {
      // Skip if we already checked these exact permissions recently
      if (permissionCheckKey === lastPermissionCheck && permissionCheckResult) {
        setIsChecking(false);
        setAccessDenied(permissionCheckResult.accessDenied);
        setDenialReason(permissionCheckResult.denialReason);
        return;
      }

      // Only show checking state if we don't have cached results
      if (!permissionCheckResult) {
        setIsChecking(true);
      }
      setAccessDenied(false);
      setDenialReason("");

      // Wait for auth to complete (parallel with warehouse loading)
      if (authLoading) return;

      const routeReqs = getRouteRequirements(pathname);
      const effectiveRequireWarehouse =
        requireWarehouse || routeReqs.requiresWarehouse;
      const effectiveRequiredRole = requiredRole || routeReqs.requiredRole;

      // Check authentication
      if (routeReqs.requiresAuth && !appUser) {
        router.push(redirectTo || "/auth/welcome");
        return;
      }

      // Handle onboarding flow
      if (appUser && onboardingStatus === "pending_company_details") {
        if (pathname !== "/onboarding/company-details") {
          router.push("/onboarding/company-details");
          return;
        }
      }

      // Check role access
      if (effectiveRequiredRole && appUser) {
        if (allowedRoles) {
          const hasAllowedRole = allowedRoles.some((role) =>
            hasRoleAccess(appUser.role, role)
          );
          if (!hasAllowedRole) {
            setAccessDenied(true);
            setDenialReason(
              `Access denied. Required role: ${allowedRoles.join(" or ")}`
            );
            setIsChecking(false);
            return;
          }
        } else if (!hasRoleAccess(appUser.role, effectiveRequiredRole)) {
          setAccessDenied(true);
          setDenialReason(
            `Access denied. Required role: ${effectiveRequiredRole} or higher`
          );
          setIsChecking(false);
          return;
        }
      }

      // Check warehouse requirements with smart loading
      if (effectiveRequireWarehouse && appUser) {
        // Only wait for warehouse loading if we don't have valid cached data
        if (isLoadingWarehouses && !hasValidWarehouseCache) return;

        // Check for warehouse loading errors
        if (warehouseError) {
          setAccessDenied(true);
          setDenialReason("Unable to load warehouse information");
          setIsChecking(false);
          return;
        }

        // Check if user has access to any warehouses
        if (accessibleWarehouses.length === 0) {
          setAccessDenied(true);
          setDenialReason("No accessible warehouses found");
          setIsChecking(false);
          return;
        }

        // Check if warehouse is selected
        if (!currentWarehouse) {
          // Auto-select first available warehouse if only one
          if (accessibleWarehouses.length === 1) {
            setCurrentWarehouse(accessibleWarehouses[0]);
            setIsChecking(false);
            return;
          }

          setAccessDenied(true);
          setDenialReason("Please select a warehouse to continue");
          setIsChecking(false);
          return;
        }

        // Check warehouse-specific role access
        if (
          effectiveRequiredRole &&
          !hasWarehouseRoleAccess(currentWarehouse.id, effectiveRequiredRole)
        ) {
          setAccessDenied(true);
          setDenialReason(
            `Insufficient permissions for warehouse: ${currentWarehouse.name}`
          );
          setIsChecking(false);
          return;
        }
      }

      // Cache the permission check result
      const result = {
        isValid: true,
        accessDenied: false,
        denialReason: "",
      };
      setPermissionCheckResult(result);
      setLastPermissionCheck(permissionCheckKey);
      setIsChecking(false);
    };

    checkAccess();
  }, [
    authLoading,
    appUser,
    onboardingStatus,
    pathname,
    currentWarehouse,
    accessibleWarehouses,
    isLoadingWarehouses,
    warehouseError,
    requireWarehouse,
    requiredRole,
    allowedRoles,
    redirectTo,
    router,
    setCurrentWarehouse,
    hasWarehouseRoleAccess,
    hasValidWarehouseCache,
    permissionCheckKey,
    lastPermissionCheck,
    permissionCheckResult,
  ]);

  // Smart loading states - only show loading when necessary and no cached results
  if (authLoading && !permissionCheckResult) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
          <p className="text-sm text-muted-foreground">Authenticating...</p>
        </div>
      </div>
    );
  }

  // Only show warehouse loading on initial load or when switching warehouses without cache
  if (
    isLoadingWarehouses &&
    (isInitialLoad || !hasValidWarehouseCache) &&
    !permissionCheckResult
  ) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
          <p className="text-sm text-muted-foreground">
            {isInitialLoad ? "Loading workspace..." : "Switching warehouse..."}
          </p>
        </div>
      </div>
    );
  }

  // Show checking permissions state only if we don't have cached results
  if (isChecking && !permissionCheckResult) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
          <p className="text-sm text-muted-foreground">
            Checking permissions...
          </p>
        </div>
      </div>
    );
  }

  // Show access denied state
  if (accessDenied) {
    if (fallbackComponent) {
      return <>{fallbackComponent}</>;
    }

    return (
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
              <ShieldAlert className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl text-red-600">
              Access Denied
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{denialReason}</AlertDescription>
            </Alert>

            {denialReason.includes("select a warehouse") &&
              accessibleWarehouses.length > 1 && (
                <div className="space-y-3">
                  <p className="text-sm text-muted-foreground">
                    Available warehouses:
                  </p>
                  <div className="grid gap-2">
                    {accessibleWarehouses.map((warehouse) => (
                      <Button
                        key={warehouse.id}
                        variant="outline"
                        className="justify-start"
                        onClick={() => setCurrentWarehouse(warehouse)}
                      >
                        <Building2 className="h-4 w-4 mr-2" />
                        {warehouse.name}
                      </Button>
                    ))}
                  </div>
                </div>
              )}

            <div className="flex justify-center space-x-3">
              <Button variant="outline" onClick={() => router.back()}>
                Go Back
              </Button>
              <Button onClick={() => router.push("/")}>Go to Dashboard</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render children if all checks pass
  return <>{children}</>;
}

// Convenience components for specific route types
export function WarehouseRequiredGuard({ children }: { children: ReactNode }) {
  return (
    <WarehouseRouteGuard requireWarehouse={true}>
      {children}
    </WarehouseRouteGuard>
  );
}

export function AdminOnlyGuard({ children }: { children: ReactNode }) {
  return (
    <WarehouseRouteGuard requiredRole={Role.TENANT_ADMIN}>
      {children}
    </WarehouseRouteGuard>
  );
}

export function ManagerOnlyGuard({ children }: { children: ReactNode }) {
  return (
    <WarehouseRouteGuard requiredRole={Role.WAREHOUSE_MANAGER}>
      {children}
    </WarehouseRouteGuard>
  );
}

// Suspense wrapper for parallel loading
export function ParallelLoadingGuard({
  children,
  fallback,
}: {
  children: ReactNode;
  fallback?: ReactNode;
}) {
  const defaultFallback = (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
        <p className="text-sm text-muted-foreground">Loading...</p>
      </div>
    </div>
  );

  return <Suspense fallback={fallback || defaultFallback}>{children}</Suspense>;
}
