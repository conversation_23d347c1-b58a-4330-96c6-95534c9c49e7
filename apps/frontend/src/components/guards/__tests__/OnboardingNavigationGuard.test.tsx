import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach } from "vitest";
import { useRouter, usePathname } from "next/navigation";
import {
  OnboardingNavigationGuard,
  useOnboardingNavigation,
} from "../OnboardingNavigationGuard";
import { useOnboarding } from "@/components/providers/onboarding-provider";
import { OnboardingStep } from "@quildora/types";

// Mock Next.js navigation
vi.mock("next/navigation", () => ({
  useRouter: vi.fn(),
  usePathname: vi.fn(),
}));

// Mock onboarding provider
vi.mock("@/components/providers/onboarding-provider", () => ({
  useOnboarding: vi.fn(),
}));

// Mock sonner toast
vi.mock("sonner", () => ({
  toast: {
    error: vi.fn(),
  },
}));

// Mock ONBOARDING_ROUTES
vi.mock("@quildora/types", () => ({
  ONBOARDING_ROUTES: [
    {
      step: "business_info",
      path: "/auth/signup/business",
      allowedFromSteps: ["business_info"],
    },
    {
      step: "admin_account",
      path: "/auth/signup/admin",
      allowedFromSteps: ["business_info", "admin_account"],
    },
    {
      step: "warehouse_setup",
      path: "/auth/signup/warehouse",
      allowedFromSteps: ["admin_account", "warehouse_setup"],
    },
    {
      step: "team_setup",
      path: "/auth/signup/team",
      allowedFromSteps: ["warehouse_setup", "team_setup"],
    },
    {
      step: "completion",
      path: "/auth/signup/complete",
      allowedFromSteps: ["team_setup", "completion"],
    },
  ],
}));

const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  prefetch: vi.fn(),
};

const mockOnboardingContext = {
  currentStep: "business_info" as OnboardingStep,
  sessionData: {
    sessionId: "session-123",
    step: "business_info" as OnboardingStep,
    businessInfo: {
      companyName: "Test Company",
      industry: "Technology",
      companySize: "10-50",
    },
    adminAccount: null,
    warehouseSetup: null,
    teamSetup: null,
  },
  error: null,
  recoveryAvailable: false,
  isLoading: false,
  updateBusinessInfo: vi.fn(),
  createAdminAccount: vi.fn(),
  setupWarehouse: vi.fn(),
  completeOnboarding: vi.fn(),
  goToStep: vi.fn(),
  goToNextStep: vi.fn(),
  goToPreviousStep: vi.fn(),
  resetOnboarding: vi.fn(),
  saveProgress: vi.fn(),
  loadProgress: vi.fn(),
};

function TestComponent() {
  return <div data-testid="protected-content">Protected Content</div>;
}

function TestNavigationHook() {
  const {
    currentStep,
    canAccessStep,
    getNextAllowedStep,
    getPreviousAllowedStep,
    hasActiveSession,
    hasRecoverableError,
    hasUnrecoverableError,
  } = useOnboardingNavigation();

  return (
    <div>
      <div data-testid="current-step">{currentStep}</div>
      <div data-testid="can-access-admin">
        {canAccessStep("admin_account").toString()}
      </div>
      <div data-testid="can-access-warehouse">
        {canAccessStep("warehouse_setup").toString()}
      </div>
      <div data-testid="next-step">{getNextAllowedStep() || "none"}</div>
      <div data-testid="previous-step">
        {getPreviousAllowedStep() || "none"}
      </div>
      <div data-testid="has-session">{hasActiveSession.toString()}</div>
      <div data-testid="has-recoverable-error">
        {hasRecoverableError?.toString() || "false"}
      </div>
      <div data-testid="has-unrecoverable-error">
        {hasUnrecoverableError.toString()}
      </div>
    </div>
  );
}

describe("OnboardingNavigationGuard", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useRouter as any).mockReturnValue(mockRouter);
    (useOnboarding as any).mockReturnValue(mockOnboardingContext);
  });

  it("should allow access to correct step", async () => {
    (usePathname as any).mockReturnValue("/auth/signup/business");

    render(
      <OnboardingNavigationGuard requiredStep="business_info">
        <TestComponent />
      </OnboardingNavigationGuard>
    );

    await waitFor(() => {
      expect(screen.getByTestId("protected-content")).toBeInTheDocument();
    });
  });

  it("should redirect when accessing future step", async () => {
    (usePathname as any).mockReturnValue("/auth/signup/warehouse");

    render(
      <OnboardingNavigationGuard requiredStep="warehouse_setup">
        <TestComponent />
      </OnboardingNavigationGuard>
    );

    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith("/auth/signup/business");
    });
  });

  it("should redirect when no active session", async () => {
    (usePathname as any).mockReturnValue("/auth/signup/business");
    (useOnboarding as any).mockReturnValue({
      ...mockOnboardingContext,
      sessionData: { ...mockOnboardingContext.sessionData, sessionId: null },
    });

    render(
      <OnboardingNavigationGuard requiredStep="business_info">
        <TestComponent />
      </OnboardingNavigationGuard>
    );

    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith("/auth/welcome");
    });
  });

  it("should redirect when unrecoverable error", async () => {
    (usePathname as any).mockReturnValue("/auth/signup/business");
    (useOnboarding as any).mockReturnValue({
      ...mockOnboardingContext,
      error: "Unrecoverable error",
      recoveryAvailable: false,
    });

    render(
      <OnboardingNavigationGuard requiredStep="business_info">
        <TestComponent />
      </OnboardingNavigationGuard>
    );

    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith("/auth/welcome");
    });
  });

  it("should allow access with recoverable error", async () => {
    (usePathname as any).mockReturnValue("/auth/signup/business");
    (useOnboarding as any).mockReturnValue({
      ...mockOnboardingContext,
      error: "Recoverable error",
      recoveryAvailable: true,
    });

    render(
      <OnboardingNavigationGuard requiredStep="business_info">
        <TestComponent />
      </OnboardingNavigationGuard>
    );

    await waitFor(() => {
      expect(screen.getByTestId("protected-content")).toBeInTheDocument();
    });
  });

  it("should redirect when missing required data", async () => {
    (usePathname as any).mockReturnValue("/auth/signup/admin");
    (useOnboarding as any).mockReturnValue({
      ...mockOnboardingContext,
      currentStep: "admin_account",
      sessionData: {
        ...mockOnboardingContext.sessionData,
        step: "admin_account",
        businessInfo: null, // Missing required business info
      },
    });

    render(
      <OnboardingNavigationGuard requiredStep="admin_account">
        <TestComponent />
      </OnboardingNavigationGuard>
    );

    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith("/auth/signup/business");
    });
  });

  it("should allow access to previous steps", async () => {
    (usePathname as any).mockReturnValue("/auth/signup/business");
    (useOnboarding as any).mockReturnValue({
      ...mockOnboardingContext,
      currentStep: "admin_account",
      sessionData: {
        ...mockOnboardingContext.sessionData,
        step: "admin_account",
        adminAccount: {
          email: "<EMAIL>",
          fullName: "Test Admin",
          password: "password123",
        },
      },
    });

    render(
      <OnboardingNavigationGuard requiredStep="business_info">
        <TestComponent />
      </OnboardingNavigationGuard>
    );

    await waitFor(() => {
      expect(screen.getByTestId("protected-content")).toBeInTheDocument();
    });
  });

  it("should show loading state during validation", () => {
    (usePathname as any).mockReturnValue("/auth/signup/business");

    render(
      <OnboardingNavigationGuard requiredStep="business_info">
        <TestComponent />
      </OnboardingNavigationGuard>
    );

    expect(screen.getByText("Validating navigation...")).toBeInTheDocument();
  });

  it("should allow access to non-onboarding routes", async () => {
    (usePathname as any).mockReturnValue("/dashboard");

    render(
      <OnboardingNavigationGuard>
        <TestComponent />
      </OnboardingNavigationGuard>
    );

    await waitFor(() => {
      expect(screen.getByTestId("protected-content")).toBeInTheDocument();
    });
  });
});

describe("useOnboardingNavigation", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (usePathname as any).mockReturnValue("/auth/signup/business");
    (useOnboarding as any).mockReturnValue(mockOnboardingContext);
  });

  it("should return correct navigation state", () => {
    render(<TestNavigationHook />);

    expect(screen.getByTestId("current-step")).toHaveTextContent(
      "business_info"
    );
    expect(screen.getByTestId("can-access-admin")).toHaveTextContent("true");
    expect(screen.getByTestId("can-access-warehouse")).toHaveTextContent(
      "false"
    );
    expect(screen.getByTestId("next-step")).toHaveTextContent("admin_account");
    expect(screen.getByTestId("previous-step")).toHaveTextContent("none");
    expect(screen.getByTestId("has-session")).toHaveTextContent("true");
    expect(screen.getByTestId("has-recoverable-error")).toHaveTextContent(
      "false"
    );
    expect(screen.getByTestId("has-unrecoverable-error")).toHaveTextContent(
      "false"
    );
  });

  it("should handle error states correctly", () => {
    (useOnboarding as any).mockReturnValue({
      ...mockOnboardingContext,
      error: "Test error",
      recoveryAvailable: true,
    });

    render(<TestNavigationHook />);

    expect(screen.getByTestId("has-recoverable-error")).toHaveTextContent(
      "true"
    );
    expect(screen.getByTestId("has-unrecoverable-error")).toHaveTextContent(
      "false"
    );
  });

  it("should handle unrecoverable error states", () => {
    (useOnboarding as any).mockReturnValue({
      ...mockOnboardingContext,
      error: "Fatal error",
      recoveryAvailable: false,
    });

    render(<TestNavigationHook />);

    expect(screen.getByTestId("has-recoverable-error")).toHaveTextContent(
      "false"
    );
    expect(screen.getByTestId("has-unrecoverable-error")).toHaveTextContent(
      "true"
    );
  });

  it("should handle missing session", () => {
    (useOnboarding as any).mockReturnValue({
      ...mockOnboardingContext,
      sessionData: { ...mockOnboardingContext.sessionData, sessionId: null },
    });

    render(<TestNavigationHook />);

    expect(screen.getByTestId("has-session")).toHaveTextContent("false");
  });
});
