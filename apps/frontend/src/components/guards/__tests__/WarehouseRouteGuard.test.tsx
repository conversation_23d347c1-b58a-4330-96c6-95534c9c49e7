import { render, screen, waitFor } from "@testing-library/react";
import { useRouter, usePathname } from "next/navigation";
import { WarehouseRouteGuard } from "../WarehouseRouteGuard";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { Role } from "@quildora/types";

// Mock Next.js navigation
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(),
}));

// Mock providers
jest.mock("@/components/providers/auth-provider", () => ({
  useAuth: jest.fn(),
}));

jest.mock("@/components/providers/warehouse-provider", () => ({
  useWarehouse: jest.fn(),
}));

const mockRouter = {
  push: jest.fn(),
  back: jest.fn(),
};

const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;
const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;
const mockUseWarehouse = useWarehouse as jest.MockedFunction<typeof useWarehouse>;

describe("WarehouseRouteGuard", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseRouter.mockReturnValue(mockRouter);
  });

  const defaultAuthState = {
    appUser: {
      id: "user1",
      email: "<EMAIL>",
      role: Role.WAREHOUSE_MEMBER,
      tenantId: "tenant1",
      warehouseRoles: new Map([["warehouse1", Role.WAREHOUSE_MEMBER]]),
    },
    isLoading: false,
    onboardingStatus: "complete",
  };

  const defaultWarehouseState = {
    currentWarehouse: { id: "warehouse1", name: "Test Warehouse" },
    accessibleWarehouses: [{ id: "warehouse1", name: "Test Warehouse" }],
    isLoadingWarehouses: false,
    warehouseError: null,
    setCurrentWarehouse: jest.fn(),
  };

  describe("Public Routes", () => {
    it("should allow access to auth page without authentication", async () => {
      mockUsePathname.mockReturnValue("/auth");
      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        appUser: null,
      });
      mockUseWarehouse.mockReturnValue(defaultWarehouseState);

      render(
        <WarehouseRouteGuard>
          <div>Auth Page Content</div>
        </WarehouseRouteGuard>
      );

      await waitFor(() => {
        expect(screen.getByText("Auth Page Content")).toBeInTheDocument();
      });
    });

    it("should allow access to signup page without authentication", async () => {
      mockUsePathname.mockReturnValue("/signup");
      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        appUser: null,
      });
      mockUseWarehouse.mockReturnValue(defaultWarehouseState);

      render(
        <WarehouseRouteGuard>
          <div>Signup Page Content</div>
        </WarehouseRouteGuard>
      );

      await waitFor(() => {
        expect(screen.getByText("Signup Page Content")).toBeInTheDocument();
      });
    });
  });

  describe("Authentication Required Routes", () => {
    it("should redirect to auth if user is not authenticated", async () => {
      mockUsePathname.mockReturnValue("/pallets");
      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        appUser: null,
      });
      mockUseWarehouse.mockReturnValue(defaultWarehouseState);

      render(
        <WarehouseRouteGuard>
          <div>Protected Content</div>
        </WarehouseRouteGuard>
      );

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith("/auth");
      });
    });
  });

  describe("Warehouse-Dependent Routes", () => {
    it("should allow access when user has warehouse selected", async () => {
      mockUsePathname.mockReturnValue("/pallets");
      mockUseAuth.mockReturnValue(defaultAuthState);
      mockUseWarehouse.mockReturnValue(defaultWarehouseState);

      render(
        <WarehouseRouteGuard>
          <div>Pallets Page Content</div>
        </WarehouseRouteGuard>
      );

      await waitFor(() => {
        expect(screen.getByText("Pallets Page Content")).toBeInTheDocument();
      });
    });

    it("should show access denied when no warehouse is selected", async () => {
      mockUsePathname.mockReturnValue("/pallets");
      mockUseAuth.mockReturnValue(defaultAuthState);
      mockUseWarehouse.mockReturnValue({
        ...defaultWarehouseState,
        currentWarehouse: null,
      });

      render(
        <WarehouseRouteGuard>
          <div>Pallets Page Content</div>
        </WarehouseRouteGuard>
      );

      await waitFor(() => {
        expect(screen.getByText("Access Denied")).toBeInTheDocument();
        expect(screen.getByText(/select a warehouse/i)).toBeInTheDocument();
      });
    });

    it("should auto-select warehouse when only one is available", async () => {
      const mockSetCurrentWarehouse = jest.fn();
      mockUsePathname.mockReturnValue("/pallets");
      mockUseAuth.mockReturnValue(defaultAuthState);
      mockUseWarehouse.mockReturnValue({
        ...defaultWarehouseState,
        currentWarehouse: null,
        setCurrentWarehouse: mockSetCurrentWarehouse,
      });

      render(
        <WarehouseRouteGuard>
          <div>Pallets Page Content</div>
        </WarehouseRouteGuard>
      );

      await waitFor(() => {
        expect(mockSetCurrentWarehouse).toHaveBeenCalledWith({
          id: "warehouse1",
          name: "Test Warehouse",
        });
      });
    });
  });

  describe("Admin-Only Routes", () => {
    it("should allow access for tenant admin", async () => {
      mockUsePathname.mockReturnValue("/warehouses");
      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        appUser: {
          ...defaultAuthState.appUser!,
          role: Role.TENANT_ADMIN,
        },
      });
      mockUseWarehouse.mockReturnValue(defaultWarehouseState);

      render(
        <WarehouseRouteGuard>
          <div>Warehouses Page Content</div>
        </WarehouseRouteGuard>
      );

      await waitFor(() => {
        expect(screen.getByText("Warehouses Page Content")).toBeInTheDocument();
      });
    });

    it("should deny access for non-admin users", async () => {
      mockUsePathname.mockReturnValue("/warehouses");
      mockUseAuth.mockReturnValue(defaultAuthState);
      mockUseWarehouse.mockReturnValue(defaultWarehouseState);

      render(
        <WarehouseRouteGuard>
          <div>Warehouses Page Content</div>
        </WarehouseRouteGuard>
      );

      await waitFor(() => {
        expect(screen.getByText("Access Denied")).toBeInTheDocument();
        expect(screen.getByText(/Required role: TENANT_ADMIN/i)).toBeInTheDocument();
      });
    });
  });

  describe("Loading States", () => {
    it("should show loading when auth is loading", () => {
      mockUsePathname.mockReturnValue("/pallets");
      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        isLoading: true,
      });
      mockUseWarehouse.mockReturnValue(defaultWarehouseState);

      render(
        <WarehouseRouteGuard>
          <div>Protected Content</div>
        </WarehouseRouteGuard>
      );

      expect(screen.getByText("Authenticating...")).toBeInTheDocument();
    });

    it("should show loading when warehouses are loading", () => {
      mockUsePathname.mockReturnValue("/pallets");
      mockUseAuth.mockReturnValue(defaultAuthState);
      mockUseWarehouse.mockReturnValue({
        ...defaultWarehouseState,
        isLoadingWarehouses: true,
      });

      render(
        <WarehouseRouteGuard>
          <div>Protected Content</div>
        </WarehouseRouteGuard>
      );

      expect(screen.getByText("Loading warehouses...")).toBeInTheDocument();
    });
  });

  describe("Onboarding Flow", () => {
    it("should redirect to onboarding when pending", async () => {
      mockUsePathname.mockReturnValue("/pallets");
      mockUseAuth.mockReturnValue({
        ...defaultAuthState,
        onboardingStatus: "pending_company_details",
      });
      mockUseWarehouse.mockReturnValue(defaultWarehouseState);

      render(
        <WarehouseRouteGuard>
          <div>Protected Content</div>
        </WarehouseRouteGuard>
      );

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith("/onboarding/company-details");
      });
    });
  });
});
