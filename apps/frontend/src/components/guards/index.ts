// Route Guard Components
export {
  WarehouseRouteGuard,
  WarehouseRequiredGuard,
  AdminOnlyGuard,
  ManagerOnlyGuard,
} from "./WarehouseRouteGuard";

export {
  OnboardingNavigationGuard,
  withOnboardingGuard,
  useOnboardingNavigation,
} from "./OnboardingNavigationGuard";

// Higher-Order Components
export {
  withWarehouseGuard,
  withWarehouseRequired,
  withAdminOnly,
  withManagerOnly,
  withWarehouseMember,
  withWarehouseManager,
  withRoles,
} from "./withWarehouseGuard";

// Provider Component
export { RouteGuardProvider } from "./RouteGuardProvider";

// Hook
export { useRouteGuard } from "../../hooks/useRouteGuard";

// Re-export types for convenience
export type { Role } from "@quildora/types";
