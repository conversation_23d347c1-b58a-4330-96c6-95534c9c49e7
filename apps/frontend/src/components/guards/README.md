# Warehouse Route Guards

This directory contains the route guard system for Quildora's warehouse-scoped access control. The guards automatically protect routes based on authentication, warehouse selection, and role-based permissions.

## Components

### WarehouseRouteGuard

The main guard component that automatically determines route requirements based on the current pathname.

```tsx
import { WarehouseRouteGuard } from "@/components/guards";

<WarehouseRouteGuard>
  <YourComponent />
</WarehouseRouteGuard>
```

### RouteGuardProvider

Provider component that should be placed in the layout to protect all routes automatically.

```tsx
import { RouteGuardProvider } from "@/components/guards";

<RouteGuardProvider>
  {children}
</RouteGuardProvider>
```

### Higher-Order Components (HOCs)

For component-level protection:

```tsx
import { withWarehouseRequired, withAdminOnly } from "@/components/guards";

// Require warehouse selection
const ProtectedComponent = withWarehouseRequired(YourComponent);

// Require admin role
const AdminComponent = withAdminOnly(YourComponent);

// Custom requirements
const CustomProtected = withWarehouseGuard(YourComponent, {
  requireWarehouse: true,
  requiredRole: Role.WAREHOUSE_MANAGER,
});
```

### Convenience Guards

```tsx
import { 
  WarehouseRequiredGuard, 
  AdminOnlyGuard, 
  ManagerOnlyGuard 
} from "@/components/guards";

<WarehouseRequiredGuard>
  <WarehouseSpecificContent />
</WarehouseRequiredGuard>

<AdminOnlyGuard>
  <AdminOnlyContent />
</AdminOnlyGuard>
```

## Route Categories

### Public Routes
- `/auth` - Authentication page
- `/signup` - User registration

### Onboarding Routes
- `/onboarding/*` - Requires authentication but no warehouse

### Warehouse-Dependent Routes
- `/` - Dashboard (requires warehouse selection)
- `/pallets` - Pallet management
- `/locations` - Location management
- `/move` - Pallet movement
- `/picking` - Picking operations
- `/receiving` - Receiving operations
- `/items` - Item management

### Admin-Only Routes
- `/warehouses` - Warehouse management
- `/settings/warehouses` - Warehouse settings
- `/settings/users` - User management
- `/settings/billing` - Billing settings

### Manager Routes
- `/settings/company` - Company settings

## Hook Usage

Use the `useRouteGuard` hook for programmatic access control:

```tsx
import { useRouteGuard } from "@/components/guards";

function MyComponent() {
  const {
    checkRouteAccess,
    canAccessWarehouseRoutes,
    canAccessAdminRoutes,
    hasRole,
    currentUserRole,
  } = useRouteGuard();

  // Check specific access
  const { canAccess, reason } = checkRouteAccess(
    true, // requireWarehouse
    Role.WAREHOUSE_MANAGER // requiredRole
  );

  // Check role
  const isAdmin = hasRole(Role.TENANT_ADMIN);

  return (
    <div>
      {canAccessAdminRoutes && <AdminButton />}
      {canAccessWarehouseRoutes && <WarehouseButton />}
    </div>
  );
}
```

## Role Hierarchy

The system uses a role hierarchy for access control:

1. **TENANT_ADMIN** (Level 3)
   - Full access to all warehouses and admin functions
   - Can manage users, warehouses, and billing

2. **WAREHOUSE_MANAGER** (Level 2)
   - Can manage specific warehouses they're assigned to
   - Can perform all warehouse operations

3. **WAREHOUSE_MEMBER** (Level 1)
   - Can perform basic warehouse operations
   - Limited to assigned warehouses

## Access Control Logic

### Authentication Check
- Public routes: No authentication required
- All other routes: Must be authenticated

### Onboarding Check
- If user has pending onboarding, redirect to onboarding flow
- Exception: Already on onboarding pages

### Role Check
- Admin routes: Require TENANT_ADMIN role
- Manager routes: Require WAREHOUSE_MANAGER or higher
- Warehouse routes: Require WAREHOUSE_MEMBER or higher

### Warehouse Check
- Warehouse-dependent routes require:
  1. User has access to at least one warehouse
  2. A warehouse is currently selected
  3. User has required role in the selected warehouse

## Error Handling

The guards provide detailed error messages and recovery options:

- **No warehouse selected**: Shows warehouse selection interface
- **Insufficient permissions**: Shows access denied with specific requirements
- **Loading errors**: Shows retry options
- **Network errors**: Shows error state with retry button

## Testing

The guards include comprehensive test coverage:

```bash
npm test -- guards
```

Test scenarios include:
- Public route access
- Authentication requirements
- Role-based access control
- Warehouse selection requirements
- Loading states
- Error conditions
- Onboarding flow

## Best Practices

1. **Use RouteGuardProvider in layout** for automatic protection
2. **Use HOCs for component-level protection** when needed
3. **Use the hook for conditional rendering** based on permissions
4. **Test all access scenarios** in your components
5. **Provide clear error messages** for access denial
6. **Handle loading states gracefully** during permission checks

## Migration from Legacy Guards

If migrating from existing route protection:

1. Remove manual authentication checks
2. Replace with appropriate guard components
3. Update role checking logic to use the new hierarchy
4. Test all protected routes thoroughly

## Security Considerations

- Guards run on the client side and should not be the only security measure
- Always validate permissions on the backend
- Use HTTPS in production
- Regularly audit access control logic
- Monitor for unauthorized access attempts
