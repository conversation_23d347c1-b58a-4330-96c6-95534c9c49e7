"use client";

import { useAuth } from "./providers/auth-provider";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function AuthStatus() {
  const { appUser, supabaseUser, logout, isLoading } = useAuth();

  if (isLoading) {
    return <div className="text-sm text-gray-500">Loading auth...</div>;
  }

  if (appUser) {
    return (
      <div className="flex items-center gap-4">
        <span className="text-sm">
          App User: {appUser.email} (Role: {appUser.role})
        </span>
        <Button onClick={logout} variant="outline" size="sm">
          Logout
        </Button>
      </div>
    );
  } else if (supabaseUser) {
    // Logged into Supabase but not yet (or failed) app login
    return (
      <div className="flex items-center gap-4">
        <span className="text-sm">
          Supabase User: {supabaseUser.email} (Verifying app session...)
        </span>
        <Button onClick={logout} variant="outline" size="sm">
          Logout from Supabase
        </Button>
      </div>
    );
  }

  return (
    <Link href="/auth" legacyBehavior>
      <Button variant="default" size="sm">
        Login
      </Button>
    </Link>
  );
}
