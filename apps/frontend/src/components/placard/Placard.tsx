'use client';

import React from 'react';
import { PlacardDataResponseDto } from '@quildora/types';
import Barcode from 'react-barcode';

interface PlacardProps {
  data: PlacardDataResponseDto;
}

const Placard: React.FC<PlacardProps> = ({ data }) => {
  if (!data) return null;

  return (
    <div className="w-full h-full p-8 bg-white text-black font-sans flex flex-col overflow-hidden">
      {/* Header */}
      <div className="flex-shrink-0">
        <div className="flex justify-between items-start border-b-4 border-black pb-4">
          <div>
            <p className="text-1xl font-semibold tracking-wider">LOCATION</p>
            <p className="text-3xl font-bold">{data.currentLocation.name}</p>
          </div>
          <div className="text-right">
            <p className="text-1xl tracking-wider">DESCRIPTION</p>
            <p className="text-3xl font-semibold">{data.description || 'N/A'}</p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-grow flex flex-col items-center justify-center text-center">
        <p className="text-2xl font-semibold tracking-wider">SHIP TO</p>
        <p className="text-7xl font-extrabold tracking-wider -my-2">{data.shipToDestination}</p>
      </div>

      {/* Footer */}
      <div className="flex-shrink-0 border-t-4 border-black pt-4">
        <div className="flex flex-col items-center justify-center">
          <Barcode value={data.barcode} width={4} height={120} displayValue={false} />
          <p className="text-4xl font-mono tracking-widest mt-2">{data.barcode}</p>
        </div>
      </div>
    </div>
  );
};

export default Placard;
