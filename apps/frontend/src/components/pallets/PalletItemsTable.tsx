import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Edit, Plus, Trash2 } from "lucide-react";
import { PalletItem } from "@quildora/types";

interface PalletItemsTableProps {
  palletItems: PalletItem[];
  onAddItem: () => void;
  onEditItem: (item: PalletItem) => void;
  onRemoveItem: (item: PalletItem) => void;
}

/**
 * Reusable table component for displaying pallet items
 * Handles item display, actions, and empty state
 */
export const PalletItemsTable: React.FC<PalletItemsTableProps> = ({
  palletItems,
  onAddItem,
  onEditItem,
  onRemoveItem,
}) => {
  return (
    <div className="mt-6">
      <div className="flex justify-between items-center mb-2">
        <h2 className="text-xl font-semibold">Pallet Items</h2>
        <Button onClick={onAddItem}>
          <Plus className="mr-2 h-4 w-4" />
          Add Item
        </Button>
      </div>
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>SKU</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {palletItems && palletItems.length > 0 ? (
              palletItems.map((palletItem: PalletItem) => (
                <TableRow key={palletItem.itemId}>
                  <TableCell className="font-mono">
                    {palletItem.item ? (
                      <span className="text-sm text-slate-700">
                        {palletItem.item.sku}
                      </span>
                    ) : (
                      <span className="text-sm text-red-500">
                        Item not found
                      </span>
                    )}
                  </TableCell>
                  <TableCell>
                    {palletItem.item ? (
                      <span className="text-sm text-slate-700">
                        {palletItem.item.name}
                      </span>
                    ) : (
                      <span className="text-sm text-red-500">
                        Item not found
                      </span>
                    )}
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-slate-700">
                      {palletItem.quantity}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEditItem(palletItem)}
                      className="mr-2"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onRemoveItem(palletItem)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={4} className="text-center">
                  No items on this pallet.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};
