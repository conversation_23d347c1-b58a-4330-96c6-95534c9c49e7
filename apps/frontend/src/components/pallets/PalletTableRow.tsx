import React from "react";
import Link from "next/link";
import {
  TableCell,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  ArrowRight,
  MoreHorizontal,
  Pencil,
  Printer,
  Trash2,
} from "lucide-react";
import { Pallet } from "@quildora/types";
import { getStatusColor, formatTableDate } from "@/utils/palletUtils";
import { useRoleAccess } from "@/components/providers/auth-provider";
import { Role } from "@quildora/types";

interface PalletTableRowProps {
  pallet: Pallet;
  hasActions: boolean;
  onEditClick: (pallet: Pallet) => void;
  onDeleteClick: (pallet: Pallet) => void;
  onPrintPlacard: (pallet: Pallet) => void;
}

/**
 * Reusable table row component for individual pallet display
 * Handles all pallet data display and action menu
 */
export const PalletTableRow: React.FC<PalletTableRowProps> = ({
  pallet,
  hasActions,
  onEditClick,
  onDeleteClick,
  onPrintPlacard,
}) => {
  const isAdmin = useRoleAccess(Role.TENANT_ADMIN);
  const isWarehouseAdmin = useRoleAccess(Role.WAREHOUSE_MANAGER);
  const isWarehouseMember = useRoleAccess(Role.WAREHOUSE_MEMBER);

  return (
    <TableRow>
      <TableCell className="font-mono text-sm text-slate-700">
        <Link
          href={`/pallets/${pallet.id}`}
          className="hover:underline"
        >
          {pallet.barcode}
        </Link>
      </TableCell>
      <TableCell>
        <span
          className={`inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
            pallet.status
          )}`}
        >
          {pallet.status}
        </span>
      </TableCell>
      <TableCell className="text-sm text-slate-700">
        {pallet.shipToDestination ?? "-"}
      </TableCell>
      <TableCell className="text-sm text-slate-700">
        {pallet.destinationCode || "-"}
      </TableCell>
      <TableCell className="text-sm text-slate-700">
        {pallet.location?.name ?? "(No Location)"}
      </TableCell>
      <TableCell className="text-sm text-slate-700">
        {pallet.description ?? "-"}
      </TableCell>
      <TableCell className="text-sm text-slate-700">
        {formatTableDate(pallet.dateCreated)}
      </TableCell>
      <TableCell className="text-sm text-slate-700">
        {formatTableDate(pallet.lastMovedDate)}
      </TableCell>
      {hasActions && (
        <TableCell className="text-right">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0 text-muted-foreground hover:bg-slate-100 hover:text-muted-foreground"
              >
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4 text-slate-500" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel className="text-sm font-bold text-slate-700">
                Actions
              </DropdownMenuLabel>
              {(isAdmin || isWarehouseAdmin || isWarehouseMember) && (
                <DropdownMenuItem asChild>
                  <Link href={`/pallets/${pallet.id}`}>
                    <ArrowRight className="mr-2 h-4 w-4" />
                    View Details
                  </Link>
                </DropdownMenuItem>
              )}
              {(isAdmin || isWarehouseAdmin || isWarehouseMember) && (
                <DropdownMenuItem
                  onClick={() => onEditClick(pallet)}
                  className="text-sm text-slate-700"
                >
                  <Pencil className="mr-2 h-4 w-4" />
                  Edit Pallet
                </DropdownMenuItem>
              )}
              {(isAdmin || isWarehouseAdmin || isWarehouseMember) && (
                <DropdownMenuItem
                  onSelect={() => onPrintPlacard(pallet)}
                >
                  <Printer className="mr-2 h-4 w-4" />
                  <span>Print Placard</span>
                </DropdownMenuItem>
              )}
              {(isAdmin || isWarehouseAdmin) && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onSelect={() => onDeleteClick(pallet)}
                    className="text-red-600"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </TableCell>
      )}
    </TableRow>
  );
};
