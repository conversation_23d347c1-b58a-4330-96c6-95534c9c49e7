"use client";

import React, { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { fetchWithAuth } from "@/lib/api";
import { useStorageLocations } from "@/hooks/api";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { Pallet, Location } from "@quildora/types";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

interface MovePalletDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  pallet: Pallet | null;
  onPalletMoved: () => void;
}

const fetchStorageLocations = async (
  appToken: string | null
): Promise<Location[]> => {
  if (!appToken) {
    throw new Error(
      "Authentication token not available for fetching locations."
    );
  }
  return fetchWithAuth("/api/locations?category=Storage", { token: appToken });
};

const movePalletApi = async ({
  palletId,
  newLocationId,
  warehouseId,
  appToken,
}: {
  palletId: string;
  newLocationId: string;
  warehouseId: string;
  appToken: string | null;
}) => {
  if (!appToken) {
    throw new Error("Authentication token not available for moving pallet.");
  }
  return fetchWithAuth(
    `/api/pallets/${palletId}/move?warehouseId=${warehouseId}`,
    {
      method: "POST",
      body: JSON.stringify({ newLocationId }),
      token: appToken,
    }
  );
};

export const MovePalletDialog: React.FC<MovePalletDialogProps> = ({
  isOpen,
  onOpenChange,
  pallet,
  onPalletMoved,
}) => {
  const [selectedLocationId, setSelectedLocationId] = useState<string>("");
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  // Fetch storage locations using warehouse-aware hook
  const {
    data: locations,
    isLoading: isLoadingLocations,
    isError: isErrorLocations,
  } = useStorageLocations();

  const moveMutation = useMutation({
    mutationFn: movePalletApi,
    onSuccess: () => {
      toast.success("Pallet moved successfully!");
      onPalletMoved();
      onOpenChange(false);
    },
    onError: (error: Error) => {
      toast.error(`Failed to move pallet: ${error.message}`);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!pallet || !selectedLocationId) {
      toast.warning("Please select a destination location.");
      return;
    }
    moveMutation.mutate({
      palletId: pallet.id,
      newLocationId: selectedLocationId,
      warehouseId: currentWarehouse?.id || "",
      appToken,
    });
  };

  if (!pallet) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Move Pallet</DialogTitle>
          <DialogDescription>
            Move pallet{" "}
            <span className="font-mono font-semibold">{pallet.barcode}</span> to
            a new location. Current location: {pallet.location?.name || "N/A"}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="py-4">
            <Label htmlFor="location-select">New Location</Label>
            {isLoadingLocations ? (
              <div className="flex items-center justify-center h-10">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : isErrorLocations ? (
              <p className="text-destructive text-sm">
                Failed to load locations. Please check your connection and try
                again.
              </p>
            ) : !locations || locations.length === 0 ? (
              <p className="text-muted-foreground text-sm">
                No storage locations available.
              </p>
            ) : (
              <Select
                value={selectedLocationId}
                onValueChange={setSelectedLocationId}
              >
                <SelectTrigger id="location-select">
                  <SelectValue placeholder="Select a storage location..." />
                </SelectTrigger>
                <SelectContent>
                  {locations?.map((location: Location) => (
                    <SelectItem key={location.id} value={location.id}>
                      {location.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={moveMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!selectedLocationId || moveMutation.isPending}
            >
              {moveMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Moving...
                </>
              ) : (
                "Move Pallet"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
