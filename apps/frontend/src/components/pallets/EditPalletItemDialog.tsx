"use client";

import React from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { PalletItem } from "@quildora/types";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";

const editQuantitySchema = z.object({
  quantity: z.coerce
    .number()
    .positive("Quantity must be positive")
    .int("Quantity must be a whole number"),
});

type FormValues = z.infer<typeof editQuantitySchema>;

// Define expected response structure
interface UpdateItemQuantityResponse {
  id: string;
  palletId: string;
  itemId: string;
  quantity: number;
  message?: string; // For toast messages
  // any other relevant fields from PalletItem
}

// Type for PalletItem with the expected structure (can be refined if needed)
interface PalletItemData {
  palletId: string;
  itemId: string;
  quantity: number;
  appToken: string | null; // Added appToken
}

// Function to update item quantity
async function updateItemQuantityApi({
  palletId,
  itemId,
  quantity,
  appToken,
}: PalletItemData): Promise<UpdateItemQuantityResponse> {
  if (!appToken) {
    throw new Error("Authentication token not available.");
  }
  try {
    // Using fetchWithAuth instead of axios.patch
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
    return fetchWithAuth(
      `${baseUrl}/api/pallets/${palletId}/items/${itemId}`,
      {
        method: "PATCH",
        body: JSON.stringify({ quantity }),
        token: appToken,
      }
    );
  } catch (error) {
    // Log the error, but let useMutation's onError handle user-facing message
    console.error("Error updating item quantity:", error);
    // Re-throw to be caught by mutation's onError
    // Ensure the error thrown has a 'message' property for consistent handling
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("An unknown error occurred while updating item quantity.");
  }
}

interface EditPalletItemDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  palletId: string;
  palletItem: PalletItem | null;
  onSuccess?: () => void;
}

export function EditPalletItemDialog({
  open,
  onOpenChange,
  palletId,
  palletItem,
  onSuccess,
}: EditPalletItemDialogProps) {
  const queryClient = useQueryClient();
  const { appToken } = useAuth(); // Get appToken

  const form = useForm<FormValues>({
    resolver: zodResolver(editQuantitySchema),
    defaultValues: {
      quantity: palletItem?.quantity || 0,
    },
    values: {
      quantity: palletItem?.quantity || 0,
    },
  });

  // Reset form when palletItem changes
  React.useEffect(() => {
    if (palletItem) {
      form.reset({
        quantity: palletItem.quantity,
      });
    }
  }, [palletItem, form]);

  const updateMutation = useMutation<
    UpdateItemQuantityResponse,
    Error,
    { quantity: number }
  >({
    mutationFn: (data) => {
      if (!palletItem?.palletId || !palletItem?.itemId) {
        // This should ideally not happen if the dialog is opened with a valid item
        // but as a safeguard:
        throw new Error("Pallet item data is missing.");
      }
      return updateItemQuantityApi({
        palletId: palletItem.palletId, // Now guaranteed to be string
        itemId: palletItem.itemId,     // Now guaranteed to be string
        quantity: data.quantity,
        appToken, // Pass appToken
      });
    },
    onSuccess: (data) => {
      toast.success(
        data.message || "Item quantity updated successfully!"
      );
      queryClient.invalidateQueries({
        queryKey: ["palletDetails", palletId],
      });
      onOpenChange(false);
      if (onSuccess) onSuccess();
    },
    onError: (error: Error) => {
      // Simplified error handling, relies on error having a message property
      toast.error("Update Failed", {
        description: error.message || "Failed to update item quantity. Please try again.",
      });
    },
  });

  const onSubmit = (data: FormValues) => {
    if (!palletItem) return;

    updateMutation.mutate({
      quantity: data.quantity,
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Item Quantity</DialogTitle>
          <DialogDescription>
            Update the quantity for {palletItem?.item?.name || "this item"}.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {palletItem && (
              <div className="grid gap-2 py-2">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium mb-1">SKU</p>
                    <p className="text-sm">{palletItem.item?.sku || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium mb-1">Item Name</p>
                    <p className="text-sm">
                      {palletItem.item?.name || "(No name)"}
                    </p>
                  </div>
                </div>
              </div>
            )}

            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Quantity</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="number"
                      min="1"
                      placeholder="Enter quantity"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={updateMutation.isPending}>
                {updateMutation.isPending ? "Updating..." : "Update Quantity"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
