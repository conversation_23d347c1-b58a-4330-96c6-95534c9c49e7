"use client";

import React from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import { Pallet } from "@quildora/types";

// Define the expected shape of data returned by the API after delete
interface DeletePalletResponse {
  id?: string;
}

export interface DeletePalletDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  pallet: Pallet | null; // Pallet to delete
  onSuccess?: () => void;
}

export function DeletePalletDialog({
  open,
  onOpenChange,
  pallet,
  onSuccess,
}: DeletePalletDialogProps) {
  const queryClient = useQueryClient();
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  const mutation = useMutation<DeletePalletResponse | null, Error, string>({
    mutationFn: async (palletId: string) => {
      if (!appToken) {
        toast.error("Authentication token not found. Please log in.");
        throw new Error("Authentication token not found.");
      }
      return fetchWithAuth(
        `/api/pallets/${palletId}?warehouseId=${currentWarehouse?.id || ""}`,
        {
          method: "DELETE",
          token: appToken,
        }
      );
    },
    onSuccess: (_data, deletedPalletId) => {
      toast.success(
        `Pallet "${pallet?.barcode ?? deletedPalletId}" deleted successfully.`
      );
      queryClient.invalidateQueries({ queryKey: ["pallets"] });
      onSuccess?.();
      onOpenChange(false);
    },
    onError: (error) => {
      toast.error(error.message || "Failed to delete pallet.");
    },
  });

  const handleDelete = () => {
    if (!pallet) return;
    mutation.mutate(pallet.id);
  };

  return (
    <AlertDialog open={open && !!pallet} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        {pallet && (
          <>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete
                pallet "<strong>{pallet.barcode}</strong>". If the pallet still
                contains items, deletion will fail.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel
                onClick={() => onOpenChange(false)}
                disabled={mutation.isPending}
              >
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                disabled={mutation.isPending}
                className="bg-red-600 hover:bg-red-700"
              >
                {mutation.isPending ? "Deleting..." : "Delete"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </>
        )}
      </AlertDialogContent>
    </AlertDialog>
  );
}
