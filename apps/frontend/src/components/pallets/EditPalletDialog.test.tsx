import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@/lib/test-utils';
import { EditPalletDialog } from './EditPalletDialog';
import * as api from '@/lib/api';
import { toast } from 'sonner';
import { type Pallet } from '@quildora/types';

// Mock dependencies
vi.mock('@/lib/api');
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

const mockedFetchWithAuth = vi.mocked(api.fetchWithAuth);
const mockedToast = vi.mocked(toast);

const mockLocations = [
  { id: 'loc1', name: 'Location A' },
  { id: 'loc2', name: 'Location B' },
];

const mockPallet: Pallet = {
  id: 'pallet123',
  label: 'P123',
  description: 'Initial Description',
  barcode: 'BC-INITIAL',
  status: 'IN_STORAGE',
  locationId: 'loc1',
  location: {
    id: 'loc1',
    name: 'Location A',
    locationType: 'STORAGE',
    warehouseId: 'w1',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    warehouse: {
      id: 'w1',
      name: 'Main Warehouse',
    },
  },
  lastMovedDate: new Date().toISOString(),
  palletItems: [],
};

describe('EditPalletDialog', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // General mock setup for successful calls
    mockedFetchWithAuth.mockImplementation(async (url) => {
      if (url === '/api/locations') {
        return Promise.resolve(mockLocations);
      }
      if (url.startsWith('/api/pallets/')) {
        return Promise.resolve({});
      }
      throw new Error(`Unhandled request: ${url}`);
    });
  });

  it('renders with pallet data and allows editing', async () => {
    const onSuccess = vi.fn();
    render(
      <EditPalletDialog
        pallet={mockPallet}
        open={true}
        onOpenChange={() => {}}
        onSuccess={onSuccess}
      />
    );

    // Wait for async operations like fetching locations to complete
    await waitFor(() => {
      expect(screen.getByLabelText(/description/i)).toHaveValue(mockPallet.description!);
    });
    expect(screen.getByLabelText(/barcode/i)).toHaveValue(mockPallet.barcode!);

    // Edit values
    fireEvent.change(screen.getByLabelText(/description/i), { target: { value: 'Updated Description' } });
    fireEvent.change(screen.getByLabelText(/barcode/i), { target: { value: 'BC-UPDATED' } });

    // Submit form
    fireEvent.click(screen.getByRole('button', { name: /save changes/i }));

    await waitFor(() => {
      expect(mockedFetchWithAuth).toHaveBeenCalledWith(`/api/pallets/${mockPallet.id}`,
        expect.objectContaining({
          method: 'PATCH',
          body: JSON.stringify({
            locationId: mockPallet.locationId,
            description: 'Updated Description',
            barcode: 'BC-UPDATED',
            shipToDestination: undefined,
          }),
        })
      );
    });

    await waitFor(() => {
      expect(onSuccess).toHaveBeenCalledTimes(1);
      expect(mockedToast.success).toHaveBeenCalled();
    });
  });

  it('shows an error toast on submission failure', async () => {
    // Override mock for this specific test to simulate failure
    mockedFetchWithAuth.mockImplementation(async (url) => {
      if (url === '/api/locations') {
        return Promise.resolve(mockLocations);
      }
      if (url.startsWith('/api/pallets/')) {
        return Promise.reject(new Error('Update Failed'));
      }
      throw new Error(`Unhandled request: ${url}`);
    });

    const onSuccess = vi.fn();
    render(
      <EditPalletDialog
        pallet={mockPallet}
        open={true}
        onOpenChange={() => {}}
        onSuccess={onSuccess}
      />
    );

    // Wait for the dialog to be ready by finding an element that depends on async setup.
    await screen.findByLabelText(/description/i);

    fireEvent.click(screen.getByRole('button', { name: /save changes/i }));

    await waitFor(() => {
      expect(onSuccess).not.toHaveBeenCalled();
      expect(mockedToast.error).toHaveBeenCalledWith('Update Failed');
    });
  });
});
