import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Move, Pencil, Printer, Trash2 } from "lucide-react";

interface PalletDetailActionsProps {
  onEdit: () => void;
  onPrint: () => void;
  onMove: () => void;
  onDelete: () => void;
  isPrinting?: boolean;
}

/**
 * Reusable action buttons component for pallet detail operations
 * Provides consistent styling and layout for pallet actions
 */
export const PalletDetailActions: React.FC<PalletDetailActionsProps> = ({
  onEdit,
  onPrint,
  onMove,
  onDelete,
  isPrinting = false,
}) => {
  return (
    <div className="flex flex-wrap gap-2">
      <Button
        variant="outline"
        size="lg"
        onClick={onEdit}
        className="h-12 px-4 text-sm font-medium"
      >
        <Pencil className="mr-2 h-4 w-4" />
        Edit
      </Button>
      
      <Button
        variant="outline"
        size="lg"
        onClick={onPrint}
        disabled={isPrinting}
        className="h-12 px-4 text-sm font-medium"
      >
        <Printer className="mr-2 h-4 w-4" />
        {isPrinting ? "Printing..." : "Print"}
      </Button>
      
      <Button
        variant="outline"
        size="lg"
        onClick={onMove}
        className="h-12 px-4 text-sm font-medium"
      >
        <Move className="mr-2 h-4 w-4" />
        Move
      </Button>
      
      <Button
        variant="destructive"
        size="lg"
        onClick={onDelete}
        className="h-12 px-4 text-sm font-medium"
      >
        <Trash2 className="mr-2 h-4 w-4" />
        Delete
      </Button>
    </div>
  );
};
