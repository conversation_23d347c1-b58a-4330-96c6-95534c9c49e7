"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { ItemCombobox } from "@/components/items/ItemCombobox";

// Define expected Item structure for combobox
interface Item {
  id: string;
  name: string;
  sku?: string | null;
}

// Zod schema for the form
const formSchema = z.object({
  itemId: z.string().min(1, { message: "Please select an item." }),
  quantity: z.coerce // Use coerce to ensure string input from number field becomes number
    .number({
      invalid_type_error: "Quantity must be a number",
      required_error: "Quantity is required",
    })
    .int({ message: "Quantity must be a whole number." })
    .min(1, { message: "Quantity must be at least 1." }),
});

type AddItemFormData = z.infer<typeof formSchema>;

// Define response type (can be simple PalletItem or adjust as needed)
interface AddItemResponse {
  palletId: string;
  itemId: string;
  quantity: number;
  message: string;
  // ... any other fields returned
}

// API function to add item to pallet
async function addItemToPalletApi(
  palletId: string,
  itemData: AddItemFormData,
  appToken: string | null
): Promise<AddItemResponse> {
  if (!appToken) {
    throw new Error(
      "Authentication token not available for adding item to pallet."
    );
  }
  const responseData = await fetchWithAuth(`/api/pallets/${palletId}/items`, {
    method: "POST",
    body: JSON.stringify(itemData),
    token: appToken,
  });
  return responseData as AddItemResponse;
}

// Dialog Props
export interface AddPalletItemDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  palletId: string; // Need the target pallet ID
  palletBarcode?: string; // User-friendly barcode for display
  onSuccess?: (addedItem: AddItemResponse) => void;
}

export function AddPalletItemDialog({
  open,
  onOpenChange,
  palletId,
  palletBarcode,
  onSuccess,
}: AddPalletItemDialogProps) {
  const queryClient = useQueryClient();
  const { appToken } = useAuth();

  const form = useForm<AddItemFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      itemId: "",
      quantity: 1,
    },
  });

  const mutation = useMutation<AddItemResponse, Error, AddItemFormData>({
    mutationFn: (data: AddItemFormData) =>
      addItemToPalletApi(palletId, data, appToken),
    onSuccess: (data) => {
      toast.success(data.message || "Item added to pallet successfully!");
      queryClient.invalidateQueries({ queryKey: ["pallets", palletId] });
      onSuccess?.(data);
      onOpenChange(false);
      form.reset();
    },
    onError: (error: Error) => {
      console.error("Error adding item to pallet:", error);
      toast.error(
        "Failed to add item to pallet",
        (error as Error).message
          ? { description: (error as Error).message }
          : { description: "An unknown error occurred." }
      );
    },
  });

  function onSubmit(values: AddItemFormData) {
    console.log("Submitting:", values);
    mutation.mutate(values);
  }

  React.useEffect(() => {
    if (open) {
      form.reset({ itemId: "", quantity: 1 });
    }
  }, [open, form]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Item to Pallet</DialogTitle>
          <DialogDescription>
            Select an item and specify the quantity to add to pallet{" "}
            {palletBarcode || palletId}.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Use ItemCombobox Component */}
            <FormField
              control={form.control}
              name="itemId"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Item</FormLabel>
                  <FormControl>
                    <ItemCombobox
                      selectedItemId={field.value}
                      onSelect={(itemId) => field.onChange(itemId)}
                      disabled={mutation.isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Quantity Input */}
            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Quantity</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter quantity"
                      min="1"
                      {...field}
                      disabled={mutation.isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={mutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={mutation.isPending || !form.formState.isValid}
              >
                {mutation.isPending ? "Adding..." : "Add Item"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
