"use client";

import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { Pencil, Package } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { DestinationAutocomplete } from "@/components/ui/destination-autocomplete";

// Expected shape of pallet data passed to the dialog
interface Pallet {
  id: string;
  locationId: string | null;
  status: string;
  description?: string | null;
  barcode?: string | null;
  shipToDestination?: string | null;
  // Add other fields if needed for context or display
}

// Zod schema for form validation - removed locationId since we have dedicated Move Pallet button
const formSchema = z.object({
  description: z.string().optional(),
  barcode: z.string().min(1, "Barcode is required"),
  shipToDestination: z.string().optional(),
});

type PalletFormData = z.infer<typeof formSchema>;

// Expected shape of data returned by the API after update
interface UpdatePalletResponse extends Pallet {
  message?: string;
}

// API function to update a pallet (removed locationId since we have dedicated Move Pallet button)
async function updatePallet(
  {
    id,
    palletData,
    warehouseId,
  }: {
    id: string;
    palletData: PalletFormData;
    warehouseId: string;
  },
  appToken: string | null
): Promise<UpdatePalletResponse> {
  if (!appToken) {
    throw new Error("Authentication token not available for updating pallet.");
  }

  const payload = {
    description: palletData.description || undefined,
    barcode: palletData.barcode || undefined,
    shipToDestination: palletData.shipToDestination || undefined,
  };
  // Using fetchWithAuth instead of native fetch
  return fetchWithAuth(`/api/pallets/${id}?warehouseId=${warehouseId}`, {
    method: "PATCH",
    body: JSON.stringify(payload),
    token: appToken,
  });
}

export interface EditPalletDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  pallet: Pallet | null; // Pallet to edit
  onSuccess?: (updatedPallet: UpdatePalletResponse) => void;
}

export function EditPalletDialog({
  open,
  onOpenChange,
  pallet,
  onSuccess,
}: EditPalletDialogProps) {
  const queryClient = useQueryClient();
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  const form = useForm<PalletFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      description: pallet?.description ?? "",
      barcode: pallet?.barcode ?? "",
      shipToDestination: pallet?.shipToDestination ?? "",
    },
  });

  // Update form default values when the pallet prop changes
  useEffect(() => {
    if (pallet) {
      form.reset({
        description: pallet.description ?? "",
        barcode: pallet.barcode ?? "",
        shipToDestination: pallet.shipToDestination ?? "",
      });
    } else {
      form.reset({
        description: "",
        barcode: "",
        shipToDestination: "",
      });
    }
  }, [pallet, form]);

  const mutation = useMutation<
    UpdatePalletResponse,
    unknown,
    { id: string; palletData: PalletFormData }
  >({
    // Pass appToken and warehouseId to updatePallet
    mutationFn: (params) =>
      updatePallet(
        { ...params, warehouseId: currentWarehouse?.id || "" },
        appToken
      ),
    onSuccess: (data) => {
      toast.success(data.message || "Pallet updated successfully.");
      queryClient.invalidateQueries({ queryKey: ["pallets"] });
      queryClient.invalidateQueries({ queryKey: ["pallets", data.id] });
      if (onSuccess) {
        onSuccess(data);
      }
      onOpenChange(false);
    },
    onError: (error) => {
      let errorMessage: string | undefined;
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === "string" && error.length > 0) {
        errorMessage = error;
      }
      toast.error(errorMessage || "Failed to update pallet.");
    },
  });

  function onSubmit(values: PalletFormData) {
    if (!pallet) return;
    mutation.mutate({ id: pallet.id, palletData: values });
  }

  return (
    <Dialog open={open && !!pallet} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        {pallet && (
          <>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Pencil className="h-5 w-5" />
                Edit Pallet: {pallet.barcode || pallet.id}
              </DialogTitle>
              <DialogDescription>
                Update the pallet details. Use the "Move Pallet" button to
                change location.
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                {/* Pallet Information Card */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm">
                      Pallet Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Barcode Input */}
                    <FormField
                      control={form.control}
                      name="barcode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-base font-semibold">
                            Pallet Barcode *
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter pallet barcode"
                              {...field}
                              value={field.value ?? ""}
                              className="font-mono text-lg"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Description Input */}
                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description (Optional)</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Enter pallet description or notes"
                              className="resize-none"
                              rows={3}
                              {...field}
                              value={field.value ?? ""}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Ship To Destination Input */}
                    <FormField
                      control={form.control}
                      name="shipToDestination"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Ship To Destination (Optional)</FormLabel>
                          <FormControl>
                            <DestinationAutocomplete
                              value={field.value ?? ""}
                              onValueChange={field.onChange}
                              placeholder="Select or enter destination..."
                              disabled={mutation.isPending}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>

                <DialogFooter className="gap-2 pt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => onOpenChange(false)}
                    disabled={mutation.isPending}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={mutation.isPending || !form.formState.isValid}
                    className="min-w-[120px]"
                  >
                    {mutation.isPending ? (
                      <>
                        <Package className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Pencil className="mr-2 h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
