"use client";

import React from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { fetchPalletAuditLogs } from "@/lib/api";
import { AuditLog } from "@quildora/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Loader2, Clock, User, Activity } from "lucide-react";
import { format } from "date-fns";

interface AuditLogSectionProps {
  palletId: string;
}

const getActionDisplayName = (action: string): string => {
  const actionMap: Record<string, string> = {
    CREATE_PALLET: "Pallet Created",
    CREATE_PALLET_RECEIVING: "Pallet Created (Receiving)",
    UPDATE_PALLET: "Pallet Updated",
    MOVE_PALLET: "Pallet Moved",
    MOVE: "Pallet Moved",
    RELEASE_PALLET: "Pallet Released",
    PICK_ITEMS: "Items Picked",
    RECEIVE_PALLET: "Pallet Received",
    RETURN_PALLET: "Pallet Returned",
    ADD_PALLET_ITEM: "Item Added",
    REMOVE_PALLET_ITEM: "Item Removed",
    UPDATE_PALLET_ITEM: "Item Updated",
  };
  return actionMap[action] || action.replace(/_/g, " ");
};

const getActionColor = (action: string): string => {
  const colorMap: Record<string, string> = {
    CREATE_PALLET: "bg-green-100 text-green-800",
    CREATE_PALLET_RECEIVING: "bg-green-100 text-green-800",
    UPDATE_PALLET: "bg-blue-100 text-blue-800",
    MOVE_PALLET: "bg-purple-100 text-purple-800",
    MOVE: "bg-purple-100 text-purple-800",
    RELEASE_PALLET: "bg-orange-100 text-orange-800",
    PICK_ITEMS: "bg-yellow-100 text-yellow-800",
    RECEIVE_PALLET: "bg-green-100 text-green-800",
    RETURN_PALLET: "bg-red-100 text-red-800",
    ADD_PALLET_ITEM: "bg-cyan-100 text-cyan-800",
    REMOVE_PALLET_ITEM: "bg-red-100 text-red-800",
    UPDATE_PALLET_ITEM: "bg-blue-100 text-blue-800",
  };
  return colorMap[action] || "bg-gray-100 text-gray-800";
};

const formatDetails = (details: Record<string, any> | null): string => {
  if (!details) return "";

  const detailStrings: string[] = [];

  // Handle move operations with new field names
  if (details.fromLocation && details.toLocation) {
    detailStrings.push(
      `From: ${details.fromLocation} → To: ${details.toLocation}`
    );
  }

  // Handle legacy move format
  if (details.from && details.to) {
    detailStrings.push(`From: ${details.from} → To: ${details.to}`);
  }

  // // Handle move description (removed because it shows duplicate information)
  // if (details.moveDescription) {
  //   detailStrings.push(details.moveDescription);
  // }

  if (details.statusUpdated) {
    detailStrings.push(`Status: ${details.statusUpdated}`);
  }

  if (details.notes) {
    detailStrings.push(`Notes: ${details.notes}`);
  }

  if (details.quantity) {
    detailStrings.push(`Quantity: ${details.quantity}`);
  }

  if (details.itemName) {
    detailStrings.push(`Item: ${details.itemName}`);
  }

  // Handle receiving workflow details
  if (details.createdVia === "receiving_workflow") {
    const receivingDetails: string[] = [];
    if (details.poNumber) receivingDetails.push(`PO: ${details.poNumber}`);
    if (details.locationName)
      receivingDetails.push(`Location: ${details.locationName}`);
    if (details.itemCount) receivingDetails.push(`Items: ${details.itemCount}`);
    if (receivingDetails.length > 0) {
      detailStrings.push(receivingDetails.join(" • "));
    }
  }

  return detailStrings.join(" • ");
};

export const AuditLogSection: React.FC<AuditLogSectionProps> = ({
  palletId,
}) => {
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  const {
    data: auditLogs,
    isLoading,
    isError,
    error,
  } = useQuery<AuditLog[]>({
    queryKey: ["palletAuditLogs", palletId, currentWarehouse?.id],
    queryFn: () =>
      fetchPalletAuditLogs(palletId, currentWarehouse?.id || "", appToken),
    enabled: !!palletId && !!appToken && !!currentWarehouse?.id,
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Audit History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading audit history...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Audit History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-destructive">
              Failed to load audit history:{" "}
              {error instanceof Error ? error.message : "Unknown error"}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!auditLogs || auditLogs.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Audit History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              No audit history available for this pallet.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Audit History
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {auditLogs.map((log, index) => (
            <div key={log.id}>
              <div className="flex items-start gap-4">
                {/* Timeline dot */}
                <div className="flex flex-col items-center">
                  <div className="w-3 h-3 bg-primary rounded-full mt-2" />
                  {index < auditLogs.length - 1 && (
                    <div className="w-px h-16 bg-border mt-2" />
                  )}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge className={getActionColor(log.action)}>
                      {getActionDisplayName(log.action)}
                    </Badge>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      {format(
                        new Date(log.timestamp),
                        "MMM d, yyyy 'at' h:mm a"
                      )}
                    </div>
                  </div>

                  {log.user && (
                    <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
                      <User className="h-3 w-3" />
                      {log.user.name || log.user.email}
                    </div>
                  )}

                  {log.details && (
                    <p className="text-sm text-muted-foreground">
                      {formatDetails(log.details)}
                    </p>
                  )}
                </div>
              </div>

              {index < auditLogs.length - 1 && <Separator className="my-4" />}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
