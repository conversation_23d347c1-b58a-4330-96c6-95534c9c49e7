"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { CreatePalletDialog } from "./CreatePalletDialog"; // Assuming dialog is in the same folder

export function PalletActions() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  return (
    <>
      <div className="mb-4">
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          Create New Pallet
        </Button>
      </div>
      <CreatePalletDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        // onSuccess callback could be added here if needed, e.g., to show a specific message
      />
    </>
  );
}
