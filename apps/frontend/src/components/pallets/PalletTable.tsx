import React from "react";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Pallet } from "@quildora/types";
import { PalletTableRow } from "./PalletTableRow";
import { getTableColumnCount } from "@/utils/palletUtils";

interface PalletTableProps {
  pallets: Pallet[];
  hasActions: boolean;
  onEditClick: (pallet: Pallet) => void;
  onDeleteClick: (pallet: Pallet) => void;
  onPrintPlacard: (pallet: Pallet) => void;
}

/**
 * Reusable table component for displaying pallets
 * Handles table structure, headers, and empty state
 */
export const PalletTable: React.FC<PalletTableProps> = ({
  pallets,
  hasActions,
  onEditClick,
  onDeleteClick,
  onPrintPlacard,
}) => {
  return (
    <div className="border rounded-md bg-card">
      <Table>
        <TableCaption className="text-xs text-slate-500">
          A list of your inventory pallets.
        </TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>Barcode</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Ship To Destination</TableHead>
            <TableHead>Destination Code</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead>Last Moved</TableHead>
            {hasActions && (
              <TableHead className="text-right">Actions</TableHead>
            )}
          </TableRow>
        </TableHeader>
        <TableBody>
          {pallets.length > 0 ? (
            pallets.map((pallet: Pallet) => (
              <PalletTableRow
                key={pallet.id}
                pallet={pallet}
                hasActions={hasActions}
                onEditClick={onEditClick}
                onDeleteClick={onDeleteClick}
                onPrintPlacard={onPrintPlacard}
              />
            ))
          ) : (
            <TableRow>
              <TableCell
                colSpan={getTableColumnCount(hasActions)}
                className="h-24 text-center"
              >
                No pallets found.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};
