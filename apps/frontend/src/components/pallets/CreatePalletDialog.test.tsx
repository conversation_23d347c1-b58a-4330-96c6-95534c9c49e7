import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@/lib/test-utils';
import { CreatePalletDialog } from './CreatePalletDialog';
import * as api from '@/lib/api';
import { toast } from 'sonner';

// Mock dependencies
vi.mock('@/lib/api');
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

const mockedFetchWithAuth = vi.mocked(api.fetchWithAuth);
const mockedToast = vi.mocked(toast);

const mockLocations = [
    { id: 'loc1', name: 'Location A' },
    { id: 'loc2', name: 'Location B' },
];

describe('CreatePalletDialog', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // General mock setup for successful calls
    mockedFetchWithAuth.mockImplementation(async (url) => {
      if (url === '/api/locations') {
        return Promise.resolve(mockLocations);
      }
      if (url === '/api/pallets') {
        return Promise.resolve({ id: 'pallet123' });
      }
      throw new Error(`Unhandled request: ${url}`);
    });
  });

  it('renders the dialog and allows creating a pallet', async () => {
    const onSuccess = vi.fn();
    render(
      <CreatePalletDialog
        open={true}
        onOpenChange={() => {}}
        onSuccess={onSuccess}
      />
    );

    // Wait for locations to load and be selectable
    await waitFor(() => {
        expect(screen.getByRole('combobox', { name: /location/i })).toBeEnabled();
    });

    // Fill out the form
    fireEvent.change(screen.getByLabelText(/description/i), { target: { value: 'New Pallet Description' } });
    fireEvent.change(screen.getByLabelText(/barcode/i), { target: { value: 'BC12345' } });

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /create pallet/i }));

    await waitFor(() => {
      expect(mockedFetchWithAuth).toHaveBeenCalledWith('/api/pallets',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            locationId: undefined,
            description: 'New Pallet Description',
            barcode: 'BC12345',
            shipToDestination: undefined,
          }),
        })
      );
    });

    await waitFor(() => {
      expect(onSuccess).toHaveBeenCalledTimes(1);
      expect(mockedToast.success).toHaveBeenCalled();
    });
  });

  it('shows an error toast on submission failure', async () => {
    // Override mock for this specific test to simulate failure
    mockedFetchWithAuth.mockImplementation(async (url) => {
      if (url === '/api/locations') {
        return Promise.resolve(mockLocations);
      }
      if (url === '/api/pallets') {
        throw new Error('Creation Failed');
      }
      throw new Error(`Unhandled request: ${url}`);
    });
    const onSuccess = vi.fn();

    render(
      <CreatePalletDialog
        open={true}
        onOpenChange={() => {}}
        onSuccess={onSuccess}
      />
    );

    // Wait for the dialog to be ready before interacting with it
    await screen.findByRole('combobox', { name: /location/i });

    // Fill out the form to pass validation
    fireEvent.change(screen.getByLabelText(/description/i), { target: { value: 'Test Description' } });
    fireEvent.change(screen.getByLabelText(/barcode/i), { target: { value: 'TEST-BC' } });

    fireEvent.click(screen.getByRole('button', { name: /create pallet/i }));

    await waitFor(() => {
      expect(onSuccess).not.toHaveBeenCalled();
      expect(mockedToast.error).toHaveBeenCalledWith('Failed to create pallet', {
        description: 'Creation Failed',
      });
    });
  });
});
