import React from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Copy } from "lucide-react";
import { toast } from "sonner";

interface PalletHeaderProps {
  palletId: string;
  barcode?: string | null;
  status: string;
  shipToDestination?: string | null;
  destinationCode?: string | null;
  description?: string | null;
  locationName?: string | null;
  dateCreated?: string;
  lastMovedDate?: string;
}

/**
 * Reusable header component for pallet detail pages
 * Displays pallet information and provides navigation
 */
export const PalletHeader: React.FC<PalletHeaderProps> = ({
  palletId,
  barcode,
  status,
  shipToDestination,
  destinationCode,
  description,
  locationName,
  dateCreated,
  lastMovedDate,
}) => {
  const handleCopyBarcode = () => {
    if (barcode) {
      navigator.clipboard.writeText(barcode);
      toast.success("Barcode copied to clipboard!");
    }
  };

  const handleCopyDestinationCode = () => {
    if (destinationCode) {
      navigator.clipboard.writeText(destinationCode);
      toast.success("Destination code copied to clipboard!");
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Stored":
        return "bg-green-100 text-green-800";
      case "Picking":
        return "bg-yellow-100 text-yellow-800";
      case "Released":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-blue-100 text-blue-800";
    }
  };

  return (
    <div>
      {/* Back Navigation */}
      <div className="mb-4">
        <Link href="/pallets">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Pallets
          </Button>
        </Link>
      </div>

      {/* Pallet Information */}
      <div className="bg-white border rounded-lg p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="text-sm font-medium text-slate-500 mb-1">Barcode</h3>
            <div className="flex items-center gap-2">
              <span className="font-mono text-lg">{barcode || palletId}</span>
              {barcode && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopyBarcode}
                  className="h-6 w-6 p-0"
                >
                  <Copy className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium text-slate-500 mb-1">Status</h3>
            <span
              className={`inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                status
              )}`}
            >
              {status}
            </span>
          </div>

          <div>
            <h3 className="text-sm font-medium text-slate-500 mb-1">
              Destination Name
            </h3>
            <p className="text-sm text-slate-700">{shipToDestination || "-"}</p>
          </div>

          {destinationCode && (
            <div>
              <h3 className="text-sm font-medium text-slate-500 mb-1">
                Destination Code
              </h3>
              <div className="flex items-center gap-2">
                <span className="font-mono text-sm text-slate-700">
                  {destinationCode}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopyDestinationCode}
                  className="h-6 w-6 p-0"
                  aria-label="Copy destination code to clipboard"
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </div>
          )}

          <div>
            <h3 className="text-sm font-medium text-slate-500 mb-1">
              Location
            </h3>
            <p className="text-sm text-slate-700">
              {locationName || "(No Location)"}
            </p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-slate-500 mb-1">
              Description
            </h3>
            <p className="text-sm text-slate-700">{description || "-"}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-slate-500 mb-1">Created</h3>
            <p className="text-sm text-slate-700">
              {dateCreated ? new Date(dateCreated).toLocaleDateString() : "-"}
            </p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-slate-500 mb-1">
              Last Moved
            </h3>
            <p className="text-sm text-slate-700">
              {lastMovedDate
                ? new Date(lastMovedDate).toLocaleDateString()
                : "-"}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
