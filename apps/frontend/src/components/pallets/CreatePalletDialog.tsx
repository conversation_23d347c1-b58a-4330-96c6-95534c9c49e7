import React, { use<PERSON><PERSON>back, useMemo } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { Plus, Trash2, Package, RefreshCw, QrCode } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import ItemCombobox from "@/components/items/ItemCombobox";
import { DestinationAutocomplete } from "@/components/ui/destination-autocomplete";
import { Location } from "@quildora/types";
import { useCreatePallet, useLocations } from "@/hooks/api";
import { useDestinationAutoPopulation } from "@/hooks/useDestinationAutoPopulation";

// Memoized sub-component for item fields to prevent unnecessary re-renders
const ItemField = React.memo(function ItemField({
  index,
  field,
  form,
  onRemove,
  isLoadingItems,
}: {
  index: number;
  field: any;
  form: any;
  onRemove: (index: number) => void;
  isLoadingItems: boolean;
}) {
  const handleRemove = useCallback(() => {
    onRemove(index);
  }, [index, onRemove]);

  return (
    <div className="flex space-x-2 items-end">
      <div className="flex-1">
        <FormField
          control={form.control}
          name={`items.${index}.itemId`}
          render={({ field: itemField }) => (
            <FormItem>
              <FormLabel>Item</FormLabel>
              <FormControl>
                <ItemCombobox
                  selectedItemId={itemField.value}
                  onSelect={itemField.onChange}
                  disabled={isLoadingItems}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <div className="w-24">
        <FormField
          control={form.control}
          name={`items.${index}.quantity`}
          render={({ field: quantityField }) => (
            <FormItem>
              <FormLabel>Qty</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min="1"
                  {...quantityField}
                  onChange={(e) =>
                    quantityField.onChange(parseInt(e.target.value) || 1)
                  }
                  className="text-base md:text-sm"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <Button
        type="button"
        variant="outline"
        size="icon"
        onClick={handleRemove}
        className="h-10 w-10 text-red-600 hover:text-red-700 hover:bg-red-50"
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
});

// Item schema for adding items to pallet during creation
const itemSchema = z.object({
  itemId: z.string().min(1, "Please select an item"),
  quantity: z.coerce
    .number({
      invalid_type_error: "Quantity must be a number",
      required_error: "Quantity is required",
    })
    .int({ message: "Quantity must be a whole number" })
    .min(1, { message: "Quantity must be at least 1" }),
});

// Updated Zod schema for form validation with required location and auto-generated barcode
const formSchema = z.object({
  barcode: z.string().min(1, "Barcode is required"),
  locationId: z.string().min(1, "Location is required"),
  description: z.string().optional(),
  shipToDestination: z.string().optional(),
  destinationCode: z
    .string()
    .optional()
    .refine((val) => !val || /^\d+$/.test(val), {
      message: "Please enter numbers only (e.g., 12345)",
    }),
  items: z.array(itemSchema).optional(),
});

type PalletFormData = z.infer<typeof formSchema>;

// Define the expected shape of data returned by the API
interface CreatePalletResponse {
  id: string;
  barcode: string;
  locationId?: string | null;
  status: string;
  dateCreated: string;
  lastMovedDate: string;
  message?: string;
}

// Generate barcode using the same formula as receiving workflow
const generateBarcode = () => {
  const randomPart = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, "0");
  const timePart = (Date.now() % 100000).toString().padStart(5, "0");
  return `${randomPart}${timePart}`;
};

// API function to fetch locations
async function fetchLocations(appToken: string | null): Promise<Location[]> {
  if (!appToken) {
    console.warn("fetchLocations: No appToken available");
    return [];
  }
  return fetchWithAuth("/api/locations", { token: appToken });
}

export interface CreatePalletDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: (newPallet: CreatePalletResponse) => void;
}

export function CreatePalletDialog({
  open,
  onOpenChange,
  onSuccess,
}: CreatePalletDialogProps) {
  const queryClient = useQueryClient();
  const { appToken } = useAuth(); // Get appToken
  const createPalletMutation = useCreatePallet(); // Use warehouse-aware hook

  // Ref for the location select trigger to enable autofocus
  const locationSelectRef = React.useRef<HTMLButtonElement>(null);

  // Ref for the barcode input to manage text selection
  const barcodeInputRef = React.useRef<HTMLInputElement>(null);

  // Fetch locations for the dropdown using warehouse-aware hook
  const { data: locations, isLoading: isLoadingLocations } = useLocations();

  const form = useForm<PalletFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      barcode: generateBarcode(),
      locationId: "",
      description: "",
      shipToDestination: "",
      destinationCode: "",
      items: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Memoized function to regenerate barcode
  const regenerateBarcode = useCallback(() => {
    form.setValue("barcode", generateBarcode());
  }, [form]);

  // Use unified destination auto-population hook
  const { handleDestinationSelect, handleDestinationCodeLookup, isLookingUp } =
    useDestinationAutoPopulation({ form });

  // Memoized function to add new item
  const addItem = useCallback(() => {
    append({ itemId: "", quantity: 1 });
  }, [append]);

  const onSubmit = useCallback(
    (values: PalletFormData) => {
      // Prepare the payload for the warehouse-aware hook
      const payload = {
        barcode: values.barcode,
        label: values.barcode, // Use barcode for label
        description: values.description || undefined,
        shipToDestination: values.shipToDestination || undefined,
        destinationCode: values.destinationCode || undefined,
        locationId: values.locationId,
        status: values.items && values.items.length > 0 ? "Stored" : "Empty",
        items:
          values.items && values.items.length > 0 ? values.items : undefined,
      };

      createPalletMutation.mutate(payload, {
        onSuccess: (data) => {
          toast.success(
            `Pallet ${data.barcode || data.label} created successfully!`,
            {
              description: `Pallet placed at ${
                locations?.find((l) => l.id === data.locationId)?.name ||
                "selected location"
              }.`,
            }
          );
          if (onSuccess) {
            onSuccess(data as CreatePalletResponse);
          }
          onOpenChange(false);
          form.reset({
            barcode: generateBarcode(),
            locationId: "",
            description: "",
            shipToDestination: "",
            destinationCode: "",
            items: [],
          });
        },
        onError: (error: any) => {
          console.log("MUTATION ERROR OBJECT:", error);
          let errorMessage: string | undefined;
          if (error instanceof Error) {
            errorMessage = error.message;
          } else if (typeof error === "string" && error.length > 0) {
            errorMessage = error;
          }
          toast.error("Failed to create pallet", {
            description: errorMessage,
          });
        },
      });
    },
    [createPalletMutation, onSuccess, onOpenChange, form]
  );

  React.useEffect(() => {
    if (open) {
      form.reset({
        barcode: generateBarcode(),
        locationId: "",
        description: "",
        shipToDestination: "",
        destinationCode: "",
        items: [],
      });

      // Focus on the location field after a short delay to ensure the dialog is fully rendered
      setTimeout(() => {
        // Clear any text selection from the barcode field
        if (barcodeInputRef.current) {
          barcodeInputRef.current.blur();
          barcodeInputRef.current.setSelectionRange(0, 0);
        }

        // Focus the location field
        if (locationSelectRef.current) {
          locationSelectRef.current.focus();
        }
      }, 100);
    }
  }, [open, form]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Create New Pallet
          </DialogTitle>
          <DialogDescription>
            Create a new pallet with auto-generated barcode. Add items during
            creation or keep it empty for later use.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Barcode Field - First and Auto-generated */}
            <FormField
              control={form.control}
              name="barcode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-semibold">
                    Pallet Barcode
                  </FormLabel>
                  <div className="flex gap-2">
                    <FormControl>
                      <Input
                        placeholder="Auto-generated barcode"
                        {...field}
                        ref={(e) => {
                          field.ref(e);
                          barcodeInputRef.current = e;
                        }}
                        className="font-mono text-lg"
                        readOnly
                      />
                    </FormControl>
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={regenerateBarcode}
                      disabled={createPalletMutation.isPending}
                      title="Generate new barcode"
                    >
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Location Select - Required */}
            <FormField
              control={form.control}
              name="locationId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-semibold">
                    Location *
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value ?? ""}
                    disabled={
                      isLoadingLocations ||
                      !locations ||
                      createPalletMutation.isPending
                    }
                  >
                    <FormControl>
                      <SelectTrigger ref={locationSelectRef}>
                        <SelectValue
                          placeholder={
                            isLoadingLocations
                              ? "Loading locations..."
                              : "Select a location"
                          }
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {locations?.map((location) => (
                        <SelectItem key={location.id} value={location.id}>
                          {location.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Ship To Destination Input */}
            <FormField
              control={form.control}
              name="shipToDestination"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-semibold flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Ship To Destination (Optional)
                  </FormLabel>
                  <FormControl>
                    <DestinationAutocomplete
                      value={field.value ?? ""}
                      onValueChange={field.onChange}
                      placeholder="Select or enter destination..."
                      disabled={createPalletMutation.isPending}
                      supportCodes={true}
                      className="text-xl h-12"
                      onDestinationSelect={handleDestinationSelect}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Destination Code Input */}
            <FormField
              control={form.control}
              name="destinationCode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-semibold flex items-center gap-2">
                    <QrCode className="h-4 w-4" />
                    Destination Code (Optional)
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Enter numbers only (e.g., 12345)"
                      className="text-xl h-12"
                      inputMode="numeric"
                      disabled={createPalletMutation.isPending || isLookingUp}
                      onBlur={(e) => {
                        field.onBlur();
                        handleDestinationCodeLookup(e.target.value);
                      }}
                    />
                  </FormControl>
                  <FormDescription>
                    Auto-fills when destination is selected, or enter manually
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description Input */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter pallet description or notes"
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            {/* Items Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-base font-semibold">Items (Optional)</h3>
                  <p className="text-sm text-muted-foreground">
                    Add items to this pallet or leave empty for later
                  </p>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addItem}
                  disabled={createPalletMutation.isPending}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Item
                </Button>
              </div>

              {fields.length > 0 && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm">Pallet Items</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {fields.map((field, index) => (
                      <div key={field.id} className="flex gap-3 items-start">
                        <div className="flex-1">
                          <FormField
                            control={form.control}
                            name={`items.${index}.itemId`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <ItemCombobox
                                    selectedItemId={field.value}
                                    onSelect={(itemId) =>
                                      field.onChange(itemId)
                                    }
                                    disabled={createPalletMutation.isPending}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        <div className="w-24">
                          <FormField
                            control={form.control}
                            name={`items.${index}.quantity`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    type="number"
                                    placeholder="Qty"
                                    min="1"
                                    {...field}
                                    disabled={createPalletMutation.isPending}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={() => remove(index)}
                          disabled={createPalletMutation.isPending}
                          className="shrink-0"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              )}
            </div>

            <DialogFooter className="gap-2 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={createPalletMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={
                  createPalletMutation.isPending ||
                  isLoadingLocations ||
                  !form.watch("locationId")
                }
                className="min-w-[120px]"
              >
                {createPalletMutation.isPending ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Package className="mr-2 h-4 w-4" />
                    Create Pallet
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
