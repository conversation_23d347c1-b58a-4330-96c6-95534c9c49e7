# Warehouse Loading States System

A comprehensive loading state management system for warehouse operations with skeleton loaders, transition animations, error handling, and progress tracking.

## Overview

The warehouse loading states system provides:
- **Skeleton loaders** for different warehouse components
- **Progress tracking** with visual indicators
- **Error handling** with retry mechanisms
- **Transition animations** for smooth warehouse switching
- **Network status monitoring** and offline handling
- **Auto-retry logic** with exponential backoff

## Core Components

### WarehouseLoadingStates.tsx
Collection of reusable loading state components:

#### WarehouseSelectorSkeleton
Loading skeleton for warehouse selector components.

```typescript
<WarehouseSelectorSkeleton 
  variant="header" // "default" | "compact" | "header"
  className="w-full"
/>
```

#### WarehouseListSkeleton
Loading skeleton for warehouse lists.

```typescript
<WarehouseListSkeleton 
  count={3} // Number of skeleton items
  className="space-y-3"
/>
```

#### WarehouseDetailsSkeleton
Loading skeleton for warehouse detail pages.

```typescript
<WarehouseDetailsSkeleton className="max-w-2xl" />
```

#### WarehouseSwitchingOverlay
Full-screen overlay during warehouse switching.

```typescript
<WarehouseSwitchingOverlay 
  isVisible={isChangingWarehouse}
  warehouseName="Main Warehouse"
/>
```

#### WarehouseLoadingError
Error state with retry functionality.

```typescript
<WarehouseLoadingError 
  error="Failed to load warehouses"
  onRetry={retryFunction}
/>
```

#### WarehouseLoadingProgress
Progress indicator with percentage and message.

```typescript
<WarehouseLoadingProgress 
  progress={75}
  message="Loading warehouses..."
/>
```

#### NetworkStatusIndicator
Shows online/offline status.

```typescript
<NetworkStatusIndicator isOnline={navigator.onLine} />
```

#### EmptyWarehouseState
Empty state when no warehouses are available.

```typescript
<EmptyWarehouseState 
  title="No Warehouses Available"
  description="Contact your administrator for access"
  action={<Button>Contact Support</Button>}
/>
```

### WarehouseLoadingPage.tsx
Comprehensive loading page with all states.

```typescript
<WarehouseLoadingPage 
  showNetworkStatus={true}
  autoRetry={true}
  maxRetries={3}
/>
```

**Features:**
- Loading state with progress tracking
- Error state with retry logic
- Empty state for no warehouses
- Success state showing loaded warehouses
- Network status monitoring
- Auto-retry with exponential backoff
- Elapsed time tracking

### WarehouseTransitionWrapper.tsx
Smooth transition animations for warehouse switching.

```typescript
<WarehouseTransitionWrapper 
  showOverlay={true}
  overlayDelay={300}
  transitionDuration={200}
>
  <YourComponent />
</WarehouseTransitionWrapper>
```

**Higher-Order Component:**
```typescript
const EnhancedComponent = withWarehouseTransition(MyComponent, {
  showOverlay: true,
  overlayDelay: 300,
});
```

**Hook:**
```typescript
function MyComponent() {
  const { 
    isChangingWarehouse, 
    isTransitioning, 
    hasWarehouse 
  } = useWarehouseTransition();
}
```

## Enhanced Warehouse Provider

### New Loading States
The warehouse provider now includes enhanced loading states:

```typescript
interface WarehouseContextType {
  // Enhanced loading states
  warehouseError: string | null;
  loadingProgress: number;
  retryCount: number;
  
  // Enhanced actions
  setCurrentWarehouse: (warehouse: WarehouseBasic | null) => Promise<void>;
  retryLoadWarehouses: () => Promise<void>;
}
```

### Progress Tracking
Loading progress is tracked through different stages:
- **25%**: Request initiated
- **75%**: Response received
- **90%**: Data processed
- **100%**: Warehouse selection complete

### Error Handling
Comprehensive error handling with:
- Network error detection
- Retry logic with exponential backoff
- Maximum retry limits
- User-friendly error messages

### Auto-Retry Logic
```typescript
// Exponential backoff: 1s, 2s, 4s, 8s, max 8s
const delay = Math.min(1000 * Math.pow(2, retryCount - 1), 8000);
```

## Usage Examples

### Basic Loading States
```typescript
import { useWarehouse } from '@/components/providers/warehouse-provider';
import { WarehouseSelectorSkeleton } from '@/components/warehouses/WarehouseLoadingStates';

function WarehouseSelector() {
  const { isLoadingWarehouses, warehouseError } = useWarehouse();

  if (isLoadingWarehouses) {
    return <WarehouseSelectorSkeleton variant="header" />;
  }

  if (warehouseError) {
    return <WarehouseLoadingError error={warehouseError} />;
  }

  // Render normal selector
}
```

### Enhanced Warehouse Selector
The WarehouseSelector component now includes:
- Loading skeletons while fetching warehouses
- Error states with retry buttons
- Progress indicators in popover
- Smooth transition animations

### Full Page Loading
```typescript
import { WarehouseLoadingPage } from '@/components/warehouses/WarehouseLoadingPage';

function WarehousePage() {
  const { isLoadingWarehouses, accessibleWarehouses } = useWarehouse();

  if (isLoadingWarehouses || accessibleWarehouses.length === 0) {
    return <WarehouseLoadingPage autoRetry={true} maxRetries={3} />;
  }

  // Render main content
}
```

### Transition Wrapper
```typescript
import { WarehouseTransitionWrapper } from '@/components/warehouses/WarehouseTransitionWrapper';

function App() {
  return (
    <WarehouseTransitionWrapper>
      <MainContent />
    </WarehouseTransitionWrapper>
  );
}
```

## Loading State Patterns

### 1. Skeleton Loading
Show skeleton loaders immediately while data loads:
```typescript
{isLoading ? <WarehouseSelectorSkeleton /> : <WarehouseSelector />}
```

### 2. Progressive Loading
Show progress for longer operations:
```typescript
<WarehouseLoadingProgress 
  progress={loadingProgress}
  message="Loading warehouses..."
/>
```

### 3. Error with Retry
Handle errors gracefully with retry options:
```typescript
<WarehouseLoadingError 
  error={warehouseError}
  onRetry={retryLoadWarehouses}
/>
```

### 4. Empty States
Show helpful empty states:
```typescript
<EmptyWarehouseState 
  title="No Warehouses"
  description="Contact admin for access"
  action={<Button>Contact Support</Button>}
/>
```

### 5. Transition Animations
Smooth transitions during state changes:
```typescript
<WarehouseTransitionWrapper>
  {content}
</WarehouseTransitionWrapper>
```

## Network Handling

### Online/Offline Detection
```typescript
const [isOnline, setIsOnline] = useState(navigator.onLine);

useEffect(() => {
  const handleOnline = () => setIsOnline(true);
  const handleOffline = () => setIsOnline(false);

  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);

  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
}, []);
```

### Auto-Retry on Reconnection
```typescript
useEffect(() => {
  if (autoRetry && warehouseError && retryCount < maxRetries && isOnline) {
    const timeout = setTimeout(() => {
      retryLoadWarehouses();
    }, 2000 * Math.pow(2, retryCount));

    return () => clearTimeout(timeout);
  }
}, [autoRetry, warehouseError, retryCount, maxRetries, isOnline]);
```

## Performance Considerations

### Skeleton Loading
- Prevents layout shift during loading
- Provides immediate visual feedback
- Matches final content structure

### Progress Tracking
- Simulated progress for better UX
- Real progress where possible
- Prevents user uncertainty

### Transition Delays
- Small delays prevent flashing
- Smooth animations improve perceived performance
- Configurable timing for different use cases

### Error Recovery
- Exponential backoff prevents server overload
- Maximum retry limits prevent infinite loops
- User control over retry attempts

## Accessibility

### Screen Reader Support
- Proper ARIA labels on loading states
- Status announcements for state changes
- Keyboard navigation support

### Visual Indicators
- High contrast loading indicators
- Clear error messaging
- Progress information

### Reduced Motion
- Respects user's motion preferences
- Fallback to instant transitions when needed

## Best Practices

### Loading States
1. Show skeleton loaders immediately
2. Provide progress feedback for long operations
3. Handle errors gracefully with retry options
4. Use appropriate empty states

### Transitions
1. Keep transitions short (200-300ms)
2. Use easing functions for natural feel
3. Prevent layout shift during transitions
4. Provide immediate feedback

### Error Handling
1. Show user-friendly error messages
2. Provide clear retry mechanisms
3. Handle network errors specifically
4. Implement reasonable retry limits

### Performance
1. Minimize loading state complexity
2. Use CSS transitions over JavaScript
3. Debounce rapid state changes
4. Clean up timers and listeners
