"use client";

import React, { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Building2,
  Loader2,
  AlertTriangle,
  RefreshCw,
  CheckCircle,
  Clock,
  Wifi,
  WifiOff,
} from "lucide-react";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import {
  WarehouseLoadingIndicator,
  WarehouseLoadingError,
  EmptyWarehouseState,
  NetworkStatusIndicator,
} from "./WarehouseLoadingStates";
import { cn } from "@/lib/utils";

interface WarehouseLoadingPageProps {
  className?: string;
  showNetworkStatus?: boolean;
  autoRetry?: boolean;
  maxRetries?: number;
}

export function WarehouseLoadingPage({
  className,
  showNetworkStatus = true,
  autoRetry = true,
  maxRetries = 3,
}: WarehouseLoadingPageProps) {
  const {
    currentWarehouse,
    accessibleWarehouses,
    isLoadingWarehouses,
    warehouseError,
    loadingProgress,
    retryCount,
    refreshWarehouses,
    retryLoadWarehouses,
  } = useWarehouse();

  const [isOnline, setIsOnline] = useState(true);
  const [loadingStartTime, setLoadingStartTime] = useState<number | null>(null);
  const [elapsedTime, setElapsedTime] = useState(0);

  // Track network status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  // Track loading time (simplified - no artificial updates)
  useEffect(() => {
    if (isLoadingWarehouses) {
      setLoadingStartTime(Date.now());
    } else {
      if (loadingStartTime) {
        setElapsedTime(Date.now() - loadingStartTime);
      }
      setLoadingStartTime(null);
    }
  }, [isLoadingWarehouses, loadingStartTime]);

  // Auto-retry logic
  useEffect(() => {
    if (autoRetry && warehouseError && retryCount < maxRetries && isOnline) {
      const timeout = setTimeout(() => {
        retryLoadWarehouses();
      }, Math.min(1000 * Math.pow(1.5, retryCount), 5000)); // Faster exponential backoff, max 5s

      return () => clearTimeout(timeout);
    }
  }, [
    autoRetry,
    warehouseError,
    retryCount,
    maxRetries,
    isOnline,
    retryLoadWarehouses,
  ]);

  // Format elapsed time
  const formatElapsedTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    return `${seconds}s`;
  };

  // Loading state
  if (isLoadingWarehouses) {
    return (
      <div
        className={cn(
          "flex items-center justify-center min-h-[400px]",
          className
        )}
      >
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="relative">
                <Building2 className="h-12 w-12 text-primary" />
                <Loader2 className="h-6 w-6 text-primary animate-spin absolute -bottom-1 -right-1" />
              </div>
            </div>
            <CardTitle>Loading Warehouses</CardTitle>
            <CardDescription>
              Please wait while we load your accessible warehouses
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <WarehouseLoadingIndicator message="Fetching warehouse data..." />

            {elapsedTime > 0 && (
              <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span>Elapsed: {formatElapsedTime(elapsedTime)}</span>
              </div>
            )}

            {showNetworkStatus && (
              <div className="flex justify-center">
                <NetworkStatusIndicator isOnline={isOnline} />
              </div>
            )}

            {retryCount > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Retry attempt {retryCount} of {maxRetries}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  // Error state
  if (warehouseError) {
    const canRetry = retryCount < maxRetries;
    const isNetworkError =
      !isOnline ||
      warehouseError.includes("network") ||
      warehouseError.includes("connection");

    return (
      <div
        className={cn(
          "flex items-center justify-center min-h-[400px]",
          className
        )}
      >
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="rounded-full bg-destructive/10 p-3">
                <AlertTriangle className="h-8 w-8 text-destructive" />
              </div>
            </div>
            <CardTitle>Failed to Load Warehouses</CardTitle>
            <CardDescription>
              {isNetworkError
                ? "Please check your internet connection and try again"
                : "There was an error loading your warehouses"}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <WarehouseLoadingError
              error={warehouseError}
              onRetry={canRetry ? retryLoadWarehouses : undefined}
            />

            {showNetworkStatus && (
              <div className="flex justify-center">
                <NetworkStatusIndicator isOnline={isOnline} />
              </div>
            )}

            {retryCount >= maxRetries && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Maximum retry attempts reached. Please refresh the page or
                  contact support.
                </AlertDescription>
              </Alert>
            )}

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={refreshWarehouses}
                className="flex-1"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              {canRetry && (
                <Button onClick={retryLoadWarehouses} className="flex-1">
                  Retry ({maxRetries - retryCount} left)
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Empty state
  if (accessibleWarehouses.length === 0) {
    return (
      <div
        className={cn(
          "flex items-center justify-center min-h-[400px]",
          className
        )}
      >
        <EmptyWarehouseState
          title="No Warehouses Available"
          description="You don't have access to any warehouses. Contact your administrator to get warehouse access."
          action={
            <Button onClick={refreshWarehouses} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Check Again
            </Button>
          }
        />
      </div>
    );
  }

  // Success state
  return (
    <div
      className={cn(
        "flex items-center justify-center min-h-[400px]",
        className
      )}
    >
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="rounded-full bg-green-100 p-3">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </div>
          <CardTitle>Warehouses Loaded</CardTitle>
          <CardDescription>
            Successfully loaded {accessibleWarehouses.length} warehouse
            {accessibleWarehouses.length !== 1 ? "s" : ""}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {accessibleWarehouses.map((warehouse) => (
              <div
                key={warehouse.id}
                className={cn(
                  "flex items-center gap-3 p-2 rounded-lg border",
                  currentWarehouse?.id === warehouse.id &&
                    "bg-primary/5 border-primary"
                )}
              >
                <Building2 className="h-4 w-4 text-muted-foreground" />
                <div className="flex-1">
                  <div className="font-medium">{warehouse.name}</div>
                  {warehouse.address && (
                    <div className="text-xs text-muted-foreground">
                      {warehouse.address}
                    </div>
                  )}
                </div>
                {currentWarehouse?.id === warehouse.id && (
                  <CheckCircle className="h-4 w-4 text-primary" />
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
