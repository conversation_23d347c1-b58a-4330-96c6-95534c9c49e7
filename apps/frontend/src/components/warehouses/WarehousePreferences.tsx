"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { AlertCircle, Save, RotateCcw } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { getWarehousePreferences, saveWarehousePreferences } from "@/lib/warehouse-persistence";

interface WarehousePreferencesProps {
  className?: string;
}

export function WarehousePreferences({ className }: WarehousePreferencesProps) {
  const [preferences, setPreferences] = useState({
    autoSelectLastWarehouse: true,
    rememberWarehouseSelection: true,
    sessionOnly: false,
  });
  
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<string | null>(null);

  // Load current preferences
  useEffect(() => {
    const currentPrefs = getWarehousePreferences();
    setPreferences({
      autoSelectLastWarehouse: currentPrefs.autoSelectLastWarehouse,
      rememberWarehouseSelection: currentPrefs.rememberWarehouseSelection,
      sessionOnly: currentPrefs.sessionOnly,
    });
  }, []);

  // Handle preference changes
  const handlePreferenceChange = (key: string, value: boolean) => {
    setPreferences(prev => {
      const updated = { ...prev, [key]: value };
      
      // Auto-disable dependent preferences
      if (key === 'rememberWarehouseSelection' && !value) {
        updated.autoSelectLastWarehouse = false;
        updated.sessionOnly = false;
      }
      
      return updated;
    });
    setHasChanges(true);
    setSaveMessage(null);
  };

  // Handle storage type change
  const handleStorageTypeChange = (value: string) => {
    const sessionOnly = value === 'session';
    handlePreferenceChange('sessionOnly', sessionOnly);
  };

  // Save preferences
  const handleSave = async () => {
    setIsSaving(true);
    try {
      saveWarehousePreferences(preferences);
      setHasChanges(false);
      setSaveMessage("Preferences saved successfully");
      
      // Clear success message after 3 seconds
      setTimeout(() => setSaveMessage(null), 3000);
    } catch (error) {
      console.error('Failed to save preferences:', error);
      setSaveMessage("Failed to save preferences");
    } finally {
      setIsSaving(false);
    }
  };

  // Reset to defaults
  const handleReset = () => {
    setPreferences({
      autoSelectLastWarehouse: true,
      rememberWarehouseSelection: true,
      sessionOnly: false,
    });
    setHasChanges(true);
    setSaveMessage(null);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Warehouse Selection Preferences</CardTitle>
        <CardDescription>
          Configure how warehouse selection is remembered and restored across sessions.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Remember warehouse selection */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="remember-selection">Remember warehouse selection</Label>
            <p className="text-sm text-muted-foreground">
              Save your warehouse choice for future sessions
            </p>
          </div>
          <Switch
            id="remember-selection"
            checked={preferences.rememberWarehouseSelection}
            onCheckedChange={(checked) => 
              handlePreferenceChange('rememberWarehouseSelection', checked)
            }
          />
        </div>

        {/* Auto-select last warehouse */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="auto-select">Auto-select last warehouse</Label>
            <p className="text-sm text-muted-foreground">
              Automatically select your last used warehouse when logging in
            </p>
          </div>
          <Switch
            id="auto-select"
            checked={preferences.autoSelectLastWarehouse}
            onCheckedChange={(checked) => 
              handlePreferenceChange('autoSelectLastWarehouse', checked)
            }
            disabled={!preferences.rememberWarehouseSelection}
          />
        </div>

        {/* Storage type */}
        {preferences.rememberWarehouseSelection && (
          <div className="space-y-3">
            <Label>Storage type</Label>
            <RadioGroup
              value={preferences.sessionOnly ? 'session' : 'persistent'}
              onValueChange={handleStorageTypeChange}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="persistent" id="persistent" />
                <Label htmlFor="persistent" className="font-normal">
                  Persistent (remember across browser sessions)
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="session" id="session" />
                <Label htmlFor="session" className="font-normal">
                  Session only (forget when browser closes)
                </Label>
              </div>
            </RadioGroup>
          </div>
        )}

        {/* Privacy notice */}
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Warehouse preferences are stored locally in your browser and are not shared with other devices or users.
          </AlertDescription>
        </Alert>

        {/* Save message */}
        {saveMessage && (
          <Alert variant={saveMessage.includes('Failed') ? 'destructive' : 'default'}>
            <AlertDescription>{saveMessage}</AlertDescription>
          </Alert>
        )}

        {/* Action buttons */}
        <div className="flex justify-between pt-4">
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={isSaving}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset to defaults
          </Button>
          
          <Button
            onClick={handleSave}
            disabled={!hasChanges || isSaving}
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save preferences'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
