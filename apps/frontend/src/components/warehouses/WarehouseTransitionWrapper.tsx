"use client";

import React, { useEffect, useState } from "react";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { WarehouseSwitchingOverlay } from "./WarehouseLoadingStates";
import { cn } from "@/lib/utils";

interface WarehouseTransitionWrapperProps {
  children: React.ReactNode;
  className?: string;
  showOverlay?: boolean;
  overlayDelay?: number;
  transitionDuration?: number;
}

export function WarehouseTransitionWrapper({
  children,
  className,
  showOverlay = true,
  overlayDelay = 300,
  transitionDuration = 200,
}: WarehouseTransitionWrapperProps) {
  const { isChangingWarehouse, currentWarehouse } = useWarehouse();
  const [showSwitchingOverlay, setShowSwitchingOverlay] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Handle warehouse switching overlay with delay
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (isChangingWarehouse) {
      setIsTransitioning(true);
      
      if (showOverlay) {
        timeoutId = setTimeout(() => {
          setShowSwitchingOverlay(true);
        }, overlayDelay);
      }
    } else {
      setShowSwitchingOverlay(false);
      
      // Keep transitioning state briefly for smooth animation
      timeoutId = setTimeout(() => {
        setIsTransitioning(false);
      }, transitionDuration);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [isChangingWarehouse, showOverlay, overlayDelay, transitionDuration]);

  return (
    <>
      <div
        className={cn(
          "transition-all duration-200 ease-in-out",
          isTransitioning && "opacity-75 pointer-events-none",
          className
        )}
        style={{
          transitionDuration: `${transitionDuration}ms`,
        }}
      >
        {children}
      </div>

      {showSwitchingOverlay && (
        <WarehouseSwitchingOverlay
          isVisible={showSwitchingOverlay}
          warehouseName={currentWarehouse?.name}
        />
      )}
    </>
  );
}

// Higher-order component version
export function withWarehouseTransition<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    showOverlay?: boolean;
    overlayDelay?: number;
    transitionDuration?: number;
  }
) {
  const WrappedComponent = (props: P) => {
    return (
      <WarehouseTransitionWrapper {...options}>
        <Component {...props} />
      </WarehouseTransitionWrapper>
    );
  };

  WrappedComponent.displayName = `withWarehouseTransition(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

// Hook for warehouse transition state
export function useWarehouseTransition() {
  const { isChangingWarehouse, currentWarehouse } = useWarehouse();
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    if (isChangingWarehouse) {
      setIsTransitioning(true);
    } else {
      // Keep transitioning state briefly for smooth animation
      const timeout = setTimeout(() => {
        setIsTransitioning(false);
      }, 200);

      return () => clearTimeout(timeout);
    }
  }, [isChangingWarehouse]);

  return {
    isChangingWarehouse,
    isTransitioning,
    currentWarehouse,
    hasWarehouse: !!currentWarehouse,
  };
}
