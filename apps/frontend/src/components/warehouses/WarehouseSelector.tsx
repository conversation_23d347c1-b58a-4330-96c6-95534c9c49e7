"use client";

import * as React from "react";
import {
  Building2,
  Check,
  ChevronsUpDown,
  AlertTriangle,
  RefreshCw,
} from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import {
  WarehouseSelectorSkeleton,
  WarehouseLoadingError,
  WarehouseLoadingIndicator,
} from "./WarehouseLoadingStates";

interface WarehouseSelectorProps {
  className?: string;
  variant?: "default" | "compact" | "header";
}

export function WarehouseSelector({
  className,
  variant = "default",
}: WarehouseSelectorProps) {
  const {
    currentWarehouse,
    accessibleWarehouses,
    isLoadingWarehouses,
    isChangingWarehouse,
    warehouseError,
    loadingProgress,
    setCurrentWarehouse,
    retryLoadWarehouses,
  } = useWarehouse();

  const [open, setOpen] = React.useState(false);

  const handleWarehouseSelect = async (warehouseId: string) => {
    const selectedWarehouse = accessibleWarehouses.find(
      (w) => w.id === warehouseId
    );
    if (selectedWarehouse) {
      await setCurrentWarehouse(selectedWarehouse);
    }
    setOpen(false);
  };

  // Show loading skeleton while loading
  if (isLoadingWarehouses && accessibleWarehouses.length === 0) {
    return (
      <WarehouseSelectorSkeleton variant={variant} className={className} />
    );
  }

  // Show error state if there's an error and no warehouses
  if (warehouseError && accessibleWarehouses.length === 0) {
    return (
      <WarehouseLoadingError
        error={warehouseError}
        onRetry={retryLoadWarehouses}
        className={className}
      />
    );
  }

  // Don't render if no warehouses available (after loading)
  if (!isLoadingWarehouses && accessibleWarehouses.length === 0) {
    return null;
  }

  // Don't render if only one warehouse (no need to switch)
  if (
    !isLoadingWarehouses &&
    accessibleWarehouses.length === 1 &&
    variant !== "default"
  ) {
    return null;
  }

  const isLoading = isLoadingWarehouses || isChangingWarehouse;

  // Compact variant for header
  if (variant === "header") {
    return (
      <div className={cn("flex items-center", className)}>
        <Building2 className="h-4 w-4 text-slate-600 mr-2" />
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              role="combobox"
              aria-expanded={open}
              className="h-8 px-2 text-sm font-medium text-slate-700 hover:text-slate-900 hover:bg-slate-100"
              disabled={isLoading}
            >
              {currentWarehouse?.name || "Select Warehouse"}
              {isLoading ? (
                <div className="ml-2 h-3 w-3 animate-spin rounded-full border-2 border-solid border-current border-r-transparent" />
              ) : (
                <ChevronsUpDown className="ml-2 h-3 w-3 shrink-0 opacity-50" />
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[280px] p-0" align="start">
            <Command>
              <CommandInput placeholder="Search warehouses..." />
              <CommandList>
                {isLoadingWarehouses && (
                  <div className="p-3">
                    <WarehouseLoadingIndicator
                      message="Loading warehouses..."
                    />
                  </div>
                )}
                {warehouseError && (
                  <div className="p-3">
                    <WarehouseLoadingError
                      error={warehouseError}
                      onRetry={retryLoadWarehouses}
                    />
                  </div>
                )}
                <CommandEmpty>No warehouses found.</CommandEmpty>
                <CommandGroup>
                  {accessibleWarehouses.map((warehouse) => (
                    <CommandItem
                      key={warehouse.id}
                      value={warehouse.id}
                      onSelect={handleWarehouseSelect}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          currentWarehouse?.id === warehouse.id
                            ? "opacity-100"
                            : "opacity-0"
                        )}
                      />
                      <div className="flex flex-col">
                        <span className="font-medium">{warehouse.name}</span>
                        {warehouse.address && (
                          <span className="text-xs text-slate-500">
                            {warehouse.address}
                          </span>
                        )}
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>
    );
  }

  // Compact variant for inline use
  if (variant === "compact") {
    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn("w-full justify-between", className)}
            disabled={isLoading}
          >
            <div className="flex items-center">
              <Building2 className="h-4 w-4 mr-2" />
              {currentWarehouse?.name || "Select Warehouse"}
            </div>
            {isLoading ? (
              <div className="ml-2 h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent" />
            ) : (
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
          <Command>
            <CommandInput placeholder="Search warehouses..." />
            <CommandList>
              <CommandEmpty>No warehouses found.</CommandEmpty>
              <CommandGroup>
                {accessibleWarehouses.map((warehouse) => (
                  <CommandItem
                    key={warehouse.id}
                    value={warehouse.id}
                    onSelect={handleWarehouseSelect}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        currentWarehouse?.id === warehouse.id
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                    <div className="flex flex-col">
                      <span className="font-medium">{warehouse.name}</span>
                      {warehouse.address && (
                        <span className="text-xs text-slate-500">
                          {warehouse.address}
                        </span>
                      )}
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    );
  }

  // Default variant - full featured
  return (
    <div className={cn("space-y-2", className)}>
      <label className="text-sm font-medium text-slate-700">
        Current Warehouse
      </label>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between h-auto p-3"
            disabled={isLoading}
          >
            <div className="flex items-start space-x-3">
              <Building2 className="h-5 w-5 mt-0.5 text-slate-500" />
              <div className="flex flex-col items-start">
                <span className="font-medium">
                  {currentWarehouse?.name || "Select a warehouse"}
                </span>
                {currentWarehouse?.address && (
                  <span className="text-sm text-slate-500">
                    {currentWarehouse.address}
                  </span>
                )}
                {currentWarehouse?.currentUserRole && (
                  <span className="text-xs text-slate-400 mt-1">
                    Role: {currentWarehouse.currentUserRole.replace("_", " ")}
                  </span>
                )}
              </div>
            </div>
            {isLoading ? (
              <div className="ml-2 h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent" />
            ) : (
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
          <Command>
            <CommandInput placeholder="Search warehouses..." />
            <CommandList>
              <CommandEmpty>No warehouses found.</CommandEmpty>
              <CommandGroup>
                {accessibleWarehouses.map((warehouse) => (
                  <CommandItem
                    key={warehouse.id}
                    value={warehouse.id}
                    onSelect={handleWarehouseSelect}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        currentWarehouse?.id === warehouse.id
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                    <div className="flex items-start space-x-3">
                      <Building2 className="h-4 w-4 mt-0.5 text-slate-500" />
                      <div className="flex flex-col">
                        <span className="font-medium">{warehouse.name}</span>
                        {warehouse.address && (
                          <span className="text-sm text-slate-500">
                            {warehouse.address}
                          </span>
                        )}
                        {warehouse.currentUserRole && (
                          <span className="text-xs text-slate-400">
                            Role: {warehouse.currentUserRole.replace("_", " ")}
                          </span>
                        )}
                      </div>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
