"use client";

import React from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { WarehouseBasic } from "@quildora/types";

interface DeleteWarehouseDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  warehouse: WarehouseBasic | null;
  onConfirmDelete: () => void; // Callback for when delete is confirmed
}

export function DeleteWarehouseDialog({
  open,
  onOpenChange,
  warehouse,
  onConfirmDelete,
}: DeleteWarehouseDialogProps) {
  const { appToken } = useAuth();
  const queryClient = useQueryClient();

  const mutation = useMutation<
    void, // Expected response type (usually nothing for delete)
    Error, // Error type
    string // Variables type (warehouseId)
  >({
    mutationFn: async (warehouseId: string) => {
      if (!appToken) {
        throw new Error("Auth token is missing.");
      }
      return fetchWithAuth(`/api/warehouses/${warehouseId}`, {
        method: "DELETE",
        token: appToken,
      });
    },
    onSuccess: () => {
      toast.success("Warehouse Deleted", {
        description: `Warehouse "${warehouse?.name}" has been deleted successfully.`,
      });
      onConfirmDelete(); // Call the passed in delete handler (which should invalidate queries)
      queryClient.invalidateQueries({ queryKey: ["warehouses"] }); // Ensure list is updated
      onOpenChange(false); // Close dialog
    },
    onError: (error) => {
      toast.error("Deletion Failed", {
        description:
          error.message || "Could not delete warehouse. Please try again.",
      });
    },
  });

  if (!warehouse) return null;

  const handleDelete = () => {
    if (!warehouse?.id) {
      toast.error("Cannot Delete", { description: "Warehouse ID is missing." });
      return;
    }
    mutation.mutate(warehouse.id);
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete the
            warehouse "<strong>{warehouse.name}</strong>" and all associated
            data (locations, pallets, etc. - actual cascading delete logic to be
            handled by backend).
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={mutation.isPending}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={mutation.isPending}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-500"
          >
            {mutation.isPending ? "Deleting..." : "Yes, delete warehouse"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
