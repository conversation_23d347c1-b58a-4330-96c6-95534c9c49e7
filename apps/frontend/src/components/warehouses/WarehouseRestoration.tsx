"use client";

import React, { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { 
  Building2, 
  RotateCcw, 
  CheckCircle, 
  AlertTriangle, 
  Clock,
  ArrowRight 
} from "lucide-react";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { useWarehouseSession } from "@/hooks/useWarehouseSession";
import { cn } from "@/lib/utils";

interface WarehouseRestorationProps {
  className?: string;
  autoRestore?: boolean;
  showSessionInfo?: boolean;
}

export function WarehouseRestoration({ 
  className, 
  autoRestore = false,
  showSessionInfo = true 
}: WarehouseRestorationProps) {
  const { currentWarehouse, isLoadingWarehouses } = useWarehouse();
  const { 
    getSessionStats, 
    restoreWarehouseSelection, 
    switchToWarehouse,
    clearWarehouseSelection 
  } = useWarehouseSession();
  
  const [sessionStats, setSessionStats] = useState(() => getSessionStats());
  const [isRestoring, setIsRestoring] = useState(false);
  const [restorationMessage, setRestorationMessage] = useState<string | null>(null);

  // Update session stats periodically
  useEffect(() => {
    const updateStats = () => setSessionStats(getSessionStats());
    updateStats();
    
    const interval = setInterval(updateStats, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, [getSessionStats, currentWarehouse]);

  // Auto-restore if enabled and suggestions available
  useEffect(() => {
    if (autoRestore && !isLoadingWarehouses && !currentWarehouse) {
      const restoreSuggestion = sessionStats.suggestions.find(s => s.type === 'restore');
      if (restoreSuggestion) {
        handleRestore();
      }
    }
  }, [autoRestore, isLoadingWarehouses, currentWarehouse, sessionStats.suggestions]);

  const handleRestore = async () => {
    setIsRestoring(true);
    setRestorationMessage(null);
    
    try {
      const restored = restoreWarehouseSelection();
      if (restored) {
        setRestorationMessage("Warehouse selection restored successfully");
      } else {
        setRestorationMessage("No saved warehouse to restore");
      }
    } catch (error) {
      console.error('Failed to restore warehouse:', error);
      setRestorationMessage("Failed to restore warehouse selection");
    } finally {
      setIsRestoring(false);
      
      // Clear message after 3 seconds
      setTimeout(() => setRestorationMessage(null), 3000);
    }
  };

  const handleSwitchToWarehouse = async (warehouseId: string) => {
    setIsRestoring(true);
    setRestorationMessage(null);
    
    try {
      const switched = switchToWarehouse(warehouseId);
      if (switched) {
        setRestorationMessage("Warehouse selected successfully");
      } else {
        setRestorationMessage("Failed to select warehouse");
      }
    } catch (error) {
      console.error('Failed to switch warehouse:', error);
      setRestorationMessage("Failed to select warehouse");
    } finally {
      setIsRestoring(false);
      
      // Clear message after 3 seconds
      setTimeout(() => setRestorationMessage(null), 3000);
    }
  };

  const handleClearSelection = () => {
    clearWarehouseSelection();
    setRestorationMessage("Warehouse selection cleared");
    setTimeout(() => setRestorationMessage(null), 3000);
  };

  // Format session duration
  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m`;
  };

  if (isLoadingWarehouses) {
    return (
      <Card className={className}>
        <CardContent className="py-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            <span className="ml-2 text-sm text-muted-foreground">Loading warehouses...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          Warehouse Session
        </CardTitle>
        <CardDescription>
          Manage your warehouse selection and session preferences
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current status */}
        <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-2">
            {sessionStats.validation.isValid ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-amber-600" />
            )}
            <span className="font-medium">
              {currentWarehouse ? currentWarehouse.name : 'No warehouse selected'}
            </span>
          </div>
          <Badge variant={sessionStats.validation.isValid ? 'default' : 'secondary'}>
            {sessionStats.validation.isValid ? 'Active' : 'Inactive'}
          </Badge>
        </div>

        {/* Session info */}
        {showSessionInfo && (
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span>Session: {formatDuration(sessionStats.sessionDuration)}</span>
            </div>
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4 text-muted-foreground" />
              <span>{sessionStats.warehouseCount} warehouses available</span>
            </div>
          </div>
        )}

        {/* Suggestions */}
        {sessionStats.suggestions.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Suggestions</h4>
            {sessionStats.suggestions.map((suggestion, index) => (
              <Alert key={index} className="py-3">
                <AlertDescription className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {suggestion.type === 'restore' && <RotateCcw className="h-4 w-4" />}
                    {suggestion.type === 'default' && <ArrowRight className="h-4 w-4" />}
                    <span>{suggestion.reason}</span>
                  </div>
                  {suggestion.warehouse && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => 
                        suggestion.type === 'restore' 
                          ? handleRestore()
                          : handleSwitchToWarehouse(suggestion.warehouse!.id)
                      }
                      disabled={isRestoring}
                    >
                      {suggestion.type === 'restore' ? 'Restore' : 'Select'}
                    </Button>
                  )}
                </AlertDescription>
              </Alert>
            ))}
          </div>
        )}

        {/* Restoration message */}
        {restorationMessage && (
          <Alert variant={restorationMessage.includes('Failed') ? 'destructive' : 'default'}>
            <AlertDescription>{restorationMessage}</AlertDescription>
          </Alert>
        )}

        {/* Actions */}
        <div className="flex justify-between pt-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleClearSelection}
            disabled={!currentWarehouse || isRestoring}
          >
            Clear Selection
          </Button>
          
          <Button
            size="sm"
            onClick={handleRestore}
            disabled={!sessionStats.suggestions.some(s => s.type === 'restore') || isRestoring}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            {isRestoring ? 'Restoring...' : 'Restore Saved'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
