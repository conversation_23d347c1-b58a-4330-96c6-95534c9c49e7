"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Building2,
  <PERSON>ader2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>fresh<PERSON><PERSON>,
  Wifi,
  WifiOff,
} from "lucide-react";
import { cn } from "@/lib/utils";

// Loading skeleton for warehouse selector
export function WarehouseSelectorSkeleton({
  variant = "default",
  className,
}: {
  variant?: "default" | "compact" | "header";
  className?: string;
}) {
  if (variant === "header") {
    return (
      <div className={cn("flex items-center", className)}>
        <Building2 className="h-4 w-4 text-slate-400 mr-2" />
        <Skeleton className="h-8 w-32" />
      </div>
    );
  }

  if (variant === "compact") {
    return (
      <div className={cn("flex items-center gap-2", classN<PERSON>)}>
        <Skeleton className="h-4 w-4 rounded" />
        <Skeleton className="h-6 w-24" />
      </div>
    );
  }

  return (
    <div className={cn("space-y-2", className)}>
      <Skeleton className="h-4 w-20" />
      <Skeleton className="h-10 w-full" />
    </div>
  );
}

// Loading skeleton for warehouse list
export function WarehouseListSkeleton({
  count = 3,
  className,
}: {
  count?: number;
  className?: string;
}) {
  return (
    <div className={cn("space-y-3", className)}>
      {Array.from({ length: count }).map((_, index) => (
        <Card key={index}>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Skeleton className="h-10 w-10 rounded-lg" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
              <Skeleton className="h-6 w-16" />
            </div>
          </CardHeader>
        </Card>
      ))}
    </div>
  );
}

// Loading skeleton for warehouse details
export function WarehouseDetailsSkeleton({
  className,
}: {
  className?: string;
}) {
  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center gap-3">
          <Skeleton className="h-12 w-12 rounded-lg" />
          <div className="space-y-2">
            <Skeleton className="h-6 w-40" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-6 w-20" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-6 w-24" />
          </div>
        </div>
        <div className="space-y-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-20 w-full" />
        </div>
      </CardContent>
    </Card>
  );
}

// Warehouse switching loading overlay
export function WarehouseSwitchingOverlay({
  isVisible,
  warehouseName,
  className,
}: {
  isVisible: boolean;
  warehouseName?: string;
  className?: string;
}) {
  if (!isVisible) return null;

  return (
    <div
      className={cn(
        "fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm",
        className
      )}
    >
      <Card className="w-full max-w-sm mx-4">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center text-center space-y-4">
            <div className="relative">
              <Building2 className="h-12 w-12 text-primary" />
              <Loader2 className="h-6 w-6 text-primary animate-spin absolute -bottom-1 -right-1" />
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold">Switching Warehouse</h3>
              {warehouseName && (
                <p className="text-sm text-muted-foreground">
                  Loading {warehouseName}...
                </p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Warehouse loading error state
export function WarehouseLoadingError({
  error,
  onRetry,
  className,
}: {
  error: string;
  onRetry?: () => void;
  className?: string;
}) {
  return (
    <Alert variant="destructive" className={className}>
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription className="flex items-center justify-between">
        <span>{error}</span>
        {onRetry && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRetry}
            className="ml-2"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Retry
          </Button>
        )}
      </AlertDescription>
    </Alert>
  );
}

// Network status indicator
export function NetworkStatusIndicator({
  isOnline = true,
  className,
}: {
  isOnline?: boolean;
  className?: string;
}) {
  return (
    <div
      className={cn(
        "flex items-center gap-2 text-xs",
        isOnline ? "text-green-600" : "text-red-600",
        className
      )}
    >
      {isOnline ? (
        <Wifi className="h-3 w-3" />
      ) : (
        <WifiOff className="h-3 w-3" />
      )}
      <span>{isOnline ? "Online" : "Offline"}</span>
    </div>
  );
}

// Warehouse loading indicator (no artificial progress)
export function WarehouseLoadingIndicator({
  message,
  className,
}: {
  message?: string;
  className?: string;
}) {
  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center justify-center text-sm">
        <Loader2 className="h-4 w-4 animate-spin mr-2" />
        <span>{message || "Loading warehouses..."}</span>
      </div>
    </div>
  );
}

// Warehouse transition animation wrapper
export function WarehouseTransition({
  children,
  isChanging,
  className,
}: {
  children: React.ReactNode;
  isChanging: boolean;
  className?: string;
}) {
  return (
    <div
      className={cn(
        "transition-all duration-300 ease-in-out",
        isChanging && "opacity-50 pointer-events-none",
        className
      )}
    >
      {children}
    </div>
  );
}

// Empty warehouse state
export function EmptyWarehouseState({
  title = "No Warehouses Available",
  description = "Contact your administrator to get access to warehouses.",
  action,
  className,
}: {
  title?: string;
  description?: string;
  action?: React.ReactNode;
  className?: string;
}) {
  return (
    <Card className={className}>
      <CardContent className="pt-6">
        <div className="flex flex-col items-center text-center space-y-4">
          <div className="rounded-full bg-muted p-3">
            <Building2 className="h-8 w-8 text-muted-foreground" />
          </div>
          <div className="space-y-2">
            <h3 className="font-semibold">{title}</h3>
            <p className="text-sm text-muted-foreground max-w-sm">
              {description}
            </p>
          </div>
          {action}
        </div>
      </CardContent>
    </Card>
  );
}
