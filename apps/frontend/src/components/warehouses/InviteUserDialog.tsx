"use client";

import React from "react";
import {
  Dialog,
  Dialog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { WarehouseBasic } from "@quildora/types";

interface InviteUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  warehouse: WarehouseBasic | null;
}

export function InviteUserDialog({
  open,
  onOpenChange,
  warehouse,
}: InviteUserDialogProps) {
  if (!warehouse) return null;

  // Placeholder for form handling
  const handleSubmitInvite = () => {
    console.log(
      `Submitting invite for warehouse: ${warehouse.name}, User email: [email_placeholder]`
    );
    // API call to backend will go here
    onOpenChange(false); // Close dialog on submit for now
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Invite User to {warehouse.name}</DialogTitle>
          <DialogDescription>
            Enter the email of the user you want to invite as a
            WAREHOUSE_MEMBER.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="email" className="text-right">
              Email
            </Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              className="col-span-3"
            />
          </div>
          {/* Role selection could be added here if WAREHOUSE_ADMIN could invite other roles */}
          {/* For now, it defaults to WAREHOUSE_MEMBER as per requirements */}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmitInvite}>Send Invite</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
