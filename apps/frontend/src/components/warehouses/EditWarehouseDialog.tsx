"use client";

import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { WarehouseBasic, Warehouse } from "@quildora/types";

// Basic Warehouse interface, ensure it matches or is imported if defined elsewhere
// Matches the backend DTO, excluding tenantId which is handled by backend
const warehouseFormSchema = z.object({
  name: z
    .string()
    .min(2, { message: "Warehouse name must be at least 2 characters." }),
  address: z.string().optional().nullable(),
});

type WarehouseFormData = z.infer<typeof warehouseFormSchema>;

interface EditWarehouseDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  warehouse: WarehouseBasic | null; // Warehouse to edit
  onWarehouseUpdated?: () => void; // Callback after successful update
}

export function EditWarehouseDialog({
  open,
  onOpenChange,
  warehouse,
  onWarehouseUpdated,
}: EditWarehouseDialogProps) {
  const { appToken } = useAuth();
  // toast is now directly available from the import
  const queryClient = useQueryClient();

  const form = useForm<WarehouseFormData>({
    resolver: zodResolver(warehouseFormSchema),
    defaultValues: {
      name: warehouse?.name || "",
      address: warehouse?.address || "",
    },
  });

  useEffect(() => {
    if (warehouse) {
      form.reset({
        name: warehouse.name,
        address: warehouse.address || "",
      });
    }
  }, [warehouse, form, open]); // Reset form when warehouse or open state changes

  const mutation = useMutation<
    Warehouse, // Expected response type
    Error, // Error type
    WarehouseFormData // Variables type (payload)
  >({
    mutationFn: async (data: WarehouseFormData) => {
      if (!warehouse?.id || !appToken) {
        throw new Error("Warehouse ID or auth token is missing.");
      }
      return fetchWithAuth(`/api/warehouses/${warehouse.id}`, {
        method: "PATCH",
        token: appToken,
        body: JSON.stringify(data),
      });
    },
    onSuccess: (data) => {
      toast.success("Warehouse Updated", {
        description: `Warehouse "${data.name}" has been updated successfully.`,
      });
      if (onWarehouseUpdated) {
        onWarehouseUpdated();
      }
      queryClient.invalidateQueries({ queryKey: ["warehouses"] }); // Invalidate cache for warehouse list
      queryClient.invalidateQueries({ queryKey: ["warehouse", warehouse?.id] }); // Invalidate cache for this specific warehouse if viewed elsewhere
      onOpenChange(false); // Close dialog
    },
    onError: (error) => {
      toast.error("Update Failed", {
        description:
          error.message || "Could not update warehouse. Please try again.",
      });
    },
  });

  const onSubmit = (data: WarehouseFormData) => {
    mutation.mutate(data);
  };

  if (!warehouse) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <DialogHeader>
              <DialogTitle>Edit Warehouse: {warehouse.name}</DialogTitle>
              <DialogDescription>
                Update the details for this warehouse. Click save when you're
                done.
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem className="grid grid-cols-4 items-center gap-4">
                    <FormLabel className="text-right">Name</FormLabel>
                    <FormControl className="col-span-3">
                      <Input {...field} placeholder="Main Warehouse" />
                    </FormControl>
                    <FormMessage className="col-span-4 text-right" />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem className="grid grid-cols-4 items-center gap-4">
                    <FormLabel className="text-right">Address</FormLabel>
                    <FormControl className="col-span-3">
                      <Input
                        {...field}
                        placeholder="123 Industrial Park, Anytown, USA"
                        value={field.value ?? ""}
                      />
                    </FormControl>
                    <FormMessage className="col-span-4 text-right" />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={mutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={mutation.isPending || !form.formState.isDirty}
              >
                {mutation.isPending ? "Saving..." : "Save Changes"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
