"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { toast } from "sonner";
import { Warehouse } from "@quildora/types";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useRouter } from "next/navigation";
import { WarehouseBasic } from "@quildora/types";

const warehouseFormSchema = z.object({
  name: z
    .string()
    .min(2, { message: "Warehouse name must be at least 2 characters." }),
  address: z.string().optional().nullable(),
});

type WarehouseFormData = z.infer<typeof warehouseFormSchema>;

interface CreateWarehouseFormProps {
  onWarehouseCreated?: (newWarehouse: WarehouseBasic) => void;
}

export function CreateWarehouseForm({
  onWarehouseCreated,
}: CreateWarehouseFormProps) {
  const { appToken } = useAuth();
  const queryClient = useQueryClient();
  const router = useRouter();

  const form = useForm<WarehouseFormData>({
    resolver: zodResolver(warehouseFormSchema),
    defaultValues: {
      name: "",
      address: "",
    },
  });

  const mutation = useMutation<
    Warehouse, // Expected response type
    Error, // Error type
    WarehouseFormData // Variables type (payload)
  >({
    mutationFn: async (data: WarehouseFormData) => {
      if (!appToken) {
        throw new Error("Auth token is missing.");
      }
      return fetchWithAuth(`/api/warehouses`, {
        method: "POST",
        token: appToken,
        body: JSON.stringify(data),
      });
    },
    onSuccess: (data) => {
      toast.success("Warehouse Created", {
        description: `Warehouse "${data.name}" has been created successfully.`,
      });
      queryClient.invalidateQueries({ queryKey: ["warehouses"] });
      if (onWarehouseCreated) {
        onWarehouseCreated(data);
      }
      router.push("/settings/warehouses"); // Redirect after successful creation
    },
    onError: (error) => {
      toast.error("Creation Failed", {
        description:
          error.message || "Could not create warehouse. Please try again.",
      });
    },
  });

  const onSubmit = (data: WarehouseFormData) => {
    mutation.mutate(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Warehouse Name</FormLabel>
              <FormControl>
                <Input
                  placeholder="e.g., Main Distribution Center"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Address (Optional)</FormLabel>
              <FormControl>
                <Input
                  placeholder="123 Industrial Park, Anytown, USA"
                  {...field}
                  value={field.value ?? ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-end space-x-3">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/settings/warehouses")}
            disabled={mutation.isPending}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={mutation.isPending || !form.formState.isDirty}
          >
            {mutation.isPending ? "Creating..." : "Create Warehouse"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
