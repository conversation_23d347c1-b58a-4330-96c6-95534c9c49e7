"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation"; // Added
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { CalendarIcon, ArrowRight } from "lucide-react";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { fetchWithAuth } from "@/lib/api";
import { Location, LocationCategory } from "@quildora/types";

// Zod Schema for validation
const shipmentDetailsFormSchema = z.object({
  poNumber: z.string().min(1, "PO Number / Reference is required."),
  supplier: z.string().optional(),
  expectedArrivalDate: z.date().optional(),
  defaultReceivingLocationId: z
    .string()
    .min(1, "Default receiving location is required."),
});

type ShipmentDetailsFormData = z.infer<typeof shipmentDetailsFormSchema>;

// API Fetching Functions
const fetchReceivingLocations = async (
  warehouseId: string,
  appToken: string | null
): Promise<Location[]> => {
  if (!appToken || !warehouseId) return [];
  return fetchWithAuth(
    `/api/locations?warehouseId=${warehouseId}&category=${LocationCategory.Receiving}`,
    { token: appToken }
  );
};

export function ShipmentDetailsForm() {
  const router = useRouter(); // Added
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse(); // Use current warehouse from provider

  const form = useForm<ShipmentDetailsFormData>({
    resolver: zodResolver(shipmentDetailsFormSchema),
    defaultValues: {
      poNumber: "",
      supplier: "",
      defaultReceivingLocationId: "",
    },
  });

  const { data: receivingLocations, isLoading: isLoadingLocations } = useQuery<
    Location[],
    Error
  >({
    queryKey: ["locations", "receiving", currentWarehouse?.id, appToken],
    queryFn: () =>
      fetchReceivingLocations(currentWarehouse?.id || "", appToken),
    enabled: !isAuthLoading && !!appToken && !!currentWarehouse?.id,
  });

  const onSubmit = (data: ShipmentDetailsFormData) => {
    console.log("Shipment Details Submitted:", data);
    toast.success("Shipment details captured!", {
      description: `Proceeding to receive pallets for PO: ${data.poNumber}`,
    });
    // Navigate to the pallet receiving page for this PO
    router.push(`/receiving/${encodeURIComponent(data.poNumber)}`);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="poNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Purchase Order (PO) Number / Reference</FormLabel>
              <FormControl>
                <Input placeholder="Enter PO Number or Reference" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="supplier"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Supplier (Optional)</FormLabel>
              <FormControl>
                <Input placeholder="Enter supplier name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="expectedArrivalDate"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Expected Arrival Date (Optional)</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="defaultReceivingLocationId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Default Receiving Bay/Location</FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
                disabled={isLoadingLocations || !currentWarehouse?.id}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a receiving location..." />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {receivingLocations?.map((location) => (
                    <SelectItem key={location.id} value={location.id}>
                      {location.name}
                    </SelectItem>
                  ))}
                  {!isLoadingLocations &&
                    !receivingLocations?.length &&
                    currentWarehouse?.id && (
                      <SelectItem value="loading_or_empty" disabled>
                        No receiving locations found for this warehouse.
                      </SelectItem>
                    )}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full">
          Start Receiving Pallets
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </form>
    </Form>
  );
}
