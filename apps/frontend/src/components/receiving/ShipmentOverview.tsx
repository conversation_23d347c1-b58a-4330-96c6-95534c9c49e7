import React from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface ShipmentOverviewProps {
  purchaseOrderNumber: string;
  status: string;
  totalPallets: number;
}

/**
 * Reusable component for displaying shipment overview information
 * Shows PO number, status, and total pallet count
 */
export const ShipmentOverview: React.FC<ShipmentOverviewProps> = ({
  purchaseOrderNumber,
  status,
  totalPallets,
}) => {
  const getBadgeVariant = (
    status: string
  ): "default" | "secondary" | "destructive" | "outline" => {
    switch (status?.toLowerCase()) {
      case "stored":
      case "received":
        return "default";
      case "receiving":
      case "processing":
        return "secondary";
      case "empty":
        return "outline";
      case "shipping":
      case "picking":
        return "destructive";
      default:
        return "secondary";
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Shipment Overview</CardTitle>
      </CardHeader>
      <CardContent className="grid gap-4 md:grid-cols-3">
        <div>
          <p className="text-sm font-medium text-muted-foreground">PO Number</p>
          <p className="text-lg font-semibold">{purchaseOrderNumber}</p>
        </div>
        <div>
          <p className="text-sm font-medium text-muted-foreground">
            Shipment Status
          </p>
          <Badge variant={getBadgeVariant(status)}>{status}</Badge>
        </div>
        <div>
          <p className="text-sm font-medium text-muted-foreground">
            Total Pallets
          </p>
          <p className="text-lg font-semibold">{totalPallets}</p>
        </div>
      </CardContent>
    </Card>
  );
};
