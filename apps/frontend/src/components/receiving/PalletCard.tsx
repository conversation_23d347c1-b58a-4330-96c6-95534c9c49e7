import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { LocationInput } from "@/components/locations/LocationInput";
import { PalletItem, Item, Location } from "@quildora/types";

// Define the pallet type with detailed items
type PalletWithDetails = {
  id: string;
  label?: string | null;
  barcode?: string | null;
  status: string;
  description?: string | null;
  locationId?: string | null;
  location: Location | null;
  palletItems: (PalletItem & { item: Item })[];
};

interface PalletCardProps {
  pallet: PalletWithDetails;
  onLocationAssign: (palletId: string, locationId: string) => void;
  isUpdatingLocation: boolean;
}

/**
 * Reusable component for displaying individual pallet information
 * Shows pallet details, contents, and location assignment
 */
export const PalletCard: React.FC<PalletCardProps> = ({
  pallet,
  onLocationAssign,
  isUpdatingLocation,
}) => {
  const getBadgeVariant = (
    status: string
  ): "default" | "secondary" | "destructive" | "outline" => {
    switch (status?.toLowerCase()) {
      case "stored":
      case "received":
        return "default";
      case "receiving":
      case "processing":
        return "secondary";
      case "empty":
        return "outline";
      case "shipping":
      case "picking":
        return "destructive";
      default:
        return "secondary";
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg">
            {pallet.label || `Pallet ${pallet.barcode}`}
          </CardTitle>
          <Badge variant={getBadgeVariant(pallet.status)}>
            {pallet.status}
          </Badge>
        </div>
        {pallet.description && (
          <p className="text-sm text-muted-foreground pt-2">
            {pallet.description}
          </p>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="font-semibold mb-2 text-sm">Contents:</h4>
          {pallet.palletItems && pallet.palletItems.length > 0 ? (
            <ul className="space-y-1 text-sm list-disc pl-4">
              {pallet.palletItems.map((palletItem) => (
                <li key={palletItem.id}>
                  <span className="font-medium">{palletItem.quantity}</span> x{" "}
                  {palletItem.item.name}
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-sm text-muted-foreground">Pallet is empty.</p>
          )}
        </div>

        <div>
          <h4 className="font-semibold mb-2 text-sm">Current Location:</h4>
          <p className="text-sm text-muted-foreground">
            {pallet.location?.name || "No location assigned"}
          </p>
        </div>

        <div>
          <LocationInput
            value={pallet.locationId || undefined}
            onLocationSelect={(locationId) =>
              onLocationAssign(pallet.id, locationId)
            }
            placeholder="Assign storage location..."
            label="Assign Location"
            disabled={isUpdatingLocation}
            filterCategory="Storage"
          />
        </div>
      </CardContent>
    </Card>
  );
};
