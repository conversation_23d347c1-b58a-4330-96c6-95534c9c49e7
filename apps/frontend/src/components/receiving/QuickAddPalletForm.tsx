"use client";

import React from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { Plus, Trash2, Package, RefreshCw, QrCode } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Location, LocationCategory, Pallet } from "@quildora/types";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { fetchWithAuth } from "@/lib/api";
import { ItemCombobox } from "@/components/items/ItemCombobox";
import { DestinationAutocomplete } from "@/components/ui/destination-autocomplete";
import { useCreatePallet } from "@/hooks/api";
import { useDestinationAutoPopulation } from "@/hooks/useDestinationAutoPopulation";

// Item schema for adding items to pallet during creation
const itemSchema = z.object({
  itemId: z.string().min(1, "Please select an item"),
  quantity: z.coerce
    .number({
      invalid_type_error: "Quantity must be a number",
      required_error: "Quantity is required",
    })
    .int({ message: "Quantity must be a whole number" })
    .min(1, { message: "Quantity must be at least 1" }),
});

// Zod Schema
const quickAddPalletSchema = z.object({
  barcode: z.string().min(1, "Barcode is required."),
  receivingLocationId: z.string().min(1, "Receiving location is required."),
  description: z.string().optional(),
  shipToDestination: z.string().optional(),
  destinationCode: z
    .string()
    .optional()
    .refine((val) => !val || /^\d+$/.test(val), {
      message: "Please enter numbers only (e.g., 12345)",
    }),
  items: z.array(itemSchema).optional(),
});

type QuickAddPalletFormData = z.infer<typeof quickAddPalletSchema>;

// API Fetching Functions
const fetchReceivingLocations = async (
  warehouseId: string,
  appToken: string | null
): Promise<Location[]> => {
  if (!appToken || !warehouseId) return [];
  // Include warehouse context in the API call
  return fetchWithAuth(
    `/api/locations?category=${LocationCategory.Receiving}&warehouseId=${warehouseId}`,
    { token: appToken }
  );
};

export function QuickAddPalletForm() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();
  const createPalletMutation = useCreatePallet();

  const generateBarcode = () => {
    const randomPart = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, "0");
    const timePart = (Date.now() % 100000).toString().padStart(5, "0");
    return `${randomPart}${timePart}`;
  };

  const form = useForm<QuickAddPalletFormData>({
    resolver: zodResolver(quickAddPalletSchema),
    defaultValues: {
      barcode: generateBarcode(),
      receivingLocationId: "",
      description: "",
      shipToDestination: "",
      destinationCode: "",
      items: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Function to regenerate barcode
  const regenerateBarcode = () => {
    form.setValue("barcode", generateBarcode());
  };

  // Function to add new item
  const addItem = () => {
    append({ itemId: "", quantity: 1 });
  };

  const { data: locations, isLoading: isLoadingLocations } = useQuery<
    Location[],
    Error
  >({
    queryKey: [
      "locations",
      { category: LocationCategory.Receiving },
      currentWarehouse?.id,
      appToken,
    ],
    queryFn: () =>
      fetchReceivingLocations(currentWarehouse?.id || "", appToken),
    enabled: !isAuthLoading && !!appToken && !!currentWarehouse?.id,
  });

  // Use unified destination auto-population hook
  const { handleDestinationSelect, handleDestinationCodeLookup, isLookingUp } =
    useDestinationAutoPopulation({ form });

  const onSubmit = (data: QuickAddPalletFormData) => {
    // Prepare the payload for the warehouse-aware hook
    const payload = {
      barcode: data.barcode,
      label: data.barcode, // Use barcode for the label as well
      description: data.description || undefined,
      locationId: data.receivingLocationId,
      status: data.items && data.items.length > 0 ? "Receiving" : "Empty",
      shipToDestination: data.shipToDestination || undefined,
      destinationCode: data.destinationCode || undefined,
      items: data.items && data.items.length > 0 ? data.items : undefined,
    };

    createPalletMutation.mutate(payload, {
      onSuccess: (data) => {
        toast.success("Pallet Created!", {
          description: `Pallet ${
            data.label || data.barcode
          } created and placed at ${
            locations?.find((l) => l.id === data.locationId)?.name ||
            "selected location"
          }.`,
        });
        router.push("/pallets"); // Navigate to pallet list or back to receiving dashboard
      },
      onError: (error) => {
        toast.error("Pallet Creation Failed", {
          description: error.message || "An unknown error occurred.",
        });
      },
    });
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-6 max-w-lg mx-auto"
      >
        <FormField
          control={form.control}
          name="barcode"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-base font-semibold">
                Pallet Barcode
              </FormLabel>
              <div className="flex gap-2">
                <FormControl>
                  <Input
                    placeholder="Auto-generated barcode"
                    {...field}
                    className="font-mono text-lg"
                    readOnly
                  />
                </FormControl>
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={regenerateBarcode}
                  disabled={createPalletMutation.isPending}
                  title="Generate new barcode"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="receivingLocationId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Receiving Location</FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
                disabled={isLoadingLocations || createPalletMutation.isPending}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a receiving dock..." />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {locations?.map((location) => (
                    <SelectItem key={location.id} value={location.id}>
                      {location.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter any notes or details"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="shipToDestination"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-base font-semibold flex items-center gap-2">
                <Package className="h-4 w-4" />
                Ship To Destination (Optional)
              </FormLabel>
              <FormControl>
                <DestinationAutocomplete
                  value={field.value ?? ""}
                  onValueChange={field.onChange}
                  placeholder="Select or enter destination..."
                  disabled={createPalletMutation.isPending}
                  supportCodes={true}
                  className="text-xl h-12"
                  onDestinationSelect={handleDestinationSelect}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="destinationCode"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-base font-semibold flex items-center gap-2">
                <QrCode className="h-4 w-4" />
                Destination Code (Optional)
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Enter numbers only (e.g., 12345)"
                  className="text-xl h-12"
                  inputMode="numeric"
                  disabled={createPalletMutation.isPending || isLookingUp}
                  onBlur={(e) => {
                    field.onBlur();
                    handleDestinationCodeLookup(e.target.value);
                  }}
                />
              </FormControl>
              <FormDescription>
                Auto-fills when destination is selected, or enter manually
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Separator />

        {/* Items Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-base font-semibold">Items (Optional)</h3>
              <p className="text-sm text-muted-foreground">
                Add items to this pallet during receiving
              </p>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addItem}
              disabled={createPalletMutation.isPending}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Item
            </Button>
          </div>

          {fields.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Pallet Items</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {fields.map((field, index) => (
                  <div key={field.id} className="flex gap-3 items-start">
                    <div className="flex-1">
                      <FormField
                        control={form.control}
                        name={`items.${index}.itemId`}
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <ItemCombobox
                                selectedItemId={field.value}
                                onSelect={(itemId) => field.onChange(itemId)}
                                disabled={createPalletMutation.isPending}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <div className="w-24">
                      <FormField
                        control={form.control}
                        name={`items.${index}.quantity`}
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="Qty"
                                min="1"
                                {...field}
                                disabled={createPalletMutation.isPending}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => remove(index)}
                      disabled={createPalletMutation.isPending}
                      className="shrink-0"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={
              createPalletMutation.isPending ||
              !form.watch("receivingLocationId")
            }
            className="min-w-[120px]"
          >
            {createPalletMutation.isPending ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Package className="mr-2 h-4 w-4" />
                Create Pallet
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
