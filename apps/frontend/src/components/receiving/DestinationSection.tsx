import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Printer } from "lucide-react";
import { PalletCard } from "./PalletCard";
import { PalletItem, Item, Location } from "@quildora/types";

// Define the pallet type with detailed items
type PalletWithDetails = {
  id: string;
  label?: string | null;
  barcode?: string | null;
  status: string;
  description?: string | null;
  locationId?: string | null;
  location: Location | null;
  palletItems: (PalletItem & { item: Item })[];
};

interface DestinationSectionProps {
  destination: string;
  pallets: PalletWithDetails[];
  onBulkPrint: (destination: string) => void;
  onLocationAssign: (palletId: string, locationId: string) => void;
  isUpdatingLocation: string | null;
  isPrintingPlacards: boolean;
}

/**
 * Reusable component for displaying pallets grouped by destination
 * Shows destination header with bulk print button and pallet cards
 */
export const DestinationSection: React.FC<DestinationSectionProps> = ({
  destination,
  pallets,
  onBulkPrint,
  onLocationAssign,
  isUpdatingLocation,
  isPrintingPlacards,
}) => {
  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold tracking-tight">
          Destination: {destination}
        </h2>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onBulkPrint(destination)}
            disabled={isPrintingPlacards || pallets.length === 0}
          >
            <Printer className="h-4 w-4 mr-2" />
            {isPrintingPlacards ? "Printing..." : "Print All Placards"}
          </Button>
        </div>
      </div>
      {pallets.length > 0 ? (
        <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
          {pallets.map((pallet) => (
            <PalletCard
              key={pallet.id}
              pallet={pallet}
              onLocationAssign={onLocationAssign}
              isUpdatingLocation={isUpdatingLocation === pallet.id}
            />
          ))}
        </div>
      ) : (
        <p>No pallets for this destination.</p>
      )}
    </div>
  );
};
