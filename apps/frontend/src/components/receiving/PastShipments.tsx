"use client";

import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { ChevronDown, ChevronRight } from "lucide-react";
import Link from "next/link";
import { useAuth } from "@/components/providers/auth-provider";
import { fetchShipments } from "@/lib/api";
import { ShipmentListResponse, ShipmentListItem } from "@quildora/types";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface PastShipmentsProps {
  className?: string;
}

export function PastShipments({ className }: PastShipmentsProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const { appToken, isLoading: isAuthLoading } = useAuth();

  const {
    data: shipmentsResponse,
    isLoading,
    isError,
    error,
  } = useQuery<ShipmentListResponse, Error>({
    queryKey: ["shipments", appToken, currentPage],
    queryFn: () =>
      fetchShipments(appToken, {
        page: currentPage,
        limit: 10,
        sortBy: "createdAt",
        sortOrder: "desc",
      }),
    enabled: !isAuthLoading && !!appToken && isExpanded,
    retry: 1,
  });

  const shipments = shipmentsResponse?.data || [];
  const totalPages = shipmentsResponse?.totalPages || 0;

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
      case "received":
        return "bg-green-100 text-green-800";
      case "receiving":
        return "bg-blue-100 text-blue-800";
      case "pending":
      case "processing":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <Button
          variant="ghost"
          onClick={handleToggle}
          className="flex items-center justify-between w-full p-0 h-auto font-medium text-left"
        >
          <span className="text-lg">Past Shipments</span>
          {isExpanded ? (
            <ChevronDown className="h-5 w-5" />
          ) : (
            <ChevronRight className="h-5 w-5" />
          )}
        </Button>
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0">
          {isLoading && (
            <div className="flex justify-center py-8">
              <div className="text-sm text-gray-500">Loading shipments...</div>
            </div>
          )}

          {isError && (
            <div className="text-center py-8">
              <div className="text-sm text-red-600">
                Error loading shipments: {error?.message}
              </div>
            </div>
          )}

          {!isLoading && !isError && shipments.length === 0 && (
            <div className="text-center py-8">
              <div className="text-sm text-gray-500">
                No past shipments found.
              </div>
            </div>
          )}

          {!isLoading && !isError && shipments.length > 0 && (
            <>
              <div className="border rounded-md">
                <Table>
                  <TableCaption>
                    {shipmentsResponse?.count} total shipment
                    {shipmentsResponse?.count !== 1 ? "s" : ""}
                  </TableCaption>
                  <TableHeader>
                    <TableRow>
                      <TableHead>PO Number</TableHead>
                      <TableHead>Total Pallets</TableHead>
                      <TableHead>Date Received</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Supplier</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {shipments.map((shipment: ShipmentListItem) => (
                      <TableRow key={shipment.id}>
                        <TableCell className="font-medium">
                          <Link
                            href={`/receiving/${shipment.poNumber}`}
                            className="text-blue-600 hover:text-blue-800 hover:underline"
                          >
                            {shipment.poNumber}
                          </Link>
                        </TableCell>
                        <TableCell>{shipment.palletCount}</TableCell>
                        <TableCell>
                          {formatDate(shipment.createdAt.toString())}
                        </TableCell>
                        <TableCell>
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(
                              shipment.status
                            )}`}
                          >
                            {shipment.status}
                          </span>
                        </TableCell>
                        <TableCell>{shipment.supplier || "-"}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center items-center space-x-2 mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage <= 1}
                  >
                    Previous
                  </Button>

                  <span className="text-sm text-gray-600">
                    Page {currentPage} of {totalPages}
                  </span>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </>
          )}
        </CardContent>
      )}
    </Card>
  );
}
