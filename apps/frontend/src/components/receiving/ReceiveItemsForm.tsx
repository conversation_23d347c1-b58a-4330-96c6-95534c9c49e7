"use client";

import React from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { ItemCombobox } from "@/components/items/ItemCombobox";
import { DestinationAutocomplete } from "@/components/ui/destination-autocomplete";
import {
  Location,
  LocationCategory,
  Pallet,
  PalletItem,
  Shipment,
} from "@quildora/types";
import { Trash2, RefreshCw, Plus, QrCode, Package } from "lucide-react";
import { useAuth } from "@/components/providers/auth-provider"; // Added
import { useWarehouse } from "@/components/providers/warehouse-provider"; // Added
import { fetchWithAuth } from "@/lib/api"; // Added
import { useDestinationAutoPopulation } from "@/hooks/useDestinationAutoPopulation";

// Zod Schema for validation - mirrors ReceiveItemsDto structure
const receiveItemDetailSchema = z.object({
  itemId: z.string().min(1, "Item is required."),
  quantity: z.number().int().min(1, "Quantity must be at least 1."),
});

const receiveItemsFormSchema = z
  .object({
    poNumber: z.string(), // Passed as a prop, not user-editable in this form.
    barcode: z.string().min(1, "Barcode is required."),
    shipToDestination: z.string().optional(),
    destinationCode: z
      .string()
      .optional()
      .refine((val) => !val || /^\d+$/.test(val), {
        message: "Please enter numbers only (e.g., 12345)",
      }),
    description: z.string().optional(),
    receivingLocationId: z.string().min(1, "Receiving location is required."),
    warehouseId: z.string().optional(), // Add warehouse ID for backend validation
    items: z.array(receiveItemDetailSchema),
  })
  .refine(
    (data) => {
      // A pallet is valid if it has a description OR at least one item with a selected itemId.
      const hasDescription =
        data.description && data.description.trim().length > 0;
      const hasValidItems =
        data.items &&
        data.items.some((item) => item.itemId && item.itemId.trim() !== "");
      return hasValidItems || hasDescription;
    },
    {
      // This message is attached to the 'items' field for better user feedback.
      message: "A pallet must have a description or at least one item.",
      path: ["items"],
    }
  );

type ReceiveItemsFormData = z.infer<typeof receiveItemsFormSchema>;

// --- API Fetching Functions --- //

const fetchReceivingLocations = async (
  warehouseId: string,
  appToken: string | null
): Promise<Location[]> => {
  if (!appToken) {
    console.warn("fetchReceivingLocations called without an appToken.");
    return [];
  }
  if (!warehouseId) {
    console.warn("fetchReceivingLocations called without a warehouseId.");
    return [];
  }
  // Include warehouse context in the API call
  const locations = await fetchWithAuth(
    `/api/locations?category=${LocationCategory.Receiving}&warehouseId=${warehouseId}`,
    {
      token: appToken,
    }
  );
  return locations as Location[];
};

// Fetch shipment data to get existing destinations for priority
const fetchShipmentByPoNumber = async (
  poNumber: string,
  warehouseId: string,
  appToken: string | null
): Promise<Shipment | null> => {
  if (!appToken || !poNumber || !warehouseId) return null;
  try {
    return await fetchWithAuth(
      `/api/shipments/by-po/${poNumber}?warehouseId=${warehouseId}`,
      {
        token: appToken,
      }
    );
  } catch (error) {
    // If shipment doesn't exist yet (first pallet for this PO), that's okay
    console.log("No existing shipment found for PO:", poNumber);
    return null;
  }
};

// Define the expected success response type based on backend service
// The backend /api/receiving endpoint actually returns a Pallet object upon success
// So, we'll adjust ReceivingSuccessResponse to be Pallet.
// If the backend returns something else, this should be updated.
type ReceivingSuccessResponse = Pallet & {
  // It returns the Pallet object
  palletItems?: PalletItem[]; // Include if needed, based on backend return
  location?: Location;
};

// Modified to accept appToken and use fetchWithAuth
const submitReceiving = async (
  data: ReceiveItemsFormData,
  appToken: string | null, // Added appToken parameter
  warehouseId: string
): Promise<ReceivingSuccessResponse> => {
  if (!appToken) {
    throw new Error(
      "Authentication token not available for submitting receiving data."
    );
  }
  // Using fetchWithAuth instead of axios.post
  const responseData = await fetchWithAuth("/api/receiving", {
    method: "POST",
    body: JSON.stringify(data),
    token: appToken,
    headers: {
      "x-warehouse-id": warehouseId,
    },
  });
  return responseData as ReceivingSuccessResponse; // Cast, as fetchWithAuth can return various types
};

// --- Component --- //

interface ReceiveItemsFormProps {
  onPalletReceived?: (pallet: Pallet) => void;
  poNumber: string;
  onCancel?: () => void;
}

export function ReceiveItemsForm({
  onPalletReceived,
  poNumber,
  onCancel,
}: ReceiveItemsFormProps) {
  const queryClient = useQueryClient();
  const { appToken, isLoading: isAuthLoading } = useAuth(); // Added
  const { currentWarehouse } = useWarehouse(); // Added for warehouse context

  // Refs for focus management
  const barcodeInputRef = React.useRef<HTMLInputElement>(null);
  const poInputRef = React.useRef<HTMLInputElement>(null);

  // Fetch receiving locations
  const { data: locations, isLoading: isLoadingLocations } = useQuery<
    Location[],
    Error
  >({
    queryKey: [
      "locations",
      { category: LocationCategory.Receiving },
      currentWarehouse?.id,
      appToken,
    ],
    queryFn: () =>
      fetchReceivingLocations(currentWarehouse?.id || "", appToken),
    enabled: !isAuthLoading && !!appToken && !!currentWarehouse?.id,
  });

  // Fetch existing shipment data to get priority destinations
  const { data: shipment } = useQuery<Shipment | null, Error>({
    queryKey: ["shipmentByPo", poNumber, currentWarehouse?.id, appToken],
    queryFn: () =>
      fetchShipmentByPoNumber(poNumber, currentWarehouse?.id || "", appToken),
    enabled:
      !isAuthLoading && !!appToken && !!poNumber && !!currentWarehouse?.id,
    retry: 1,
  });

  // Extract priority destinations from existing pallets in this PO
  const priorityDestinations = React.useMemo(() => {
    if (!shipment) return [];

    // Get shipment summary to extract destinations
    const destinations = new Set<string>();

    // If shipment has pallets, extract their destinations
    if ("pallets" in shipment && Array.isArray(shipment.pallets)) {
      shipment.pallets.forEach((pallet: any) => {
        if (pallet.shipToDestination) {
          destinations.add(pallet.shipToDestination);
        }
      });
    }

    return Array.from(destinations);
  }, [shipment]);

  // Function to generate a random 8-digit alphanumeric string for the barcode
  const generateBarcode = () => {
    // 1. Generate a 3-digit random number (from 000 to 999).
    //    The .padStart(3, '0') ensures it's always 3 digits long (e.g., 42 becomes "042").
    const randomPart = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, "0");

    // 2. Get the last 5 digits of the current timestamp in milliseconds.
    //    This provides a rapidly changing, sequential component that resets every 100 seconds.
    const timePart = (Date.now() % 100000).toString().padStart(5, "0");

    // 3. Concatenate the random part and the time part to create the final 8-digit code.
    //    Example: "123" (random) + "45678" (time) = "12345678"
    return `${randomPart}${timePart}`;
  };

  // Function to regenerate barcode
  const regenerateBarcode = () => {
    form.setValue("barcode", generateBarcode());
  };

  const form = useForm<ReceiveItemsFormData>({
    resolver: zodResolver(receiveItemsFormSchema),
    defaultValues: {
      poNumber: poNumber,
      barcode: generateBarcode(),
      shipToDestination: "",
      destinationCode: "",
      description: "",
      receivingLocationId: "",
      warehouseId: currentWarehouse?.id || "", // Include current warehouse ID
      items: [{ itemId: "", quantity: 1 }], // Start with one empty item row
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Use unified destination auto-population hook
  const { handleDestinationSelect, handleDestinationCodeLookup, isLookingUp } =
    useDestinationAutoPopulation({ form });

  const mutation = useMutation<
    ReceivingSuccessResponse,
    Error,
    ReceiveItemsFormData
  >({
    // Pass appToken and warehouseId to submitReceiving
    mutationFn: (data: ReceiveItemsFormData) =>
      submitReceiving(data, appToken, currentWarehouse?.id || ""),
    onSuccess: (data: ReceivingSuccessResponse) => {
      // Explicitly type data
      toast.success("Items received successfully!", {
        description: `New pallet ${data.barcode} created at location ${
          locations?.find((l) => l.id === form.getValues("receivingLocationId"))
            ?.name
        }.`,
      });
      form.reset();
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["pallets"] });
      queryClient.invalidateQueries({ queryKey: ["locations"] });
      // Invalidate destination caches to ensure new destinations appear immediately
      queryClient.invalidateQueries({ queryKey: ["destinations"] });
      queryClient.invalidateQueries({ queryKey: ["destinations-with-codes"] });

      if (onPalletReceived) {
        // Assuming 'data' is the successfully created Pallet object
        // The backend currently returns an Item-like structure, but it should be a Pallet.
        // For now, we'll cast, but this needs alignment with the actual backend response.
        onPalletReceived(data as Pallet);
      }
    },
    onError: (error: Error) => {
      console.error("Receiving error:", error);
      toast.error(
        "Receiving Failed",
        (error as Error).message
          ? { description: (error as Error).message }
          : {
              description:
                "An unknown error occurred. Please check details and try again.",
            }
      );
    },
  });

  const onSubmit = (formData: ReceiveItemsFormData) => {
    if (formData.poNumber) {
      console.log("Receiving items for PO:", formData.poNumber);
    }

    // Ensure quantity is a number before submitting and prepare data for API
    const submissionData: ReceiveItemsFormData = {
      ...formData,
      items: formData.items.map(
        (item: { itemId: string; quantity: string | number }) => ({
          itemId: item.itemId,
          quantity: Number(item.quantity), // Ensure quantity is number
        })
      ),
    };

    console.log("Submitting data to API:", submissionData);
    mutation.mutate(submissionData);
  };

  // Focus management effect - clear text selection from input fields when form is ready
  React.useEffect(() => {
    // Small delay to ensure form is fully rendered
    const timer = setTimeout(() => {
      // Clear any text selection from the PO Number field
      if (poInputRef.current) {
        poInputRef.current.blur();
        poInputRef.current.setSelectionRange(0, 0);
      }

      // Clear any text selection from the barcode field
      if (barcodeInputRef.current) {
        barcodeInputRef.current.blur();
        barcodeInputRef.current.setSelectionRange(0, 0);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [poNumber]); // Re-run when poNumber changes (form initialization)

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="poNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-base font-semibold">
                  Purchase Order Number
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    readOnly
                    className="bg-muted"
                    ref={(e) => {
                      field.ref(e);
                      poInputRef.current = e;
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="barcode"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-base font-semibold">
                  Pallet Barcode
                </FormLabel>
                <div className="flex gap-2">
                  <FormControl>
                    <Input
                      placeholder="Auto-generated barcode"
                      {...field}
                      ref={(e) => {
                        field.ref(e);
                        barcodeInputRef.current = e;
                      }}
                      className="font-mono text-lg"
                    />
                  </FormControl>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={regenerateBarcode}
                    disabled={mutation.isPending}
                    title="Generate new barcode"
                  >
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Ship To Destination */}
        <FormField
          control={form.control}
          name="shipToDestination"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-base font-semibold flex items-center gap-2">
                <Package className="h-4 w-4" />
                Ship To Destination (Optional)
              </FormLabel>
              <FormControl>
                <DestinationAutocomplete
                  value={field.value ?? ""}
                  onValueChange={field.onChange}
                  placeholder="Select or enter destination..."
                  priorityDestinations={priorityDestinations}
                  disabled={mutation.isPending}
                  supportCodes={true}
                  className="text-xl h-12"
                  onDestinationSelect={handleDestinationSelect}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Destination Code */}
        <FormField
          control={form.control}
          name="destinationCode"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-base font-semibold flex items-center gap-2">
                <QrCode className="h-4 w-4" />
                Destination Code (Optional)
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Enter numbers only (e.g., 12345)"
                  className="text-xl h-12"
                  inputMode="numeric"
                  disabled={mutation.isPending || isLookingUp}
                  onBlur={(e) => {
                    field.onBlur();
                    handleDestinationCodeLookup(e.target.value);
                  }}
                />
              </FormControl>
              <FormDescription>
                Auto-fills when destination is selected, or enter manually
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Location Selection */}
        <FormField
          control={form.control}
          name="receivingLocationId"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-base font-semibold">
                Receiving Location *
              </FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
                disabled={isLoadingLocations || mutation.isPending}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue
                      placeholder={
                        isLoadingLocations
                          ? "Loading locations..."
                          : "Select a receiving dock..."
                      }
                    />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {locations?.map((location) => (
                    <SelectItem key={location.id} value={location.id}>
                      {location.name}
                    </SelectItem>
                  ))}
                  {!isLoadingLocations && !locations?.length && (
                    <SelectItem value="none" disabled>
                      No receiving docks found
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Description */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter any notes or details about this pallet"
                  className="resize-none"
                  rows={3}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Items Section */}
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-base font-semibold">Items to Receive</h3>
            <p className="text-sm text-muted-foreground">
              Add items to this pallet or leave empty for later
            </p>
          </div>
        </div>
        <div className="space-y-4">
          {fields.map((field, index) => (
            <div
              key={field.id}
              className="flex items-end space-x-2 p-3 border rounded-md"
            >
              {/* Item Selection */}
              <FormField
                control={form.control}
                name={`items.${index}.itemId`}
                render={({ field: itemField }) => (
                  <FormItem className="flex-grow">
                    <FormLabel>Item</FormLabel>
                    <ItemCombobox
                      selectedItemId={itemField.value}
                      onSelect={(itemId) => {
                        itemField.onChange(itemId);
                      }}
                      disabled={mutation.isPending}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Quantity Input */}
              <FormField
                control={form.control}
                name={`items.${index}.quantity`}
                render={({ field: qtyField }) => (
                  <FormItem style={{ flexBasis: "100px" }}>
                    <FormLabel>Quantity</FormLabel>
                    <FormControl>
                      <Input
                        {...qtyField}
                        type="number"
                        min="1"
                        placeholder="Qty"
                        onChange={(e) =>
                          qtyField.onChange(parseInt(e.target.value, 10) || 0)
                        }
                        value={qtyField.value || ""}
                        disabled={mutation.isPending}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Remove Button */}
              <Button
                type="button"
                variant="destructive"
                size="icon"
                onClick={() => remove(index)}
                disabled={fields.length <= 1 || mutation.isPending}
                aria-label="Remove item"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>

        {/* Add Item Button */}
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => append({ itemId: "", quantity: 1 })}
          disabled={mutation.isPending}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Another Item
        </Button>

        {/* Submission Error Message */}
        {form.formState.errors.items && !form.formState.errors.items.root && (
          <p className="text-sm font-medium text-destructive">
            {form.formState.errors.items.message}
          </p>
        )}
        {form.formState.errors.items?.root && (
          <p className="text-sm font-medium text-destructive">
            {form.formState.errors.items.root.message}
          </p>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row justify-end gap-2 pt-6">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={mutation.isPending}
            >
              Cancel
            </Button>
          )}
          <Button
            type="submit"
            disabled={mutation.isPending || isLoadingLocations}
            className="min-w-[120px]"
          >
            {mutation.isPending ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Receiving...
              </>
            ) : (
              "Receive Items"
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
