"use client";

import React, { useState } from "react";
import {
  QueryClient,
  QueryClientProvider as TanstackQueryClientProvider,
} from "@tanstack/react-query";

export default function ReactQueryProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  // Use useState to ensure the client is only created once per render
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Optimized defaults for warehouse context performance
            staleTime: 5 * 60 * 1000, // 5 minutes - data stays fresh
            gcTime: 10 * 60 * 1000, // 10 minutes - keep in cache longer
            retry: 2,
            refetchOnWindowFocus: false, // Prevent focus-related re-renders
            refetchOnMount: false, // Prevent mount-related re-renders
            refetchOnReconnect: true, // Only refetch on network reconnect
            refetchInterval: false, // Disable automatic polling
            refetchIntervalInBackground: false, // Disable background polling
          },
          mutations: {
            retry: 1,
            // Add network mode for better offline handling
            networkMode: "online",
          },
        },
      })
  );

  return (
    <TanstackQueryClientProvider client={queryClient}>
      {children}
    </TanstackQueryClientProvider>
  );
}
