"use client";

import {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
  useCallback,
  useMemo,
} from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "./auth-provider";
import { fetchWithAuth } from "@/lib/api";
import {
  warehousePersistence,
  getDefaultWarehouse,
  saveWarehouseSelection,
  clearWarehouseData,
} from "@/lib/warehouse-persistence";
import {
  getCachedWarehouseValidation,
  cacheWarehouseValidation,
  clearAllWarehouseValidationCache,
  cleanupExpiredWarehouseValidation,
} from "@/lib/warehouse-validation-cache";
import { userKeys, queryKeyUtils } from "@/lib/query-keys";
import { getWarehouseQueryConfig } from "@/lib/query-config";

// Import types from shared package
import { Role, WarehouseBasic, WarehouseContextType } from "@quildora/types";

const WarehouseContext = createContext<WarehouseContextType | undefined>(
  undefined
);

interface WarehouseProviderProps {
  children: ReactNode;
}

export function WarehouseProvider({ children }: WarehouseProviderProps) {
  const { appUser, appToken, isLoading: authLoading } = useAuth();
  const queryClient = useQueryClient();

  const [currentWarehouse, setCurrentWarehouseState] =
    useState<WarehouseBasic | null>(null);
  const [isChangingWarehouse, setIsChangingWarehouse] = useState(false);

  // React Query for warehouse data with optimized hierarchical caching
  const {
    data: accessibleWarehouses = [],
    isLoading: isLoadingWarehouses,
    error: warehouseQueryError,
    refetch: refetchWarehouses,
  } = useQuery<WarehouseBasic[], Error>({
    queryKey: userKeys.warehouses(appUser?.id || ""),
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      return fetchWithAuth("/api/warehouses", { token: appToken });
    },
    enabled: !!appToken && !!appUser,
    ...getWarehouseQueryConfig("userWarehouses"),
  });

  // Convert query error to string for backward compatibility
  const warehouseError = warehouseQueryError?.message || null;

  // Auto-select warehouse when warehouses are loaded
  useEffect(() => {
    if (!currentWarehouse && accessibleWarehouses.length > 0) {
      const defaultWarehouse = getDefaultWarehouse(accessibleWarehouses);
      if (defaultWarehouse) {
        setCurrentWarehouseState(defaultWarehouse);
      }
    }
  }, [accessibleWarehouses, currentWarehouse]);

  // Set current warehouse with enhanced persistence (no artificial delays)
  const setCurrentWarehouse = useCallback(
    async (warehouse: WarehouseBasic | null) => {
      setIsChangingWarehouse(true);

      try {
        setCurrentWarehouseState(warehouse);
        // Use enhanced persistence system
        saveWarehouseSelection(warehouse);
      } catch (error) {
        console.error("Error switching warehouse:", error);
      } finally {
        setIsChangingWarehouse(false);
      }
    },
    []
  );

  // Refresh warehouses using React Query refetch
  const refreshWarehouses = useCallback(async () => {
    await refetchWarehouses();
  }, [refetchWarehouses]);

  // Retry loading warehouses (simplified - React Query handles retries)
  const retryLoadWarehouses = useCallback(async () => {
    await refetchWarehouses();
  }, [refetchWarehouses]);

  // Utility functions with caching
  const hasWarehouseAccess = useCallback(
    (warehouseId: string): boolean => {
      if (!appUser) return false;

      // Clean up expired cache entries periodically
      cleanupExpiredWarehouseValidation();

      // Check cache first
      const cached = getCachedWarehouseValidation(warehouseId, appUser.id);
      if (cached) {
        return cached.hasAccess;
      }

      // Fall back to client-side validation
      let hasAccess = false;
      let userRole: Role | null = null;

      // TENANT_ADMIN has access to all warehouses in their tenant
      if (appUser.role === "TENANT_ADMIN") {
        hasAccess = true;
        userRole = Role.TENANT_ADMIN;
      } else {
        // Check if user has specific warehouse access
        const warehouseUser = appUser.warehouseUsers?.find(
          (wu) => wu.warehouseId === warehouseId
        );
        hasAccess = !!warehouseUser;
        userRole = warehouseUser?.role || null;
      }

      // Cache the result
      const isManager =
        userRole === Role.WAREHOUSE_MANAGER || userRole === Role.TENANT_ADMIN;
      const isAdmin = userRole === Role.TENANT_ADMIN;
      cacheWarehouseValidation(
        warehouseId,
        appUser.id,
        hasAccess,
        userRole,
        isManager,
        isAdmin
      );

      return hasAccess;
    },
    [appUser]
  );

  const getUserWarehouseRole = useCallback(
    (warehouseId: string): Role | null => {
      if (!appUser) return null;

      // Check cache first
      const cached = getCachedWarehouseValidation(warehouseId, appUser.id);
      if (cached) {
        return cached.userRole;
      }

      // Fall back to client-side validation
      let userRole: Role | null = null;

      // TENANT_ADMIN has admin access to all warehouses
      if (appUser.role === Role.TENANT_ADMIN) {
        userRole = Role.TENANT_ADMIN;
      } else {
        // Find specific warehouse role
        const warehouseUser = appUser.warehouseUsers?.find(
          (wu) => wu.warehouseId === warehouseId
        );
        userRole = warehouseUser?.role || null;
      }

      // Cache the result
      const hasAccess = !!userRole || appUser.role === "TENANT_ADMIN";
      const isManager =
        userRole === Role.WAREHOUSE_MANAGER || userRole === Role.TENANT_ADMIN;
      const isAdmin = userRole === Role.TENANT_ADMIN;
      cacheWarehouseValidation(
        warehouseId,
        appUser.id,
        hasAccess,
        userRole,
        isManager,
        isAdmin
      );

      return userRole;
    },
    [appUser]
  );

  const canManageWarehouse = useCallback(
    (warehouseId: string): boolean => {
      if (!appUser) return false;

      // Check cache first
      const cached = getCachedWarehouseValidation(warehouseId, appUser.id);
      if (cached) {
        return cached.isManager;
      }

      // Fall back to role-based check
      const role = getUserWarehouseRole(warehouseId);
      return role === Role.WAREHOUSE_MANAGER || role === Role.TENANT_ADMIN;
    },
    [appUser, getUserWarehouseRole]
  );

  // Initialize persistence system
  useEffect(() => {
    warehousePersistence.migrateOldStorage();
  }, []);

  // Clear warehouse data when user logs out
  useEffect(() => {
    if (!appUser) {
      setCurrentWarehouseState(null);
      clearWarehouseData();
      // Clear React Query cache for all user-related data using hierarchical keys
      queryClient.removeQueries({ queryKey: userKeys.all });
      // Clear warehouse validation cache
      clearAllWarehouseValidationCache();
    }
  }, [appUser, queryClient]);

  // Memoize context value to prevent unnecessary re-renders of all consuming components
  const contextValue: WarehouseContextType = useMemo(
    () => ({
      currentWarehouse,
      accessibleWarehouses,
      isLoadingWarehouses,
      isChangingWarehouse,
      warehouseError,
      loadingProgress: 0, // No longer used with React Query
      retryCount: 0, // No longer used with React Query
      setCurrentWarehouse,
      refreshWarehouses,
      retryLoadWarehouses,
      hasWarehouseAccess,
      getUserWarehouseRole,
      canManageWarehouse,
    }),
    [
      currentWarehouse,
      accessibleWarehouses,
      isLoadingWarehouses,
      isChangingWarehouse,
      warehouseError,
      setCurrentWarehouse,
      refreshWarehouses,
      retryLoadWarehouses,
      hasWarehouseAccess,
      getUserWarehouseRole,
      canManageWarehouse,
    ]
  );

  return (
    <WarehouseContext.Provider value={contextValue}>
      {children}
    </WarehouseContext.Provider>
  );
}

export const useWarehouse = () => {
  const context = useContext(WarehouseContext);
  if (context === undefined) {
    throw new Error("useWarehouse must be used within a WarehouseProvider");
  }
  return context;
};

// Convenience hooks for common use cases
export const useCurrentWarehouse = () => {
  const { currentWarehouse } = useWarehouse();
  return currentWarehouse;
};

export const useWarehouseRole = (warehouseId?: string) => {
  const { getUserWarehouseRole, currentWarehouse } = useWarehouse();
  const targetWarehouseId = warehouseId || currentWarehouse?.id;
  return targetWarehouseId ? getUserWarehouseRole(targetWarehouseId) : null;
};

export const useCanManageWarehouse = (warehouseId?: string) => {
  const { canManageWarehouse, currentWarehouse } = useWarehouse();
  const targetWarehouseId = warehouseId || currentWarehouse?.id;
  return targetWarehouseId ? canManageWarehouse(targetWarehouseId) : false;
};
