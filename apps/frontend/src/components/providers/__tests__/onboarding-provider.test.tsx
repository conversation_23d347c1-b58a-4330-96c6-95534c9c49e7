import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach } from "vitest";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { OnboardingProvider, useOnboarding } from "../onboarding-provider";
import { AuthContext } from "../auth-provider";
import { Role } from "@quildora/types";

// Mock the API
vi.mock("@/lib/api", () => ({
  fetchWithAuth: vi.fn(),
}));

// Mock the error recovery
vi.mock("@/lib/onboarding-error-recovery", () => ({
  OnboardingErrorRecovery: {
    logError: vi.fn(),
    getRecoveryActions: vi.fn(() => [
      {
        strategy: "RETRY",
        action: vi.fn(),
        label: "Retry",
      },
    ]),
  },
  OnboardingErrorType: {
    NETWORK_ERROR: "NETWORK_ERROR",
    SESSION_EXPIRED: "SESSION_EXPIRED",
    VALIDATION_ERROR: "VALIDATION_ERROR",
    SERVER_ERROR: "SERVER_ERROR",
  },
}));

// Test component that uses the onboarding context
function TestComponent() {
  const {
    currentStep,
    sessionData,
    isLoading,
    error,
    updateBusinessInfo,
    createAdminAccount,
    setupWarehouse,
    completeOnboarding,
    goToStep,
    retryLastAction,
    clearError,
  } = useOnboarding();

  return (
    <div>
      <div data-testid="current-step">{currentStep}</div>
      <div data-testid="is-loading">{isLoading.toString()}</div>
      <div data-testid="error">{error || "no-error"}</div>
      <div data-testid="session-id">
        {sessionData.sessionId || "no-session"}
      </div>

      <button
        data-testid="update-business-info"
        onClick={() =>
          updateBusinessInfo({
            companyName: "Test Company",
            industry: "Technology",
            companySize: "10-50",
          })
        }
      >
        Update Business Info
      </button>

      <button
        data-testid="create-admin"
        onClick={() =>
          createAdminAccount({
            email: "<EMAIL>",
            fullName: "Test Admin",
            password: "password123",
          })
        }
      >
        Create Admin
      </button>

      <button
        data-testid="setup-warehouse"
        onClick={() =>
          setupWarehouse({
            warehouseName: "Test Warehouse",
            warehouseType: "distribution",
            address: "123 Test St, Test City, TS 12345, US",
            expectedVolume: "medium",
          })
        }
      >
        Setup Warehouse
      </button>

      <button
        data-testid="complete-onboarding"
        onClick={() => completeOnboarding()}
      >
        Complete Onboarding
      </button>

      <button
        data-testid="go-to-admin-step"
        onClick={() => goToStep("admin_account")}
      >
        Go to Admin Step
      </button>

      <button data-testid="retry-action" onClick={() => retryLastAction?.()}>
        Retry Action
      </button>

      <button data-testid="clear-error" onClick={() => clearError?.()}>
        Clear Error
      </button>
    </div>
  );
}

// Mock auth context
const mockAuthContext = {
  supabase: {} as any,
  supabaseSession: null,
  supabaseUser: null,
  appUser: {
    id: "user-1",
    email: "<EMAIL>",
    role: Role.TENANT_ADMIN,
    name: "Test User",
    authUserId: "auth-1",
    tenantId: "tenant-1",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  appToken: "test-token",
  isLoading: false,
  onboardingStatus: "incomplete",
  isOnboarding: true,
  onboardingStep: "business_info" as const,
  onboardingData: null,
  startBusinessOnboarding: vi.fn(),
  joinTenantWithInvitation: vi.fn(),
  completeOnboarding: vi.fn(),
  loginWithSupabaseToken: vi.fn(),
  logout: vi.fn(),
};

function renderWithProviders(component: React.ReactElement) {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <AuthContext.Provider value={mockAuthContext}>
        <OnboardingProvider>{component}</OnboardingProvider>
      </AuthContext.Provider>
    </QueryClientProvider>
  );
}

describe("OnboardingProvider", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorage.clear();
  });

  it("should render with initial state", () => {
    renderWithProviders(<TestComponent />);

    expect(screen.getByTestId("current-step")).toHaveTextContent(
      "business_info"
    );
    expect(screen.getByTestId("is-loading")).toHaveTextContent("false");
    expect(screen.getByTestId("error")).toHaveTextContent("no-error");
    expect(screen.getByTestId("session-id")).toHaveTextContent("no-session");
  });

  it("should update business info successfully", async () => {
    const { fetchWithAuth } = await import("@/lib/api");
    (fetchWithAuth as any).mockResolvedValueOnce({
      sessionId: "session-123",
      step: "admin_account",
    });

    renderWithProviders(<TestComponent />);

    fireEvent.click(screen.getByTestId("update-business-info"));

    await waitFor(() => {
      expect(fetchWithAuth).toHaveBeenCalledWith(
        "/api/onboarding/business-info",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            companyName: "Test Company",
            industry: "Technology",
            companySize: "10-50",
          }),
          token: "test-token",
        }
      );
    });
  });

  it("should handle business info update error", async () => {
    const { fetchWithAuth } = await import("@/lib/api");
    (fetchWithAuth as any).mockRejectedValueOnce(new Error("Network error"));

    renderWithProviders(<TestComponent />);

    fireEvent.click(screen.getByTestId("update-business-info"));

    await waitFor(() => {
      expect(screen.getByTestId("error")).toHaveTextContent("Network error");
    });
  });

  it("should create admin account successfully", async () => {
    const { fetchWithAuth } = await import("@/lib/api");
    (fetchWithAuth as any).mockResolvedValueOnce({
      sessionId: "session-123",
      step: "warehouse_setup",
    });

    renderWithProviders(<TestComponent />);

    fireEvent.click(screen.getByTestId("create-admin"));

    await waitFor(() => {
      expect(fetchWithAuth).toHaveBeenCalledWith(
        "/api/onboarding/admin-account",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            email: "<EMAIL>",
            fullName: "Test Admin",
            password: "password123",
          }),
          token: "test-token",
        }
      );
    });
  });

  it("should setup warehouse successfully", async () => {
    const { fetchWithAuth } = await import("@/lib/api");
    (fetchWithAuth as any).mockResolvedValueOnce({
      sessionId: "session-123",
      step: "team_setup",
    });

    renderWithProviders(<TestComponent />);

    fireEvent.click(screen.getByTestId("setup-warehouse"));

    await waitFor(() => {
      expect(fetchWithAuth).toHaveBeenCalledWith(
        "/api/onboarding/warehouse-setup",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            warehouseName: "Test Warehouse",
            warehouseType: "DISTRIBUTION",
            address: "123 Test St",
            city: "Test City",
            state: "TS",
            zipCode: "12345",
            country: "US",
          }),
          token: "test-token",
        }
      );
    });
  });

  it("should complete onboarding successfully", async () => {
    const { fetchWithAuth } = await import("@/lib/api");
    (fetchWithAuth as any).mockResolvedValueOnce({
      success: true,
      redirectUrl: "/dashboard",
    });

    renderWithProviders(<TestComponent />);

    fireEvent.click(screen.getByTestId("complete-onboarding"));

    await waitFor(() => {
      expect(fetchWithAuth).toHaveBeenCalledWith("/api/onboarding/complete", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({}),
        token: "test-token",
      });
    });
  });

  it("should navigate between steps", () => {
    renderWithProviders(<TestComponent />);

    fireEvent.click(screen.getByTestId("go-to-admin-step"));

    expect(screen.getByTestId("current-step")).toHaveTextContent(
      "admin_account"
    );
  });

  it("should handle error recovery", async () => {
    const { OnboardingErrorRecovery } = await import(
      "@/lib/onboarding-error-recovery"
    );
    const mockRetryAction = vi.fn();

    (OnboardingErrorRecovery.getRecoveryActions as any).mockReturnValueOnce([
      {
        strategy: "RETRY",
        action: mockRetryAction,
        label: "Retry",
      },
    ]);

    renderWithProviders(<TestComponent />);

    // Simulate an error first
    const { fetchWithAuth } = await import("@/lib/api");
    (fetchWithAuth as any).mockRejectedValueOnce(new Error("Test error"));

    fireEvent.click(screen.getByTestId("update-business-info"));

    await waitFor(() => {
      expect(screen.getByTestId("error")).toHaveTextContent("Test error");
    });

    // Now retry the action
    fireEvent.click(screen.getByTestId("retry-action"));

    await waitFor(() => {
      expect(mockRetryAction).toHaveBeenCalled();
    });
  });

  it("should clear errors", async () => {
    renderWithProviders(<TestComponent />);

    // Simulate an error first
    const { fetchWithAuth } = await import("@/lib/api");
    (fetchWithAuth as any).mockRejectedValueOnce(new Error("Test error"));

    fireEvent.click(screen.getByTestId("update-business-info"));

    await waitFor(() => {
      expect(screen.getByTestId("error")).toHaveTextContent("Test error");
    });

    // Clear the error
    fireEvent.click(screen.getByTestId("clear-error"));

    expect(screen.getByTestId("error")).toHaveTextContent("no-error");
  });

  it("should save and load progress from localStorage", () => {
    // Mock localStorage with existing progress
    const mockProgress = {
      step: "warehouse_setup",
      sessionId: "session-123",
      businessInfo: {
        companyName: "Saved Company",
        industry: "Technology",
        companySize: "10-50",
      },
    };

    localStorage.setItem(
      "quildora_onboarding_progress",
      JSON.stringify(mockProgress)
    );

    renderWithProviders(<TestComponent />);

    // Should load the saved progress
    expect(screen.getByTestId("current-step")).toHaveTextContent(
      "warehouse_setup"
    );
    expect(screen.getByTestId("session-id")).toHaveTextContent("session-123");
  });
});
