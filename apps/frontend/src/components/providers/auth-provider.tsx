"use client";

import {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
  useCallback,
  useMemo,
} from "react";
import { createClient } from "@/lib/supabase/client";
import type { Session, SupabaseClient } from "@supabase/supabase-js";
import { usePathname, useRouter } from "next/navigation";

import {
  Role,
  FrontendAppUser,
  AuthContextType as SharedAuthContextType,
  OnboardingStep,
  OnboardingState,
  BusinessInfo,
} from "@quildora/types";

// Re-export for other files that might have been importing it from here
export { Role };

// Extend the shared auth context type to include Supabase-specific properties
interface AuthContextType extends SharedAuthContextType {
  supabase: SupabaseClient;
}

export const AuthContext = createContext<AuthContextType | undefined>(
  undefined
);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const supabase = createClient();
  const [supabaseSession, setSupabaseSession] = useState<Session | null>(null);
  const [appUser, setAppUser] = useState<FrontendAppUser | null>(null);
  const [appToken, setAppToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [onboardingStatus, setOnboardingStatus] = useState<string | null>(null);

  // New onboarding state
  const [isOnboarding, setIsOnboarding] = useState(false);
  const [onboardingStep, setOnboardingStep] = useState<OnboardingStep | null>(
    null
  );
  const [onboardingData, setOnboardingData] = useState<OnboardingState | null>(
    null
  );

  const router = useRouter();
  const pathname = usePathname();

  const logout = useCallback(async () => {
    await supabase.auth.signOut();
    setSupabaseSession(null);
    setAppUser(null);
    setAppToken(null);
    setOnboardingStatus(null);

    // Clear onboarding state
    setIsOnboarding(false);
    setOnboardingStep(null);
    setOnboardingData(null);

    // The onAuthStateChange listener will handle the redirect
    // to prevent race conditions, but we can push here as a fallback.
    if (pathname !== "/auth/welcome") {
      router.push("/auth/welcome");
    }
    setIsLoading(false);
  }, [supabase, pathname, router]);

  const loginWithSupabaseToken = useCallback(
    async (supabaseAccessToken: string) => {
      try {
        setIsLoading(true);
        const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/login`;
        const response = await fetch(apiUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ supabaseToken: supabaseAccessToken }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error("Failed to get app token:", errorData);
          throw new Error(
            errorData.message ||
              "Failed to exchange Supabase token for app token"
          );
        }

        // Destructure the full response from the backend
        const {
          accessToken,
          user: newAppUser,
          onboardingStatus: newOnboardingStatus,
        } = await response.json();

        setAppToken(accessToken);
        setAppUser(newAppUser as FrontendAppUser); // Set the full user object
        setOnboardingStatus(newOnboardingStatus); // Set the onboarding status

        // Redirect based on onboarding status
        if (newOnboardingStatus === "pending_company_details") {
          router.push("/onboarding/company-details");
        } else {
          // If onboarding is complete, and user was on auth pages, redirect to home
          // Otherwise, existing logic in other useEffects might handle redirection or stay on current page
          if (pathname.startsWith("/auth")) {
            router.push("/");
          }
        }
      } catch (error) {
        console.error("Error exchanging Supabase token for app token:", error);
        // If token exchange fails, the user is effectively logged out from our app's perspective.
        // Call logout to clear all state and redirect to the auth page.
        await logout();
      } finally {
        setIsLoading(false);
      }
    },
    [router, pathname, logout]
  ); // Added useCallback

  useEffect(() => {
    const getInitialSession = async () => {
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();
      if (error) {
        console.error("Error getting initial session:", error);
        setIsLoading(false);
        return;
      }
      setSupabaseSession(session);
      if (session?.access_token) {
        await loginWithSupabaseToken(session.access_token);
      } else {
        setIsLoading(false);
      }
    };

    getInitialSession();

    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSupabaseSession(session);
        if (
          (event === "SIGNED_IN" || event === "TOKEN_REFRESHED") &&
          session?.access_token
        ) {
          await loginWithSupabaseToken(session.access_token);
        } else if (event === "SIGNED_OUT") {
          setAppToken(null);
          setAppUser(null);
          setOnboardingStatus(null); // Clear onboarding status
        }
        if (event !== "INITIAL_SESSION") {
          setIsLoading(false); // Stop loading after first auth event unless it's initial session handled by getInitialSession
        }
      }
    );

    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, [supabase, loginWithSupabaseToken]);

  // Effect for handling protected routes and redirects
  useEffect(() => {
    if (isLoading) return; // Don't run redirects until initial auth check is done

    const publicPaths = [
      "/auth",
      "/auth/welcome",
      "/auth/signin",
      "/auth/signup",
    ];
    const isPublicPath = publicPaths.some((path) => pathname.startsWith(path));

    if (appUser && onboardingStatus === "pending_company_details") {
      if (pathname !== "/onboarding/company-details") {
        router.push("/onboarding/company-details");
      }
    } else if (appUser && onboardingStatus === "complete") {
      if (isPublicPath) {
        router.push("/");
      }
    } else if (!appUser && !isPublicPath) {
      router.push("/auth/welcome");
    }
  }, [isLoading, appUser, onboardingStatus, pathname, router]);

  // New onboarding methods
  const startBusinessOnboarding = useCallback(
    async (businessInfo: BusinessInfo) => {
      try {
        setIsOnboarding(true);
        setOnboardingStep("business_info");
        setOnboardingData({ step: "business_info", businessInfo });

        // Navigate to onboarding flow
        router.push("/auth/signup/business");
      } catch (error) {
        console.error("Failed to start business onboarding:", error);
        setIsOnboarding(false);
        setOnboardingStep(null);
        setOnboardingData(null);
        throw error;
      }
    },
    [router]
  );

  const joinTenantWithInvitation = useCallback(
    async (invitationCode: string) => {
      try {
        setIsOnboarding(true);
        setOnboardingStep("admin_account");
        setOnboardingData({ step: "admin_account" });

        // Navigate to invitation acceptance flow
        router.push(`/auth/signup/join?code=${invitationCode}`);
      } catch (error) {
        console.error("Failed to join tenant with invitation:", error);
        setIsOnboarding(false);
        setOnboardingStep(null);
        setOnboardingData(null);
        throw error;
      }
    },
    [router]
  );

  const completeOnboarding = useCallback(async () => {
    try {
      setIsOnboarding(false);
      setOnboardingStep(null);
      setOnboardingData(null);
      setOnboardingStatus("complete");

      // Navigate to main application
      router.push("/");
    } catch (error) {
      console.error("Failed to complete onboarding:", error);
      throw error;
    }
  }, [router]);

  // Memoize supabaseUser to prevent unnecessary re-renders
  const supabaseUser = useMemo(
    () => supabaseSession?.user ?? null,
    [supabaseSession?.user]
  );

  // Memoize context value to prevent unnecessary re-renders of all consuming components
  const contextValue = useMemo(
    () => ({
      supabase,
      supabaseSession,
      supabaseUser,
      appUser,
      appToken,
      isLoading,
      onboardingStatus,

      // New onboarding properties
      isOnboarding,
      onboardingStep,
      onboardingData,

      // Enhanced methods
      startBusinessOnboarding,
      joinTenantWithInvitation,
      completeOnboarding,

      // Existing methods
      loginWithSupabaseToken,
      logout,
    }),
    [
      supabase,
      supabaseSession,
      supabaseUser,
      appUser,
      appToken,
      isLoading,
      onboardingStatus,
      isOnboarding,
      onboardingStep,
      onboardingData,
      startBusinessOnboarding,
      joinTenantWithInvitation,
      completeOnboarding,
      loginWithSupabaseToken,
      logout,
    ]
  );

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

// New hook for role-based access control
export const useRoleAccess = (requiredRoles: Role | Role[]): boolean => {
  const { appUser } = useAuth();

  if (!appUser || !appUser.role) {
    return false;
  }

  if (Array.isArray(requiredRoles)) {
    return requiredRoles.includes(appUser.role);
  }
  return appUser.role === requiredRoles;
};
