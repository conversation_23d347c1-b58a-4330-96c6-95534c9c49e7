"use client";

import { ReactNode } from "react";
import { useWarehouseSync } from "@/hooks/api/useWarehouseSync";

interface WarehouseSyncWrapperProps {
  children: ReactNode;
}

/**
 * Wrapper component that automatically handles warehouse data synchronization
 * This component uses the useWarehouseSync hook to invalidate queries when warehouse changes
 */
export function WarehouseSyncWrapper({ children }: WarehouseSyncWrapperProps) {
  // This hook automatically handles query invalidation when warehouse changes
  useWarehouseSync();

  return <>{children}</>;
}
