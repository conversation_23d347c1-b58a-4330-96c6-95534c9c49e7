"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, <PERSON>ertCircle, CheckCircle2 } from "lucide-react";

interface OnboardingFormWrapperProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  isSubmitting?: boolean;
  error?: string | null;
  success?: string | null;
  showBackButton?: boolean;
  showNextButton?: boolean;
  nextButtonText?: string;
  nextButtonDisabled?: boolean;
  onBack?: () => void;
  onNext?: () => void;
  onSubmit?: (e: React.FormEvent) => void;
  className?: string;
  contentClassName?: string;
}

export default function OnboardingFormWrapper({
  children,
  title,
  description,
  isSubmitting = false,
  error,
  success,
  showBackButton = true,
  showNextButton = true,
  nextButtonText = "Continue",
  nextButtonDisabled = false,
  onBack,
  onNext,
  onSubmit,
  className = "",
  contentClassName = "",
}: OnboardingFormWrapperProps) {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSubmit) {
      onSubmit(e);
    } else if (onNext) {
      onNext();
    }
  };

  return (
    <Card className={`w-full ${className}`}>
      {(title || description) && (
        <CardHeader className="text-center pb-6">
          {title && (
            <CardTitle className="text-xl font-semibold text-gray-900">
              {title}
            </CardTitle>
          )}
          {description && <p className="text-gray-600 mt-2">{description}</p>}
        </CardHeader>
      )}

      <CardContent className={`space-y-6 ${contentClassName}`}>
        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Success Alert */}
        {success && (
          <Alert className="border-green-200 bg-green-50 text-green-800">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        {/* Form Content */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {children}

          {/* Form Actions */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            {showBackButton && onBack && (
              <Button
                type="button"
                variant="outline"
                onClick={onBack}
                disabled={isSubmitting}
                className="w-full sm:w-auto order-2 sm:order-1"
              >
                Back
              </Button>
            )}

            {showNextButton && (
              <Button
                type="submit"
                disabled={nextButtonDisabled || isSubmitting}
                className="w-full sm:flex-1 order-1 sm:order-2 min-h-[44px]"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  nextButtonText
                )}
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}

// Specialized form field components for onboarding
interface OnboardingFieldProps {
  label: string;
  required?: boolean;
  error?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

export function OnboardingField({
  label,
  required = false,
  error,
  description,
  children,
  className = "",
}: OnboardingFieldProps) {
  return (
    <div className={`space-y-2 ${className}`}>
      <label className="text-sm font-medium text-gray-700 block">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>

      {description && <p className="text-sm text-gray-500">{description}</p>}

      {children}

      {error && (
        <p className="text-sm text-red-600 flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          {error}
        </p>
      )}
    </div>
  );
}

// Loading state component for forms
interface OnboardingLoadingProps {
  message?: string;
  className?: string;
}

export function OnboardingLoading({
  message = "Loading...",
  className = "",
}: OnboardingLoadingProps) {
  return (
    <div className={`flex items-center justify-center py-8 ${className}`}>
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-2" />
        <p className="text-gray-600">{message}</p>
      </div>
    </div>
  );
}

// Success state component
interface OnboardingSuccessProps {
  title?: string;
  message?: string;
  children?: React.ReactNode;
  className?: string;
}

export function OnboardingSuccess({
  title = "Success!",
  message,
  children,
  className = "",
}: OnboardingSuccessProps) {
  return (
    <div className={`text-center py-8 ${className}`}>
      <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
        <CheckCircle2 className="h-6 w-6 text-green-600" />
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      {message && <p className="text-gray-600 mb-4">{message}</p>}
      {children}
    </div>
  );
}

// Error state component
interface OnboardingErrorProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  retryText?: string;
  children?: React.ReactNode;
  className?: string;
}

export function OnboardingError({
  title = "Something went wrong",
  message,
  onRetry,
  retryText = "Try Again",
  children,
  className = "",
}: OnboardingErrorProps) {
  return (
    <div className={`text-center py-8 ${className}`}>
      <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
        <AlertCircle className="h-6 w-6 text-red-600" />
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      {message && <p className="text-gray-600 mb-4">{message}</p>}
      {onRetry && (
        <Button onClick={onRetry} variant="outline" className="mb-4">
          {retryText}
        </Button>
      )}
      {children}
    </div>
  );
}

// Components are already exported above as named exports

// Type exports
export type {
  OnboardingFormWrapperProps,
  OnboardingFieldProps,
  OnboardingLoadingProps,
  OnboardingSuccessProps,
  OnboardingErrorProps,
};
