'use client';

import React from 'react';
import { Check, Circle, ArrowRight } from 'lucide-react';
import { OnboardingStep, ONBOARDING_ROUTES } from '@quildora/types';
import { cn } from '@/lib/utils';
import { useOnboarding } from '@/components/providers/onboarding-provider';

interface OnboardingProgressIndicatorProps {
  className?: string;
  variant?: 'horizontal' | 'vertical';
  showLabels?: boolean;
  showStepNumbers?: boolean;
}

interface StepInfo {
  step: OnboardingStep;
  title: string;
  description: string;
  icon?: React.ReactNode;
}

const STEP_INFO: Record<OnboardingStep, StepInfo> = {
  business_info: {
    step: 'business_info',
    title: 'Business Information',
    description: 'Tell us about your company',
    icon: <Circle className="h-4 w-4" />,
  },
  admin_account: {
    step: 'admin_account',
    title: 'Admin Account',
    description: 'Create your admin account',
    icon: <Circle className="h-4 w-4" />,
  },
  warehouse_setup: {
    step: 'warehouse_setup',
    title: 'Warehouse Setup',
    description: 'Configure your warehouse',
    icon: <Circle className="h-4 w-4" />,
  },
  team_setup: {
    step: 'team_setup',
    title: 'Team Setup',
    description: 'Invite your team members',
    icon: <Circle className="h-4 w-4" />,
  },
  completion: {
    step: 'completion',
    title: 'Complete',
    description: 'Finish your setup',
    icon: <Circle className="h-4 w-4" />,
  },
};

export function OnboardingProgressIndicator({
  className,
  variant = 'horizontal',
  showLabels = true,
  showStepNumbers = false,
}: OnboardingProgressIndicatorProps) {
  const { currentStep, sessionData } = useOnboarding();

  const steps = ONBOARDING_ROUTES.map(route => route.step);
  const currentStepIndex = steps.indexOf(currentStep);

  const getStepStatus = (step: OnboardingStep, index: number): 'completed' | 'current' | 'upcoming' => {
    if (index < currentStepIndex) return 'completed';
    if (index === currentStepIndex) return 'current';
    return 'upcoming';
  };

  const isStepCompleted = (step: OnboardingStep): boolean => {
    switch (step) {
      case 'business_info':
        return !!sessionData.businessInfo?.companyName;
      case 'admin_account':
        return !!sessionData.adminAccount?.email;
      case 'warehouse_setup':
        return !!sessionData.warehouseSetup?.warehouseName;
      case 'team_setup':
        return true; // Team setup is optional
      case 'completion':
        return false; // Never completed until final step
      default:
        return false;
    }
  };

  const getStepIcon = (step: OnboardingStep, status: string, index: number) => {
    if (status === 'completed' || isStepCompleted(step)) {
      return (
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-500 text-white">
          <Check className="h-4 w-4" />
        </div>
      );
    }

    if (status === 'current') {
      return (
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white">
          {showStepNumbers ? (
            <span className="text-sm font-medium">{index + 1}</span>
          ) : (
            <Circle className="h-4 w-4 fill-current" />
          )}
        </div>
      );
    }

    return (
      <div className="flex h-8 w-8 items-center justify-center rounded-full border-2 border-gray-300 bg-white">
        {showStepNumbers ? (
          <span className="text-sm font-medium text-gray-500">{index + 1}</span>
        ) : (
          <Circle className="h-4 w-4 text-gray-300" />
        )}
      </div>
    );
  };

  const getConnectorClass = (fromStatus: string, toStatus: string) => {
    if (fromStatus === 'completed') {
      return 'bg-green-500';
    }
    if (fromStatus === 'current' && toStatus === 'upcoming') {
      return 'bg-gradient-to-r from-primary to-gray-300';
    }
    return 'bg-gray-300';
  };

  if (variant === 'vertical') {
    return (
      <div className={cn('flex flex-col space-y-4', className)}>
        {steps.map((step, index) => {
          const status = getStepStatus(step, index);
          const stepInfo = STEP_INFO[step];
          const isLast = index === steps.length - 1;

          return (
            <div key={step} className="flex items-start">
              <div className="flex flex-col items-center">
                {getStepIcon(step, status, index)}
                {!isLast && (
                  <div
                    className={cn(
                      'mt-2 h-8 w-0.5',
                      getConnectorClass(status, getStepStatus(steps[index + 1], index + 1))
                    )}
                  />
                )}
              </div>
              {showLabels && (
                <div className="ml-4 flex-1">
                  <h3
                    className={cn(
                      'text-sm font-medium',
                      status === 'current' ? 'text-primary' : 
                      status === 'completed' ? 'text-green-600' : 'text-gray-500'
                    )}
                  >
                    {stepInfo.title}
                  </h3>
                  <p className="text-xs text-gray-500">{stepInfo.description}</p>
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  }

  // Horizontal variant
  return (
    <div className={cn('flex items-center justify-between', className)}>
      {steps.map((step, index) => {
        const status = getStepStatus(step, index);
        const stepInfo = STEP_INFO[step];
        const isLast = index === steps.length - 1;

        return (
          <React.Fragment key={step}>
            <div className="flex flex-col items-center">
              {getStepIcon(step, status, index)}
              {showLabels && (
                <div className="mt-2 text-center">
                  <p
                    className={cn(
                      'text-xs font-medium',
                      status === 'current' ? 'text-primary' : 
                      status === 'completed' ? 'text-green-600' : 'text-gray-500'
                    )}
                  >
                    {stepInfo.title}
                  </p>
                  <p className="text-xs text-gray-400">{stepInfo.description}</p>
                </div>
              )}
            </div>
            {!isLast && (
              <div className="flex-1 px-2">
                <div
                  className={cn(
                    'h-0.5 w-full',
                    getConnectorClass(status, getStepStatus(steps[index + 1], index + 1))
                  )}
                />
              </div>
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
}

/**
 * Compact progress indicator for mobile
 */
export function OnboardingProgressMobile() {
  const { currentStep } = useOnboarding();
  const steps = ONBOARDING_ROUTES.map(route => route.step);
  const currentStepIndex = steps.indexOf(currentStep);
  const progress = ((currentStepIndex + 1) / steps.length) * 100;

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium text-gray-700">
          Step {currentStepIndex + 1} of {steps.length}
        </span>
        <span className="text-sm text-gray-500">
          {Math.round(progress)}% Complete
        </span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className="bg-primary h-2 rounded-full transition-all duration-300 ease-in-out"
          style={{ width: `${progress}%` }}
        />
      </div>
      <div className="mt-2 text-center">
        <h3 className="text-sm font-medium text-gray-900">
          {STEP_INFO[currentStep].title}
        </h3>
        <p className="text-xs text-gray-500">
          {STEP_INFO[currentStep].description}
        </p>
      </div>
    </div>
  );
}

/**
 * Success indicator component for completed steps
 */
export function StepSuccessIndicator({ 
  message, 
  show = true,
  onAnimationComplete 
}: { 
  message: string; 
  show?: boolean;
  onAnimationComplete?: () => void;
}) {
  React.useEffect(() => {
    if (show && onAnimationComplete) {
      const timer = setTimeout(onAnimationComplete, 2000);
      return () => clearTimeout(timer);
    }
  }, [show, onAnimationComplete]);

  if (!show) return null;

  return (
    <div className="fixed top-4 right-4 z-50 animate-in slide-in-from-right duration-300">
      <div className="flex items-center space-x-2 bg-green-50 border border-green-200 rounded-lg px-4 py-3 shadow-lg">
        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-500">
          <Check className="h-4 w-4 text-white" />
        </div>
        <span className="text-sm font-medium text-green-800">{message}</span>
      </div>
    </div>
  );
}

/**
 * Navigation arrows for step progression
 */
export function OnboardingNavigation({
  onPrevious,
  onNext,
  canGoBack = true,
  canGoForward = true,
  nextLabel = 'Continue',
  previousLabel = 'Back',
  isLoading = false,
}: {
  onPrevious?: () => void;
  onNext?: () => void;
  canGoBack?: boolean;
  canGoForward?: boolean;
  nextLabel?: string;
  previousLabel?: string;
  isLoading?: boolean;
}) {
  return (
    <div className="flex items-center justify-between pt-6 border-t border-gray-200">
      <button
        type="button"
        onClick={onPrevious}
        disabled={!canGoBack || isLoading}
        className={cn(
          'flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-md transition-colors',
          canGoBack && !isLoading
            ? 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
            : 'text-gray-400 cursor-not-allowed'
        )}
      >
        <ArrowRight className="h-4 w-4 rotate-180" />
        <span>{previousLabel}</span>
      </button>

      <button
        type="button"
        onClick={onNext}
        disabled={!canGoForward || isLoading}
        className={cn(
          'flex items-center space-x-2 px-6 py-2 text-sm font-medium rounded-md transition-colors',
          canGoForward && !isLoading
            ? 'bg-primary text-white hover:bg-primary/90'
            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
        )}
      >
        <span>{isLoading ? 'Processing...' : nextLabel}</span>
        {!isLoading && <ArrowRight className="h-4 w-4" />}
      </button>
    </div>
  );
}
