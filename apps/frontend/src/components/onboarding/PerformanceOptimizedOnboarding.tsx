"use client";

import React, { Suspense, lazy, useEffect, useMemo } from "react";
import { useRouter, usePathname } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import { useOnboardingPerformance } from "@/hooks/useOnboardingPerformance";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

// Lazy load onboarding steps for better performance
const BusinessInfoStep = lazy(() => import("@/app/auth/signup/business/page"));
const WarehouseSetupStep = lazy(
  () => import("@/app/auth/signup/business/warehouse/page")
);
// Additional steps can be added here as needed

interface PerformanceOptimizedOnboardingProps {
  children: React.ReactNode;
}

// Step transition animations
const stepTransitions = {
  initial: { opacity: 0, x: 20 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -20 },
  transition: { duration: 0.3 },
};

// Loading fallback component
function StepLoadingFallback({ stepName }: { stepName?: string }) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="text-center">
        <LoadingSpinner size="lg" className="mx-auto mb-4" />
        <p className="text-gray-600">
          {stepName ? `Loading ${stepName}...` : "Loading..."}
        </p>
      </div>
    </div>
  );
}

export function PerformanceOptimizedOnboarding({
  children,
}: PerformanceOptimizedOnboardingProps) {
  const pathname = usePathname();
  const performance = useOnboardingPerformance();

  // Track step navigation performance
  useEffect(() => {
    performance.trackStepStart();

    // Track step completion after a brief delay
    const timer = setTimeout(() => {
      performance.trackStepComplete();
    }, 100);

    return () => clearTimeout(timer);
  }, [pathname, performance]);

  // Preload next steps based on current location
  useEffect(() => {
    const preloadNextSteps = () => {
      const stepMap: Record<string, string[]> = {
        "/auth/signup/business": ["/auth/signup/business/admin"],
        "/auth/signup/business/admin": ["/auth/signup/business/warehouse"],
        "/auth/signup/business/warehouse": ["/auth/signup/business/team"],
        "/auth/signup/business/team": ["/auth/signup/business/complete"],
      };

      const nextSteps = stepMap[pathname];
      if (nextSteps) {
        nextSteps.forEach((step) => {
          performance.preloadNextStep(step);
        });
      }
    };

    // Preload after a short delay to avoid blocking current step
    const timer = setTimeout(preloadNextSteps, 500);
    return () => clearTimeout(timer);
  }, [pathname, performance]);

  // Memoize step name for performance
  const stepName = useMemo(() => {
    const stepNames: Record<string, string> = {
      "/auth/signup/business": "Business Information",
      "/auth/signup/business/admin": "Admin Account",
      "/auth/signup/business/warehouse": "Warehouse Setup",
      "/auth/signup/business/team": "Team Setup",
      "/auth/signup/business/complete": "Completion",
    };
    return stepNames[pathname] || "Onboarding";
  }, [pathname]);

  return (
    <div className="performance-optimized-onboarding">
      {/* Performance monitoring in development */}
      {process.env.NODE_ENV === "development" && <PerformanceMonitor />}

      {/* Animated step transitions */}
      <AnimatePresence mode="wait">
        <motion.div
          key={pathname}
          {...stepTransitions}
          className="min-h-screen"
        >
          <Suspense fallback={<StepLoadingFallback stepName={stepName} />}>
            {children}
          </Suspense>
        </motion.div>
      </AnimatePresence>
    </div>
  );
}

// Performance monitoring component for development
function PerformanceMonitor() {
  const performance = useOnboardingPerformance();
  const [showMonitor, setShowMonitor] = React.useState(false);

  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      // Toggle performance monitor with Ctrl+Shift+P
      if (e.ctrlKey && e.shiftKey && e.key === "P") {
        setShowMonitor((prev) => !prev);
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, []);

  if (!showMonitor) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setShowMonitor(true)}
          className="bg-gray-800 text-white px-3 py-1 rounded text-xs hover:bg-gray-700 transition-colors"
        >
          📊 Performance
        </button>
      </div>
    );
  }

  const report = performance.getPerformanceReport();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="fixed bottom-4 right-4 z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm"
    >
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-semibold text-sm">Performance Monitor</h3>
        <button
          onClick={() => setShowMonitor(false)}
          className="text-gray-400 hover:text-gray-600 text-sm"
        >
          ✕
        </button>
      </div>

      <div className="space-y-2 text-xs">
        <div className="grid grid-cols-2 gap-2">
          <div>
            <span className="text-gray-500">Step Load:</span>
            <span className="ml-1 font-mono">
              {report.metrics.stepLoadTime}ms
            </span>
          </div>
          <div>
            <span className="text-gray-500">API Time:</span>
            <span className="ml-1 font-mono">
              {report.metrics.apiResponseTime}ms
            </span>
          </div>
          <div>
            <span className="text-gray-500">Form Submit:</span>
            <span className="ml-1 font-mono">
              {report.metrics.formSubmissionTime}ms
            </span>
          </div>
          <div>
            <span className="text-gray-500">Total Time:</span>
            <span className="ml-1 font-mono">
              {Math.round(report.metrics.totalOnboardingTime / 1000)}s
            </span>
          </div>
        </div>

        <div className="border-t pt-2">
          <div className="flex justify-between">
            <span className="text-gray-500">Cache:</span>
            <span className="font-mono">
              {report.cache.active}/{report.cache.total}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">Errors:</span>
            <span className="font-mono text-red-600">
              {report.metrics.errorCount}
            </span>
          </div>
        </div>

        {report.recommendations.length > 0 && (
          <div className="border-t pt-2">
            <div className="text-yellow-600 text-xs">
              ⚠️ {report.recommendations.length} recommendation(s)
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
}

// HOC for performance-optimized form components
export function withFormPerformance<T extends object>(
  WrappedComponent: React.ComponentType<T>
) {
  return function PerformanceOptimizedForm(props: T) {
    const performance = useOnboardingPerformance();

    // Track form render performance
    useEffect(() => {
      const renderStart =
        performance.getCachedData<number>("form_render_start");
      if (renderStart) {
        const renderTime = Date.now() - renderStart;
        performance.cacheData("form_render_time", renderTime);
      }
    });

    // Set render start time
    useEffect(() => {
      performance.cacheData("form_render_start", Date.now());
    }, []);

    return <WrappedComponent {...props} />;
  };
}

// Hook for optimized step navigation
export function useOptimizedNavigation() {
  const router = useRouter();
  const performance = useOnboardingPerformance();

  const navigateToStep = React.useCallback(
    async (stepPath: string, options: { preload?: boolean } = {}) => {
      // Track navigation start
      performance.trackStepStart();

      // Preload if requested
      if (options.preload) {
        performance.preloadNextStep(stepPath);
        // Small delay to allow preloading
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      // Navigate
      router.push(stepPath);
    },
    [router, performance]
  );

  const navigateWithTransition = React.useCallback(
    async (stepPath: string) => {
      // Add a smooth transition delay
      await new Promise((resolve) => setTimeout(resolve, 150));
      return navigateToStep(stepPath, { preload: true });
    },
    [navigateToStep]
  );

  return {
    navigateToStep,
    navigateWithTransition,
    preloadStep: performance.preloadNextStep,
  };
}

// Performance-optimized input component
export const OptimizedInput = React.memo(function OptimizedInput({
  onChange,
  debounceMs = 300,
  ...props
}: React.InputHTMLAttributes<HTMLInputElement> & {
  debounceMs?: number;
}) {
  const [value, setValue] = React.useState(props.value || "");
  const timeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  const handleChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setValue(newValue);

      // Clear previous timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Debounce the onChange call
      timeoutRef.current = setTimeout(() => {
        onChange?.(e);
      }, debounceMs);
    },
    [onChange, debounceMs]
  );

  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return <input {...props} value={value} onChange={handleChange} />;
});
