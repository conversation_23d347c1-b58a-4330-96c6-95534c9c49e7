'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ChevronLeft, ChevronRight, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

/**
 * Mobile-optimized form container with proper spacing and touch targets
 */
export function MobileFormContainer({ 
  children, 
  className 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) {
  return (
    <div className={cn(
      'w-full max-w-md mx-auto px-4 py-6 space-y-6',
      'md:max-w-2xl md:px-8 md:py-8 md:space-y-8',
      className
    )}>
      {children}
    </div>
  );
}

/**
 * Mobile-optimized input field with larger touch targets
 */
export function MobileInput({
  label,
  error,
  required = false,
  className,
  ...props
}: {
  label: string;
  error?: string;
  required?: boolean;
  className?: string;
} & React.InputHTMLAttributes<HTMLInputElement>) {
  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      <input
        className={cn(
          'w-full px-4 py-3 border border-gray-300 rounded-lg',
          'focus:ring-2 focus:ring-primary focus:border-transparent',
          'text-base', // Prevents zoom on iOS
          'min-h-[44px]', // Touch target size
          'transition-colors duration-200',
          error && 'border-red-500 focus:ring-red-500',
          className
        )}
        {...props}
      />
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  );
}

/**
 * Mobile-optimized select field
 */
export function MobileSelect({
  label,
  error,
  required = false,
  children,
  className,
  ...props
}: {
  label: string;
  error?: string;
  required?: boolean;
  children: React.ReactNode;
  className?: string;
} & React.SelectHTMLAttributes<HTMLSelectElement>) {
  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      <select
        className={cn(
          'w-full px-4 py-3 border border-gray-300 rounded-lg',
          'focus:ring-2 focus:ring-primary focus:border-transparent',
          'text-base appearance-none bg-white',
          'min-h-[44px]',
          'transition-colors duration-200',
          error && 'border-red-500 focus:ring-red-500',
          className
        )}
        {...props}
      >
        {children}
      </select>
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  );
}

/**
 * Mobile-optimized button with proper touch targets
 */
export function MobileButton({
  children,
  variant = 'primary',
  size = 'default',
  fullWidth = false,
  className,
  ...props
}: {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  fullWidth?: boolean;
  className?: string;
} & React.ButtonHTMLAttributes<HTMLButtonElement>) {
  const baseClasses = 'font-medium rounded-lg transition-all duration-200 min-h-[44px] touch-manipulation';
  
  const variantClasses = {
    primary: 'bg-primary text-white hover:bg-primary/90 active:bg-primary/80',
    secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300',
    outline: 'border-2 border-gray-300 text-gray-700 hover:bg-gray-50 active:bg-gray-100',
    ghost: 'text-gray-700 hover:bg-gray-100 active:bg-gray-200',
  };

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    default: 'px-4 py-3 text-base',
    lg: 'px-6 py-4 text-lg',
  };

  return (
    <button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        fullWidth && 'w-full',
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
}

/**
 * Mobile-optimized navigation buttons
 */
export function MobileNavigation({
  onPrevious,
  onNext,
  canGoBack = true,
  canGoForward = true,
  nextLabel = 'Continue',
  previousLabel = 'Back',
  isLoading = false,
  showProgress = false,
  currentStep = 1,
  totalSteps = 4,
}: {
  onPrevious?: () => void;
  onNext?: () => void;
  canGoBack?: boolean;
  canGoForward?: boolean;
  nextLabel?: string;
  previousLabel?: string;
  isLoading?: boolean;
  showProgress?: boolean;
  currentStep?: number;
  totalSteps?: number;
}) {
  return (
    <div className="space-y-4">
      {/* Progress indicator for mobile */}
      {showProgress && (
        <div className="flex justify-center space-x-2">
          {Array.from({ length: totalSteps }, (_, i) => (
            <div
              key={i}
              className={cn(
                'w-2 h-2 rounded-full transition-colors',
                i < currentStep ? 'bg-primary' : 'bg-gray-300'
              )}
            />
          ))}
        </div>
      )}

      {/* Navigation buttons */}
      <div className="flex gap-3">
        {canGoBack && (
          <MobileButton
            variant="outline"
            onClick={onPrevious}
            disabled={isLoading}
            className="flex-1 flex items-center justify-center"
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            {previousLabel}
          </MobileButton>
        )}
        
        <MobileButton
          variant="primary"
          onClick={onNext}
          disabled={!canGoForward || isLoading}
          className={cn(
            'flex items-center justify-center',
            canGoBack ? 'flex-2' : 'flex-1'
          )}
          fullWidth={!canGoBack}
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              Processing...
            </>
          ) : (
            <>
              {nextLabel}
              <ChevronRight className="h-4 w-4 ml-1" />
            </>
          )}
        </MobileButton>
      </div>
    </div>
  );
}

/**
 * Mobile-optimized card component
 */
export function MobileCard({
  title,
  description,
  children,
  className,
}: {
  title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <Card className={cn('shadow-sm border-gray-200', className)}>
      <CardContent className="p-4 md:p-6">
        {title && (
          <div className="mb-4">
            <h2 className="text-lg md:text-xl font-semibold text-gray-900 mb-1">
              {title}
            </h2>
            {description && (
              <p className="text-sm text-gray-600">{description}</p>
            )}
          </div>
        )}
        {children}
      </CardContent>
    </Card>
  );
}

/**
 * Mobile-optimized step indicator
 */
export function MobileStepIndicator({
  currentStep,
  totalSteps,
  stepTitles,
}: {
  currentStep: number;
  totalSteps: number;
  stepTitles?: string[];
}) {
  return (
    <div className="mb-6">
      {/* Progress bar */}
      <div className="flex items-center mb-3">
        <span className="text-sm font-medium text-gray-600 mr-3">
          {currentStep} of {totalSteps}
        </span>
        <div className="flex-1 bg-gray-200 rounded-full h-2">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-300"
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          />
        </div>
      </div>

      {/* Current step title */}
      {stepTitles && stepTitles[currentStep - 1] && (
        <h1 className="text-xl md:text-2xl font-bold text-gray-900">
          {stepTitles[currentStep - 1]}
        </h1>
      )}
    </div>
  );
}

/**
 * Mobile-optimized success indicator
 */
export function MobileSuccessIndicator({
  message,
  show = true,
}: {
  message: string;
  show?: boolean;
}) {
  if (!show) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="fixed top-4 left-4 right-4 z-50 md:top-6 md:left-auto md:right-6 md:max-w-sm"
    >
      <div className="bg-green-50 border border-green-200 rounded-lg p-3 shadow-lg">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="bg-green-500 rounded-full p-1">
              <Check className="h-3 w-3 text-white" />
            </div>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-green-800">{message}</p>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

/**
 * Mobile-optimized form validation summary
 */
export function MobileValidationSummary({
  errors,
  show = true,
}: {
  errors: string[];
  show?: boolean;
}) {
  if (!show || errors.length === 0) return null;

  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4"
    >
      <h3 className="text-sm font-medium text-red-800 mb-2">
        Please fix the following errors:
      </h3>
      <ul className="text-sm text-red-700 space-y-1">
        {errors.map((error, index) => (
          <li key={index} className="flex items-start">
            <span className="text-red-500 mr-2">•</span>
            {error}
          </li>
        ))}
      </ul>
    </motion.div>
  );
}

/**
 * Hook for mobile-specific behavior
 */
export function useMobileOptimizations() {
  const [isMobile, setIsMobile] = React.useState(false);
  const [isTablet, setIsTablet] = React.useState(false);

  React.useEffect(() => {
    const checkDevice = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
    };

    checkDevice();
    window.addEventListener('resize', checkDevice);
    return () => window.removeEventListener('resize', checkDevice);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const preventZoom = (e: React.FocusEvent<HTMLInputElement>) => {
    if (isMobile) {
      e.target.style.fontSize = '16px';
    }
  };

  return {
    isMobile,
    isTablet,
    scrollToTop,
    preventZoom,
  };
}
