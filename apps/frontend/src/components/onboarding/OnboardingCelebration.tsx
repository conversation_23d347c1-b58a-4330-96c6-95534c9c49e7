"use client";

import React, { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import confetti from "canvas-confetti";
import {
  CheckCircle,
  Sparkles,
  ArrowRight,
  Users,
  Package,
  BarChart3,
  Warehouse,
  Star,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface OnboardingCelebrationProps {
  companyName: string;
  warehouseName: string;
  adminName: string;
  onContinue: () => void;
  className?: string;
}

export function OnboardingCelebration({
  companyName,
  warehouseName,
  adminName,
  onContinue,
  className,
}: OnboardingCelebrationProps) {
  const [showConfetti, setShowConfetti] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  // Trigger confetti animation
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowConfetti(true);

      // Multiple confetti bursts
      const duration = 3000;
      const animationEnd = Date.now() + duration;
      const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 };

      function randomInRange(min: number, max: number) {
        return Math.random() * (max - min) + min;
      }

      const interval = setInterval(() => {
        const timeLeft = animationEnd - Date.now();

        if (timeLeft <= 0) {
          clearInterval(interval);
          return;
        }

        const particleCount = 50 * (timeLeft / duration);

        // Left side
        confetti({
          ...defaults,
          particleCount,
          origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 },
        });

        // Right side
        confetti({
          ...defaults,
          particleCount,
          origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 },
        });
      }, 250);

      return () => clearInterval(interval);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // Step progression animation
  useEffect(() => {
    const stepTimers = [
      setTimeout(() => setCurrentStep(1), 1000),
      setTimeout(() => setCurrentStep(2), 2000),
      setTimeout(() => setCurrentStep(3), 3000),
    ];

    return () => stepTimers.forEach(clearTimeout);
  }, []);

  const achievements = [
    {
      icon: <CheckCircle className="h-6 w-6" />,
      title: "Business Profile Created",
      description: `${companyName} is ready for industrial operations`,
      color: "bg-green-500",
    },
    {
      icon: <Users className="h-6 w-6" />,
      title: "Admin Account Setup",
      description: `Welcome aboard, ${adminName}!`,
      color: "bg-blue-500",
    },
    {
      icon: <Warehouse className="h-6 w-6" />,
      title: "Warehouse Configured",
      description: `${warehouseName} is ready for operations`,
      color: "bg-purple-500",
    },
  ];

  const nextSteps = [
    {
      icon: <Package className="h-5 w-5" />,
      title: "Create Your First Pallet",
      description: "Start tracking materials with your first pallet",
      action: "Create Pallet",
    },
    {
      icon: <Users className="h-5 w-5" />,
      title: "Invite Team Members",
      description: "Add warehouse staff to collaborate",
      action: "Invite Team",
    },
    {
      icon: <BarChart3 className="h-5 w-5" />,
      title: "View Dashboard",
      description: "Monitor your warehouse operations",
      action: "View Dashboard",
    },
  ];

  return (
    <div
      className={cn(
        "min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center p-4",
        className
      )}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="w-full max-w-4xl mx-auto"
      >
        {/* Main Success Card */}
        <Card className="relative overflow-hidden border-0 shadow-2xl bg-white/80 backdrop-blur-sm">
          <CardContent className="p-8 md:p-12 text-center">
            {/* Success Icon with Animation */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
              className="relative mx-auto mb-6"
            >
              <div className="relative">
                <div className="absolute inset-0 bg-green-500 rounded-full animate-ping opacity-20" />
                <div className="relative bg-green-500 rounded-full p-4">
                  <CheckCircle className="h-12 w-12 text-white" />
                </div>
              </div>

              {/* Sparkles Animation */}
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                className="absolute -top-2 -right-2"
              >
                <Sparkles className="h-6 w-6 text-yellow-400" />
              </motion.div>
              <motion.div
                animate={{ rotate: -360 }}
                transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
                className="absolute -bottom-2 -left-2"
              >
                <Star className="h-5 w-5 text-purple-400" />
              </motion.div>
            </motion.div>

            {/* Success Message */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="mb-8"
            >
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                🎉 Welcome to Quildora!
              </h1>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Congratulations! Your warehouse management system is ready.
                Let's start optimizing your industrial supply chain.
              </p>
            </motion.div>

            {/* Achievement Cards */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8 }}
              className="grid md:grid-cols-3 gap-4 mb-8"
            >
              {achievements.map((achievement, index) => (
                <motion.div
                  key={achievement.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{
                    opacity: currentStep > index ? 1 : 0.3,
                    y: currentStep > index ? 0 : 20,
                  }}
                  transition={{ delay: 1 + index * 0.2 }}
                >
                  <Card className="border-0 shadow-lg bg-white/60 backdrop-blur-sm">
                    <CardContent className="p-4 text-center">
                      <div
                        className={cn(
                          "inline-flex p-3 rounded-full text-white mb-3",
                          achievement.color
                        )}
                      >
                        {achievement.icon}
                      </div>
                      <h3 className="font-semibold text-gray-900 mb-1">
                        {achievement.title}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {achievement.description}
                      </p>
                      <AnimatePresence>
                        {currentStep > index && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="mt-2"
                          >
                            <Badge
                              variant="secondary"
                              className="bg-green-100 text-green-800"
                            >
                              ✓ Complete
                            </Badge>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </motion.div>

            {/* Next Steps */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 2 }}
              className="mb-8"
            >
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                What's Next?
              </h2>
              <div className="grid md:grid-cols-3 gap-4">
                {nextSteps.map((step, index) => (
                  <motion.div
                    key={step.title}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 2.2 + index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                    className="group"
                  >
                    <Card className="border-0 shadow-md bg-white/40 backdrop-blur-sm hover:bg-white/60 transition-all duration-200 cursor-pointer">
                      <CardContent className="p-4">
                        <div className="flex items-start space-x-3">
                          <div className="bg-primary/10 p-2 rounded-lg group-hover:bg-primary/20 transition-colors">
                            {step.icon}
                          </div>
                          <div className="flex-1 text-left">
                            <h3 className="font-semibold text-gray-900 mb-1">
                              {step.title}
                            </h3>
                            <p className="text-sm text-gray-600 mb-2">
                              {step.description}
                            </p>
                            <span className="text-xs text-primary font-medium group-hover:underline">
                              {step.action} →
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Continue Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 2.5 }}
            >
              <Button
                onClick={onContinue}
                size="lg"
                className="bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90 text-white px-8 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
              >
                Enter Your Dashboard
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <p className="text-sm text-gray-500 mt-3">
                Ready to revolutionize your warehouse operations
              </p>
            </motion.div>
          </CardContent>

          {/* Background Decorations */}
          <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
            <div className="absolute top-10 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-20 animate-pulse" />
            <div
              className="absolute top-20 right-20 w-16 h-16 bg-purple-200 rounded-full opacity-20 animate-pulse"
              style={{ animationDelay: "1s" }}
            />
            <div
              className="absolute bottom-20 left-20 w-12 h-12 bg-green-200 rounded-full opacity-20 animate-pulse"
              style={{ animationDelay: "2s" }}
            />
            <div
              className="absolute bottom-10 right-10 w-24 h-24 bg-yellow-200 rounded-full opacity-20 animate-pulse"
              style={{ animationDelay: "0.5s" }}
            />
          </div>
        </Card>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 3 }}
          className="text-center mt-8"
        >
          <p className="text-gray-500 text-sm">
            Need help getting started? Check out our{" "}
            <a href="#" className="text-primary hover:underline">
              Quick Start Guide
            </a>{" "}
            or{" "}
            <a href="#" className="text-primary hover:underline">
              Contact Support
            </a>
          </p>
        </motion.div>
      </motion.div>
    </div>
  );
}
