"use client";

import React from "react";
import { Building2, Factory, Store, Package, HelpCircle } from "lucide-react";

interface WarehouseType {
  value: string;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  features: string[];
}

interface WarehouseTypeSelectorProps {
  value?: string;
  onChange: (value: string) => void;
  error?: string;
  disabled?: boolean;
  className?: string;
}

const WAREHOUSE_TYPES: WarehouseType[] = [
  {
    value: "distribution",
    label: "Distribution Center",
    description:
      "Central hub for receiving, storing, and distributing industrial supplies and materials",
    icon: Building2,
    features: [
      "High-volume material handling",
      "Cross-docking for job sites",
      "Multi-client industrial inventory",
      "Bulk material processing",
    ],
  },
  {
    value: "kitting",
    label: "Job Site Kitting Facility",
    description:
      "Specialized facility for assembling and preparing material kits for specific job sites or projects",
    icon: Package,
    features: [
      "Project-specific material kitting",
      "Job site delivery coordination",
      "Custom assembly operations",
      "Construction project support",
    ],
  },
  {
    value: "industrial_supply",
    label: "Industrial Supply Warehouse",
    description:
      "Storage and distribution of industrial equipment, tools, and supplies for B2B customers",
    icon: Factory,
    features: [
      "Industrial equipment storage",
      "B2B customer fulfillment",
      "Technical product handling",
      "Specialized material management",
    ],
  },
  {
    value: "manufacturing_support",
    label: "Manufacturing Support Warehouse",
    description:
      "Facility supporting manufacturing operations with raw materials, components, and finished goods",
    icon: Building2,
    features: [
      "Raw materials management",
      "Component inventory control",
      "Production line support",
      "Manufacturing logistics",
    ],
  },
  {
    value: "materials_yard",
    label: "Construction Materials Yard",
    description:
      "Outdoor and covered storage for construction materials, lumber, steel, and building supplies",
    icon: Package,
    features: [
      "Outdoor material storage",
      "Heavy material handling",
      "Construction supply management",
      "Weather-resistant operations",
    ],
  },
  {
    value: "equipment_storage",
    label: "Equipment & Parts Storage",
    description:
      "Specialized storage for heavy equipment, machinery parts, and industrial components",
    icon: Factory,
    features: [
      "Heavy equipment storage",
      "Machinery parts inventory",
      "Maintenance support operations",
      "Industrial component tracking",
    ],
  },
  {
    value: "other",
    label: "Other Industrial",
    description: "Custom industrial warehouse type not listed above",
    icon: HelpCircle,
    features: [
      "Flexible industrial configuration",
      "Custom B2B workflows",
      "Adaptable to industrial needs",
      "Scalable operations",
    ],
  },
];

export default function WarehouseTypeSelector({
  value,
  onChange,
  error,
  disabled = false,
  className = "",
}: WarehouseTypeSelectorProps) {
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="grid grid-cols-1 gap-4">
        {WAREHOUSE_TYPES.map((type) => {
          const Icon = type.icon;
          const isSelected = value === type.value;

          return (
            <label
              key={type.value}
              className={`
                relative flex cursor-pointer rounded-lg border p-4 transition-all
                ${disabled ? "opacity-50 cursor-not-allowed" : ""}
                ${
                  isSelected
                    ? "border-primary bg-primary/5 ring-2 ring-primary/20"
                    : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                }
                ${error ? "border-red-300" : ""}
              `}
            >
              <input
                type="radio"
                value={type.value}
                checked={isSelected}
                onChange={(e) => onChange(e.target.value)}
                disabled={disabled}
                className="sr-only"
              />

              <div className="flex items-start space-x-4 flex-1">
                {/* Icon */}
                <div
                  className={`
                  flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center
                  ${
                    isSelected
                      ? "bg-primary text-primary-foreground"
                      : "bg-gray-100 text-gray-600"
                  }
                `}
                >
                  <Icon className="w-5 h-5" />
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium text-gray-900">
                      {type.label}
                    </h3>
                    {isSelected && (
                      <div className="w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full" />
                      </div>
                    )}
                  </div>

                  <p className="mt-1 text-sm text-gray-500">
                    {type.description}
                  </p>

                  {/* Features (shown when selected) */}
                  {isSelected && (
                    <div className="mt-3 space-y-1">
                      <p className="text-xs font-medium text-gray-700">
                        Key Features:
                      </p>
                      <ul className="text-xs text-gray-600 space-y-1">
                        {type.features.map((feature, index) => (
                          <li key={index} className="flex items-center">
                            <div className="w-1 h-1 bg-primary rounded-full mr-2 flex-shrink-0" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </label>
          );
        })}
      </div>

      {error && (
        <p className="text-sm text-red-600 flex items-center gap-1">
          <HelpCircle className="h-3 w-3" />
          {error}
        </p>
      )}
    </div>
  );
}

// Volume selector component
interface VolumeOption {
  value: string;
  label: string;
  description: string;
  range: string;
  recommended: string[];
}

interface VolumeSelectorProps {
  value?: string;
  onChange: (value: string) => void;
  error?: string;
  disabled?: boolean;
  className?: string;
}

const VOLUME_OPTIONS: VolumeOption[] = [
  {
    value: "small",
    label: "Small Operation",
    description: "Local contractors and small industrial suppliers",
    range: "1-500 pallets",
    recommended: [
      "Local construction contractors",
      "Small industrial suppliers",
      "Specialty equipment dealers",
    ],
  },
  {
    value: "medium",
    label: "Medium Operation",
    description: "Regional distributors and growing industrial operations",
    range: "500-2,000 pallets",
    recommended: [
      "Regional industrial distributors",
      "Growing construction companies",
      "Multi-site manufacturing support",
    ],
  },
  {
    value: "large",
    label: "Large Operation",
    description: "Major distributors and multi-site industrial operations",
    range: "2,000-10,000 pallets",
    recommended: [
      "Major industrial distributors",
      "Large construction companies",
      "Multi-facility manufacturers",
    ],
  },
  {
    value: "enterprise",
    label: "Enterprise",
    description: "Large industrial enterprises with complex supply chains",
    range: "10,000+ pallets",
    recommended: [
      "Fortune 500 industrial companies",
      "Global supply chain operations",
      "Major industrial distributors",
    ],
  },
];

export function VolumeSelector({
  value,
  onChange,
  error,
  disabled = false,
  className = "",
}: VolumeSelectorProps) {
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {VOLUME_OPTIONS.map((option) => {
          const isSelected = value === option.value;

          return (
            <label
              key={option.value}
              className={`
                relative flex cursor-pointer rounded-lg border p-4 transition-all
                ${disabled ? "opacity-50 cursor-not-allowed" : ""}
                ${
                  isSelected
                    ? "border-primary bg-primary/5 ring-2 ring-primary/20"
                    : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                }
                ${error ? "border-red-300" : ""}
              `}
            >
              <input
                type="radio"
                value={option.value}
                checked={isSelected}
                onChange={(e) => onChange(e.target.value)}
                disabled={disabled}
                className="sr-only"
              />

              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-gray-900">
                    {option.label} ({option.range})
                  </h3>
                  {isSelected && (
                    <div className="w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full" />
                    </div>
                  )}
                </div>

                <p className="mt-1 text-sm text-gray-500">
                  {option.description}
                </p>

                {/* Recommended for (shown when selected) */}
                {isSelected && (
                  <div className="mt-3">
                    <p className="text-xs font-medium text-gray-700">
                      Recommended for:
                    </p>
                    <ul className="text-xs text-gray-600 mt-1 space-y-1">
                      {option.recommended.map((rec, index) => (
                        <li key={index} className="flex items-center">
                          <div className="w-1 h-1 bg-primary rounded-full mr-2 flex-shrink-0" />
                          {rec}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </label>
          );
        })}
      </div>

      {error && (
        <p className="text-sm text-red-600 flex items-center gap-1">
          <HelpCircle className="h-3 w-3" />
          {error}
        </p>
      )}
    </div>
  );
}

// Export types
export type {
  WarehouseType,
  VolumeOption,
  WarehouseTypeSelectorProps,
  VolumeSelectorProps,
};
