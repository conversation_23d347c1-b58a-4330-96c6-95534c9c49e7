'use client';

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, X, Sparkles } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SuccessToastProps {
  message: string;
  description?: string;
  show: boolean;
  onClose?: () => void;
  duration?: number;
  position?: 'top-right' | 'top-center' | 'bottom-right' | 'bottom-center';
  showConfetti?: boolean;
}

export function SuccessToast({
  message,
  description,
  show,
  onClose,
  duration = 4000,
  position = 'top-right',
  showConfetti = false,
}: SuccessToastProps) {
  const [isVisible, setIsVisible] = useState(show);

  useEffect(() => {
    setIsVisible(show);
    
    if (show && duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        onClose?.();
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, [show, duration, onClose]);

  const positionClasses = {
    'top-right': 'top-4 right-4',
    'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
    'bottom-right': 'bottom-4 right-4',
    'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2',
  };

  const slideDirection = {
    'top-right': { x: 400, y: 0 },
    'top-center': { x: 0, y: -100 },
    'bottom-right': { x: 400, y: 0 },
    'bottom-center': { x: 0, y: 100 },
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ 
            opacity: 0, 
            ...slideDirection[position],
            scale: 0.8 
          }}
          animate={{ 
            opacity: 1, 
            x: 0, 
            y: 0,
            scale: 1 
          }}
          exit={{ 
            opacity: 0, 
            ...slideDirection[position],
            scale: 0.8 
          }}
          transition={{ 
            type: 'spring', 
            stiffness: 300, 
            damping: 30 
          }}
          className={cn(
            'fixed z-50 max-w-sm w-full',
            positionClasses[position]
          )}
        >
          <div className="relative bg-white border border-green-200 rounded-lg shadow-lg overflow-hidden">
            {/* Success gradient background */}
            <div className="absolute inset-0 bg-gradient-to-r from-green-50 to-emerald-50" />
            
            {/* Content */}
            <div className="relative p-4">
              <div className="flex items-start space-x-3">
                {/* Success Icon */}
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
                  className="flex-shrink-0"
                >
                  <div className="relative">
                    <div className="bg-green-500 rounded-full p-1.5">
                      <CheckCircle className="h-5 w-5 text-white" />
                    </div>
                    
                    {/* Sparkle animation */}
                    {showConfetti && (
                      <motion.div
                        animate={{ 
                          rotate: 360,
                          scale: [1, 1.2, 1] 
                        }}
                        transition={{ 
                          duration: 2, 
                          repeat: Infinity,
                          ease: 'linear' 
                        }}
                        className="absolute -top-1 -right-1"
                      >
                        <Sparkles className="h-3 w-3 text-yellow-400" />
                      </motion.div>
                    )}
                  </div>
                </motion.div>

                {/* Message Content */}
                <div className="flex-1 min-w-0">
                  <motion.h3
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="text-sm font-semibold text-green-800"
                  >
                    {message}
                  </motion.h3>
                  
                  {description && (
                    <motion.p
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="mt-1 text-xs text-green-600"
                    >
                      {description}
                    </motion.p>
                  )}
                </div>

                {/* Close Button */}
                {onClose && (
                  <motion.button
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.5 }}
                    onClick={() => {
                      setIsVisible(false);
                      onClose();
                    }}
                    className="flex-shrink-0 p-1 rounded-full hover:bg-green-100 transition-colors"
                  >
                    <X className="h-4 w-4 text-green-600" />
                  </motion.button>
                )}
              </div>
            </div>

            {/* Progress bar */}
            {duration > 0 && (
              <motion.div
                initial={{ scaleX: 1 }}
                animate={{ scaleX: 0 }}
                transition={{ duration: duration / 1000, ease: 'linear' }}
                className="absolute bottom-0 left-0 h-1 bg-green-500 origin-left"
              />
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

/**
 * Hook for managing success toasts
 */
export function useSuccessToast() {
  const [toasts, setToasts] = useState<Array<{
    id: string;
    message: string;
    description?: string;
    showConfetti?: boolean;
  }>>([]);

  const showToast = (
    message: string, 
    description?: string, 
    showConfetti = false
  ) => {
    const id = Math.random().toString(36).substr(2, 9);
    setToasts(prev => [...prev, { id, message, description, showConfetti }]);
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const ToastContainer = () => (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map((toast, index) => (
        <SuccessToast
          key={toast.id}
          message={toast.message}
          description={toast.description}
          show={true}
          showConfetti={toast.showConfetti}
          onClose={() => removeToast(toast.id)}
          position="top-right"
        />
      ))}
    </div>
  );

  return {
    showToast,
    ToastContainer,
  };
}
