'use client';

import React, { useEffect, useState } from 'react';
import { CheckCircle2, Sparkles, Star, Heart, Zap } from 'lucide-react';
import Confetti from 'react-confetti';

interface CompletionAnimationProps {
  title?: string;
  subtitle?: string;
  showConfetti?: boolean;
  confettiDuration?: number;
  animationType?: 'bounce' | 'pulse' | 'spin' | 'scale';
  iconType?: 'check' | 'star' | 'heart' | 'zap' | 'sparkles';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  onAnimationComplete?: () => void;
}

const ICON_COMPONENTS = {
  check: CheckCircle2,
  star: Star,
  heart: Heart,
  zap: Zap,
  sparkles: Sparkles,
};

const SIZE_CLASSES = {
  sm: {
    container: 'w-12 h-12',
    icon: 'w-6 h-6',
    accent: 'w-4 h-4',
    title: 'text-lg',
    subtitle: 'text-sm',
  },
  md: {
    container: 'w-16 h-16',
    icon: 'w-8 h-8',
    accent: 'w-5 h-5',
    title: 'text-xl',
    subtitle: 'text-base',
  },
  lg: {
    container: 'w-20 h-20',
    icon: 'w-10 h-10',
    accent: 'w-6 h-6',
    title: 'text-2xl',
    subtitle: 'text-lg',
  },
  xl: {
    container: 'w-24 h-24',
    icon: 'w-12 h-12',
    accent: 'w-7 h-7',
    title: 'text-3xl',
    subtitle: 'text-xl',
  },
};

const ANIMATION_CLASSES = {
  bounce: 'animate-bounce',
  pulse: 'animate-pulse',
  spin: 'animate-spin',
  scale: 'animate-ping',
};

export default function CompletionAnimation({
  title = 'Success!',
  subtitle,
  showConfetti = true,
  confettiDuration = 5000,
  animationType = 'bounce',
  iconType = 'check',
  size = 'lg',
  className = '',
  onAnimationComplete,
}: CompletionAnimationProps) {
  const [windowDimensions, setWindowDimensions] = useState({ width: 0, height: 0 });
  const [showConfettiState, setShowConfettiState] = useState(showConfetti);
  const [isVisible, setIsVisible] = useState(false);

  const IconComponent = ICON_COMPONENTS[iconType];
  const sizeClasses = SIZE_CLASSES[size];
  const animationClass = ANIMATION_CLASSES[animationType];

  // Get window dimensions for confetti
  useEffect(() => {
    const updateWindowDimensions = () => {
      setWindowDimensions({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    updateWindowDimensions();
    window.addEventListener('resize', updateWindowDimensions);

    return () => {
      window.removeEventListener('resize', updateWindowDimensions);
    };
  }, []);

  // Handle confetti timing
  useEffect(() => {
    if (showConfetti) {
      const timer = setTimeout(() => {
        setShowConfettiState(false);
        if (onAnimationComplete) {
          onAnimationComplete();
        }
      }, confettiDuration);

      return () => clearTimeout(timer);
    }
  }, [showConfetti, confettiDuration, onAnimationComplete]);

  // Entrance animation
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className={`relative ${className}`}>
      {/* Confetti */}
      {showConfettiState && (
        <Confetti
          width={windowDimensions.width}
          height={windowDimensions.height}
          recycle={false}
          numberOfPieces={200}
          gravity={0.3}
          colors={['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#F97316']}
        />
      )}

      {/* Main Animation Container */}
      <div
        className={`
          text-center transition-all duration-1000 transform
          ${isVisible ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 translate-y-4 scale-95'}
        `}
      >
        {/* Icon Container */}
        <div className="relative inline-flex items-center justify-center mb-6">
          <div
            className={`
              ${sizeClasses.container} bg-green-100 rounded-full flex items-center justify-center
              transition-all duration-500 ${animationType === 'scale' ? '' : animationClass}
            `}
          >
            <IconComponent className={`${sizeClasses.icon} text-green-600`} />
          </div>
          
          {/* Accent Icon */}
          <div className="absolute -top-1 -right-1">
            <Sparkles className={`${sizeClasses.accent} text-yellow-500 animate-pulse`} />
          </div>

          {/* Ripple Effect */}
          {animationType === 'scale' && (
            <div
              className={`
                absolute ${sizeClasses.container} bg-green-200 rounded-full
                animate-ping opacity-75
              `}
            />
          )}
        </div>

        {/* Text Content */}
        <div className="space-y-2">
          <h1 className={`${sizeClasses.title} font-bold text-gray-900`}>
            {title}
          </h1>
          {subtitle && (
            <p className={`${sizeClasses.subtitle} text-gray-600`}>
              {subtitle}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

// Preset configurations for common use cases
export const CompletionPresets = {
  onboarding: {
    title: 'Welcome to Quildora! 🎉',
    subtitle: 'Your warehouse management system is ready to go',
    iconType: 'check' as const,
    size: 'xl' as const,
    animationType: 'bounce' as const,
    confettiDuration: 5000,
  },
  
  taskComplete: {
    title: 'Task Completed!',
    subtitle: 'Great job! Moving on to the next step.',
    iconType: 'check' as const,
    size: 'lg' as const,
    animationType: 'pulse' as const,
    confettiDuration: 3000,
  },
  
  achievement: {
    title: 'Achievement Unlocked!',
    subtitle: 'You\'ve reached a new milestone.',
    iconType: 'star' as const,
    size: 'lg' as const,
    animationType: 'scale' as const,
    confettiDuration: 4000,
  },
  
  celebration: {
    title: 'Congratulations!',
    subtitle: 'You did it!',
    iconType: 'heart' as const,
    size: 'xl' as const,
    animationType: 'bounce' as const,
    confettiDuration: 6000,
  },
  
  quickWin: {
    title: 'Success!',
    iconType: 'zap' as const,
    size: 'md' as const,
    animationType: 'pulse' as const,
    confettiDuration: 2000,
    showConfetti: false,
  },
};

// Hook for easy preset usage
export function useCompletionAnimation(preset: keyof typeof CompletionPresets) {
  return CompletionPresets[preset];
}

// Export types
export type { CompletionAnimationProps };
