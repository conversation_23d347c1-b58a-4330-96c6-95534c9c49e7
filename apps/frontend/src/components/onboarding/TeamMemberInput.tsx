"use client";

import React from "react";
import {
  Control,
  useFieldArray,
  UseFormRegister,
  UseFormWatch,
  UseFormSetValue,
  FieldErrors,
} from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { OnboardingField } from "@/components/onboarding/OnboardingFormWrapper";
import { Plus, X, Mail, UserCheck, Users, AlertCircle } from "lucide-react";

interface TeamMember {
  email: string;
  role: "WAREHOUSE_MEMBER" | "WAREHOUSE_MANAGER";
}

interface TeamMemberInputProps {
  control: Control<any>;
  register: UseFormRegister<any>;
  watch: UseFormWatch<any>;
  setValue: UseFormSetValue<any>;
  errors: FieldErrors<any>;
  fieldName?: string;
  maxMembers?: number;
  showRoleDescriptions?: boolean;
  allowRemoveAll?: boolean;
  className?: string;
}

const ROLE_OPTIONS = [
  {
    value: "WAREHOUSE_MEMBER" as const,
    label: "Warehouse Member",
    description:
      "Can perform daily warehouse operations like receiving, picking, and shipping",
    icon: Users,
  },
  {
    value: "WAREHOUSE_MANAGER" as const,
    label: "Warehouse Manager",
    description:
      "Can manage warehouse operations, users, and settings in addition to member permissions",
    icon: UserCheck,
  },
];

export default function TeamMemberInput({
  control,
  register,
  watch,
  setValue,
  errors,
  fieldName = "teamMembers",
  maxMembers = 20,
  showRoleDescriptions = true,
  allowRemoveAll = false,
  className = "",
}: TeamMemberInputProps) {
  const { fields, append, remove } = useFieldArray({
    control,
    name: fieldName,
  });

  const teamMembers = watch(fieldName) || [];

  const handleAddMember = () => {
    if (fields.length < maxMembers) {
      append({ email: "", role: "WAREHOUSE_MEMBER" });
    }
  };

  const handleRemoveMember = (index: number) => {
    if (allowRemoveAll || fields.length > 1) {
      remove(index);
    }
  };

  const getRoleInfo = (role: string) => {
    return ROLE_OPTIONS.find((option) => option.value === role);
  };

  const getFieldError = (index: number, field: "email" | "role") => {
    const fieldErrors = errors[fieldName] as any;
    return fieldErrors?.[index]?.[field]?.message;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Team Members</h3>
          <p className="text-sm text-gray-500">
            Invite team members to collaborate in your warehouse
          </p>
        </div>
        <Button
          type="button"
          variant="outline"
          onClick={handleAddMember}
          disabled={fields.length >= maxMembers}
          className="min-h-[44px]"
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Member
        </Button>
      </div>

      {/* Team Member Cards */}
      <div className="space-y-4">
        {fields.map((field, index) => {
          const currentRole = watch(`${fieldName}.${index}.role`);
          const roleInfo = getRoleInfo(currentRole);

          return (
            <Card
              key={field.id}
              className="border border-gray-200 hover:border-gray-300 transition-colors"
            >
              <CardContent className="p-4">
                <div className="flex items-start space-x-4">
                  {/* Avatar */}
                  <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Mail className="h-5 w-5 text-primary" />
                  </div>

                  <div className="flex-1 space-y-4">
                    {/* Email Input */}
                    <OnboardingField
                      label={`Email Address ${index + 1}`}
                      required
                      error={getFieldError(index, "email")}
                    >
                      <Input
                        {...register(`${fieldName}.${index}.email`)}
                        type="email"
                        placeholder="Enter team member's email"
                        className="h-12"
                        autoComplete="email"
                      />
                    </OnboardingField>

                    {/* Role Selection */}
                    <OnboardingField
                      label="Role"
                      required
                      error={getFieldError(index, "role")}
                      description={
                        showRoleDescriptions ? roleInfo?.description : undefined
                      }
                    >
                      <Select
                        value={currentRole}
                        onValueChange={(value) =>
                          setValue(`${fieldName}.${index}.role`, value)
                        }
                      >
                        <SelectTrigger className="h-12">
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                        <SelectContent>
                          {ROLE_OPTIONS.map((role) => {
                            const Icon = role.icon;
                            return (
                              <SelectItem key={role.value} value={role.value}>
                                <div className="flex items-center space-x-2">
                                  <Icon className="h-4 w-4" />
                                  <div>
                                    <div className="font-medium">
                                      {role.label}
                                    </div>
                                    {showRoleDescriptions && (
                                      <div className="text-xs text-gray-500 max-w-xs">
                                        {role.description}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    </OnboardingField>
                  </div>

                  {/* Remove Button */}
                  {(allowRemoveAll || fields.length > 1) && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveMember(index)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50 min-h-[44px] min-w-[44px]"
                    >
                      <X className="h-4 w-4" />
                      <span className="sr-only">Remove team member</span>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Limits and Warnings */}
      <div className="space-y-2">
        {fields.length >= maxMembers && (
          <div className="flex items-center space-x-2 text-amber-600 bg-amber-50 p-3 rounded-lg">
            <AlertCircle className="h-4 w-4 flex-shrink-0" />
            <p className="text-sm">
              Maximum of {maxMembers} team members can be added at once. You can
              add more later.
            </p>
          </div>
        )}

        {fields.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Users className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm">No team members added yet</p>
            <p className="text-xs">
              Click "Add Member" to invite your first team member
            </p>
          </div>
        )}
      </div>

      {/* Summary */}
      {teamMembers.length > 0 && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Users className="h-5 w-5 text-blue-600" />
              <h4 className="font-medium text-blue-900">Invitation Summary</h4>
            </div>
            <div className="space-y-1 text-sm text-blue-700">
              <p>
                {teamMembers.length} team member
                {teamMembers.length !== 1 ? "s" : ""} will receive invitation
                emails.
              </p>
              <div className="flex flex-wrap gap-2 mt-2">
                {ROLE_OPTIONS.map((role) => {
                  const count = teamMembers.filter(
                    (member: TeamMember) => member.role === role.value
                  ).length;
                  if (count === 0) return null;

                  return (
                    <span
                      key={role.value}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                    >
                      {count} {role.label}
                      {count !== 1 ? "s" : ""}
                    </span>
                  );
                })}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Utility function to validate team members
export function validateTeamMembers(teamMembers: TeamMember[]): string[] {
  const errors: string[] = [];
  const emails = new Set<string>();

  teamMembers.forEach((member, index) => {
    // Check for duplicate emails
    if (emails.has(member.email.toLowerCase())) {
      errors.push(`Duplicate email found: ${member.email}`);
    } else {
      emails.add(member.email.toLowerCase());
    }

    // Check for valid email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(member.email)) {
      errors.push(`Invalid email format: ${member.email}`);
    }

    // Check for valid role
    if (!["WAREHOUSE_MEMBER", "WAREHOUSE_MANAGER"].includes(member.role)) {
      errors.push(`Invalid role for ${member.email}: ${member.role}`);
    }
  });

  return errors;
}

// Export types
export type { TeamMember, TeamMemberInputProps };
