"use client";

import { useState, useMemo, useCallback, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Search, X, Loader2 } from "lucide-react";
import { useDestinationsWithCodesSuspense } from "@/hooks/api/useWarehouseData";
import { cn } from "@/lib/utils";

interface DestinationFilterProps {
  destinations: string[];
  selectedDestination: string;
  onDestinationSelect: (destination: string) => void;
  isProcessing?: boolean;
  onProcessingStart?: () => void;
}

export default function DestinationFilter({
  destinations,
  selectedDestination,
  onDestinationSelect,
  isProcessing = false,
  onProcessingStart,
}: DestinationFilterProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [optimisticSelection, setOptimisticSelection] = useState<string>("");
  const [processingDestination, setProcessingDestination] =
    useState<string>("");

  // Fetch destinations with codes for enhanced search and caching
  const { data: destinationsWithCodes = [] } =
    useDestinationsWithCodesSuspense();

  // Reset optimistic state when processing completes
  useEffect(() => {
    if (!isProcessing && selectedDestination) {
      // Clear optimistic state after processing completes
      setOptimisticSelection("");
      setProcessingDestination("");
    }
  }, [isProcessing, selectedDestination]);

  const destinationCodeMap = useMemo(
    () => new Map(destinationsWithCodes.map((dest) => [dest.name, dest.code])),
    [destinationsWithCodes]
  );

  // Filter destinations based on search term (name or code)
  const filteredDestinations = useMemo(() => {
    if (!searchTerm.trim()) return destinations;

    return destinations.filter((destination) => {
      const lowerSearchTerm = searchTerm.toLowerCase();
      const destinationCode = destinationCodeMap.get(destination);

      // Search by destination name or code
      return (
        destination.toLowerCase().includes(lowerSearchTerm) ||
        (destinationCode &&
          destinationCode.toLowerCase().includes(lowerSearchTerm))
      );
    });
  }, [destinations, searchTerm, destinationCodeMap]);

  // Optimistic click handler with immediate feedback
  const handleDestinationClick = useCallback(
    async (destination: string) => {
      // Immediate visual feedback - optimistic update
      setOptimisticSelection(destination);
      setProcessingDestination(destination);

      // Notify parent that processing is starting
      onProcessingStart?.();

      // Small delay to ensure UI updates are visible and provide satisfying feedback
      // await new Promise((resolve) => setTimeout(resolve, 100));

      // Call the actual handler
      onDestinationSelect(destination);
    },
    [onDestinationSelect, onProcessingStart]
  );

  const handleClearSelection = useCallback(() => {
    setOptimisticSelection("");
    setProcessingDestination("");
    onDestinationSelect("");
    setSearchTerm("");
  }, [onDestinationSelect]);

  // Helper function to format destination display with code
  const formatDestinationDisplay = useCallback(
    (destination: string) => {
      const destinationCode = destinationCodeMap.get(destination);
      if (destinationCode) {
        return `${destination} (${destinationCode})`;
      }
      return destination;
    },
    [destinationCodeMap]
  );

  const destinationButtons = useMemo(
    () =>
      filteredDestinations.map((destination) => {
        const isSelected =
          selectedDestination === destination ||
          optimisticSelection === destination;
        const isCurrentlyProcessing = processingDestination === destination;
        const isDisabled = isProcessing || isCurrentlyProcessing;

        return (
          <Button
            key={destination}
            variant={isSelected ? "default" : "outline"}
            className={cn(
              "w-full justify-between text-xl h-12 transition-colors duration-150",
              "focus:ring-2 focus:ring-offset-2 focus:ring-blue-500", // Accessibility
              isCurrentlyProcessing && "bg-blue-50 border-blue-300",
              isDisabled && "opacity-70 cursor-not-allowed"
            )}
            onClick={() => handleDestinationClick(destination)}
            disabled={isDisabled}
          >
            <span>{formatDestinationDisplay(destination)}</span>
            {isCurrentlyProcessing && (
              <Loader2 className="h-4 w-4 animate-spin ml-2" />
            )}
          </Button>
        );
      }),
    [
      filteredDestinations,
      selectedDestination,
      optimisticSelection,
      processingDestination,
      isProcessing,
      handleDestinationClick,
      formatDestinationDisplay,
    ]
  );

  const selectedDestinationDisplay = useMemo(() => {
    const displayDestination = selectedDestination || optimisticSelection;
    return displayDestination
      ? formatDestinationDisplay(displayDestination)
      : "";
  }, [selectedDestination, optimisticSelection, formatDestinationDisplay]);

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search destinations by name or code..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 text-xl h-12"
        />
      </div>

      {/* Selected Destination */}
      {(selectedDestination || optimisticSelection) && (
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">
            {isProcessing ? "Processing:" : "Selected:"}
          </span>
          <Badge variant="default" className="flex items-center gap-2">
            {selectedDestinationDisplay}
            {isProcessing && <Loader2 className="h-3 w-3 animate-spin" />}
            {!isProcessing && (
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={handleClearSelection}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </Badge>
        </div>
      )}

      {/* Destination List */}
      <div className="relative space-y-2 max-h-60 overflow-y-auto">
        {filteredDestinations.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            {searchTerm
              ? "No destinations match your search"
              : "No destinations available"}
          </div>
        ) : (
          <div
            className={cn(
              "space-y-2 transition-opacity duration-200",
              isProcessing && "opacity-75"
            )}
          >
            {destinationButtons}
          </div>
        )}
        {isProcessing && filteredDestinations.length > 0 && (
          <div className="absolute bottom-0 left-0 right-0 text-center py-1 text-sm text-muted-foreground bg-white/90 backdrop-blur-sm">
            Loading destination data...
          </div>
        )}
      </div>
    </div>
  );
}
