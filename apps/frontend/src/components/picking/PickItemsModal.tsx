"use client";

import React, { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Loader2, AlertCircle, PackageOpen, Minus, Plus } from "lucide-react";
import { toast } from "sonner";
import { Pallet, PalletItem, Location } from "@quildora/types";

interface PickItemsModalProps {
  pallet: Pallet;
  isOpen: boolean;
  onClose: () => void;
}

interface PickedItem {
  palletItemId: string;
  pickedQuantity: number;
}

export default function PickItemsModal({
  pallet,
  isOpen,
  onClose,
}: PickItemsModalProps) {
  const [pickedItems, setPickedItems] = useState<Record<string, number>>({});
  const [notes, setNotes] = useState("");
  const [releasedTo, setReleasedTo] = useState("");
  const queryClient = useQueryClient();
  const { appToken } = useAuth();

  const pickMutation = useMutation({
    mutationFn: async (data: {
      items: PickedItem[];
      notes?: string;
      releasedTo?: string;
    }) => {
      if (!appToken) {
        throw new Error("Authentication token not available.");
      }
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
      return fetchWithAuth(`${baseUrl}/api/pallets/${pallet.id}/pick-items`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
        token: appToken,
      });
    },
    onSuccess: () => {
      toast.success("Items picked successfully");
      queryClient.invalidateQueries({ queryKey: ["pallets"] });
      handleClose();
    },
    onError: (error: unknown) => {
      toast.error(
        error instanceof Error ? error.message : "Failed to pick items"
      );
    },
  });

  const handleClose = () => {
    setPickedItems({});
    setNotes("");
    setReleasedTo("");
    onClose();
  };

  const updatePickedQuantity = (palletItemId: string, quantity: number) => {
    setPickedItems((prev) => ({
      ...prev,
      [palletItemId]: Math.max(0, quantity),
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const items: PickedItem[] = Object.entries(pickedItems)
      .filter(([_, quantity]) => quantity > 0)
      .map(([palletItemId, pickedQuantity]) => ({
        palletItemId,
        pickedQuantity,
      }));

    if (items.length === 0) {
      toast.error("Please select at least one item to pick");
      return;
    }

    pickMutation.mutate({
      items,
      notes: notes.trim() || undefined,
      releasedTo: releasedTo.trim() || undefined,
    });
  };

  const totalPickedItems = Object.values(pickedItems).reduce(
    (sum, qty) => sum + qty,
    0
  );

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <PackageOpen className="h-5 w-5" />
            Pick Items from Pallet
          </DialogTitle>
          <DialogDescription>
            Select quantities to pick from pallet{" "}
            {pallet.barcode || pallet.id.slice(-8)}.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Items Selection */}
          <div className="space-y-3">
            <Label>Items to Pick</Label>
            <div className="border rounded-lg divide-y">
              {pallet.palletItems
                ?.filter((item) => item.id)
                .map((palletItem) => {
                  const pickedQty = pickedItems[palletItem.id!] || 0;
                  return (
                    <div key={palletItem.id} className="p-4 space-y-3">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h4 className="font-medium">
                            {palletItem.item.name}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            SKU: {palletItem.item.sku}
                          </p>
                          <Badge variant="outline" className="mt-1">
                            Available: {palletItem.quantity}
                          </Badge>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Label
                          htmlFor={`qty-${palletItem.id}`}
                          className="text-sm"
                        >
                          Pick Quantity:
                        </Label>
                        <div className="flex items-center gap-1">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              updatePickedQuantity(
                                palletItem.id!,
                                pickedQty - 1
                              )
                            }
                            disabled={pickedQty <= 0}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <Input
                            id={`qty-${palletItem.id}`}
                            type="number"
                            min="0"
                            max={palletItem.quantity}
                            value={pickedQty}
                            onChange={(e) =>
                              updatePickedQuantity(
                                palletItem.id!,
                                Math.min(
                                  parseInt(e.target.value) || 0,
                                  palletItem.quantity
                                )
                              )
                            }
                            className="w-20 text-center"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              updatePickedQuantity(
                                palletItem.id!,
                                pickedQty + 1
                              )
                            }
                            disabled={pickedQty >= palletItem.quantity}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              updatePickedQuantity(
                                palletItem.id!,
                                palletItem.quantity
                              )
                            }
                          >
                            All
                          </Button>
                        </div>
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>

          {/* Summary */}
          {totalPickedItems > 0 ? (
            <Alert>
              <PackageOpen className="h-4 w-4" />
              <AlertDescription>
                Total items to pick: {totalPickedItems}
              </AlertDescription>
            </Alert>
          ) : null}

          {(() => (
            <>
              <div className="space-y-2">
                <Label htmlFor="releasedTo">Released To (Optional)</Label>
                <Input
                  id="releasedTo"
                  placeholder="Driver name, company, etc."
                  value={releasedTo}
                  onChange={(e) => setReleasedTo(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  placeholder="Any additional notes about this pick..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={3}
                />
              </div>

              {pickMutation.error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    {pickMutation.error instanceof Error
                      ? pickMutation.error.message
                      : "Failed to pick items"}
                  </AlertDescription>
                </Alert>
              )}
            </>
          ))()}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={pickMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={pickMutation.isPending || totalPickedItems === 0}
              className="flex items-center gap-2"
            >
              {pickMutation.isPending && (
                <Loader2 className="h-4 w-4 animate-spin" />
              )}
              Pick {totalPickedItems} Items
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
