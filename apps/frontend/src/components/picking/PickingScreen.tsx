"use client";

import { useState } from "react";
import { useSuspenseQuery } from "@tanstack/react-query";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import CreatePicklistModal from "./CreatePicklistModal";
import PicklistScreen from "./PicklistScreen";
import { usePickingOperations } from "@/hooks/usePickingOperations";
import { PickingOverview } from "./PickingOverview";
import { DestinationSelectionCard } from "./DestinationSelectionCard";
import { PalletDisplayCard } from "./PalletDisplayCard";

// API functions for data fetching
const fetchDestinations = async (
  warehouseId: string,
  token: string | null
): Promise<string[]> => {
  const searchParams = new URLSearchParams();
  searchParams.append("warehouseId", warehouseId);

  return fetchWithAuth(`/api/pallets/destinations?${searchParams.toString()}`, {
    token,
  });
};

const fetchPallets = async (
  warehouseId: string,
  token: string | null,
  filters?: { shipToDestination?: string }
): Promise<any[]> => {
  const searchParams = new URLSearchParams();
  searchParams.append("warehouseId", warehouseId);

  if (filters?.shipToDestination) {
    searchParams.append("shipToDestination", filters.shipToDestination);
  }

  const url = `/api/pallets${
    searchParams.toString() ? `?${searchParams.toString()}` : ""
  }`;
  return fetchWithAuth(url, { token });
};

export default function PickingScreen() {
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  // Use custom hook for picking operations
  const {
    selectedDestination,
    currentView,
    activePicklist,
    showCreatePicklist,
    setSelectedDestination,
    handleCreatePicklist,
    handleBackToDestinations,
    handleCompletePicklist,
    handleUpdateDestination,
    handleOpenCreatePicklist,
    handleCloseCreatePicklist,
  } = usePickingOperations();

  // Fetch destinations using useSuspenseQuery
  const { data: destinations = [] } = useSuspenseQuery<string[], Error>({
    queryKey: ["destinations", currentWarehouse?.id],
    queryFn: () => fetchDestinations(currentWarehouse?.id || "", appToken),
  });

  // Add processing state for destination selection
  const [isProcessingDestination, setIsProcessingDestination] = useState(false);

  // Enhanced destination selection handler with async processing
  const handleDestinationSelectWithProcessing = async (destination: string) => {
    setIsProcessingDestination(true);

    try {
      // Simulate processing time for heavy operations (filtering, calculations, etc.)
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Call the original handler
      setSelectedDestination(destination);
    } finally {
      // Reset processing state after a short delay to show completion
      setTimeout(() => {
        setIsProcessingDestination(false);
      }, 300);
    }
  };

  // Handler for when processing starts (optimistic feedback)
  const handleProcessingStart = () => {
    setIsProcessingDestination(true);
  };

  // Fetch pallets for selected destination using useSuspenseQuery
  const { data: pallets = [] } = useSuspenseQuery<any[], Error>({
    queryKey: ["pallets", currentWarehouse?.id, selectedDestination || "none"],
    queryFn: () => {
      if (!selectedDestination) {
        return Promise.resolve([]);
      }
      return fetchPallets(currentWarehouse?.id || "", appToken, {
        shipToDestination: selectedDestination,
      });
    },
  });

  // All handler functions moved to usePickingOperations hook

  // useSuspenseQuery handles loading and error states automatically

  // Show picklist view if active
  if (currentView === "picklist" && activePicklist) {
    return (
      <PicklistScreen
        picklist={activePicklist}
        onBack={handleBackToDestinations}
        onComplete={handleCompletePicklist}
        onUpdateDestination={handleUpdateDestination}
      />
    );
  }

  return (
    <div className="space-y-6">
      <PickingOverview onCreatePicklist={handleOpenCreatePicklist} />

      <DestinationSelectionCard
        destinations={destinations}
        selectedDestination={selectedDestination}
        onDestinationSelect={handleDestinationSelectWithProcessing}
        isProcessing={isProcessingDestination}
        onProcessingStart={handleProcessingStart}
      />

      <PalletDisplayCard
        selectedDestination={selectedDestination}
        pallets={pallets}
      />

      <CreatePicklistModal
        isOpen={showCreatePicklist}
        onClose={handleCloseCreatePicklist}
        onCreatePicklist={handleCreatePicklist}
      />
    </div>
  );
}
