"use client";

import React, { useState, useCallback, useMemo } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Package,
  MapPin,
  Calendar,
  Truck,
  PackageOpen,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { Pallet, PalletItem, Location } from "@quildora/types";
import ReleasePalletModal from "./ReleasePalletModal";
import PickItemsModal from "./PickItemsModal";

interface PalletCardProps {
  pallet: Pallet;
}

function PalletCard({ pallet }: PalletCardProps) {
  const [showItems, setShowItems] = useState(false);
  const [showReleaseModal, setShowReleaseModal] = useState(false);
  const [showPickModal, setShowPickModal] = useState(false);

  // Memoized calculations to prevent recalculation on every render
  const { totalItems, uniqueItems } = useMemo(
    () => ({
      totalItems:
        pallet.palletItems?.reduce(
          (sum: number, item: PalletItem) => sum + item.quantity,
          0
        ) || 0,
      uniqueItems: pallet.palletItems?.length || 0,
    }),
    [pallet.palletItems]
  );

  // Memoized status color function
  const getStatusColor = useCallback((status: string) => {
    switch (status) {
      case "Stored":
        return "bg-green-100 text-green-800";
      case "Picking":
        return "bg-yellow-100 text-yellow-800";
      case "Released":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-blue-100 text-blue-800";
    }
  }, []);

  // Memoized derived values
  const isReleased = useMemo(
    () => pallet.status === "Released",
    [pallet.status]
  );

  // Memoized event handlers
  const toggleItems = useCallback(() => {
    setShowItems((prev) => !prev);
  }, []);

  const handlePickItems = useCallback(() => {
    setShowPickModal(true);
  }, []);

  const handleReleasePallet = useCallback(() => {
    setShowReleaseModal(true);
  }, []);

  const closeReleaseModal = useCallback(() => {
    setShowReleaseModal(false);
  }, []);

  const closePickModal = useCallback(() => {
    setShowPickModal(false);
  }, []);

  return (
    <>
      <Card className="w-full">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <CardTitle className="text-lg">
                {pallet.barcode || `Pallet ${pallet.id.slice(-8)}`}
              </CardTitle>
              {pallet.description && (
                <p className="text-sm text-muted-foreground">
                  {pallet.description}
                </p>
              )}
            </div>
            <Badge className={getStatusColor(pallet.status)}>
              {pallet.status}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Location and Last Moved */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <span>
                {pallet.location?.warehouse?.name || "Unknown"} -{" "}
                {pallet.location?.name || "Unknown"}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>
                Moved{" "}
                {formatDistanceToNow(
                  new Date(pallet.lastMovedDate || new Date())
                )}{" "}
                ago
              </span>
            </div>
          </div>

          {/* Items Summary */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Package className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                {totalItems} items ({uniqueItems} unique)
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowItems(!showItems)}
              className="flex items-center gap-1"
            >
              {showItems ? "Hide" : "Show"} Items
              {showItems ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>

          {/* Items List */}
          {showItems && (
            <div className="border rounded-lg p-3 space-y-2 bg-muted/50">
              {pallet.palletItems?.map((palletItem) => (
                <div
                  key={palletItem.id}
                  className="flex justify-between items-center text-sm"
                >
                  <div>
                    <span className="font-medium">{palletItem.item.name}</span>
                    <span className="text-muted-foreground ml-2">
                      ({palletItem.item.sku})
                    </span>
                  </div>
                  <Badge variant="outline">Qty: {palletItem.quantity}</Badge>
                </div>
              ))}
            </div>
          )}

          {/* Action Buttons */}
          {!isReleased && (
            <div className="flex gap-2 pt-2">
              <Button
                onClick={handleReleasePallet}
                className="flex-1 flex items-center gap-2"
              >
                <Truck className="h-4 w-4" />
                Release Pallet
              </Button>
              <Button
                variant="outline"
                onClick={handlePickItems}
                className="flex-1 flex items-center gap-2"
              >
                <PackageOpen className="h-4 w-4" />
                Pick Items
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modals */}
      <ReleasePalletModal
        pallet={pallet}
        isOpen={showReleaseModal}
        onClose={closeReleaseModal}
      />
      <PickItemsModal
        pallet={pallet}
        isOpen={showPickModal}
        onClose={closePickModal}
      />
    </>
  );
}

// Memoize the component to prevent unnecessary re-renders
// Only re-render when pallet data actually changes
export default React.memo(PalletCard, (prevProps, nextProps) => {
  const prevPallet = prevProps.pallet;
  const nextPallet = nextProps.pallet;

  // Compare key properties that affect rendering
  return (
    prevPallet.id === nextPallet.id &&
    prevPallet.barcode === nextPallet.barcode &&
    prevPallet.status === nextPallet.status &&
    prevPallet.location?.id === nextPallet.location?.id &&
    prevPallet.shipToDestination === nextPallet.shipToDestination &&
    prevPallet.description === nextPallet.description &&
    JSON.stringify(prevPallet.palletItems) ===
      JSON.stringify(nextPallet.palletItems)
  );
});
