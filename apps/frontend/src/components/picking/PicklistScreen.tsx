"use client";

import { useState, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { usePallets } from "@/hooks/api";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ArrowLeft, CheckCircle2, Package, Truck } from "lucide-react";
import { Picklist, PicklistDestination } from "./types";
import { Pallet } from "@quildora/types";
import PicklistDestinationCard from "./PicklistDestinationCard";
import PicklistReleaseScreen from "./PicklistReleaseScreen";

interface PicklistScreenProps {
  picklist: Picklist;
  onBack: () => void;
  onComplete: () => void;
  onUpdateDestination: (
    destination: string,
    updates: Partial<PicklistDestination>
  ) => void;
}

export default function PicklistScreen({
  picklist,
  onBack,
  onComplete,
  onUpdateDestination,
}: PicklistScreenProps) {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const [expandedDestination, setExpandedDestination] = useState<string | null>(
    null
  );
  const [currentView, setCurrentView] = useState<"picklist" | "release">(
    "picklist"
  );

  // Calculate progress - treat both 'picked' and 'partial' as completed
  const progress = useMemo(() => {
    const completed = picklist.destinations.filter(
      (d) => d.status === "picked" || d.status === "partial"
    ).length;
    const total = picklist.destinations.length;
    return {
      completed,
      total,
      percentage: total > 0 ? Math.round((completed / total) * 100) : 0,
      isComplete: completed === total,
    };
  }, [picklist.destinations]);

  // Fetch all pallets for the picklist destinations using warehouse-aware hook
  const {
    data: allPallets = [],
    isLoading: palletsLoading,
    error: palletsError,
  } = usePallets();

  // Create pallets by destination data structure for bulk release
  const palletsByDestination = useMemo(() => {
    const result: Record<string, Pallet[]> = {};

    picklist.destinations.forEach((dest) => {
      const destinationPallets = allPallets.filter(
        (pallet) => pallet.shipToDestination === dest.destination
      );
      result[dest.destination] = destinationPallets;
    });

    return result;
  }, [allPallets, picklist.destinations]);

  const handleDestinationToggle = (destination: string) => {
    setExpandedDestination(
      expandedDestination === destination ? null : destination
    );
  };

  const handleMarkAsPicked = (destination: string) => {
    onUpdateDestination(destination, {
      status: "picked",
      pickedAt: new Date(),
    });
  };

  const handleMarkAsPartial = (destination: string, notes: string) => {
    onUpdateDestination(destination, {
      status: "partial",
      notes,
    });
  };

  const handleUpdateNotes = (destination: string, notes: string) => {
    onUpdateDestination(destination, { notes });
  };

  const handleShowReleaseScreen = () => {
    setCurrentView("release");
  };

  const handleBackToPicklist = () => {
    setCurrentView("picklist");
  };

  // Show release screen if in release view
  if (currentView === "release") {
    return (
      <PicklistReleaseScreen
        picklist={picklist}
        palletsByDestination={palletsByDestination}
        onBack={handleBackToPicklist}
        onComplete={onComplete}
      />
    );
  }

  return (
    <div className="min-h-screen bg-slate-50 p-4">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="h-10 w-10 p-0"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-slate-900">
                {picklist.name}
              </h1>
              <p className="text-sm text-slate-600">
                Created {picklist.createdAt.toLocaleDateString()}
              </p>
            </div>
          </div>

          {/* Header Release Button - shown when complete */}
          {progress.isComplete && (
            <Button
              onClick={handleShowReleaseScreen}
              className="bg-green-600 hover:bg-green-700 flex items-center gap-2"
            >
              <Truck className="h-4 w-4" />
              Release Pallets
            </Button>
          )}
        </div>

        {/* Progress Card */}
        <Card className="bg-white shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <Package className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-slate-900">
                  Progress: {progress.completed} of {progress.total}{" "}
                  destinations
                </span>
              </div>
              <Badge
                variant={progress.percentage === 100 ? "default" : "secondary"}
                className="text-sm"
              >
                {progress.percentage}% Complete
              </Badge>
            </div>
            <Progress value={progress.percentage} className="h-2" />
            <div className="flex items-center justify-between mt-2 text-sm text-slate-600">
              <span>{picklist.totalPallets} total pallets</span>
              <span>
                {progress.completed === progress.total
                  ? "All destinations picked!"
                  : `${progress.total - progress.completed} remaining`}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Center Release Button - shown when complete */}
      {progress.isComplete && (
        <div className="mb-6 flex justify-center">
          <Button
            onClick={handleShowReleaseScreen}
            size="lg"
            className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 text-lg font-medium flex items-center gap-3 shadow-lg"
          >
            <Truck className="h-5 w-5" />
            Release Pallets
            <CheckCircle2 className="h-5 w-5" />
          </Button>
        </div>
      )}

      {/* Destinations List */}
      <div className="space-y-4">
        {picklist.destinations.map((destination) => {
          const pallets = palletsByDestination[destination.destination] || [];

          return (
            <PicklistDestinationCard
              key={destination.destination}
              destination={destination}
              pallets={pallets}
              isLoading={palletsLoading}
              error={palletsError}
              isExpanded={expandedDestination === destination.destination}
              onToggle={() => handleDestinationToggle(destination.destination)}
              onMarkAsPicked={() => handleMarkAsPicked(destination.destination)}
              onMarkAsPartial={(notes: string) =>
                handleMarkAsPartial(destination.destination, notes)
              }
              onUpdateNotes={(notes: string) =>
                handleUpdateNotes(destination.destination, notes)
              }
            />
          );
        })}
      </div>

      {/* Summary Footer */}
      {progress.percentage === 100 && (
        <Card className="mt-6 bg-green-50 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <CheckCircle2 className="h-6 w-6 text-green-600" />
              <div>
                <h3 className="font-semibold text-green-900">
                  Picklist Complete!
                </h3>
                <p className="text-sm text-green-700">
                  All destinations have been picked. You can now proceed with
                  releasing pallets.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Center Release Button - shown when complete */}
      {progress.isComplete && (
        <div className="mb-6 flex justify-center mt-5">
          <Button
            onClick={handleShowReleaseScreen}
            size="lg"
            className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 text-lg font-medium flex items-center gap-3 shadow-lg"
          >
            <Truck className="h-5 w-5" />
            Release Pallets
            <CheckCircle2 className="h-5 w-5" />
          </Button>
        </div>
      )}
    </div>
  );
}
