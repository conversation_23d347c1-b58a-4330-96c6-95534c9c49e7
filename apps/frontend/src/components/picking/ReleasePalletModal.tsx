"use client";

import React, { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle, Truck } from "lucide-react";
import { toast } from "sonner";
import { Pallet, PalletItem, Location } from "@quildora/types";

interface ReleasePalletModalProps {
  pallet: Pallet;
  isOpen: boolean;
  onClose: () => void;
}

export default function ReleasePalletModal({
  pallet,
  isOpen,
  onClose,
}: ReleasePalletModalProps) {
  const [notes, setNotes] = useState("");
  const [releasedTo, setReleasedTo] = useState("");
  const queryClient = useQueryClient();
  const { appToken } = useAuth();

  const releaseMutation = useMutation({
    mutationFn: async (data: { notes?: string; releasedTo?: string }) => {
      if (!appToken) {
        throw new Error("Authentication token not available.");
      }
      return fetchWithAuth(`/api/pallets/${pallet.id}/release`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
        token: appToken,
      });
    },
    onSuccess: () => {
      toast.success("Pallet released successfully");
      queryClient.invalidateQueries({ queryKey: ["pallets"] });
      handleClose();
    },
    onError: (error: unknown) => {
      toast.error(
        error instanceof Error ? error.message : "Failed to release pallet"
      );
    },
  });

  const handleClose = () => {
    setNotes("");
    setReleasedTo("");
    onClose();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    releaseMutation.mutate({
      notes: notes.trim() || undefined,
      releasedTo: releasedTo.trim() || undefined,
    });
  };

  const totalItems =
    pallet.palletItems?.reduce((sum, item) => sum + item.quantity, 0) || 0;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Release Pallet
          </DialogTitle>
          <DialogDescription>
            Release pallet {pallet.barcode || pallet.id.slice(-8)} with{" "}
            {totalItems} items to {pallet.shipToDestination}.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="releasedTo">Released To (Optional)</Label>
            <Input
              id="releasedTo"
              placeholder="Driver name, company, etc."
              value={releasedTo}
              onChange={(e) => setReleasedTo(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              placeholder="Any additional notes about this release..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>

          {(() => (
            <>
              {releaseMutation.error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    {releaseMutation.error instanceof Error
                      ? releaseMutation.error.message
                      : "Failed to release pallet"}
                  </AlertDescription>
                </Alert>
              )}
            </>
          ))()}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={releaseMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={releaseMutation.isPending}
              className="flex items-center gap-2"
            >
              {releaseMutation.isPending && (
                <Loader2 className="h-4 w-4 animate-spin" />
              )}
              Release Pallet
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
