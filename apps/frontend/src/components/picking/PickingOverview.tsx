import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";

interface PickingOverviewProps {
  onCreatePicklist: () => void;
}

/**
 * Reusable component for displaying picking workflow overview
 * Shows header and main action button for creating picklists
 */
export const PickingOverview: React.FC<PickingOverviewProps> = ({
  onCreatePicklist,
}) => {
  return (
    <div className="flex items-center justify-between">
      <h1 className="text-2xl font-bold text-slate-900">Picking Workflow</h1>
      <Button
        onClick={onCreatePicklist}
        className="bg-blue-600 hover:bg-blue-700"
      >
        <Plus className="h-4 w-4 mr-2" />
        Create Picklist
      </Button>
    </div>
  );
};
