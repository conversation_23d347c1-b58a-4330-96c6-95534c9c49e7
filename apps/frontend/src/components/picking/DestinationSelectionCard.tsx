import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { List } from "lucide-react";
import DestinationFilter from "./DestinationFilter";

interface DestinationSelectionCardProps {
  destinations: string[];
  selectedDestination: string;
  onDestinationSelect: (destination: string) => void;
  isProcessing?: boolean;
  onProcessingStart?: () => void;
}

/**
 * Reusable component for destination selection in picking workflow
 * Encapsulates destination filtering functionality in a card layout
 */
export const DestinationSelectionCard: React.FC<
  DestinationSelectionCardProps
> = ({
  destinations,
  selectedDestination,
  onDestinationSelect,
  isProcessing = false,
  onProcessingStart,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <List className="h-5 w-5" />
          Select Destination
        </CardTitle>
      </CardHeader>
      <CardContent>
        <DestinationFilter
          destinations={destinations}
          selectedDestination={selectedDestination}
          onDestinationSelect={onDestinationSelect}
          isProcessing={isProcessing}
          onProcessingStart={onProcessingStart}
        />
      </CardContent>
    </Card>
  );
};
