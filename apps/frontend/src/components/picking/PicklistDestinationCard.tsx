"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  ChevronDown,
  ChevronRight,
  CheckCircle2,
  Clock,
  Package,
  MapPin,
  AlertCircle,
  Loader2,
  Edit3,
  Save,
  X,
} from "lucide-react";
import { PicklistDestination } from "./types";
import { Pallet } from "@quildora/types";
import { useDestinationsWithCodes } from "@/hooks/api/useWarehouseData";

interface PicklistDestinationCardProps {
  destination: PicklistDestination;
  pallets: Pallet[];
  isLoading: boolean;
  error: Error | null;
  isExpanded: boolean;
  onToggle: () => void;
  onMarkAsPicked: () => void;
  onMarkAsPartial: (notes: string) => void;
  onUpdateNotes: (notes: string) => void;
}

export default function PicklistDestinationCard({
  destination,
  pallets,
  isLoading,
  error,
  isExpanded,
  onToggle,
  onMarkAsPicked,
  onMarkAsPartial,
  onUpdateNotes,
}: PicklistDestinationCardProps) {
  const [isEditingNotes, setIsEditingNotes] = useState(false);
  const [tempNotes, setTempNotes] = useState(destination.notes);
  const [showPartialModal, setShowPartialModal] = useState(false);
  const [partialNotes, setPartialNotes] = useState("");

  // Fetch destinations with codes for display
  const { data: destinationsWithCodes = [] } = useDestinationsWithCodes();

  // Helper function to format destination display with code
  const formatDestinationDisplay = (destinationName: string) => {
    const destinationWithCode = destinationsWithCodes.find(
      (dest) => dest.name === destinationName
    );
    if (destinationWithCode?.code) {
      return `${destinationName} (${destinationWithCode.code})`;
    }
    return destinationName;
  };

  const getStatusIcon = () => {
    switch (destination.status) {
      case "picked":
        return <CheckCircle2 className="h-5 w-5 text-green-600" />;
      case "partial":
        return <Clock className="h-5 w-5 text-yellow-600" />;
      default:
        return <Package className="h-5 w-5 text-slate-400" />;
    }
  };

  const getStatusBadge = () => {
    switch (destination.status) {
      case "picked":
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200">
            Picked
          </Badge>
        );
      case "partial":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
            Partial
          </Badge>
        );
      default:
        return <Badge variant="secondary">Pending</Badge>;
    }
  };

  const handleSaveNotes = () => {
    onUpdateNotes(tempNotes);
    setIsEditingNotes(false);
  };

  const handleCancelNotes = () => {
    setTempNotes(destination.notes);
    setIsEditingNotes(false);
  };

  const handlePartialPick = () => {
    onMarkAsPartial(partialNotes);
    setShowPartialModal(false);
    setPartialNotes("");
  };

  // Get unique locations from pallets
  const locations = Array.from(
    new Set(pallets.map((p) => p.location?.name).filter(Boolean))
  ).slice(0, 3); // Show max 3 locations

  // Get unique item types from pallets
  const itemTypes = Array.from(
    new Set(
      pallets.flatMap((p) => p.palletItems?.map((item) => item.item.name) || [])
    )
  ).slice(0, 3); // Show max 3 item types

  return (
    <Card
      className={`transition-all duration-200 ${
        destination.status === "picked"
          ? "bg-green-50 border-green-200"
          : destination.status === "partial"
          ? "bg-yellow-50 border-yellow-200"
          : "bg-white border-slate-200"
      }`}
    >
      <CardContent className="p-0">
        {/* Main Header - Always Visible */}
        <div
          className="p-4 cursor-pointer hover:bg-slate-50 transition-colors"
          onClick={onToggle}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 flex-1">
              {getStatusIcon()}
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-lg text-slate-900 truncate">
                  {formatDestinationDisplay(destination.destination)}
                </h3>
                <div className="flex items-center gap-4 mt-1 text-sm text-slate-600">
                  <span className="flex items-center gap-1">
                    <Package className="h-4 w-4" />
                    {destination.palletCount} pallets
                  </span>
                  {locations.length > 0 && (
                    <span className="flex items-center gap-1">
                      <MapPin className="h-4 w-4" />
                      {locations.join(", ")}
                      {pallets.length > 3 && " +more"}
                    </span>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {getStatusBadge()}
              {isExpanded ? (
                <ChevronDown className="h-5 w-5 text-slate-400" />
              ) : (
                <ChevronRight className="h-5 w-5 text-slate-400" />
              )}
            </div>
          </div>
        </div>

        {/* Expanded Content */}
        {isExpanded && (
          <div className="border-t border-slate-200 p-4 space-y-4">
            {/* Loading State */}
            {isLoading && (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading pallets...</span>
              </div>
            )}

            {/* Error State */}
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Failed to load pallets for this destination.
                </AlertDescription>
              </Alert>
            )}

            {/* Pallet Details */}
            {!isLoading && !error && pallets.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium text-slate-900">Pallets to Pick:</h4>
                <div className="grid gap-2">
                  {pallets.map((pallet) => (
                    <div
                      key={pallet.id}
                      className="flex items-center justify-between p-3 bg-slate-50 rounded-lg"
                    >
                      <div>
                        <span className="font-medium text-slate-900">
                          {pallet.barcode || pallet.label}
                        </span>
                        <div className="text-sm text-slate-600">
                          {pallet.location?.name} •{" "}
                          {pallet.palletItems?.length || 0} items
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {pallet.status}
                      </Badge>
                    </div>
                  ))}
                </div>

                {/* Item Types Summary */}
                {itemTypes.length > 0 && (
                  <div>
                    <h5 className="text-sm font-medium text-slate-700 mb-2">
                      Item Types:
                    </h5>
                    <div className="flex flex-wrap gap-1">
                      {itemTypes.map((itemType) => (
                        <Badge
                          key={itemType}
                          variant="secondary"
                          className="text-xs"
                        >
                          {itemType}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Notes Section */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h5 className="text-sm font-medium text-slate-700">Notes:</h5>
                {!isEditingNotes && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsEditingNotes(true)}
                    className="h-8 px-2"
                  >
                    <Edit3 className="h-3 w-3" />
                  </Button>
                )}
              </div>

              {isEditingNotes ? (
                <div className="space-y-2">
                  <Textarea
                    value={tempNotes}
                    onChange={(e) => setTempNotes(e.target.value)}
                    placeholder="Add picking notes..."
                    className="min-h-[80px] text-base"
                  />
                  <div className="flex gap-2">
                    <Button size="sm" onClick={handleSaveNotes}>
                      <Save className="h-3 w-3 mr-1" />
                      Save
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleCancelNotes}
                    >
                      <X className="h-3 w-3 mr-1" />
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="p-3 bg-slate-50 rounded-lg min-h-[60px]">
                  {destination.notes ? (
                    <p className="text-sm text-slate-700">
                      {destination.notes}
                    </p>
                  ) : (
                    <p className="text-sm text-slate-500 italic">
                      No notes added
                    </p>
                  )}
                </div>
              )}
            </div>

            {/* Action Buttons */}
            {destination.status === "pending" && (
              <div className="flex gap-3 pt-2">
                <Button
                  onClick={onMarkAsPicked}
                  className="flex-1 h-12 text-base bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle2 className="h-5 w-5 mr-2" />
                  Mark as Picked
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowPartialModal(true)}
                  className="flex-1 h-12 text-base"
                >
                  <Clock className="h-5 w-5 mr-2" />
                  Partial Pick
                </Button>
              </div>
            )}

            {destination.status === "picked" && destination.pickedAt && (
              <div className="text-sm text-green-700 bg-green-50 p-3 rounded-lg">
                ✓ Picked on {destination.pickedAt.toLocaleDateString()} at{" "}
                {destination.pickedAt.toLocaleTimeString()}
              </div>
            )}
          </div>
        )}
      </CardContent>

      {/* Partial Pick Modal */}
      {showPartialModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-md">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">
                Mark as Partial Pick
              </h3>
              <p className="text-sm text-slate-600 mb-4">
                Add notes about what was picked or what's remaining:
              </p>
              <Textarea
                value={partialNotes}
                onChange={(e) => setPartialNotes(e.target.value)}
                placeholder="e.g., 'Picked 3/5 pallets - missing converters, check tomorrow'"
                className="min-h-[100px] text-base mb-4"
              />
              <div className="flex gap-3">
                <Button onClick={handlePartialPick} className="flex-1">
                  Save Partial
                </Button>
                <Button
                  variant="ghost"
                  onClick={() => setShowPartialModal(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </Card>
  );
}
