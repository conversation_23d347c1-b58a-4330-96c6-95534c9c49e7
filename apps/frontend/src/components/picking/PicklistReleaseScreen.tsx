"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  MapPin,
  Package,
  ChevronDown,
  ChevronUp,
  Trophy,
  CheckCircle2,
} from "lucide-react";
import confetti from "canvas-confetti";
import { Picklist } from "./types";
import { Pallet } from "@quildora/types";
import PalletCard from "./PalletCard";

interface PicklistReleaseScreenProps {
  picklist: Picklist;
  palletsByDestination: Record<string, Pallet[]>;
  onBack: () => void;
  onComplete: () => void;
}

export default function PicklistReleaseScreen({
  picklist,
  palletsByDestination,
  onBack,
  onComplete,
}: PicklistReleaseScreenProps) {
  const [expandedDestinations, setExpandedDestinations] = useState<Set<string>>(
    new Set(Object.keys(palletsByDestination)) // Start with all destinations expanded
  );
  const [isCompleting, setIsCompleting] = useState(false);

  const handleDestinationToggle = (destination: string) => {
    const newExpanded = new Set(expandedDestinations);
    if (newExpanded.has(destination)) {
      newExpanded.delete(destination);
    } else {
      newExpanded.add(destination);
    }
    setExpandedDestinations(newExpanded);
  };

  const handleCompleteRelease = async () => {
    setIsCompleting(true);

    // Trigger confetti animation
    const duration = 3000; // 3 seconds
    const animationEnd = Date.now() + duration;

    const randomInRange = (min: number, max: number) => {
      return Math.random() * (max - min) + min;
    };

    const confettiInterval = setInterval(() => {
      const timeLeft = animationEnd - Date.now();

      if (timeLeft <= 0) {
        clearInterval(confettiInterval);
        // Navigate back to main picking screen after confetti
        setTimeout(() => {
          setIsCompleting(false);
          onComplete();
        }, 500);
        return;
      }

      const particleCount = 50 * (timeLeft / duration);

      // Left side confetti
      confetti({
        particleCount,
        startVelocity: 30,
        spread: 360,
        origin: {
          x: randomInRange(0.1, 0.3),
          y: Math.random() - 0.2,
        },
      });

      // Right side confetti
      confetti({
        particleCount,
        startVelocity: 30,
        spread: 360,
        origin: {
          x: randomInRange(0.7, 0.9),
          y: Math.random() - 0.2,
        },
      });
    }, 250);
  };

  // Calculate totals
  const totalPallets = Object.values(palletsByDestination).flat().length;
  const totalDestinations = Object.keys(palletsByDestination).length;

  return (
    <div className="min-h-screen bg-slate-50 p-4">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="h-10 w-10 p-0"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-slate-900">
                Release Pallets - {picklist.name}
              </h1>
              <p className="text-sm text-slate-600">
                Select individual pallets or items to release
              </p>
            </div>
          </div>
        </div>

        {/* Summary Card */}
        <Card className="bg-white shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Package className="h-5 w-5 text-blue-600" />
                  <span className="font-medium text-slate-900">
                    {totalPallets} pallets across {totalDestinations}{" "}
                    destinations
                  </span>
                </div>
              </div>
              <Badge variant="outline" className="text-sm">
                Picklist Complete
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Destinations with Pallets */}
      <div className="space-y-6">
        {Object.entries(palletsByDestination).map(([destination, pallets]) => {
          const isExpanded = expandedDestinations.has(destination);
          const destinationItems = pallets.reduce(
            (sum, pallet) =>
              sum +
              (pallet.palletItems?.reduce(
                (itemSum, item) => itemSum + item.quantity,
                0
              ) || 0),
            0
          );

          return (
            <Card key={destination} className="bg-white shadow-sm">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <MapPin className="h-5 w-5 text-slate-600" />
                    <div>
                      <CardTitle className="text-lg">{destination}</CardTitle>
                      <p className="text-sm text-slate-600 mt-1">
                        {pallets.length} pallets • {destinationItems} items
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDestinationToggle(destination)}
                    className="h-8 w-8 p-0"
                  >
                    {isExpanded ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </CardHeader>

              {isExpanded && (
                <CardContent className="pt-0">
                  <div className="space-y-4">
                    {pallets.map((pallet) => (
                      <PalletCard key={pallet.id} pallet={pallet} />
                    ))}
                  </div>
                </CardContent>
              )}
            </Card>
          );
        })}
      </div>

      {/* Footer with Action Buttons */}
      <div className="mt-8 flex justify-center gap-4">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex items-center gap-2"
          disabled={isCompleting}
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Picklist
        </Button>

        <Button
          onClick={handleCompleteRelease}
          disabled={isCompleting}
          size="lg"
          className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 text-lg font-medium flex items-center gap-3 shadow-lg"
        >
          {isCompleting ? (
            <>
              <CheckCircle2 className="h-5 w-5 animate-pulse" />
              Completing...
            </>
          ) : (
            <>
              <Trophy className="h-5 w-5" />
              Complete Release
              <CheckCircle2 className="h-5 w-5" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
