"use client";

import React from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Package } from "lucide-react";
import { Pallet, PalletItem, Location } from "@quildora/types";
import PalletCard from "./PalletCard";

interface PalletListViewProps {
  pallets: Pallet[];
}

function PalletListView({ pallets }: PalletListViewProps) {
  if (pallets.length === 0) {
    return (
      <Alert>
        <Package className="h-4 w-4" />
        <AlertDescription>
          No pallets found for this destination.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4">
      {pallets.map((pallet) => (
        <PalletCard key={pallet.id} pallet={pallet} />
      ))}
    </div>
  );
}

// Memoize the component to prevent unnecessary re-renders
// Only re-render when pallets array actually changes
export default React.memo(PalletListView, (prevProps, nextProps) => {
  // Custom comparison: check if pallets array has the same items
  if (prevProps.pallets.length !== nextProps.pallets.length) {
    return false;
  }

  // Compare each pallet by ID and key properties that affect rendering
  return prevProps.pallets.every((prevPallet, index) => {
    const nextPallet = nextProps.pallets[index];
    return (
      prevPallet.id === nextPallet.id &&
      prevPallet.barcode === nextPallet.barcode &&
      prevPallet.status === nextPallet.status &&
      prevPallet.location?.id === nextPallet.location?.id
    );
  });
});
