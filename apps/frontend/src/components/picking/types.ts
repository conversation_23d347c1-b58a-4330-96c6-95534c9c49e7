// Picklist-specific types and interfaces

export interface PicklistDestination {
  destination: string;
  palletCount: number;
  primaryLocations: string[];
  itemTypes: string[];
  status: "pending" | "picked" | "partial";
  notes: string;
  pickedAt?: Date;
}

export interface Picklist {
  id: string;
  name: string;
  destinations: PicklistDestination[];
  createdAt: Date;
  totalPallets: number;
  completedDestinations: number;
}

export interface PicklistProgress {
  total: number;
  completed: number;
  percentage: number;
}

// Extended interface for the API response structure
export interface PalletWithPalletItems {
  id: string;
  label: string;
  status: string;
  locationId: string | null;
  location: {
    id: string;
    name: string;
    locationType: string;
    warehouseId: string;
    status?: string;
    createdAt: string;
    updatedAt: string;
    warehouse: {
      id: string;
      name: string;
    };
  };
  description?: string | null;
  barcode?: string | null;
  dateCreated?: string;
  lastMovedDate?: string;
  shipToDestination?: string | null;
  palletItems: Array<{
    id?: string;
    palletId: string;
    itemId: string;
    quantity: number;
    item: {
      id: string;
      name: string;
      sku: string | null;
      description: string | null;
      unitOfMeasure?: string | null;
      defaultCost?: number | null;
      lowStockThreshold?: number | null;
      status?: string;
      createdAt: string;
      updatedAt: string;
      quantityOnHand?: number;
    };
  }>;
}
