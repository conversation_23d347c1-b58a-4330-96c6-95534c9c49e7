"use client";

import { useState, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { useDestinations, usePallets } from "@/hooks/api";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  X,
  Plus,
  Package,
  MapPin,
  Loader2,
  AlertCircle,
  Search,
} from "lucide-react";
import { Picklist, PicklistDestination, PalletWithPalletItems } from "./types";

interface CreatePicklistModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreatePicklist: (picklist: Picklist) => void;
}

export default function CreatePicklistModal({
  isOpen,
  onClose,
  onCreatePicklist,
}: CreatePicklistModalProps) {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const [picklistName, setPicklistName] = useState("");
  const [selectedDestinations, setSelectedDestinations] = useState<Set<string>>(
    new Set()
  );
  const [searchTerm, setSearchTerm] = useState("");

  // Fetch destinations and pallets using warehouse-aware hooks
  const {
    data: destinations = [],
    isLoading: destinationsLoading,
    error: destinationsError,
  } = useDestinations();

  const { data: allPallets = [], isLoading: palletsLoading } = usePallets();

  // Filter destinations based on search
  const filteredDestinations = useMemo(() => {
    if (!searchTerm.trim()) return destinations;
    return destinations.filter((dest) =>
      dest.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [destinations, searchTerm]);

  // Get destination data with pallet counts
  const destinationData = useMemo(() => {
    return filteredDestinations.map((destination) => {
      // Filter pallets for this destination
      const pallets = allPallets.filter(
        (pallet) => pallet.shipToDestination === destination
      );

      // Get unique locations
      const locations = Array.from(
        new Set(
          pallets
            .map((p) => p.location?.name)
            .filter((name): name is string => Boolean(name))
        )
      ).slice(0, 3);

      // Get unique item types
      const itemTypes = Array.from(
        new Set(
          pallets.flatMap(
            (p) => p.palletItems?.map((item) => item.item.name) || []
          )
        )
      ).slice(0, 3);

      return {
        destination,
        palletCount: pallets.length,
        locations,
        itemTypes,
        isLoading: palletsLoading,
        error: null,
      };
    });
  }, [filteredDestinations, allPallets, palletsLoading]);

  const handleDestinationToggle = (destination: string) => {
    const newSelected = new Set(selectedDestinations);
    if (newSelected.has(destination)) {
      newSelected.delete(destination);
    } else {
      newSelected.add(destination);
    }
    setSelectedDestinations(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedDestinations.size === filteredDestinations.length) {
      setSelectedDestinations(new Set());
    } else {
      setSelectedDestinations(new Set(filteredDestinations));
    }
  };

  const handleCreatePicklist = () => {
    if (!picklistName.trim() || selectedDestinations.size === 0) return;

    const picklistDestinations: PicklistDestination[] = Array.from(
      selectedDestinations
    ).map((dest) => {
      const destData = destinationData.find((d) => d.destination === dest);
      return {
        destination: dest,
        palletCount: destData?.palletCount || 0,
        primaryLocations: destData?.locations || [],
        itemTypes: destData?.itemTypes || [],
        status: "pending" as const,
        notes: "",
      };
    });

    const totalPallets = picklistDestinations.reduce(
      (sum, dest) => sum + dest.palletCount,
      0
    );

    const newPicklist: Picklist = {
      id: `picklist-${Date.now()}`,
      name: picklistName.trim(),
      destinations: picklistDestinations,
      createdAt: new Date(),
      totalPallets,
      completedDestinations: 0,
    };

    onCreatePicklist(newPicklist);

    // Reset form
    setPicklistName("");
    setSelectedDestinations(new Set());
    setSearchTerm("");
    onClose();
  };

  const totalSelectedPallets = Array.from(selectedDestinations).reduce(
    (sum, dest) => {
      const destData = destinationData.find((d) => d.destination === dest);
      return sum + (destData?.palletCount || 0);
    },
    0
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-xl">Create New Picklist</CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>

        <CardContent className="space-y-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Picklist Name */}
          <div className="space-y-2">
            <Label htmlFor="picklistName" className="text-sm font-medium">
              Picklist Name
            </Label>
            <Input
              id="picklistName"
              value={picklistName}
              onChange={(e) => setPicklistName(e.target.value)}
              placeholder="e.g., Morning Pick Run - Zone A"
              className="text-base h-11"
            />
          </div>

          {/* Search and Select All */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Select Destinations</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSelectAll}
                disabled={
                  destinationsLoading || filteredDestinations.length === 0
                }
              >
                {selectedDestinations.size === filteredDestinations.length
                  ? "Deselect All"
                  : "Select All"}
              </Button>
            </div>

            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search destinations..."
                className="pl-10 text-base h-11"
              />
            </div>
          </div>

          {/* Loading State */}
          {destinationsLoading && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading destinations...</span>
            </div>
          )}

          {/* Error State */}
          {destinationsError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Failed to load destinations. Please try again.
              </AlertDescription>
            </Alert>
          )}

          {/* Destinations List */}
          {!destinationsLoading && !destinationsError && (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {destinationData.length === 0 ? (
                <div className="text-center py-8 text-slate-500">
                  {searchTerm
                    ? "No destinations match your search"
                    : "No destinations available"}
                </div>
              ) : (
                destinationData.map(
                  ({
                    destination,
                    palletCount,
                    locations,
                    itemTypes,
                    isLoading,
                  }) => (
                    <div
                      key={destination}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedDestinations.has(destination)
                          ? "bg-blue-50 border-blue-200"
                          : "bg-white border-slate-200 hover:bg-slate-50"
                      }`}
                      onClick={() => handleDestinationToggle(destination)}
                    >
                      <div className="flex items-start gap-3">
                        <Checkbox
                          checked={selectedDestinations.has(destination)}
                          onChange={() => handleDestinationToggle(destination)}
                          className="mt-1"
                        />
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-slate-900 truncate">
                            {destination}
                          </h4>
                          <div className="flex items-center gap-4 mt-1 text-sm text-slate-600">
                            <span className="flex items-center gap-1">
                              <Package className="h-4 w-4" />
                              {isLoading ? "..." : `${palletCount} pallets`}
                            </span>
                            {locations.length > 0 && (
                              <span className="flex items-center gap-1">
                                <MapPin className="h-4 w-4" />
                                {locations.join(", ")}
                              </span>
                            )}
                          </div>
                          {itemTypes.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-2">
                              {itemTypes.map((itemType) => (
                                <Badge
                                  key={itemType}
                                  variant="secondary"
                                  className="text-xs"
                                >
                                  {itemType}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                )
              )}
            </div>
          )}

          {/* Summary */}
          {selectedDestinations.size > 0 && (
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center justify-between">
                <span className="font-medium text-blue-900">
                  Selected: {selectedDestinations.size} destinations
                </span>
                <span className="text-blue-700">
                  {totalSelectedPallets} total pallets
                </span>
              </div>
            </div>
          )}
        </CardContent>

        {/* Footer */}
        <div className="p-6 border-t bg-slate-50 flex gap-3">
          <Button variant="ghost" onClick={onClose} className="flex-1">
            Cancel
          </Button>
          <Button
            onClick={handleCreatePicklist}
            disabled={!picklistName.trim() || selectedDestinations.size === 0}
            className="flex-1"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Picklist
          </Button>
        </div>
      </Card>
    </div>
  );
}
