import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import PalletListView from "./PalletListView";

interface PalletDisplayCardProps {
  selectedDestination: string;
  pallets: any[];
}

/**
 * Reusable component for displaying pallets for a selected destination
 * Encapsulates pallet list display functionality in a card layout
 */
export const PalletDisplayCard: React.FC<PalletDisplayCardProps> = ({
  selectedDestination,
  pallets,
}) => {
  if (!selectedDestination) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Pallets for {selectedDestination}</CardTitle>
      </CardHeader>
      <CardContent>
        <PalletListView pallets={pallets} />
      </CardContent>
    </Card>
  );
};
