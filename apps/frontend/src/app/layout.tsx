import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import ReactQueryProvider from "@/components/providers/ReactQueryProvider";
import { Toaster } from "@/components/ui/sonner";
import { AuthProvider } from "@/components/providers/auth-provider";
import { OnboardingProvider } from "@/components/providers/onboarding-provider";
import { WarehouseProvider } from "@/components/providers/warehouse-provider";
import { WarehouseSyncWrapper } from "@/components/providers/warehouse-sync-wrapper";
import { PageTitleProvider } from "@/components/providers/PageTitleContext";
import { RouteGuardProvider } from "@/components/guards/RouteGuardProvider";

import ConditionalLayout from "@/components/layout/ConditionalLayout";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    template: "%s | Quildora",
    default: "Quildora - Inventory Management",
  },
  description:
    "Streamlined inventory management for small to mid-sized businesses.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ReactQueryProvider>
          <AuthProvider>
            <OnboardingProvider>
              <WarehouseProvider>
                <WarehouseSyncWrapper>
                  <PageTitleProvider>
                    <RouteGuardProvider>
                      <ConditionalLayout>{children}</ConditionalLayout>
                    </RouteGuardProvider>
                    <Toaster />
                  </PageTitleProvider>
                </WarehouseSyncWrapper>
              </WarehouseProvider>
            </OnboardingProvider>
          </AuthProvider>
        </ReactQueryProvider>
      </body>
    </html>
  );
}
