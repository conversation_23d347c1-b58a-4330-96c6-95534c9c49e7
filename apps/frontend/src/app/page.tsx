"use client"; // Make it a client component

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import AuthStatus from "@/components/auth-status";
import { useAuth } from "@/components/providers/auth-provider"; // Import useAuth
import { usePageTitle } from "@/components/providers/PageTitleContext"; // Added
import { useEffect } from "react"; // Added
import { AppLoadingScreen } from "@/components/loading/AppLoadingScreen";
import { useAppLoading } from "@/hooks/useAppLoading";

export default function DashboardPage() {
  const { appUser } = useAuth(); // Get appUser
  const { setTitle } = usePageTitle(); // Added
  const { isLoading, isReady } = useAppLoading(); // Use unified loading state

  useEffect(() => {
    setTitle("Dashboard");
  }, [setTitle]); // Added

  // Show unified loading screen for both auth and warehouse loading
  if (isLoading || !isReady) {
    return <AppLoadingScreen showProgress={true} showDetails={true} />;
  }

  // The protected route logic in AuthProvider should handle redirection
  // if appUser is null. If we reach here and appUser is still null
  // after loading, it means the user is not authenticated and shouldn't see the dashboard.
  if (!appUser) {
    // This case should ideally not be reached if AuthProvider redirect works correctly.
    // You might want to redirect from here too, or show a message.
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        Please log in to view the dashboard.
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* <h1 className="text-3xl font-bold mb-6">Warehouse Dashboard</h1> */}
      {/* Removed h1 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Items</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4">Manage inventory items.</p>
            <Button asChild>
              <Link href="/items">Go to Items</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Pallets</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4">Manage storage pallets.</p>
            <Button asChild>
              <Link href="/pallets">Go to Pallets</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Receiving</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4">Process incoming inventory.</p>
            <Button asChild variant="secondary">
              <Link href="/receiving">Start Receiving</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
      <div className="fixed pt-16 bottom-0 left-0 flex items-end justify-center w-full h-48 bg-gradient-to-t from-white via-white dark:from-black dark:via-black lg:static lg:h-auto lg:w-auto lg:bg-none">
        <AuthStatus />
      </div>
    </div>
  );
}
