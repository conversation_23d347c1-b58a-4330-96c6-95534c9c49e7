"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useAuth } from "@/components/providers/auth-provider";

export default function AuthPage() {
  const router = useRouter();
  const { supabaseSession, isLoading } = useAuth();

  useEffect(() => {
    // If user is already authenticated, redirect to home
    if (!isLoading && supabaseSession) {
      router.push("/");
      return;
    }

    // If not loading and not authenticated, redirect to welcome page
    if (!isLoading && !supabaseSession) {
      router.push("/auth/welcome");
      return;
    }
  }, [isLoading, supabaseSession, router]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // This should not be reached due to the redirects above, but just in case
  return null;
}
