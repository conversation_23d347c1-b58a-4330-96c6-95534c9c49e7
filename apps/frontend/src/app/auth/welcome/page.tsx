"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Package,
  Warehouse,
  Users,
  BarChart3,
  CheckCircle,
  ArrowRight,
  Building2,
  UserPlus,
  LogIn,
} from "lucide-react";

export default function WelcomePage() {
  const router = useRouter();

  const handleStartTrial = () => {
    router.push("/auth/signup/business");
  };

  const handleJoinTeam = () => {
    router.push("/auth/signup/join");
  };

  const handleSignIn = () => {
    router.push("/auth/signin");
  };

  const features = [
    {
      icon: Package,
      title: "Pallet-Level Inventory Control",
      description:
        "Track materials and supplies at the pallet level with industrial-grade barcode scanning and real-time updates",
    },
    {
      icon: Warehouse,
      title: "Multi-Site Operations",
      description:
        "Manage distribution centers, job sites, and storage facilities with role-based access control",
    },
    {
      icon: Users,
      title: "Workforce Management",
      description:
        "Coordinate warehouse teams, site supervisors, and field personnel with customizable roles",
    },
    {
      icon: BarChart3,
      title: "Operational Intelligence",
      description:
        "Monitor material flow, job site deliveries, and distribution performance with real-time dashboards",
    },
  ];

  const benefits = [
    "Eliminate material shortages and overstock situations",
    "Streamline job site kitting and distribution workflows",
    "Improve picking accuracy for critical industrial supplies",
    "Real-time visibility across distribution centers and job sites",
    "Rugged mobile interface designed for industrial environments",
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <div className="bg-primary rounded-lg p-3 mr-4">
              <Package className="h-8 w-8 text-primary-foreground" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900">Quildora</h1>
          </div>

          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Industrial Inventory Management for B2B Operations
          </h2>

          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Optimize your industrial supply chain with pallet-centric inventory
            tracking. Built for construction companies, manufacturing suppliers,
            and distribution operations that need reliable material management
            and job site coordination.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button
              size="lg"
              onClick={handleStartTrial}
              className="text-lg px-8 py-6 h-auto"
            >
              <Building2 className="mr-2 h-5 w-5" />
              Start Free Trial
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>

            <Button
              variant="outline"
              size="lg"
              onClick={handleJoinTeam}
              className="text-lg px-8 py-6 h-auto"
            >
              <UserPlus className="mr-2 h-5 w-5" />
              Join Existing Team
            </Button>

            <Button
              variant="ghost"
              size="lg"
              onClick={handleSignIn}
              className="text-lg px-8 py-6 h-auto"
            >
              <LogIn className="mr-2 h-5 w-5" />
              Sign In
            </Button>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {/* Features Section */}
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-6">
              Key Features
            </h3>
            <div className="space-y-4">
              {features.map((feature, index) => (
                <Card key={index} className="border-l-4 border-l-primary">
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-3">
                      <div className="bg-primary/10 rounded-lg p-2 mt-1">
                        <feature.icon className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-1">
                          {feature.title}
                        </h4>
                        <p className="text-gray-600 text-sm">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Benefits Section */}
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-6">
              Why Choose Quildora?
            </h3>
            <Card>
              <CardContent className="p-6">
                <ul className="space-y-3">
                  {benefits.map((benefit, index) => (
                    <li key={index} className="flex items-start space-x-3">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{benefit}</span>
                    </li>
                  ))}
                </ul>

                <div className="mt-6 pt-6 border-t">
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="secondary">Mobile Optimized</Badge>
                    <Badge variant="secondary">Barcode Scanning</Badge>
                    <Badge variant="secondary">Real-time Updates</Badge>
                    <Badge variant="secondary">Multi-tenant</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Customer Success Stories */}
        <Card className="mb-8">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">
              Trusted by Growing Businesses
            </CardTitle>
            <CardDescription>
              Join hundreds of companies that have streamlined their warehouse
              operations with Quildora
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6 text-center">
              <div>
                <div className="text-3xl font-bold text-primary mb-2">95%</div>
                <div className="text-gray-600">
                  Reduction in inventory discrepancies
                </div>
              </div>
              <div>
                <div className="text-3xl font-bold text-primary mb-2">50%</div>
                <div className="text-gray-600">Faster receiving processes</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-primary mb-2">24/7</div>
                <div className="text-gray-600">
                  Real-time inventory visibility
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Call to Action */}
        <div className="text-center">
          <p className="text-gray-600 mb-4">
            Ready to transform your warehouse operations?
          </p>
          <Button
            size="lg"
            onClick={handleStartTrial}
            className="text-lg px-12 py-6 h-auto"
          >
            Get Started Today - It's Free
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
          <p className="text-sm text-gray-500 mt-2">
            No credit card required • 30-day free trial • Setup in minutes
          </p>
        </div>
      </div>
    </div>
  );
}
