"use client";

import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Building2,
  Users,
  Mail,
  ArrowLeft,
  Loader2,
  CheckCircle2,
} from "lucide-react";
import { OnboardingField } from "@/components/onboarding/OnboardingFormWrapper";

interface InvitationData {
  id: string;
  email: string;
  role: string;
  status: string;
  expiresAt: string;
  tenant: {
    id: string;
    name: string;
  };
  warehouse: {
    id: string;
    name: string;
    address?: string;
  };
  invitedBy: {
    name: string;
    email: string;
  };
}

// Validation schema
const joinAccountSchema = z
  .object({
    fullName: z
      .string()
      .min(2, "Full name must be at least 2 characters")
      .max(100, "Full name must be less than 100 characters")
      .regex(
        /^[a-zA-Z\s'-]+$/,
        "Full name can only contain letters, spaces, hyphens, and apostrophes"
      ),
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .max(128, "Password must be less than 128 characters")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/[0-9]/, "Password must contain at least one number"),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

type JoinAccountForm = z.infer<typeof joinAccountSchema>;

export default function JoinAccountPage() {
  const router = useRouter();
  const params = useParams();
  const token = params.token as string;

  const [invitation, setInvitation] = useState<InvitationData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
  } = useForm<JoinAccountForm>({
    resolver: zodResolver(joinAccountSchema),
    mode: "onChange",
  });

  const password = watch("password");

  useEffect(() => {
    if (!token) {
      setError("Invalid invitation link");
      setIsLoading(false);
      return;
    }

    validateInvitation();
  }, [token]);

  const validateInvitation = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/invitations/validate/${token}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        const errorData = await response
          .json()
          .catch(() => ({ message: "Invalid invitation" }));
        throw new Error(errorData.message || "Failed to validate invitation");
      }

      const data = await response.json();

      if (!data.valid) {
        throw new Error("This invitation is invalid or has expired");
      }

      setInvitation(data);
    } catch (error) {
      console.error("Failed to validate invitation:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to validate invitation";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (data: JoinAccountForm) => {
    if (!invitation) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/invitations/accept/${token}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            fullName: data.fullName,
            password: data.password,
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response
          .json()
          .catch(() => ({ message: "Failed to create account" }));
        throw new Error(errorData.message || "Failed to create account");
      }

      const result = await response.json();

      toast.success("Account created successfully! Welcome to Quildora!");

      // Store auth data and redirect to main app
      if (result.accessToken) {
        localStorage.setItem("quildora_token", result.accessToken);
        router.push("/");
      } else {
        // Fallback to sign in page
        router.push("/auth/signin");
      }
    } catch (error) {
      console.error("Failed to accept invitation:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create account";
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    router.push(`/auth/join/${token}`);
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case "WAREHOUSE_MANAGER":
        return "Warehouse Manager";
      case "WAREHOUSE_MEMBER":
        return "Warehouse Member";
      case "TENANT_ADMIN":
        return "Administrator";
      default:
        return role;
    }
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "TENANT_ADMIN":
        return "default";
      case "WAREHOUSE_MANAGER":
        return "secondary";
      case "WAREHOUSE_MEMBER":
        return "outline";
      default:
        return "outline";
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
            <h2 className="text-lg font-semibold text-gray-900 mb-2">
              Validating Invitation
            </h2>
            <p className="text-gray-600">
              Please wait while we verify your invitation...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !invitation) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="text-red-500 mb-4">
              <Mail className="h-12 w-12 mx-auto" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Unable to Create Account
            </h2>
            <p className="text-gray-600 mb-6">
              {error || "This invitation link is invalid or has expired."}
            </p>
            <Button
              onClick={() => router.push("/auth/welcome")}
              className="w-full"
            >
              Go to Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4">
            <Users className="w-8 h-8 text-primary" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Create Your Account
          </h1>
          <p className="text-lg text-gray-600">
            Complete your account setup to join {invitation.tenant.name}
          </p>
        </div>

        {/* Invitation Summary */}
        <Card className="mb-8 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center space-x-2">
                <CheckCircle2 className="h-5 w-5 text-green-600" />
                <span>Joining as {getRoleDisplayName(invitation.role)}</span>
              </span>
              <Badge variant={getRoleBadgeVariant(invitation.role)}>
                {getRoleDisplayName(invitation.role)}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-3">
                <Building2 className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="font-medium text-gray-900">
                    {invitation.tenant.name}
                  </p>
                  <p className="text-sm text-gray-500">Company</p>
                </div>
              </div>
              {invitation.warehouse && (
                <div className="flex items-center space-x-3">
                  <Building2 className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="font-medium text-gray-900">
                      {invitation.warehouse.name}
                    </p>
                    <p className="text-sm text-gray-500">Warehouse</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Account Creation Form */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>Account Information</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Email (Read-only) */}
              <OnboardingField
                label="Email Address"
                description="This email address was invited to join the team"
              >
                <Input
                  type="email"
                  value={invitation.email}
                  disabled
                  className="h-12 bg-gray-50"
                />
              </OnboardingField>

              {/* Full Name */}
              <OnboardingField
                label="Full Name"
                required
                error={errors.fullName?.message}
              >
                <Input
                  {...register("fullName")}
                  type="text"
                  placeholder="Enter your full name"
                  className="h-12"
                  autoComplete="name"
                />
              </OnboardingField>

              {/* Password */}
              <OnboardingField
                label="Password"
                required
                error={errors.password?.message}
                description="Create a strong password for your account"
              >
                <Input
                  {...register("password")}
                  type="password"
                  placeholder="Create a password"
                  className="h-12"
                  autoComplete="new-password"
                />
              </OnboardingField>

              {/* Confirm Password */}
              <OnboardingField
                label="Confirm Password"
                required
                error={errors.confirmPassword?.message}
              >
                <Input
                  {...register("confirmPassword")}
                  type="password"
                  placeholder="Confirm your password"
                  className="h-12"
                  autoComplete="new-password"
                />
              </OnboardingField>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleBack}
                  className="flex-1 h-12"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>

                <Button
                  type="submit"
                  disabled={!isValid || isSubmitting}
                  className="flex-1 h-12"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating Account...
                    </>
                  ) : (
                    "Create Account & Join Team"
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Help Text */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500">
            By creating an account, you agree to join {invitation.tenant.name}{" "}
            and accept the role of {getRoleDisplayName(invitation.role)}.
          </p>
        </div>
      </div>
    </div>
  );
}
