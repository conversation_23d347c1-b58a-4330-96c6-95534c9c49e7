'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Building2, Users, Mail, CheckCircle2, AlertCircle, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface InvitationData {
  id: string;
  email: string;
  role: string;
  status: string;
  expiresAt: string;
  tenant: {
    id: string;
    name: string;
  };
  warehouse: {
    id: string;
    name: string;
    address?: string;
  };
  invitedBy: {
    name: string;
    email: string;
  };
}

export default function JoinTeamPage() {
  const router = useRouter();
  const params = useParams();
  const token = params.token as string;
  
  const [invitation, setInvitation] = useState<InvitationData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!token) {
      setError('Invalid invitation link');
      setIsLoading(false);
      return;
    }

    validateInvitation();
  }, [token]);

  const validateInvitation = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/invitations/validate/${token}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Invalid invitation' }));
        throw new Error(errorData.message || 'Failed to validate invitation');
      }

      const data = await response.json();
      setInvitation(data);
    } catch (error) {
      console.error('Failed to validate invitation:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to validate invitation';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAcceptInvitation = () => {
    if (!invitation) return;
    
    // Navigate to account creation with invitation token
    router.push(`/auth/join/${token}/account`);
  };

  const handleDeclineInvitation = () => {
    // For now, just show a message
    toast.info('Invitation declined. You can close this page.');
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'WAREHOUSE_MANAGER':
        return 'Warehouse Manager';
      case 'WAREHOUSE_MEMBER':
        return 'Warehouse Member';
      case 'TENANT_ADMIN':
        return 'Administrator';
      default:
        return role;
    }
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'TENANT_ADMIN':
        return 'default';
      case 'WAREHOUSE_MANAGER':
        return 'secondary';
      case 'WAREHOUSE_MEMBER':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const isExpired = invitation && new Date(invitation.expiresAt) < new Date();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
            <h2 className="text-lg font-semibold text-gray-900 mb-2">Validating Invitation</h2>
            <p className="text-gray-600">Please wait while we verify your invitation...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !invitation) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Invalid Invitation</h2>
            <p className="text-gray-600 mb-6">
              {error || 'This invitation link is invalid or has expired.'}
            </p>
            <Button 
              onClick={() => router.push('/auth/welcome')}
              className="w-full"
            >
              Go to Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isExpired) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <AlertCircle className="h-12 w-12 text-amber-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Invitation Expired</h2>
            <p className="text-gray-600 mb-6">
              This invitation has expired. Please contact your team administrator for a new invitation.
            </p>
            <Button 
              onClick={() => router.push('/auth/welcome')}
              className="w-full"
            >
              Go to Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4">
            <Users className="w-8 h-8 text-primary" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            You're Invited to Join Quildora!
          </h1>
          <p className="text-lg text-gray-600">
            {invitation.invitedBy.name} has invited you to join their warehouse team
          </p>
        </div>

        {/* Invitation Details */}
        <Card className="mb-8 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Mail className="h-5 w-5 text-primary" />
              <span>Invitation Details</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Company Info */}
            <div className="flex items-center space-x-4 p-4 bg-blue-50 rounded-lg">
              <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Building2 className="w-5 h-5 text-blue-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">{invitation.tenant.name}</h3>
                <p className="text-sm text-gray-600">Company</p>
              </div>
            </div>

            {/* Warehouse Info */}
            <div className="flex items-center space-x-4 p-4 bg-green-50 rounded-lg">
              <div className="flex-shrink-0 w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <Building2 className="w-5 h-5 text-green-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">{invitation.warehouse.name}</h3>
                <p className="text-sm text-gray-600">
                  {invitation.warehouse.address || 'Warehouse'}
                </p>
              </div>
            </div>

            {/* Role Info */}
            <div className="flex items-center justify-between p-4 bg-purple-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0 w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <CheckCircle2 className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Your Role</h3>
                  <p className="text-sm text-gray-600">Access level and permissions</p>
                </div>
              </div>
              <Badge variant={getRoleBadgeVariant(invitation.role)}>
                {getRoleDisplayName(invitation.role)}
              </Badge>
            </div>

            {/* Invited By */}
            <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                <Users className="w-5 h-5 text-gray-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">{invitation.invitedBy.name}</h3>
                <p className="text-sm text-gray-600">{invitation.invitedBy.email}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4">
          <Button
            onClick={handleAcceptInvitation}
            className="flex-1 h-12 text-lg font-medium"
          >
            Accept Invitation & Create Account
          </Button>
          
          <Button
            variant="outline"
            onClick={handleDeclineInvitation}
            className="flex-1 h-12 text-lg font-medium"
          >
            Decline
          </Button>
        </div>

        {/* Help Text */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500">
            Already have an account?{' '}
            <button 
              onClick={() => router.push('/auth/signin')}
              className="text-primary hover:underline"
            >
              Sign in here
            </button>
          </p>
        </div>
      </div>
    </div>
  );
}
