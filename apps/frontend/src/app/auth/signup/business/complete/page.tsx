"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/components/providers/auth-provider";
import { useOnboarding } from "@/components/providers/onboarding-provider";
import { OnboardingCelebration } from "@/components/onboarding/OnboardingCelebration";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { toast } from "sonner";

export default function CompletePage() {
  const router = useRouter();
  const { appUser, appToken } = useAuth();
  const { sessionData } = useOnboarding();
  const [isLoading, setIsLoading] = useState(true);
  const [invitations, setInvitations] = useState<any[]>([]);

  // Check authentication and load invitation data
  useEffect(() => {
    // Check if user is authenticated
    if (!appToken || !appUser) {
      toast.error("Authentication required. Please sign in again.");
      router.push("/auth/signin");
      return;
    }

    // Load invitation data from localStorage if available
    const storedInvitations = localStorage.getItem("quildora_invitations");
    if (storedInvitations) {
      try {
        setInvitations(JSON.parse(storedInvitations));
        // Clear the stored invitations after loading
        localStorage.removeItem("quildora_invitations");
      } catch (error) {
        console.error("Error parsing stored invitations:", error);
      }
    }

    setIsLoading(false);
  }, [appToken, appUser, router]);

  const handleContinueToDashboard = () => {
    // Clear any remaining onboarding data
    localStorage.removeItem("quildora_onboarding_session");

    // Show success message
    toast.success(
      "🎉 Welcome to Quildora! Let's start optimizing your industrial operations."
    );

    // Navigate to dashboard
    router.push("/dashboard");
  };

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Finalizing your setup..." />
      </div>
    );
  }

  // Show error if not authenticated
  if (!appUser || !appToken) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Authentication Required
          </h2>
          <p className="text-gray-600 mb-4">
            Please sign in to complete your setup.
          </p>
          <button
            onClick={() => router.push("/auth/signin")}
            className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/90 transition-colors"
          >
            Sign In
          </button>
        </div>
      </div>
    );
  }

  // Extract data for celebration component
  const companyName = sessionData.businessInfo?.companyName || "Your Company";
  const warehouseName =
    sessionData.warehouseSetup?.warehouseName || "Your Warehouse";
  const adminName =
    sessionData.adminAccount?.fullName || appUser?.name || "Admin";

  return (
    <OnboardingCelebration
      companyName={companyName}
      warehouseName={warehouseName}
      adminName={adminName}
      onContinue={handleContinueToDashboard}
    />
  );
}
