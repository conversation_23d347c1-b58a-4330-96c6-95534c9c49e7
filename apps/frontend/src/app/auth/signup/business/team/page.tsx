"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";

import OnboardingLayout from "@/components/layout/OnboardingLayout";
import OnboardingFormWrapper, {
  OnboardingField,
} from "@/components/onboarding/OnboardingFormWrapper";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { useOnboarding } from "@/components/providers/onboarding-provider";
import { Plus, X, Users, Mail, UserCheck } from "lucide-react";

// Validation schema
const teamMemberSchema = z.object({
  email: z
    .string()
    .email("Please enter a valid email address")
    .max(255, "Email must be less than 255 characters"),
  role: z.enum(["WAREHOUSE_MEMBER", "WAREHOUSE_MANAGER"], {
    required_error: "Please select a role",
  }),
});

const teamSetupSchema = z.object({
  teamMembers: z
    .array(teamMemberSchema)
    .max(20, "You can invite up to 20 team members at once")
    .optional(),
  skipTeamSetup: z.boolean().optional(),
});

type TeamSetupForm = z.infer<typeof teamSetupSchema>;
type TeamMember = z.infer<typeof teamMemberSchema>;

const ROLE_OPTIONS = [
  {
    value: "WAREHOUSE_MEMBER" as const,
    label: "Warehouse Member",
    description:
      "Can perform daily warehouse operations like receiving, picking, and shipping",
  },
  {
    value: "WAREHOUSE_MANAGER" as const,
    label: "Warehouse Manager",
    description:
      "Can manage warehouse operations, users, and settings in addition to member permissions",
  },
];

export default function TeamSetupPage() {
  const router = useRouter();
  const {
    completeOnboarding,
    sessionData,
    isLoading: onboardingLoading,
  } = useOnboarding();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [skipTeam, setSkipTeam] = useState(false);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isValid },
    watch,
    setValue,
  } = useForm<TeamSetupForm>({
    resolver: zodResolver(teamSetupSchema),
    mode: "onChange",
    defaultValues: {
      teamMembers: [{ email: "", role: "WAREHOUSE_MEMBER" }],
      skipTeamSetup: false,
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "teamMembers",
  });

  const teamMembers = watch("teamMembers") || [];

  const onSubmit = async (data: TeamSetupForm) => {
    if (!sessionData?.sessionId) {
      toast.error("Session expired. Please start over.");
      router.push("/auth/signup/business");
      return;
    }

    setIsSubmitting(true);
    try {
      const teamSetup =
        skipTeam || !data.teamMembers?.length
          ? undefined
          : {
              inviteEmails: data.teamMembers.map((member) => member.email),
              defaultRole:
                data.teamMembers[0]?.role || ("WAREHOUSE_MEMBER" as const),
            };

      const result = await completeOnboarding(teamSetup);

      // Store authentication data
      if (result.accessToken) {
        localStorage.setItem("quildora_token", result.accessToken);
        // The auth provider will pick up the token and authenticate the user
      }

      // Store invitation codes for display on completion page
      if (result.invitations && result.invitations.length > 0) {
        localStorage.setItem(
          "quildora_invitations",
          JSON.stringify(result.invitations)
        );
      }

      toast.success("Onboarding completed successfully!");
      router.push("/auth/signup/business/complete");
    } catch (error) {
      console.error("Failed to complete onboarding:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to complete onboarding";
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    router.push("/auth/signup/business/warehouse");
  };

  const handleSkipTeam = () => {
    setSkipTeam(true);
    setValue("skipTeamSetup", true);
    // Clear team members when skipping
    setValue("teamMembers", []);
  };

  const handleAddTeamMember = () => {
    if (fields.length < 20) {
      append({ email: "", role: "WAREHOUSE_MEMBER" });
    }
  };

  const handleRemoveTeamMember = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  const getRoleDescription = (role: string) => {
    return (
      ROLE_OPTIONS.find((option) => option.value === role)?.description || ""
    );
  };

  if (onboardingLoading) {
    return (
      <OnboardingLayout
        currentStep="team_setup"
        title="Team Setup"
        description="Invite your team members"
        showBackButton={false}
      >
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
      </OnboardingLayout>
    );
  }

  return (
    <OnboardingLayout
      currentStep="team_setup"
      title="Team Setup"
      description="Invite team members to collaborate in your warehouse"
      onBack={handleBack}
    >
      <OnboardingFormWrapper
        title="Invite Your Team"
        description="Add team members who will help manage your warehouse operations. You can always invite more people later."
        isSubmitting={isSubmitting}
        nextButtonText={
          skipTeam ? "Complete Setup" : "Send Invitations & Complete"
        }
        nextButtonDisabled={
          !skipTeam &&
          (!isValid ||
            teamMembers.some((member) => !member.email || !member.role))
        }
        onSubmit={handleSubmit(onSubmit)}
        onBack={handleBack}
      >
        <div className="space-y-6">
          {/* Skip Option */}
          <Card className="border-dashed border-gray-300">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Users className="h-5 w-5 text-gray-400" />
                  <div>
                    <h3 className="text-sm font-medium text-gray-900">
                      Skip team setup for now
                    </h3>
                    <p className="text-sm text-gray-500">
                      You can invite team members later from settings
                    </p>
                  </div>
                </div>
                <Button
                  type="button"
                  variant={skipTeam ? "default" : "outline"}
                  onClick={skipTeam ? () => setSkipTeam(false) : handleSkipTeam}
                  className="min-h-[44px]"
                >
                  {skipTeam ? "Add Team Instead" : "Skip for Now"}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Team Members Section */}
          {!skipTeam && (
            <>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">
                    Team Members
                  </h3>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleAddTeamMember}
                    disabled={fields.length >= 20}
                    className="min-h-[44px]"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add Member
                  </Button>
                </div>

                {fields.map((field, index) => (
                  <Card key={field.id} className="border border-gray-200">
                    <CardContent className="p-4">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                          <Mail className="h-5 w-5 text-primary" />
                        </div>

                        <div className="flex-1 space-y-4">
                          {/* Email Input */}
                          <OnboardingField
                            label={`Email Address ${index + 1}`}
                            required
                            error={errors.teamMembers?.[index]?.email?.message}
                          >
                            <Input
                              {...register(`teamMembers.${index}.email`)}
                              type="email"
                              placeholder="Enter team member's email"
                              className="h-12"
                              autoComplete="email"
                            />
                          </OnboardingField>

                          {/* Role Selection */}
                          <OnboardingField
                            label="Role"
                            required
                            error={errors.teamMembers?.[index]?.role?.message}
                            description={getRoleDescription(
                              watch(`teamMembers.${index}.role`)
                            )}
                          >
                            <Select
                              value={watch(`teamMembers.${index}.role`)}
                              onValueChange={(value) =>
                                setValue(
                                  `teamMembers.${index}.role`,
                                  value as
                                    | "WAREHOUSE_MEMBER"
                                    | "WAREHOUSE_MANAGER"
                                )
                              }
                            >
                              <SelectTrigger className="h-12">
                                <SelectValue placeholder="Select role" />
                              </SelectTrigger>
                              <SelectContent>
                                {ROLE_OPTIONS.map((role) => (
                                  <SelectItem
                                    key={role.value}
                                    value={role.value}
                                  >
                                    <div className="flex items-center space-x-2">
                                      <UserCheck className="h-4 w-4" />
                                      <span>{role.label}</span>
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </OnboardingField>
                        </div>

                        {/* Remove Button */}
                        {fields.length > 1 && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveTeamMember(index)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50 min-h-[44px] min-w-[44px]"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}

                {fields.length >= 20 && (
                  <p className="text-sm text-gray-500 text-center">
                    Maximum of 20 team members can be invited at once. You can
                    invite more later.
                  </p>
                )}
              </div>

              {/* Team Summary */}
              {teamMembers.length > 0 && (
                <Card className="bg-blue-50 border-blue-200">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Users className="h-5 w-5 text-blue-600" />
                      <h4 className="font-medium text-blue-900">
                        Invitation Summary
                      </h4>
                    </div>
                    <p className="text-sm text-blue-700">
                      {teamMembers.length} team member
                      {teamMembers.length !== 1 ? "s" : ""} will receive
                      invitation emails to join your warehouse. They'll be able
                      to access the system once they accept their invitations.
                    </p>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </div>
      </OnboardingFormWrapper>
    </OnboardingLayout>
  );
}
