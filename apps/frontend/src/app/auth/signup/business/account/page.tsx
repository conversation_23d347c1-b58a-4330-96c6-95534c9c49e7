'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';

import OnboardingLayout from '@/components/layout/OnboardingLayout';
import OnboardingFormWrapper, { OnboardingField } from '@/components/onboarding/OnboardingFormWrapper';
import { Input } from '@/components/ui/input';
import { useOnboarding } from '@/components/providers/onboarding-provider';

// Validation schema
const adminAccountSchema = z.object({
  fullName: z
    .string()
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name must be less than 100 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Full name can only contain letters, spaces, hyphens, and apostrophes'),
  
  email: z
    .string()
    .email('Please enter a valid email address')
    .max(255, 'Email must be less than 255 characters'),
  
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must be less than 128 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  
  confirmPassword: z.string(),
  
  phoneNumber: z
    .string()
    .optional()
    .refine((val) => !val || /^\+?[\d\s\-\(\)]+$/.test(val), 'Please enter a valid phone number'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type AdminAccountForm = z.infer<typeof adminAccountSchema>;

export default function AdminAccountPage() {
  const router = useRouter();
  const { createAdminAccount, sessionData, isLoading: onboardingLoading } = useOnboarding();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
  } = useForm<AdminAccountForm>({
    resolver: zodResolver(adminAccountSchema),
    mode: 'onChange',
    defaultValues: {
      fullName: '',
      email: '',
      password: '',
      confirmPassword: '',
      phoneNumber: '',
    },
  });

  // Watch password for strength indicator
  const password = watch('password');

  const onSubmit = async (data: AdminAccountForm) => {
    if (!sessionData?.sessionId) {
      toast.error('Session expired. Please start over.');
      router.push('/auth/signup/business');
      return;
    }

    setIsSubmitting(true);
    try {
      await createAdminAccount({
        fullName: data.fullName,
        email: data.email,
        password: data.password,
        phoneNumber: data.phoneNumber || undefined,
      });
      
      toast.success('Admin account created successfully!');
      router.push('/auth/signup/business/warehouse');
    } catch (error) {
      console.error('Failed to create admin account:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create admin account';
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    router.push('/auth/signup/business');
  };

  // Password strength calculation
  const getPasswordStrength = (password: string): { score: number; label: string; color: string } => {
    if (!password) return { score: 0, label: '', color: '' };
    
    let score = 0;
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/\d/.test(password)) score++;
    if (/[^a-zA-Z\d]/.test(password)) score++;
    
    if (score <= 2) return { score, label: 'Weak', color: 'bg-red-500' };
    if (score <= 3) return { score, label: 'Fair', color: 'bg-yellow-500' };
    if (score <= 4) return { score, label: 'Good', color: 'bg-blue-500' };
    return { score, label: 'Strong', color: 'bg-green-500' };
  };

  const passwordStrength = getPasswordStrength(password);

  if (onboardingLoading) {
    return (
      <OnboardingLayout
        currentStep="admin_account"
        title="Create Admin Account"
        description="Set up your administrator account"
        showBackButton={false}
      >
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
      </OnboardingLayout>
    );
  }

  return (
    <OnboardingLayout
      currentStep="admin_account"
      title="Create Admin Account"
      description="Set up your administrator account to manage your business"
      onBack={handleBack}
    >
      <OnboardingFormWrapper
        title="Administrator Details"
        description="This account will have full access to manage your business settings and users."
        isSubmitting={isSubmitting}
        nextButtonText="Create Account"
        nextButtonDisabled={!isValid}
        onSubmit={handleSubmit(onSubmit)}
        onBack={handleBack}
      >
        <div className="space-y-6">
          {/* Full Name */}
          <OnboardingField
            label="Full Name"
            required
            error={errors.fullName?.message}
          >
            <Input
              {...register('fullName')}
              placeholder="Enter your full name"
              className="h-12"
              autoComplete="name"
              autoFocus
            />
          </OnboardingField>

          {/* Email */}
          <OnboardingField
            label="Email Address"
            required
            error={errors.email?.message}
            description="This will be your login email and where we'll send important notifications."
          >
            <Input
              {...register('email')}
              type="email"
              placeholder="Enter your email address"
              className="h-12"
              autoComplete="email"
            />
          </OnboardingField>

          {/* Password */}
          <OnboardingField
            label="Password"
            required
            error={errors.password?.message}
          >
            <Input
              {...register('password')}
              type="password"
              placeholder="Create a strong password"
              className="h-12"
              autoComplete="new-password"
            />
            
            {/* Password Strength Indicator */}
            {password && (
              <div className="mt-2">
                <div className="flex items-center gap-2 mb-1">
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${passwordStrength.color}`}
                      style={{ width: `${(passwordStrength.score / 5) * 100}%` }}
                    />
                  </div>
                  <span className="text-xs text-gray-600">{passwordStrength.label}</span>
                </div>
                <p className="text-xs text-gray-500">
                  Password must contain at least 8 characters with uppercase, lowercase, and numbers
                </p>
              </div>
            )}
          </OnboardingField>

          {/* Confirm Password */}
          <OnboardingField
            label="Confirm Password"
            required
            error={errors.confirmPassword?.message}
          >
            <Input
              {...register('confirmPassword')}
              type="password"
              placeholder="Confirm your password"
              className="h-12"
              autoComplete="new-password"
            />
          </OnboardingField>

          {/* Phone Number (Optional) */}
          <OnboardingField
            label="Phone Number"
            error={errors.phoneNumber?.message}
            description="Optional. We may use this for account security and important notifications."
          >
            <Input
              {...register('phoneNumber')}
              type="tel"
              placeholder="Enter your phone number"
              className="h-12"
              autoComplete="tel"
            />
          </OnboardingField>
        </div>
      </OnboardingFormWrapper>
    </OnboardingLayout>
  );
}
