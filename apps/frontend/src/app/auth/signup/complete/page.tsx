"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useOnboarding } from "@/components/providers/onboarding-provider";
import { OnboardingCelebration } from "@/components/onboarding/OnboardingCelebration";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { useAuth } from "@/components/providers/auth-provider";
import { toast } from "sonner";

export default function OnboardingCompletePage() {
  const router = useRouter();
  const { sessionData, completeOnboarding, isLoading, error } = useOnboarding();
  const { appUser } = useAuth();
  const [isCompleting, setIsCompleting] = useState(false);
  const [showCelebration, setShowCelebration] = useState(false);

  // Check if user has completed all required steps
  useEffect(() => {
    if (
      !sessionData.businessInfo?.companyName ||
      !sessionData.adminAccount?.email ||
      !sessionData.warehouseSetup?.warehouseName
    ) {
      // Redirect to appropriate step if not completed
      if (!sessionData.businessInfo?.companyName) {
        router.push("/auth/signup/business");
      } else if (!sessionData.adminAccount?.email) {
        router.push("/auth/signup/admin");
      } else if (!sessionData.warehouseSetup?.warehouseName) {
        router.push("/auth/signup/warehouse");
      }
      return;
    }

    // Show celebration after a brief delay
    const timer = setTimeout(() => {
      setShowCelebration(true);
    }, 500);

    return () => clearTimeout(timer);
  }, [sessionData, router]);

  const handleCompleteOnboarding = async () => {
    if (isCompleting) return;

    setIsCompleting(true);

    try {
      // Complete the onboarding process
      await completeOnboarding(sessionData.teamSetup);

      // If we get here, the onboarding was successful
      toast.success("🎉 Welcome to Quildora! Your account is ready.");

      // Small delay for the toast to show
      setTimeout(() => {
        router.push("/");
      }, 1500);
    } catch (err) {
      console.error("Error completing onboarding:", err);
      toast.error("Failed to complete setup. Please try again.");
      setIsCompleting(false);
    }
  };

  // Show loading state while checking session data
  if (isLoading || !showCelebration) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" className="mx-auto mb-4" />
          <p className="text-gray-600">Preparing your celebration...</p>
        </div>
      </div>
    );
  }

  // Show error state if there's an issue
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <h2 className="text-lg font-semibold text-red-800 mb-2">
              Setup Error
            </h2>
            <p className="text-red-600 text-sm">{error}</p>
          </div>
          <button
            onClick={() => router.push("/auth/signup/business")}
            className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/90 transition-colors"
          >
            Start Over
          </button>
        </div>
      </div>
    );
  }

  // Extract data for celebration component
  const companyName = sessionData.businessInfo?.companyName || "Your Company";
  const warehouseName =
    sessionData.warehouseSetup?.warehouseName || "Your Warehouse";
  const adminName =
    sessionData.adminAccount?.fullName || appUser?.name || "Admin";

  return (
    <OnboardingCelebration
      companyName={companyName}
      warehouseName={warehouseName}
      adminName={adminName}
      onContinue={handleCompleteOnboarding}
      className={isCompleting ? "pointer-events-none opacity-75" : ""}
    />
  );
}
