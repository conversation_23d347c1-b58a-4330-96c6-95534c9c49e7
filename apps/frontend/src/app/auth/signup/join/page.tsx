'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Users, Mail, AlertCircle, Loader2 } from 'lucide-react';
import { OnboardingField } from '@/components/onboarding/OnboardingFormWrapper';

// Validation schema
const joinTeamSchema = z.object({
  invitationCode: z
    .string()
    .min(1, 'Please enter an invitation code')
    .regex(/^INV-[A-Z0-9]{12}$/, 'Invalid invitation code format (should be INV-XXXXXXXXXXXX)'),
});

type JoinTeamForm = z.infer<typeof joinTeamSchema>;

export default function JoinTeamPage() {
  const router = useRouter();
  const [isValidating, setIsValidating] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
  } = useForm<JoinTeamForm>({
    resolver: zodResolver(joinTeamSchema),
    mode: 'onChange',
  });

  const invitationCode = watch('invitationCode');

  const onSubmit = async (data: JoinTeamForm) => {
    setIsValidating(true);
    try {
      // Validate the invitation code first
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/invitations/validate/${data.invitationCode}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to validate invitation code');
      }

      const result = await response.json();
      
      if (!result.valid) {
        throw new Error('This invitation code is invalid or has expired');
      }

      // Redirect to the invitation page
      router.push(`/auth/join/${data.invitationCode}`);
    } catch (error) {
      console.error('Failed to validate invitation:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to validate invitation code';
      toast.error(errorMessage);
    } finally {
      setIsValidating(false);
    }
  };

  const handleBack = () => {
    router.push('/auth/welcome');
  };

  const formatInvitationCode = (value: string) => {
    // Remove any non-alphanumeric characters except hyphens
    const cleaned = value.replace(/[^A-Z0-9-]/gi, '').toUpperCase();
    
    // If it doesn't start with INV-, add it
    if (cleaned && !cleaned.startsWith('INV-')) {
      return `INV-${cleaned.replace(/^INV-?/, '')}`;
    }
    
    return cleaned;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4">
            <Users className="w-8 h-8 text-primary" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Join a Team
          </h1>
          <p className="text-lg text-gray-600">
            Enter your invitation code to join an existing warehouse team
          </p>
        </div>

        {/* Join Form */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Mail className="h-5 w-5 text-primary" />
              <span>Invitation Code</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Invitation Code Input */}
              <OnboardingField
                label="Invitation Code"
                required
                error={errors.invitationCode?.message}
                description="This code was provided in your invitation email (format: INV-XXXXXXXXXXXX)"
              >
                <Input
                  {...register('invitationCode', {
                    onChange: (e) => {
                      e.target.value = formatInvitationCode(e.target.value);
                    }
                  })}
                  type="text"
                  placeholder="INV-XXXXXXXXXXXX"
                  className="h-12 font-mono"
                  autoComplete="off"
                  maxLength={16}
                />
              </OnboardingField>

              {/* Help Text */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <AlertCircle className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
                  <div className="text-sm text-blue-700">
                    <p className="font-medium mb-1">Need help finding your invitation code?</p>
                    <ul className="space-y-1 text-xs">
                      <li>• Check your email for an invitation from your team administrator</li>
                      <li>• The code starts with "INV-" followed by 12 characters</li>
                      <li>• Contact your team administrator if you can't find the invitation</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleBack}
                  className="flex-1 h-12"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                
                <Button
                  type="submit"
                  disabled={!isValid || isValidating}
                  className="flex-1 h-12"
                >
                  {isValidating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Validating...
                    </>
                  ) : (
                    'Continue'
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Alternative Options */}
        <div className="text-center mt-8 space-y-4">
          <p className="text-sm text-gray-500">
            Don't have an invitation code?
          </p>
          
          <div className="flex flex-col sm:flex-row gap-2 justify-center">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => router.push('/auth/signup/business')}
              className="text-primary hover:text-primary/80"
            >
              Start your own business
            </Button>
            
            <span className="text-gray-300 hidden sm:inline">•</span>
            
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => router.push('/auth/signin')}
              className="text-primary hover:text-primary/80"
            >
              Sign in to existing account
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
