"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/components/providers/auth-provider";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { fetchWithAuth } from "@/lib/api"; // Corrected import path

export default function CompanyDetailsPage() {
  const {
    appToken,
    onboardingStatus,
    isLoading,
    appUser,
    loginWithSupabaseToken,
    supabaseSession,
  } = useAuth();
  const router = useRouter();
  const [companyName, setCompanyName] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (!isLoading) {
      if (!appToken) {
        router.replace("/auth");
      } else if (onboardingStatus === "complete") {
        router.replace("/");
      }
    }
  }, [appToken, onboardingStatus, isLoading, router]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!companyName.trim()) {
      toast.error("Company name is required.");
      return;
    }
    if (!appToken) {
      toast.error("Authentication error. Please log in again.");
      router.push("/auth/welcome");
      return;
    }

    setIsSubmitting(true);
    try {
      await fetchWithAuth("/api/onboarding/complete-profile", {
        method: "POST",
        token: appToken,
        body: JSON.stringify({ companyName }),
      });

      // Assuming backend returns the updated app user and new token, or confirms success
      // For now, let's assume success means onboarding is complete.
      // A robust way would be for backend to signal AuthProvider to refresh user state.
      toast.success("Company profile completed successfully!");

      // Option 1: Force refresh of auth state by re-triggering login (if supabase token available)
      if (supabaseSession?.access_token) {
        await loginWithSupabaseToken(supabaseSession.access_token);
        // The AuthProvider's loginWithSupabaseToken should now receive 'complete' status and redirect to '/'
      } else {
        // Fallback: if supabase token is not readily available, redirect and let AuthProvider sort it out
        router.push("/");
        // It might briefly show this page again before AuthProvider redirects based on new status.
        // A better UX would involve the backend signaling a state update more directly.
      }
    } catch (error: unknown) {
      console.error("Failed to complete company profile:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to save company details. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading || !appToken || onboardingStatus === "complete") {
    // Show loading spinner or null while redirecting or if not applicable
    return (
      <div className="flex justify-center items-center min-h-screen">
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col justify-center items-center min-h-screen bg-gray-100">
      <div className="w-full max-w-md p-8 space-y-6 bg-white rounded-lg shadow-md">
        <h2 className="text-2xl font-bold text-center">
          Complete Your Profile
        </h2>
        <p className="text-center text-sm text-gray-600">
          Welcome, {appUser?.email}! Please enter your company name to get
          started.
        </p>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="companyName">Company Name</Label>
            <Input
              id="companyName"
              type="text"
              value={companyName}
              onChange={(e) => setCompanyName(e.target.value)}
              required
              className="mt-1"
              disabled={isSubmitting}
            />
          </div>
          <Button
            type="submit"
            className="w-full"
            disabled={isSubmitting || !companyName.trim()}
          >
            {isSubmitting ? "Saving..." : "Save and Continue"}
          </Button>
        </form>
      </div>
    </div>
  );
}
