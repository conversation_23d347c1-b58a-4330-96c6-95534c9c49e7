"use client";

import React, { useEffect } from "react";
import { ShipmentDetailsForm } from "@/components/receiving/ShipmentDetailsForm";
import { PastShipments } from "@/components/receiving/PastShipments";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { usePageTitle } from "@/components/providers/PageTitleContext";

export default function ReceivingPage() {
  const { setTitle } = usePageTitle();

  useEffect(() => {
    setTitle("New Inbound Shipment");
  }, [setTitle]);

  return (
    <div className="container mx-auto p-4 space-y-6">
      {/* <h1 className="text-2xl font-semibold text-slate-800 mb-6 text-center md:text-left">Receive New Items</h1> */}
      {/* Removed h1 */}
      <Card className="max-w-4xl mx-auto">
        <CardContent className="pt-6">
          <ShipmentDetailsForm />
        </CardContent>
      </Card>

      <div className="text-center">
        <Button variant="outline" asChild>
          <Link href="/receiving/quick-add">Quick Add Existing Pallet</Link>
        </Button>
      </div>

      {/* Past Shipments Section */}
      <div className="max-w-4xl mx-auto">
        <PastShipments />
      </div>
    </div>
  );
}
