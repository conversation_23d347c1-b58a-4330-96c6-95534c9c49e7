"use client";

import Link from "next/link";
import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { usePageTitle } from "@/components/providers/PageTitleContext";
import { ReceiveItemsForm } from "@/components/receiving/ReceiveItemsForm";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Pallet } from "@quildora/types";

export default function PalletReceivingPage() {
  const { setTitle } = usePageTitle();
  const params = useParams();
  const poNumber = params.poNumber as string;
  const [pallets, setPallets] = useState<Pallet[]>([]);
  const [showAddPalletForm, setShowAddPalletForm] = useState(false);

  useEffect(() => {
    if (poNumber) {
      setTitle(`Receive Pallets for PO: ${poNumber}`);
    } else {
      setTitle("Receive Pallets");
    }
  }, [poNumber, setTitle]);

  const handlePalletReceived = (newPallet: Pallet) => {
    setPallets((prevPallets) => [...prevPallets, newPallet]);
    setShowAddPalletForm(false);
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Shipment Details</CardTitle>
            <CardDescription>
              Currently receiving items for Purchase Order:{" "}
              <strong>{poNumber || "N/A"}</strong>
            </CardDescription>
          </div>
          <Link href={`/receiving/${poNumber}/summary`} passHref>
            <Button variant="outline">View Summary</Button>
          </Link>
        </CardHeader>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Received Pallets ({pallets.length})</CardTitle>
            <CardDescription>
              Pallets received for PO: {poNumber}
            </CardDescription>
          </div>
          <Dialog open={showAddPalletForm} onOpenChange={setShowAddPalletForm}>
            <DialogTrigger asChild>
              <Button onClick={() => setShowAddPalletForm(true)}>
                Add Pallet
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Add New Pallet</DialogTitle>
                <DialogDescription>
                  Fill in the details to add a new pallet to PO: {poNumber}.
                </DialogDescription>
              </DialogHeader>
              <div className="py-4">
                <ReceiveItemsForm
                  onPalletReceived={handlePalletReceived}
                  poNumber={poNumber}
                  onCancel={() => setShowAddPalletForm(false)}
                />
              </div>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          {pallets.length === 0 ? (
            <p className="text-sm text-muted-foreground">
              No pallets received yet for this shipment.
            </p>
          ) : (
            pallets.map((pallet) => (
              <div key={pallet.id} className="border p-3 rounded-md mb-2">
                <p>
                  <strong>Barcode:</strong> {pallet.barcode || "N/A"}
                </p>
                <p>
                  <strong>Destination:</strong>{" "}
                  {pallet.shipToDestination?.toUpperCase() || "N/A"}
                </p>
                <p>
                  <strong>Description:</strong> {pallet.description || "N/A"}
                </p>
              </div>
            ))
          )}
        </CardContent>
      </Card>
    </div>
  );
}
