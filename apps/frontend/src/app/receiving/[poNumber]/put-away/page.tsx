"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSuspenseQuery } from "@tanstack/react-query";
import { fetchWithAuth } from "@/lib/api";
import { Shipment } from "@quildora/types";
import { usePageTitle } from "@/components/providers/PageTitleContext";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { useEffect } from "react";
import { usePutAwayOperations } from "@/hooks/usePutAwayOperations";
import { PutAwayOverview } from "@/components/put-away/PutAwayOverview";
import { PutAwaySection } from "@/components/put-away/PutAwaySection";
import { PutAwayCompletion } from "@/components/put-away/PutAwayCompletion";

// Using shared types from @quildora/types and components
interface ShipmentSummary {
  shipmentId: string;
  referenceNumber: string;
  status: string;
  purchaseOrderNumber: string;
  palletsByDestination: Record<string, any[]>; // Typed by components
}

const fetchShipmentByPoNumber = async (
  poNumber: string,
  warehouseId: string,
  token: string | null
): Promise<Shipment> => {
  return fetchWithAuth(
    `/api/shipments/by-po/${poNumber}?warehouseId=${warehouseId}`,
    { token }
  );
};

const fetchShipmentSummary = async (
  shipmentId: string,
  warehouseId: string,
  token: string | null
): Promise<ShipmentSummary> => {
  return fetchWithAuth(
    `/api/shipments/summary/${shipmentId}?warehouseId=${warehouseId}`,
    {
      token,
    }
  );
};

// Utility functions moved to usePutAwayOperations hook

const PutAwayPage = () => {
  const params = useParams();
  const router = useRouter();
  const { poNumber } = params;
  const { setTitle } = usePageTitle();
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  useEffect(() => {
    if (poNumber) {
      setTitle(`Put-Away for PO: ${poNumber}`);
    }
  }, [poNumber, setTitle]);

  const { data: shipment } = useSuspenseQuery<Shipment, Error>({
    queryKey: ["shipmentByPo", poNumber, currentWarehouse?.id],
    queryFn: () =>
      fetchShipmentByPoNumber(
        poNumber as string,
        currentWarehouse?.id || "",
        appToken
      ),
  });

  const shipmentId = shipment?.id;

  const { data: summary } = useSuspenseQuery<ShipmentSummary, Error>({
    queryKey: ["shipmentSummary", shipmentId, currentWarehouse?.id],
    queryFn: () =>
      fetchShipmentSummary(shipmentId!, currentWarehouse?.id || "", appToken),
  });

  // Use custom hook for put-away operations
  const {
    handleMovePallet,
    handleCompleteShipment,
    movingPallets,
    completedPallets,
    isCompletingShipment,
  } = usePutAwayOperations(shipmentId, poNumber as string);

  // All mutation logic moved to usePutAwayOperations hook

  // Navigation handler
  const handleBackToSummary = () => {
    router.push(`/receiving/${poNumber}/summary`);
  };

  // useSuspenseQuery handles loading and error states automatically

  const allPallets = Object.values(summary.palletsByDestination).flat();
  const pendingPallets = allPallets.filter(
    (p) => p.status !== "Stored" && !completedPallets.has(p.id)
  );
  const storedPallets = allPallets.filter(
    (p) => p.status === "Stored" || completedPallets.has(p.id)
  );

  return (
    <div className="space-y-8 p-8">
      <PutAwayOverview
        purchaseOrderNumber={summary.purchaseOrderNumber}
        totalPallets={allPallets.length}
        pendingPallets={pendingPallets.length}
        completedPallets={storedPallets.length}
        onBackToSummary={handleBackToSummary}
        onCompleteShipment={() => handleCompleteShipment(shipment.id)}
        canCompleteShipment={shipment?.status === "Processing"}
        isCompletingShipment={isCompletingShipment}
      />

      <PutAwaySection
        title="Pending Pallets"
        pallets={pendingPallets}
        onMovePallet={handleMovePallet}
        movingPallets={movingPallets}
        variant="pending"
      />

      <PutAwaySection
        title="Completed Pallets"
        pallets={storedPallets}
        onMovePallet={handleMovePallet}
        movingPallets={movingPallets}
        variant="completed"
        titleColor="text-green-600"
      />

      <PutAwayCompletion
        isComplete={pendingPallets.length === 0}
        totalPallets={allPallets.length}
      />
    </div>
  );
};

export default PutAwayPage;
