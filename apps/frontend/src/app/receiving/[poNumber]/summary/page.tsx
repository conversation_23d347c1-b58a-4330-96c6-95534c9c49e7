"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSuspenseQuery } from "@tanstack/react-query";
import { fetchWithAuth } from "@/lib/api";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { Shipment } from "@quildora/types";
import { usePageTitle } from "@/components/providers/PageTitleContext";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { useEffect } from "react";
import { useReceivingSummary } from "@/hooks/useReceivingSummary";
import { ShipmentOverview } from "@/components/receiving/ShipmentOverview";
import { DestinationSection } from "@/components/receiving/DestinationSection";

// Using shared types from @quildora/types and components
interface ShipmentSummary {
  shipmentId: string;
  purchaseOrderNumber: string;
  status: string;
  palletsByDestination: Record<string, any[]>; // Typed by components
}

const fetchShipmentByPoNumber = async (
  poNumber: string,
  warehouseId: string,
  token: string | null
): Promise<Shipment> => {
  return fetchWithAuth(
    `/api/shipments/by-po/${poNumber}?warehouseId=${warehouseId}`,
    { token }
  );
};

const fetchShipmentSummary = async (
  shipmentId: string,
  warehouseId: string,
  token: string | null
): Promise<ShipmentSummary> => {
  return fetchWithAuth(
    `/api/shipments/summary/${shipmentId}?warehouseId=${warehouseId}`,
    {
      token,
    }
  );
};

// Utility functions moved to reusable components and hooks

const ReceivingSummaryPage = () => {
  const params = useParams();
  const router = useRouter();
  const { poNumber } = params;
  const { setTitle } = usePageTitle();
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  useEffect(() => {
    if (poNumber) {
      setTitle(`Receiving Summary for PO: ${poNumber}`);
    }
  }, [poNumber, setTitle]);

  const { data: shipment } = useSuspenseQuery<Shipment, Error>({
    queryKey: ["shipment", poNumber, currentWarehouse?.id],
    queryFn: () =>
      fetchShipmentByPoNumber(
        poNumber as string,
        currentWarehouse?.id || "",
        appToken
      ),
  });

  const shipmentId = shipment?.id;

  const { data: summary } = useSuspenseQuery<ShipmentSummary, Error>({
    queryKey: ["shipmentSummary", shipmentId, currentWarehouse?.id],
    queryFn: () =>
      fetchShipmentSummary(shipmentId!, currentWarehouse?.id || "", appToken),
  });

  // Use custom hook for receiving summary operations
  const {
    handleLocationAssign,
    handleBulkPrint,
    isUpdatingLocation,
    isPrintingPlacards,
  } = useReceivingSummary(shipmentId);

  // Mutation logic moved to useReceivingSummary hook

  // All handler functions moved to useReceivingSummary hook

  const handleProceedToPutAway = () => {
    router.push(`/receiving/${poNumber}/put-away`);
  };

  // useSuspenseQuery handles loading and error states automatically

  const totalPallets = Object.values(summary.palletsByDestination).flat()
    .length;

  return (
    <div className="space-y-8 p-8">
      <ShipmentOverview
        purchaseOrderNumber={summary.purchaseOrderNumber}
        status={summary.status}
        totalPallets={totalPallets}
      />

      {Object.entries(summary.palletsByDestination).map(
        ([destination, pallets]) => (
          <DestinationSection
            key={destination}
            destination={destination}
            pallets={pallets}
            onBulkPrint={handleBulkPrint}
            onLocationAssign={handleLocationAssign}
            isUpdatingLocation={isUpdatingLocation}
            isPrintingPlacards={isPrintingPlacards}
          />
        )
      )}

      {/* Proceed to Put-Away Button */}
      <div className="flex justify-center pt-8">
        <Button size="lg" onClick={handleProceedToPutAway} className="px-8">
          Proceed to Put-Away
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  );
};

export default ReceivingSummaryPage;
