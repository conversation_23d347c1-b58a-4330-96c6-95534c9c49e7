"use client";

import React, { useEffect } from 'react';
import { QuickAddPalletForm } from '@/components/receiving/QuickAddPalletForm';
import { usePageTitle } from '@/components/providers/PageTitleContext';
// Card components are commented out but can be used for styling consistency
// import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function QuickAddPalletPage() {
  const { setTitle } = usePageTitle();

  useEffect(() => {
    setTitle('Quick Add Pallet');
  }, [setTitle]);

  return (
    <div className="container mx-auto py-10">
      {/* 
      // Optional: Wrap form in a Card for consistent styling 
      // <Card className="max-w-2xl mx-auto">
      //   <CardHeader>
      // <CardTitle>Quick Add Existing Pallet</CardTitle>
      //   </CardHeader>
      //   <CardContent className="pt-6">
      //     <QuickAddPalletForm />
      //   </CardContent>
      // </Card>
      */}
      <QuickAddPalletForm />
    </div>
  );
}