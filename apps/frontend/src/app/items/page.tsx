"use client"; // Make it a Client Component

import React, { useState, useEffect } from "react"; // Added useEffect
import { useItemsSuspense } from "@/hooks/api";
import { MoreHorizontal } from "lucide-react"; // Import icon
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CreateItemDialog } from "@/components/items/CreateItemDialog";
import { EditItemDialog } from "@/components/items/EditItemDialog";
import { DeleteItemDialog } from "@/components/items/DeleteItemDialog";
import { useAuth } from "@/components/providers/auth-provider"; // Import useAuth
import { useRoleAccess } from "@/components/providers/auth-provider"; // Added
import { Role } from "@quildora/types";
import { usePageTitle } from "@/components/providers/PageTitleContext"; // Added
import { Item } from "@quildora/types";

export default function ItemsPage() {
  const { appToken, isLoading: isAuthLoading } = useAuth(); // Get appToken and auth loading state
  const isAdmin = useRoleAccess(Role.TENANT_ADMIN); // Changed from Role.ADMIN
  const { setTitle } = usePageTitle(); // Added

  useEffect(() => {
    setTitle("Search Items");
  }, [setTitle]); // Added

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedItemForEdit, setSelectedItemForEdit] = useState<Item | null>(
    null
  );
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedItemForDelete, setSelectedItemForDelete] =
    useState<Item | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterLowStock, setFilterLowStock] = useState(false);

  // Use suspense hook for instant loading without spinners
  const { data: items = [] } = useItemsSuspense();

  // Handlers to open dialogs
  const handleEditClick = (item: Item) => {
    setSelectedItemForEdit(item);
    setIsEditDialogOpen(true);
  };

  const handleDeleteClick = (item: Item) => {
    setSelectedItemForDelete(item);
    setIsDeleteDialogOpen(true);
  };

  // No loading state needed - useSuspenseQuery handles this automatically

  // Client-side filtering
  const filteredItems = items.filter((item: Item) => {
    const searchTermLower = searchTerm.toLowerCase();
    const matchesSearch =
      item.name.toLowerCase().includes(searchTermLower) ||
      (item.sku && item.sku.toLowerCase().includes(searchTermLower));

    if (!matchesSearch) {
      return false;
    }

    if (filterLowStock) {
      const isLow =
        typeof item.quantityOnHand === "number" &&
        typeof item.lowStockThreshold === "number" &&
        item.quantityOnHand <= item.lowStockThreshold;
      return isLow;
    }

    return true;
  });

  // No error state needed - useSuspenseQuery handles this automatically

  return (
    <div className="container mx-auto p-4">
      {/* Search Bar and Create Button Row */}
      <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
        <div className="w-full sm:w-1/2 md:w-1/3">
          <input
            type="text"
            placeholder="Search by Item SKU or Name..."
            className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        {isAdmin && (
          <Button
            onClick={() => setIsCreateDialogOpen(true)}
            className="w-full sm:w-auto"
          >
            Create Item
          </Button>
        )}
      </div>

      {/* Filter Options */}
      <div className="mb-6 p-4 border border-gray-200 rounded-md bg-white shadow-sm">
        <h3 className="text-lg font-medium mb-3 text-slate-700">
          Filter Options
        </h3>
        <div className="flex flex-wrap gap-x-6 gap-y-4 items-center">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="lowStockFilter"
              checked={filterLowStock}
              onChange={(e) => setFilterLowStock(e.target.checked)}
              className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
            />
            <label
              htmlFor="lowStockFilter"
              className="ml-2 block text-sm text-gray-900"
            >
              Low Stock Only
            </label>
          </div>
          {/* Placeholder for Category filter */}
          <div className="text-sm text-gray-400 italic">
            (Category filter coming soon)
          </div>
          {/* Placeholder for Warehouse filter */}
          <div className="text-sm text-gray-400 italic">
            (Warehouse filter coming soon)
          </div>
        </div>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableCaption>A list of your inventory items.</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">SKU</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created At</TableHead>
              {isAdmin && <TableHead className="text-right">Actions</TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredItems.length > 0 ? (
              filteredItems.map((item: Item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">
                    {item.sku ?? "N/A"}
                  </TableCell>
                  <TableCell>{item.name}</TableCell>
                  <TableCell
                    className="max-w-[200px] truncate"
                    title={item.description ?? ""}
                  >
                    {item.description ?? "-"}
                  </TableCell>
                  <TableCell>{item.status ?? "N/A"}</TableCell>
                  <TableCell>
                    {new Date(item.createdAt).toLocaleDateString()}
                  </TableCell>
                  {isAdmin && (
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            className="h-8 w-8 p-0 text-muted-foreground hover:bg-slate-100 hover:text-muted-foreground"
                          >
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem
                            onClick={() => handleEditClick(item)}
                          >
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleDeleteClick(item)}
                            className="text-red-600 focus:text-red-700 focus:bg-red-50"
                          >
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  )}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={isAdmin ? 6 : 5}
                  className="h-24 text-center"
                >
                  No items found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Render the Create Item Dialog */}
      <CreateItemDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
      />

      {/* Render the Edit Item Dialog */}
      <EditItemDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        item={selectedItemForEdit}
      />

      {/* Render the Delete Item Dialog */}
      <DeleteItemDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        item={selectedItemForDelete}
      />
    </div>
  );
}
