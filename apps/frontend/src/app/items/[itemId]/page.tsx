"use client";

import React, { useEffect, useState } from "react"; // Added useState
import { useParams } from "next/navigation"; // To get itemId from route
import { useQuery, useQueryClient } from "@tanstack/react-query"; // Added useQueryClient
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { fetchWithAuth } from "@/lib/api";
import { usePageTitle } from "@/components/providers/PageTitleContext";
import { Button } from "@/components/ui/button";
import { Edit, ClipboardEdit, History, Printer } from "lucide-react"; // Icons
import { EditItemDialog } from "@/components/items/EditItemDialog"; // Import the dialog

// Define the expected structure of a single Item (can be expanded based on API response)
// This should align with the Item interface in items/page.tsx and backend DTOs
interface ItemDetail {
  id: string;
  name: string;
  sku: string | null;
  description: string | null;
  unitOfMeasure?: string | null;
  defaultCost?: number | null;
  lowStockThreshold?: number | null;
  status?: string;
  createdAt: string;
  updatedAt: string;
  quantityOnHand?: number; // From our previous backend change
  // TODO: Add fields for pallet locations if the API provides them directly
  // e.g., palletLocations: Array<{ warehouseName: string; locationName: string; palletId: string; quantity: number }>;
  // Added fields to match potential backend ItemDetailResponse
  numberOfPallets?: number;
  stockByLocationAndPallet?: Array<{
    palletId: string;
    locationName: string | null;
    warehouseName: string | null;
    quantity: number;
  }>;
}

export default function ItemDetailPage() {
  const params = useParams();
  const itemId = params.itemId as string;
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();
  const { setTitle } = usePageTitle();
  const queryClient = useQueryClient(); // For invalidating queries

  // State for managing the edit dialog
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [itemToEdit, setItemToEdit] = useState<ItemDetail | null>(null);

  const {
    data: item,
    isLoading,
    isError,
    error,
  } = useQuery<ItemDetail, Error>({
    queryKey: ["item", itemId, currentWarehouse?.id, appToken],
    queryFn: async () => {
      if (!itemId) throw new Error("Item ID is required.");
      if (!currentWarehouse) throw new Error("No warehouse selected.");
      // Do not throw if appToken is not yet available but auth is loading
      if (!appToken && !isAuthLoading)
        throw new Error("Authentication token not available.");
      // Ensure queryFn doesn't run if appToken is missing and auth isn't just loading
      if (!appToken) return Promise.reject(new Error("Token not ready"));
      // Removed <ItemDetail> generic as fetchWithAuth is not generic
      return fetchWithAuth(
        `/api/items/${itemId}?warehouseId=${currentWarehouse.id}`,
        { token: appToken }
      );
    },
    enabled: !!itemId && !!appToken && !!currentWarehouse?.id && !isAuthLoading, // Enable query only when itemId and appToken are available and auth is not loading
  });

  // Effect to update page title when item data is successfully fetched
  useEffect(() => {
    if (item) {
      setTitle(`${item.name} - ${item.sku || "N/A"}`);
    }
  }, [item, setTitle]);

  if (isLoading || isAuthLoading) {
    return <div className="container mx-auto p-4">Loading item details...</div>;
  }

  if (isError) {
    return (
      <div className="container mx-auto p-4 text-red-500">
        Error loading item: {error?.message || "Unknown error"}
      </div>
    );
  }

  if (!item) {
    return <div className="container mx-auto p-4">Item not found.</div>;
  }

  const handleEditMasterData = () => {
    if (item) {
      setItemToEdit(item); // Set the current item for editing
      setIsEditDialogOpen(true); // Open the dialog
    } else {
      console.error("Cannot edit: item data is not available.");
      // Optionally, show a toast error to the user
    }
  };

  return (
    <>
      <div className="container mx-auto p-4 space-y-6">
        {/* App Header (Implicitly handled by PageTitleContext and layout) */}
        {/* Action icon for Edit (Master Data) - Placed near where title would be or in a specific actions bar */}
        <div className="flex justify-end mb-4">
          <Button
            variant="outline"
            onClick={handleEditMasterData}
            className="flex items-center"
          >
            <Edit className="mr-2 h-4 w-4" /> Edit Item
          </Button>
        </div>
        {/* Main Content Area */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Left Column: Master Data & Actions */}
          <div className="md:col-span-1 space-y-6">
            <div className="bg-white p-4 shadow rounded-lg">
              <h2 className="text-xl font-semibold mb-3 text-slate-700">
                Item Details
              </h2>
              <p>
                <strong>Name:</strong> {item.name}
              </p>
              <p>
                <strong>SKU:</strong> {item.sku || "N/A"}
              </p>
              <p>
                <strong>Description:</strong> {item.description || "N/A"}
              </p>
              <p>
                <strong>Unit of Measure:</strong> {item.unitOfMeasure || "N/A"}
              </p>
              <p>
                <strong>Default Cost:</strong>{" "}
                {item.defaultCost !== null && item.defaultCost !== undefined
                  ? `$${item.defaultCost.toFixed(2)}`
                  : "N/A"}
              </p>
              <p>
                <strong>Low Stock Threshold:</strong>{" "}
                {item.lowStockThreshold !== null
                  ? item.lowStockThreshold
                  : "N/A"}
              </p>
              <p>
                <strong>Status:</strong> {item.status || "N/A"}
              </p>
            </div>

            <div className="bg-white p-4 shadow rounded-lg">
              <h2 className="text-xl font-semibold mb-3 text-slate-700">
                Actions
              </h2>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  className="w-full flex items-center justify-start"
                >
                  <ClipboardEdit className="mr-2 h-4 w-4" /> Adjust Inventory
                  (Cycle Count)
                </Button>
                <Button
                  variant="outline"
                  className="w-full flex items-center justify-start"
                >
                  <History className="mr-2 h-4 w-4" /> View Transaction History
                </Button>
                <Button
                  variant="outline"
                  className="w-full flex items-center justify-start"
                >
                  <Printer className="mr-2 h-4 w-4" /> Print Item Label
                </Button>
              </div>
            </div>
          </div>
          {/* Right Column: Inventory Summary & Locations List */}
          <div className="md:col-span-2 space-y-6">
            <div className="bg-white p-6 shadow rounded-lg">
              <h2 className="text-xl font-semibold mb-3 text-slate-700">
                Current Inventory
              </h2>
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <p className="text-3xl font-bold text-indigo-600">
                    {item.quantityOnHand ?? 0}
                  </p>
                  <p className="text-sm text-gray-500">
                    Total Quantity Available
                  </p>
                </div>
                <div>
                  <p className="text-3xl font-bold text-indigo-600">
                    {item.numberOfPallets ?? "--"}
                  </p>
                  <p className="text-sm text-gray-500">Number of Pallets</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-4 shadow rounded-lg">
              <h2 className="text-xl font-semibold mb-3 text-slate-700">
                Stock by Location & Pallet
              </h2>
              <div className="max-h-80 overflow-y-auto border rounded-md p-2">
                {/* Placeholder for list */}
                {item.stockByLocationAndPallet &&
                item.stockByLocationAndPallet.length > 0 ? (
                  <ul className="space-y-2">
                    {item.stockByLocationAndPallet.map((stock, index) => (
                      <li
                        key={`${stock.palletId}-${index}`}
                        className="text-sm p-2 border-b last:border-b-0 hover:bg-slate-50 rounded"
                      >
                        <span className="font-medium text-slate-700">
                          Pallet:
                        </span>{" "}
                        {stock.palletId}
                        <br />
                        <span className="font-medium text-slate-600">
                          Location:
                        </span>{" "}
                        {stock.locationName || "N/A"} (
                        {stock.warehouseName || "N/A"})<br />
                        <span className="font-medium text-slate-500">
                          Quantity:
                        </span>{" "}
                        <strong className="text-indigo-600">
                          {stock.quantity}
                        </strong>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-sm text-gray-500 p-4 text-center">
                    No detailed stock by location and pallet information
                    available.
                  </p>
                )}
              </div>{" "}
              {/* This closes max-h-80 overflow-y-auto */}
            </div>{" "}
            {/* This closes bg-white p-4 shadow rounded-lg for Stock by Location */}
          </div>{" "}
          {/* This closes md:col-span-2 space-y-6 */}
        </div>{" "}
        {/* This closes grid grid-cols-1 md:grid-cols-3 gap-6 */}
      </div>{" "}
      {/* This closes container mx-auto p-4 space-y-6 */}
      {itemToEdit && (
        <EditItemDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          item={{
            id: itemToEdit.id,
            name: itemToEdit.name,
            sku: itemToEdit.sku,
            description: itemToEdit.description,
            unitOfMeasure: itemToEdit.unitOfMeasure,
            defaultCost: itemToEdit.defaultCost,
            lowStockThreshold: itemToEdit.lowStockThreshold,
            status: itemToEdit.status,
            createdAt: itemToEdit.createdAt, // Pass createdAt
            updatedAt: itemToEdit.updatedAt, // Pass updatedAt
          }}
          onSuccess={() => {
            queryClient.invalidateQueries({
              queryKey: ["item", itemId, appToken],
            });
            setIsEditDialogOpen(false); // Close dialog
          }}
        />
      )}
    </>
  );
  // The previous replacement added <>
  // return (
  //   <>
  //     <div className="container mx-auto p-4 space-y-6">
  //       {/* ... content ... */}
  //     </div>
  //     {itemToEdit && <EditItemDialog ... />}
  //   </>
  // );
}
