import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@/lib/test-utils';
import ItemsPage from './page';
import * as api from '@/lib/api';
import { CreateItemDialog } from '@/components/items/CreateItemDialog';

// --- Mocks ---
vi.mock('@/lib/api');
vi.mock('@/components/items/CreateItemDialog', () => ({
  CreateItemDialog: vi.fn(({ open }) => {
    return open ? <div data-testid="mock-create-item-dialog">Mock Dialog</div> : null;
  }),
}));

const mockItems = [
  {
    id: 'item1',
    name: 'Test Item 1',
    sku: 'T1',
    description: 'Desc 1',
    unitOfMeasure: 'Each',
    lowStockThreshold: 5,
    status: 'Active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'item2',
    name: 'Test Item 2',
    sku: 'T2',
    description: 'Desc 2',
    unitOfMeasure: 'Case',
    lowStockThreshold: 10,
    status: 'Active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

const mockedFetchWithAuth = vi.mocked(api.fetchWithAuth);
const MockedCreateItemDialog = vi.mocked(CreateItemDialog);

describe('ItemsPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockedFetchWithAuth.mockResolvedValue(mockItems);
  });

  it('should render the items table and a "Create Item" button', async () => {
    render(<ItemsPage />);

    // Wait for the items to be loaded and displayed
    await waitFor(() => {
      expect(screen.getByText('Test Item 1')).toBeInTheDocument();
      expect(screen.getByText('Test Item 2')).toBeInTheDocument();
    });

    expect(screen.getByRole('button', { name: /create item/i })).toBeInTheDocument();
    expect(screen.queryByTestId('mock-create-item-dialog')).not.toBeInTheDocument();
  });

  it('should open the CreateItemDialog when the button is clicked', async () => {
    render(<ItemsPage />);

    // Wait for the page to load
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /create item/i })).toBeInTheDocument();
    });

    const createButton = screen.getByRole('button', { name: /create item/i });
    fireEvent.click(createButton);

    await waitFor(() => {
      expect(screen.getByTestId('mock-create-item-dialog')).toBeInTheDocument();
    });

    // Check that the mocked dialog was rendered with the correct props
    expect(MockedCreateItemDialog).toHaveBeenCalledWith(
      expect.objectContaining({ open: true }),
      expect.anything()
    );
  });
});
