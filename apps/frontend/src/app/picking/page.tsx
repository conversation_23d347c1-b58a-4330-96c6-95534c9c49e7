"use client";

import { useEffect } from "react";
import { usePageTitle } from "@/components/providers/PageTitleContext";
import PickingScreen from "@/components/picking/PickingScreen";

export default function PickingPage() {
  const { setTitle } = usePageTitle();

  useEffect(() => {
    setTitle("Picking");
  }, [setTitle]);

  return (
    <div className="container mx-auto p-4">
      <PickingScreen />
    </div>
  );
}
