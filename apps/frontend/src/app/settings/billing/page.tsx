"use client";

import React, { useEffect } from 'react';
import { usePageTitle } from '@/components/providers/PageTitleContext';
import { useAuth, Role } from '@/components/providers/auth-provider';
import { useRouter } from 'next/navigation';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';

export default function BillingSettingsPage() {
  const { setTitle } = usePageTitle();
  const { appUser, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    setTitle('Subscription & Billing');
  }, [setTitle]);

  useEffect(() => {
    if (!isLoading && appUser?.role !== Role.TENANT_ADMIN) {
      router.push('/settings'); // Redirect if not tenant admin
    }
  }, [appUser, isLoading, router]);

  if (isLoading || (!isLoading && appUser?.role !== Role.TENANT_ADMIN)) {
    return <div className="container mx-auto p-4">Loading or unauthorized...</div>; 
  }

  return (
    <div className="container mx-auto max-w-3xl py-8 px-4 md:px-0">
      <Breadcrumb className="mb-8">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/settings">Settings</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Subscription & Billing</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <h1 className="text-3xl font-bold mb-8">Subscription & Billing</h1>
      <p>Manage your subscription plan, view billing history, and update payment methods.</p>
      {/* Placeholder for subscription details, billing history, payment method management */}
      {/* This would typically integrate with a payment provider like Stripe */}
    </div>
  );
}
