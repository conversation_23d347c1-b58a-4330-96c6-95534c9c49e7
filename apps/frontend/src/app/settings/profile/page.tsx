"use client";

import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import { usePageTitle } from "@/components/providers/PageTitleContext";
import { useAuth } from "@/components/providers/auth-provider";
import { useUpdateProfile } from "@/hooks/api/useProfile";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { User, Mail, Save } from "lucide-react";

const profileFormSchema = z.object({
  name: z
    .string()
    .min(1, { message: "Name must be at least 1 character long." })
    .max(100, { message: "Name must be at most 100 characters long." }),
});

type ProfileFormData = z.infer<typeof profileFormSchema>;

export default function ProfileSettingsPage() {
  const { setTitle } = usePageTitle();
  const { appUser, isLoading } = useAuth();
  const updateProfileMutation = useUpdateProfile();

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: "",
    },
  });

  useEffect(() => {
    setTitle("Edit Profile");
  }, [setTitle]);

  // Update form when user data loads
  useEffect(() => {
    if (appUser) {
      form.reset({
        name: appUser.name || "",
      });
    }
  }, [appUser, form]);

  const onSubmit = async (data: ProfileFormData) => {
    try {
      await updateProfileMutation.mutateAsync(data);
      toast.success("Profile updated successfully!");
    } catch (error: any) {
      const errorMsg = error.message || "Failed to update profile.";
      toast.error(errorMsg);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto max-w-3xl py-8 px-4 md:px-0">
        <div className="text-center">Loading profile...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-3xl py-8 px-4 md:px-0">
      <Breadcrumb className="mb-8">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/settings">Settings</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Edit Profile</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <h1 className="text-3xl font-bold mb-8">Edit Profile</h1>

      <div className="space-y-6">
        {/* Profile Information Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="mr-2 h-5 w-5" />
              Profile Information
            </CardTitle>
            <CardDescription>
              Update your personal information and display preferences.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Display Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter your display name"
                          {...field}
                          className="text-base md:text-sm" // Larger text for mobile
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Separator />

                {/* Read-only email field */}
                <div className="space-y-2">
                  <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Email Address
                  </label>
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <Input
                      value={appUser?.email || ""}
                      disabled
                      className="bg-muted text-muted-foreground"
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Email address cannot be changed. Contact support if you need
                    to update your email.
                  </p>
                </div>

                <div className="flex justify-end">
                  <Button
                    type="submit"
                    disabled={updateProfileMutation.isPending}
                    className="min-h-[44px] px-6" // Larger touch target for mobile
                  >
                    <Save className="mr-2 h-4 w-4" />
                    {updateProfileMutation.isPending
                      ? "Saving..."
                      : "Save Changes"}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
