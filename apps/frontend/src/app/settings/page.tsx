"use client";

import React, { useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { usePageTitle } from "@/components/providers/PageTitleContext";
import { useAuth, Role } from "@/components/providers/auth-provider";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  User,
  Briefcase,
  Settings as SettingsIcon,
  HelpCircle,
  LogOut,
  Bell,
  Palette,
  FileText,
  Shield,
  CheckCircle,
  Clock,
} from "lucide-react";

interface SettingsSectionProps {
  title: string;
  icon?: React.ElementType;
  children: React.ReactNode;
}

const SettingsSection: React.FC<SettingsSectionProps> = ({
  title,
  icon: Icon,
  children,
}) => (
  <Card className="mb-6">
    <CardHeader>
      <CardTitle className="flex items-center">
        {Icon && <Icon className="mr-2 h-5 w-5 text-primary" />}
        {title}
      </CardTitle>
    </CardHeader>
    <CardContent>{children}</CardContent>
  </Card>
);

interface SettingsLinkProps {
  href: string;
  label: string;
  icon?: React.ElementType;
  description?: string;
  external?: boolean;
  status?: "functional" | "placeholder";
}

const SettingsLink: React.FC<SettingsLinkProps> = ({
  href,
  label,
  icon: Icon,
  description,
  external,
  status = "functional",
}) => (
  <Link
    href={href}
    className="block p-3 hover:bg-muted rounded-md transition-colors group"
    target={external ? "_blank" : undefined}
    rel={external ? "noopener noreferrer" : undefined}
  >
    <>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {Icon && <Icon className="mr-3 h-5 w-5 text-muted-foreground" />}
          <span className="font-medium">{label}</span>
          {status === "functional" && (
            <span title="Functional">
              <CheckCircle className="ml-2 h-4 w-4 text-green-600" />
            </span>
          )}
          {status === "placeholder" && (
            <span title="Coming Soon">
              <Clock className="ml-2 h-4 w-4 text-amber-600" />
            </span>
          )}
        </div>
        <SettingsIcon className="h-4 w-4 text-muted-foreground transform transition-transform group-hover:translate-x-1" />
      </div>
      {description && (
        <p className="text-sm text-muted-foreground mt-1 ml-8">{description}</p>
      )}
    </>
  </Link>
);

export default function SettingsPage() {
  const { setTitle } = usePageTitle();
  const { appUser, logout, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    setTitle("Settings");
  }, [setTitle]);

  const handleLogout = async () => {
    await logout();
    // The logout function in AuthProvider will handle the redirect to /auth/welcome
  };

  if (isLoading) {
    return <div className="container mx-auto p-4">Loading settings...</div>;
  }

  const isTenantAdmin = appUser?.role === Role.TENANT_ADMIN;

  return (
    <div className="container mx-auto max-w-3xl pt-8 px-4 md:px-0">
      <h1 className="text-3xl font-bold mb-4">Settings</h1>

      {/* Status Legend */}
      <div className="mb-8 p-4 bg-muted/50 rounded-lg">
        <p className="text-sm font-medium mb-2">Feature Status:</p>
        <div className="flex flex-wrap gap-4 text-sm">
          <div className="flex items-center">
            <CheckCircle className="h-4 w-4 text-green-600 mr-1" />
            <span>Functional</span>
          </div>
          <div className="flex items-center">
            <Clock className="h-4 w-4 text-amber-600 mr-1" />
            <span>Coming Soon</span>
          </div>
        </div>
      </div>

      <SettingsSection title="My Profile" icon={User}>
        {appUser && (
          <div className="mb-4 p-3 bg-muted/50 rounded-md">
            <p className="text-sm font-medium">{appUser.name || "User"}</p>
            <p className="text-sm text-muted-foreground">{appUser.email}</p>
            <p className="text-sm text-muted-foreground capitalize">
              Role: {appUser.role?.toLowerCase().replace("_", " ") || "N/A"}
            </p>
          </div>
        )}
        <SettingsLink
          href="/settings/profile"
          label="Edit Profile"
          description="Update your personal information."
          status="functional"
        />
      </SettingsSection>

      {isTenantAdmin && (
        <SettingsSection title="Workspace" icon={Briefcase}>
          <SettingsLink
            href="/settings/company"
            label="Company Settings"
            description="Manage your company details and branding."
            status="placeholder"
          />
          <SettingsLink
            href="/settings/users"
            label="User Management"
            description="Invite and manage team members."
            status="placeholder"
          />
          <SettingsLink
            href="/settings/warehouses"
            label="Warehouse Management"
            description="Configure warehouses and locations."
            status="functional"
          />
          <SettingsLink
            href="/settings/billing"
            label="Subscription & Billing"
            description="Manage your subscription and payment methods."
            status="placeholder"
          />
        </SettingsSection>
      )}

      <SettingsSection title="Application" icon={SettingsIcon}>
        <SettingsLink
          href="/settings/notifications"
          label="Notifications"
          icon={Bell}
          description="Configure your notification preferences."
          status="placeholder"
        />
        <SettingsLink
          href="/settings/appearance"
          label="Appearance"
          icon={Palette}
          description="Customize the look and feel of the app."
          status="placeholder"
        />
      </SettingsSection>

      <SettingsSection title="Support & Legal" icon={HelpCircle}>
        <SettingsLink
          href="/support"
          label="Help & Support Center"
          description="Find answers to your questions."
          external
        />
        <SettingsLink
          href="/terms"
          label="Terms of Service"
          icon={FileText}
          description="Read our terms of service."
          external
        />
        <SettingsLink
          href="/privacy"
          label="Privacy Policy"
          icon={Shield}
          description="Understand how we handle your data."
          external
        />
      </SettingsSection>

      <Separator className="my-8" />

      <div className="flex justify-end">
        <Button
          variant="outline"
          onClick={handleLogout}
          disabled={isLoading}
          className="min-h-[44px] px-6" // Larger touch target for mobile
        >
          <LogOut className="mr-2 h-4 w-4" /> Logout
        </Button>
      </div>
    </div>
  );
}
