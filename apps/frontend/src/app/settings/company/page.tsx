"use client";

import React, { useEffect } from 'react';
import { usePageTitle } from '@/components/providers/PageTitleContext';
import { useAuth, Role } from '@/components/providers/auth-provider';
import { useRouter } from 'next/navigation';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';

export default function CompanySettingsPage() {
  const { setTitle } = usePageTitle();
  const { appUser, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    setTitle('Company Settings');
  }, [setTitle]);

  useEffect(() => {
    if (!isLoading && appUser?.role !== Role.TENANT_ADMIN) {
      router.push('/settings'); // Redirect if not tenant admin
    }
  }, [appUser, isLoading, router]);

  if (isLoading || (!isLoading && appUser?.role !== Role.TENANT_ADMIN)) {
    // Render loading state or null while redirecting or if not authorized
    return <div className="container mx-auto p-4">Loading or unauthorized...</div>; 
  }

  return (
    <div className="container mx-auto max-w-3xl py-8 px-4 md:px-0">
      <Breadcrumb className="mb-8">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/settings">Settings</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Company Settings</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <h1 className="text-3xl font-bold mb-8">Company Settings</h1>
      <p>Company details, branding, and other organization-level settings will go here.</p>
      {/* Placeholder for company settings form */}
    </div>
  );
}
