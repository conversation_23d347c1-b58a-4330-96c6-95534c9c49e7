"use client";

import React, { useEffect } from "react";
import { usePageTitle } from "@/components/providers/PageTitleContext";
import { useAuth } from "@/components/providers/auth-provider";
import { Role } from "@quildora/types";
import { useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { fetchWithAuth } from "@/lib/api";

import { User } from "@quildora/types";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  PlusCircle,
  Mail,
  Copy,
  MoreHorizontal,
  RefreshCw,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { useState } from "react";

export default function UserManagementPage() {
  const { setTitle } = usePageTitle();
  const { appUser, isLoading: isAuthLoading, appToken } = useAuth();
  const router = useRouter();

  // Invitation management state
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [inviteEmail, setInviteEmail] = useState("");
  const [inviteRole, setInviteRole] = useState<Role>(Role.WAREHOUSE_MEMBER);
  const [isInviting, setIsInviting] = useState(false);

  const {
    data: usersData = [],
    isLoading: isUsersLoading,
    error: usersError,
    refetch: refetchUsers,
  } = useQuery<User[]>({
    queryKey: ["tenantUsers", appToken],
    queryFn: () => fetchWithAuth("/api/users", { token: appToken }),
    enabled: !!appToken && appUser?.role === Role.TENANT_ADMIN, // Fetch only if token exists and user is admin
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    staleTime: 10 * 60 * 1000, // 10 minutes for user management data
    gcTime: 15 * 60 * 1000, // 15 minutes cache
  });

  // Fetch invitations
  const {
    data: invitationsData = [],
    isLoading: isInvitationsLoading,
    error: invitationsError,
    refetch: refetchInvitations,
  } = useQuery<any[]>({
    queryKey: ["tenantInvitations", appToken],
    queryFn: () => fetchWithAuth("/api/invitations", { token: appToken }),
    enabled: !!appToken && appUser?.role === Role.TENANT_ADMIN,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    staleTime: 5 * 60 * 1000, // 5 minutes for invitations
    gcTime: 10 * 60 * 1000, // 10 minutes cache
  });

  useEffect(() => {
    setTitle("User Management");
  }, [setTitle]);

  useEffect(() => {
    if (!isAuthLoading && appUser?.role !== Role.TENANT_ADMIN) {
      router.push("/settings"); // Redirect if not tenant admin
    }
  }, [appUser, isAuthLoading, router]);

  // Handle invitation creation
  const handleCreateInvitation = async () => {
    if (!inviteEmail.trim()) {
      toast.error("Please enter an email address");
      return;
    }

    setIsInviting(true);
    try {
      await fetchWithAuth("/api/invitations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: inviteEmail.trim(),
          role: inviteRole,
          warehouseIds: [], // Empty array means all warehouses the admin has access to
        }),
        token: appToken,
      });

      toast.success(`Invitation sent to ${inviteEmail}`);
      setInviteEmail("");
      setInviteRole(Role.WAREHOUSE_MEMBER);
      setIsInviteDialogOpen(false);
      refetchInvitations();
    } catch (error) {
      console.error("Failed to create invitation:", error);
      toast.error("Failed to send invitation. Please try again.");
    } finally {
      setIsInviting(false);
    }
  };

  // Handle invitation resend
  const handleResendInvitation = async (
    invitationId: string,
    email: string
  ) => {
    try {
      await fetchWithAuth(`/api/invitations/${invitationId}/resend`, {
        method: "POST",
        token: appToken,
      });
      toast.success(`Invitation resent to ${email}`);
      refetchInvitations();
    } catch (error) {
      console.error("Failed to resend invitation:", error);
      toast.error("Failed to resend invitation. Please try again.");
    }
  };

  // Handle invitation cancellation
  const handleCancelInvitation = async (
    invitationId: string,
    email: string
  ) => {
    try {
      await fetchWithAuth(`/api/invitations/${invitationId}`, {
        method: "DELETE",
        token: appToken,
      });
      toast.success(`Invitation to ${email} cancelled`);
      refetchInvitations();
    } catch (error) {
      console.error("Failed to cancel invitation:", error);
      toast.error("Failed to cancel invitation. Please try again.");
    }
  };

  // Handle copying invitation code
  const handleCopyInvitationCode = (code: string) => {
    navigator.clipboard.writeText(code);
    toast.success("Invitation code copied to clipboard");
  };

  if (
    isAuthLoading ||
    (!isAuthLoading && appUser?.role !== Role.TENANT_ADMIN)
  ) {
    return (
      <div className="container mx-auto p-4">Loading or unauthorized...</div>
    );
  }

  return (
    <div className="container mx-auto max-w-3xl py-8 px-4 md:px-0">
      <Breadcrumb className="mb-8">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/settings">Settings</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>User Management</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">User Management</h1>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Current Users</CardTitle>
            <CardDescription>Manage users within your tenant.</CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                refetchUsers();
                refetchInvitations();
              }}
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
            <Dialog
              open={isInviteDialogOpen}
              onOpenChange={setIsInviteDialogOpen}
            >
              <DialogTrigger asChild>
                <Button>
                  <PlusCircle className="mr-2 h-4 w-4" /> Invite User
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Invite New User</DialogTitle>
                  <DialogDescription>
                    Send an invitation to a new team member. They'll receive an
                    email with instructions to join.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={inviteEmail}
                      onChange={(e) => setInviteEmail(e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="role">Role</Label>
                    <Select
                      value={inviteRole}
                      onValueChange={(value) => setInviteRole(value as Role)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={Role.WAREHOUSE_MEMBER}>
                          Warehouse Member
                        </SelectItem>
                        <SelectItem value={Role.WAREHOUSE_MANAGER}>
                          Warehouse Manager
                        </SelectItem>
                        <SelectItem value={Role.TENANT_ADMIN}>
                          Tenant Admin
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsInviteDialogOpen(false)}
                    disabled={isInviting}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleCreateInvitation}
                    disabled={isInviting}
                  >
                    {isInviting ? "Sending..." : "Send Invitation"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {isUsersLoading && <p>Loading users...</p>}
          {usersError && (
            <p className="text-red-500">
              Error fetching users: {usersError.message}
            </p>
          )}
          {usersData && usersData.length === 0 && <p>No users found.</p>}
          {usersData && usersData.length > 0 && (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {usersData.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>{user.name || "N/A"}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>{user.role}</TableCell>
                    <TableCell>{user.status}</TableCell>
                    <TableCell>{/* Placeholder for actions */}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Pending Invitations Card */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Pending Invitations
          </CardTitle>
          <CardDescription>
            Manage pending invitations that haven't been accepted yet.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isInvitationsLoading && <p>Loading invitations...</p>}
          {invitationsError && (
            <p className="text-red-500">
              Error fetching invitations: {invitationsError.message}
            </p>
          )}
          {invitationsData && invitationsData.length === 0 && (
            <p className="text-gray-500">No pending invitations.</p>
          )}
          {invitationsData && invitationsData.length > 0 && (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Invitation Code</TableHead>
                  <TableHead>Expires</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invitationsData.map((invitation) => (
                  <TableRow key={invitation.id}>
                    <TableCell>{invitation.email}</TableCell>
                    <TableCell>
                      <Badge variant="outline">{invitation.role}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          invitation.status === "PENDING"
                            ? "default"
                            : invitation.status === "ACCEPTED"
                            ? "secondary"
                            : "destructive"
                        }
                      >
                        {invitation.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                          {invitation.invitationCode}
                        </code>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            handleCopyInvitationCode(invitation.invitationCode)
                          }
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell>
                      {new Date(invitation.expiresAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {invitation.status === "PENDING" && (
                            <>
                              <DropdownMenuItem
                                onClick={() =>
                                  handleResendInvitation(
                                    invitation.id,
                                    invitation.email
                                  )
                                }
                              >
                                <Mail className="mr-2 h-4 w-4" />
                                Resend
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() =>
                                  handleCancelInvitation(
                                    invitation.id,
                                    invitation.email
                                  )
                                }
                                className="text-red-600"
                              >
                                Cancel
                              </DropdownMenuItem>
                            </>
                          )}
                          {invitation.status !== "PENDING" && (
                            <DropdownMenuItem
                              onClick={() =>
                                handleCancelInvitation(
                                  invitation.id,
                                  invitation.email
                                )
                              }
                              className="text-red-600"
                            >
                              Remove
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
