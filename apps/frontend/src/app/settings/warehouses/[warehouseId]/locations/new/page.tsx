"use client";

import React, { useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { usePageTitle } from '@/components/providers/PageTitleContext';
import { useAuth, Role } from '@/components/providers/auth-provider';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { CreateLocationForm } from '@/components/locations/CreateLocationForm';

export default function AddNewLocationPage() {
  const { setTitle } = usePageTitle();
  const params = useParams();
  const warehouseId = params.warehouseId as string;
  const warehouseName = params.warehouseName as string;
  const { appUser, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (warehouseName) {
      setTitle(`Add New Location: Warehouse ${warehouseName}`);
    } else {
      setTitle('Add New Location');
    }
  }, [setTitle, warehouseName]);

  useEffect(() => {
    if (!isLoading && appUser?.role !== Role.TENANT_ADMIN) {
      router.push('/settings'); // Redirect if not tenant admin
    }
  }, [appUser, isLoading, router]);

  if (isLoading || (!isLoading && appUser?.role !== Role.TENANT_ADMIN)) {
    return <div className="container mx-auto p-4">Loading or unauthorized...</div>; 
  }

  return (
    <div className="container mx-auto max-w-3xl py-8 px-4 md:px-0">
      <Breadcrumb className="mb-8">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/settings">Settings</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/settings/warehouses">Warehouse Management</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/settings/warehouses/${warehouseId}/locations`}>Warehouse {warehouseName}</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/settings/warehouses/${warehouseId}/locations`}>Manage Locations</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Add New Location</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <h1 className="text-3xl font-bold mb-8">Add New Location <span className='text-muted-foreground text-2xl'>(Warehouse: {warehouseName})</span></h1>
      
      <CreateLocationForm warehouseId={warehouseId} />
    </div>
  );
}
