"use client";

import React, { useEffect, useState } from "react";
import Link from "next/link";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { usePageTitle } from "@/components/providers/PageTitleContext";
import { useAuth, Role } from "@/components/providers/auth-provider";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { PlusCircle, Edit, Trash2, AlertTriangle } from "lucide-react";
import { EditLocationDialog } from "@/components/locations/EditLocationDialog";
import { DeleteLocationDialog } from "@/components/locations/DeleteLocationDialog";
import { BulkCreateLocationsDialog } from "@/components/locations/BulkCreateLocationsDialog";
import { useQuery } from "@tanstack/react-query";
import { fetchWithAuth } from "@/lib/api";
import { useWarehouses } from "@/hooks/api";
import { Location } from "@quildora/types";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export default function ManageWarehouseLocationsPage() {
  const { setTitle } = usePageTitle();
  const params = useParams();
  const warehouseId = params.warehouseId as string;
  const { appUser, isLoading, appToken } = useAuth();
  const router = useRouter();

  // Fetch warehouses to get the warehouse name
  const { data: warehouses, isLoading: isLoadingWarehouses } = useWarehouses();
  const currentWarehouse = warehouses?.find((w) => w.id === warehouseId);
  const warehouseName = currentWarehouse?.name;

  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(
    null
  );
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [locationToDelete, setLocationToDelete] = useState<Location | null>(
    null
  );
  const [isBulkCreateDialogOpen, setIsBulkCreateDialogOpen] = useState(false);

  const handleEditLocation = (location: Location) => {
    setSelectedLocation(location);
    setIsEditDialogOpen(true);
  };

  const handleDeleteLocation = (location: Location) => {
    setLocationToDelete(location);
    setIsDeleteDialogOpen(true);
  };

  const {
    data: locations,
    isLoading: isLoadingLocations,
    error: locationsError,
  } = useQuery<Location[], Error>({
    queryKey: ["locations", warehouseId],
    queryFn: async () => {
      if (!appToken || !warehouseId)
        throw new Error("Not authorized or warehouse ID missing");
      // The backend findAll for locations implicitly filters by tenantId from currentUser
      // and can be filtered by warehouseId if we pass it as a query param.
      // However, the current locationsService.findAll filters by warehouseId based on user permissions
      // or if no warehouseId is provided, it lists all accessible.
      // For this page, we specifically want locations for *this* warehouseId.
      // We need to ensure the backend /api/locations can filter by a specific warehouseId if provided.
      // Backend now supports ?warehouseId=xxx for filtering
      return fetchWithAuth(`/api/locations?warehouseId=${warehouseId}`, {
        token: appToken,
      });
    },
    enabled: !!appToken && !!warehouseId && !isLoading, // Only run query if token and warehouseId are available and auth is not loading
  });

  useEffect(() => {
    if (warehouseName) {
      setTitle(`Manage Locations: ${warehouseName}`);
    } else if (isLoadingWarehouses) {
      setTitle("Manage Locations: Loading...");
    } else {
      setTitle("Manage Warehouse Locations");
    }
  }, [setTitle, warehouseName, isLoadingWarehouses]);

  useEffect(() => {
    if (!isLoading && appUser?.role !== Role.TENANT_ADMIN) {
      router.push("/settings"); // Redirect if not tenant admin
    }
  }, [appUser, isLoading, router]);

  if (isLoading || (!isLoading && appUser?.role !== Role.TENANT_ADMIN)) {
    return (
      <div className="container mx-auto p-4">Loading or unauthorized...</div>
    );
  }

  return (
    <div className="container mx-auto max-w-3xl py-8 px-4 md:px-0">
      <Breadcrumb className="mb-8">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/settings">Settings</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/settings/warehouses">
              Warehouse Management
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/settings/warehouses`}>
              Warehouse Details
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Manage Locations</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">
          Manage Locations{" "}
          <span className="text-muted-foreground text-2xl">
            {warehouseName
              ? `(Warehouse: ${warehouseName})`
              : isLoadingWarehouses
              ? "(Loading...)"
              : "(Unknown Warehouse)"}
          </span>
        </h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setIsBulkCreateDialogOpen(true)}
            className="min-h-[44px]" // Larger touch target for mobile
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Bulk Add
          </Button>
          <Button asChild className="min-h-[44px]">
            <Link href={`/settings/warehouses/${warehouseId}/locations/new`}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add New Location
            </Link>
          </Button>
        </div>
      </div>
      {isLoadingLocations && <p>Loading locations...</p>}
      {locationsError && (
        <div className="text-red-600 bg-red-100 border border-red-400 p-4 rounded-md flex items-center">
          <AlertTriangle className="mr-2 h-5 w-5" />
          <p>Error loading locations: {locationsError.message}</p>
        </div>
      )}
      {!isLoadingLocations &&
        !locationsError &&
        locations &&
        locations.length === 0 && (
          <div className="border rounded-lg p-6 bg-card text-card-foreground mt-6 text-center">
            <p className="text-muted-foreground">
              No locations found for Warehouse {warehouseId}.
            </p>
            <p className="text-sm text-muted-foreground">
              Get started by adding a new location.
            </p>
          </div>
        )}
      {!isLoadingLocations &&
        !locationsError &&
        locations &&
        locations.length > 0 && (
          <div className="border rounded-lg mt-6">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created At</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {locations.map((location) => (
                  <TableRow key={location.id}>
                    <TableCell className="font-medium">
                      {location.name}
                    </TableCell>
                    <TableCell>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {location.category || "Storage"}
                      </span>
                    </TableCell>
                    <TableCell>{location.locationType}</TableCell>
                    <TableCell>{location.status}</TableCell>
                    <TableCell>
                      {new Date(location.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditLocation(location)}
                        className="mr-2"
                      >
                        <Edit className="h-4 w-4" />{" "}
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteLocation(location)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />{" "}
                        <span className="sr-only">Delete</span>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      {selectedLocation && (
        <EditLocationDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          location={selectedLocation}
          onSuccess={() => {
            // queryClient.invalidateQueries({ queryKey: ['locations', warehouseId] }); // Already handled in EditLocationDialog
            setIsEditDialogOpen(false); // Close dialog
            setSelectedLocation(null); // Clear selected location
          }}
        />
      )}
      {locationToDelete && (
        <DeleteLocationDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          location={locationToDelete}
          onSuccess={() => {
            // queryClient.invalidateQueries({ queryKey: ['locations', warehouseId] }); // Already handled in DeleteLocationDialog
            setIsDeleteDialogOpen(false);
            setLocationToDelete(null);
          }}
        />
      )}
      <BulkCreateLocationsDialog
        open={isBulkCreateDialogOpen}
        onOpenChange={setIsBulkCreateDialogOpen}
        warehouseId={warehouseId}
        warehouseName={warehouseName || "Loading..."}
        onSuccess={() => {
          setIsBulkCreateDialogOpen(false);
        }}
      />
    </div>
  );
}
