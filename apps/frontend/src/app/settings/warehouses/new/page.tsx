"use client";

import React, { useEffect } from 'react';
import { usePageTitle } from '@/components/providers/PageTitleContext';
import { useAuth, Role } from '@/components/providers/auth-provider';
import { useRouter } from 'next/navigation';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { CreateWarehouseForm } from '@/components/warehouses/CreateWarehouseForm';

export default function AddNewWarehousePage() {
  const { setTitle } = usePageTitle();
  const { appUser, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    setTitle('Add New Warehouse');
  }, [setTitle]);

  useEffect(() => {
    if (!isLoading && appUser?.role !== Role.TENANT_ADMIN) {
      router.push('/settings'); // Redirect if not tenant admin
    }
  }, [appUser, isLoading, router]);

  if (isLoading || (!isLoading && appUser?.role !== Role.TENANT_ADMIN)) {
    return <div className="container mx-auto p-4">Loading or unauthorized...</div>; 
  }

  return (
    <div className="container mx-auto max-w-3xl py-8 px-4 md:px-0">
      <Breadcrumb className="mb-8">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/settings">Settings</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/settings/warehouses">Warehouse Management</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Add New Warehouse</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <h1 className="text-3xl font-bold mb-8">Add New Warehouse</h1>
      
      <CreateWarehouseForm />
    </div>
  );
}
