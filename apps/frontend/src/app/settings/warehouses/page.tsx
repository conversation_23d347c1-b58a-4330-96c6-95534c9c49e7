"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { usePageTitle } from "@/components/providers/PageTitleContext";
import { useAuth, Role } from "@/components/providers/auth-provider";
import { useRouter } from "next/navigation";
import { WarehouseBreadcrumb } from "@/components/layout/WarehouseBreadcrumb";
import { Button } from "@/components/ui/button";
import { PlusCircle, MoreHorizontal } from "lucide-react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { fetchWithAuth } from "@/lib/api";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { EditWarehouseDialog } from "@/components/warehouses/EditWarehouseDialog";
import { DeleteWarehouseDialog } from "@/components/warehouses/DeleteWarehouseDialog";
import { Warehouse } from "@quildora/types";

export default function WarehouseManagementPage() {
  const { setTitle } = usePageTitle();
  const { appUser, appToken, isLoading: isAuthLoading } = useAuth();
  const router = useRouter();
  const queryClient = useQueryClient();

  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedWarehouseForEdit, setSelectedWarehouseForEdit] =
    useState<Warehouse | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedWarehouseForDelete, setSelectedWarehouseForDelete] =
    useState<Warehouse | null>(null);

  const {
    data: warehouses = [],
    isLoading: isLoadingWarehouses,
    isError,
    error,
  } = useQuery<Warehouse[], Error>({
    queryKey: ["warehouses", appToken],
    queryFn: async () => {
      if (!appToken && !isAuthLoading) {
        throw new Error("Authentication token not available.");
      }
      return fetchWithAuth("/api/warehouses", { token: appToken });
    },
    enabled: !isAuthLoading && !!appToken,
    retry: 1,
  });

  const handleEditClick = (warehouse: Warehouse) => {
    setSelectedWarehouseForEdit(warehouse);
    setIsEditDialogOpen(true);
  };

  const handleDeleteClick = (warehouse: Warehouse) => {
    setSelectedWarehouseForDelete(warehouse);
    setIsDeleteDialogOpen(true);
  };

  const onWarehouseUpdated = () => {
    queryClient.invalidateQueries({ queryKey: ["warehouses"] });
  };

  const onWarehouseDeleted = () => {
    queryClient.invalidateQueries({ queryKey: ["warehouses"] });
  };

  useEffect(() => {
    setTitle("Warehouse Management");
  }, [setTitle]);

  useEffect(() => {
    if (!isAuthLoading && appUser?.role !== Role.TENANT_ADMIN) {
      router.push("/settings"); // Redirect if not tenant admin
    }
  }, [appUser, isAuthLoading, router]);

  if (isAuthLoading || isLoadingWarehouses) {
    return <div className="container mx-auto p-4">Loading...</div>;
  }

  if (!isAuthLoading && appUser?.role !== Role.TENANT_ADMIN) {
    // This check is redundant if the effect above redirects, but good for safety
    return <div className="container mx-auto p-4">Unauthorized.</div>;
  }

  if (isError) {
    return (
      <div className="container mx-auto p-4 text-red-600">
        Error loading warehouses: {error?.message}
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-3xl py-8 px-4 md:px-0">
      <WarehouseBreadcrumb
        items={[
          { label: "Settings", href: "/settings" },
          { label: "Warehouse Management", isCurrentPage: true },
        ]}
        showWarehouse={false}
        className="mb-8"
      />
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Warehouse Management</h1>
        <Button asChild>
          <Link href="/settings/warehouses/new">
            <PlusCircle className="mr-2 h-4 w-4" /> Add New Warehouse
          </Link>
        </Button>
      </div>
      <p className="mb-6 text-muted-foreground">
        Configure warehouses, manage their details, and set up locations within
        each warehouse.
      </p>
      <div className="border rounded-md">
        <Table>
          <TableCaption>
            A list of warehouses configured for this workspace.
          </TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Address</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {warehouses.length > 0 ? (
              warehouses.map((warehouse) => (
                <TableRow key={warehouse.id}>
                  <TableCell className="font-medium">
                    {warehouse.name}
                  </TableCell>
                  <TableCell>{warehouse.address ?? "N/A"}</TableCell>
                  <TableCell>
                    {new Date(warehouse.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="h-8 w-8 p-0 text-muted-foreground hover:bg-slate-100 hover:text-muted-foreground"
                        >
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem
                          onClick={() => handleEditClick(warehouse)}
                        >
                          Edit Details
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link
                            href={`/settings/warehouses/${warehouse.id}/locations`}
                          >
                            Manage Locations
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDeleteClick(warehouse)}
                          className="text-red-600 focus:text-red-700 focus:bg-red-50"
                        >
                          Delete Warehouse
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center">
                  No warehouses found. Click "Add New Warehouse" to create one.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {selectedWarehouseForEdit && (
        <EditWarehouseDialog
          open={isEditDialogOpen}
          onOpenChange={(openState) => {
            if (!openState) {
              setIsEditDialogOpen(false);
              setSelectedWarehouseForEdit(null);
            }
          }}
          warehouse={selectedWarehouseForEdit}
          onWarehouseUpdated={onWarehouseUpdated}
        />
      )}
      {selectedWarehouseForDelete && (
        <DeleteWarehouseDialog
          open={isDeleteDialogOpen}
          onOpenChange={(openState) => {
            if (!openState) {
              setIsDeleteDialogOpen(false);
              setSelectedWarehouseForDelete(null);
            }
          }}
          warehouse={selectedWarehouseForDelete} // Pass the whole warehouse object as per DeleteWarehouseDialog props
          onConfirmDelete={onWarehouseDeleted} // onConfirmDelete is the prop in DeleteWarehouseDialog
        />
      )}
    </div>
  );
}
