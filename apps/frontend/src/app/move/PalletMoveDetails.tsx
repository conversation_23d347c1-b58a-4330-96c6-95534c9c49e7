"use client";

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { Pallet, Location } from '@quildora/types';
import { LocationCombobox } from '@/components/combobox/LocationCombobox';

interface PalletMoveDetailsProps {
  pallet: Pallet;
  onDetailsProvided: (newLocation: Location) => void;
  onCancel: () => void; // To go back to lookup step
}

export default function PalletMoveDetails({
  pallet,
  onDetailsProvided,
  onCancel,
}: PalletMoveDetailsProps) {
  const [newLocation, setNewLocation] = useState<Location | null>(null);

  const handleProceed = useCallback(() => {
    if (!newLocation) {
      toast.error('New Location Required', {
        description: 'Please select the new location for the pallet.',
      });
      return;
    }
    onDetailsProvided(newLocation);
    toast.success('Location Specified', {
      description: `Ready to move pallet to ${newLocation.name}. Please confirm.`,
    });
  }, [newLocation, onDetailsProvided]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Pallet Details & New Location</CardTitle>
        <CardDescription>
          Review pallet information and specify its new location.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-2">Selected Pallet</h3>
          <p><strong>Barcode:</strong> {pallet.barcode}</p>
          {pallet.label && <p><strong>Label:</strong> {pallet.label}</p>}
          {pallet.description && <p><strong>Description:</strong> {pallet.description}</p>}
          <p><strong>Current Location:</strong> {pallet.location?.name || 'N/A'}</p>
          {pallet.shipToDestination && <p><strong>Ship to:</strong> {pallet.shipToDestination}</p>}
        </div>

        <div className="space-y-2">
          <h3 className="text-lg font-semibold">New Location</h3>
          <p className="text-sm text-muted-foreground">
            Select the new location for the pallet.
          </p>
          <LocationCombobox 
            warehouseId={pallet.location?.warehouseId} 
            onLocationSelected={setNewLocation} 
          />
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={onCancel}>
            Back to Lookup
          </Button>
          <Button onClick={handleProceed} disabled={!newLocation}>
            Proceed to Confirmation
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
