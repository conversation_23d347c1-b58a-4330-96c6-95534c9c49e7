"use client";

import React, { useState, useCallback } from 'react';
import { Pallet } from '@quildora/types';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useAuth } from '@/components/providers/auth-provider';
import { fetchWithAuth } from '@/lib/api';

interface PalletLookupProps {
  onPalletFound: (pallet: Pallet) => void;
  // Optional: onError callback if MoveScreen needs to handle lookup errors specifically
  // onError: (message: string) => void;
}

export default function PalletLookup({ onPalletFound }: PalletLookupProps) {
  const [barcode, setBarcode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  

  const { appToken } = useAuth();

  const handleLookup = useCallback(async () => {
    if (!barcode.trim()) {
      toast.error('Barcode Required', {
        description: 'Please enter a pallet barcode.',
      });
      return;
    }
    setIsLoading(true);
    if (!appToken) {
      toast.error('Authentication error', { description: 'Could not verify user session.' });
      setIsLoading(false);
      return;
    }

    try {
      const pallets: Pallet[] = await fetchWithAuth(
        `/api/pallets?barcode=${encodeURIComponent(barcode)}`,
        {
          method: 'GET',
          token: appToken,
        }
      );

      if (pallets.length > 0) {
        onPalletFound(pallets[0]);
      } else {
        const errorMessage = 'Pallet not found.';
        toast.error('Pallet not found', { description: errorMessage });
      }
    } catch (err: unknown) {
      console.error('Lookup error:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred during lookup.';
      toast.error('Lookup Failed', {
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  }, [barcode, onPalletFound, appToken]);

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Find Pallet</h2>
      <p className="text-sm text-muted-foreground">
        Enter or scan the pallet barcode to begin the move process.
      </p>
      <div className="flex w-full max-w-sm items-center space-x-2">
        <Input 
          type="text" 
          placeholder="Pallet Barcode" 
          value={barcode}
          onChange={(e) => setBarcode(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleLookup()}
          disabled={isLoading}
          autoFocus
        />
        <Button onClick={handleLookup} disabled={isLoading || !barcode.trim()}>
          {isLoading ? 'Searching...' : 'Find Pallet'}
        </Button>
      </div>
      {/* Consider adding a dedicated scan button that might integrate with a camera API */}
    </div>
  );
}
