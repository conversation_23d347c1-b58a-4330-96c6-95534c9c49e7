"use client";

import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'; // Assuming Shadcn UI AlertDialog

import { Pallet, Location } from '@quildora/types';

interface ConfirmationModalProps {
  isOpen: boolean; // To control modal visibility from parent
  pallet: Pallet;
  newLocation: Location;
  onConfirm: () => void;
  onCancel: () => void;
  isConfirming: boolean; // To show loading state on confirm button
}

export default function ConfirmationModal({
  isOpen,
  pallet,
  newLocation,
  onConfirm,
  onCancel,
  isConfirming,
}: ConfirmationModalProps) {
  if (!isOpen) return null;

  return (
    <AlertDialog open={isOpen} onOpenChange={(open) => !open && onCancel()}> 
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Confirm Pallet Move</AlertDialogTitle>
          <AlertDialogDescription>
            Please confirm that you want to move pallet 
            <strong className="px-1">{pallet.label || pallet.barcode}</strong> 
            to location <strong className="px-1">{newLocation.name}</strong>.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onCancel} disabled={isConfirming}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction onClick={onConfirm} disabled={isConfirming}>
            {isConfirming ? 'Confirming...' : 'Confirm Move'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
