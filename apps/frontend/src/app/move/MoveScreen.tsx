"use client";

import React, { useState, useCallback } from 'react';
import { Pallet, Location } from '@quildora/types';
import { useAuth } from '@/components/providers/auth-provider';
import { fetchWithAuth } from '@/lib/api';
import PalletLookup from './PalletLookup';
import PalletMoveDetails from './PalletMoveDetails';
import ConfirmationModal from './ConfirmationModal';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

type WorkflowState = 'LOOKUP' | 'DETAILS' | 'CONFIRMING' | 'SUCCESS' | 'ERROR';

export default function MoveScreen() {
  const { appToken } = useAuth();


  const [workflowState, setWorkflowState] = useState<WorkflowState>('LOOKUP');
  const [selectedPallet, setSelectedPallet] = useState<Pallet | null>(null);
  const [newLocation, setNewLocation] = useState<Location | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isConfirming, setIsConfirming] = useState<boolean>(false);

  const handlePalletFound = useCallback((pallet: Pallet) => {
    setSelectedPallet(pallet);
    setWorkflowState('DETAILS');
    setError(null);
  }, []);

  const handleDetailsProvided = useCallback((location: Location) => {
    setNewLocation(location);
    setWorkflowState('CONFIRMING');
  }, []);

  const handleConfirmMove = async () => {
    if (!selectedPallet || !newLocation || !appToken) return;

    setIsConfirming(true);
    setError(null);

    try {
      const updatedPallet = await fetchWithAuth(
        `/api/pallets/${selectedPallet.id}/move`,
        {
          method: 'POST',
          token: appToken,
          body: JSON.stringify({ newLocationId: newLocation.id }),
        }
      );

      toast.success('Success!', {
        description: `Pallet ${selectedPallet.barcode || selectedPallet.label} moved to ${newLocation.name}.`,
      });
      
      setSelectedPallet(updatedPallet); // Update with the returned pallet data
      setWorkflowState('SUCCESS');
      // TODO: Invalidate React Query cache for pallets if used

    } catch (err: unknown) {
      console.error('Move error:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred.';
      setError(errorMessage);
      setWorkflowState('ERROR');
      toast.error('Error Moving Pallet', {
        description: errorMessage,
      });
    } finally {
      setIsConfirming(false);
    }
  };

  const handleReset = useCallback(() => {
    setSelectedPallet(null);
    setNewLocation(null);
    setError(null);
    setWorkflowState('LOOKUP');
    setIsConfirming(false);
  }, []);

  const handleBackToDetails = useCallback(() => {
    setWorkflowState('DETAILS');
  }, []);

  const renderContent = () => {
    switch (workflowState) {
      case 'LOOKUP':
        return <PalletLookup onPalletFound={handlePalletFound} />;
      case 'DETAILS':
        if (selectedPallet) {
          return (
            <PalletMoveDetails
              pallet={selectedPallet}
              onDetailsProvided={handleDetailsProvided}
              onCancel={handleReset}
            />
          );
        }
        return null;
      case 'CONFIRMING':
        if (selectedPallet && newLocation) {
          return (
            <ConfirmationModal
              isOpen={true}
              pallet={selectedPallet}
              newLocation={newLocation}
              onConfirm={handleConfirmMove}
              onCancel={handleBackToDetails}
              isConfirming={isConfirming}
            />
          );
        }
        return null;
      case 'SUCCESS':
        return (
          <div className="text-center">
            <h2 className="text-2xl font-semibold text-green-600 mb-4">Move Successful!</h2>
            <p>Pallet <strong>{selectedPallet?.barcode || selectedPallet?.label}</strong> has been moved.</p>
            <Button onClick={handleReset} className="mt-6">Move Another Pallet</Button>
          </div>
        );
      case 'ERROR':
        return (
          <div className="text-center">
            <h2 className="text-2xl font-semibold text-red-600 mb-4">Move Failed</h2>
            <p className="mb-4">{error}</p>
            <Button onClick={handleReset} className="mt-6">Try Again</Button>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6 text-center">Move Pallet</h1>
      {renderContent()}
    </div>
  );
}
