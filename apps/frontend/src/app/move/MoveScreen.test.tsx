import { render, screen, fireEvent, waitFor } from '@/lib/test-utils';
import { vi } from 'vitest';

import MoveScreen from './MoveScreen';
import * as api from '@/lib/api';
import { toast } from 'sonner';

// Mock the api utility and sonner
vi.mock('@/lib/api');
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

const mockPallet = {
  id: 'pallet123',
  barcode: 'P12345',
  description: 'Test Pallet',
  location: { id: 'loc1', name: 'A-1-1' },
  shipToDestination: 'Test Destination',
  palletItems: [{ id: 'pi1', quantity: 10, item: { id: 'item1', name: 'Test Item' } }],
};

const mockLocation = {
  id: 'loc2',
  name: 'B-2-2',
  barcode: 'L54321',
};

describe('MoveScreen', () => {
  const mockedFetchWithAuth = vi.mocked(api.fetchWithAuth);

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should complete the full pallet move workflow successfully', async () => {
    render(<MoveScreen />);
    expect(screen.getByText('Scan or Enter Pallet Barcode')).toBeInTheDocument();

    // Mock the pallet lookup, location lookup, and move API calls
    mockedFetchWithAuth
      .mockResolvedValueOnce(mockPallet) // Pallet lookup
      .mockResolvedValueOnce(mockLocation) // Location lookup
      .mockResolvedValueOnce({ ...mockPallet, location: mockLocation }); // Move

    const palletInput = screen.getByLabelText('Pallet Barcode');
    fireEvent.change(palletInput, { target: { value: 'P12345' } });
    fireEvent.click(screen.getByRole('button', { name: 'Look Up Pallet' }));

    await waitFor(() => {
      expect(screen.getByText('Pallet Details')).toBeInTheDocument();
    });

    const locationInput = screen.getByLabelText('New Location');
    fireEvent.change(locationInput, { target: { value: 'B-2-2' } });
    fireEvent.click(screen.getByRole('button', { name: 'Move Pallet' }));

    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith('Success!', {
        description: `Pallet P12345 moved to B-2-2.`,
      });
    });

    expect(screen.getByText('Scan or Enter Pallet Barcode')).toBeInTheDocument();
  });

  it('should show an error toast if pallet lookup fails', async () => {
    render(<MoveScreen />);

    const errorMessage = 'Pallet not found';
    mockedFetchWithAuth.mockRejectedValue(new Error(errorMessage));

    const palletInput = screen.getByLabelText('Pallet Barcode');
    fireEvent.change(palletInput, { target: { value: 'INVALID' } });
    fireEvent.click(screen.getByRole('button', { name: 'Look Up Pallet' }));

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Error Looking Up Pallet', {
        description: errorMessage,
      });
    });

    expect(screen.getByText('Scan or Enter Pallet Barcode')).toBeInTheDocument();
  });
});
