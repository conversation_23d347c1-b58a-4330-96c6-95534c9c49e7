@import "tailwindcss";

@config "../../tailwind.config.js";

:root {
  --radius: 0.625rem; /* Unchanged from original, design guidelines don't specify radius */

  /* Quildora UI Design Guidelines - Color Palette */
  --background: #f1f5f9; /* slate-100 - App Background */
  --foreground: #1e293b; /* slate-800 - Primary Text */

  --card: #ffffff; /* white - Content/Card Background */
  --card-foreground: #1e293b; /* slate-800 - Primary Text on Card */

  --popover: #ffffff; /* white - Popover Background */
  --popover-foreground: #1e293b; /* slate-800 - Primary Text on Popover */

  --primary: #0284c7; /* sky-600 - Primary Action Blue */
  --primary-foreground: #ffffff; /* white - Text on Primary Action */

  --secondary: #e2e8f0; /* slate-200 - Secondary Button Background */
  --secondary-foreground: #334155; /* slate-700 - Text on Secondary Button */

  --muted: #f1f5f9; /* slate-100 - Background for muted elements */
  --muted-foreground: #64748b; /* slate-500 - Secondary Text/Labels */

  --accent: #10b981; /* emerald-500 - Accent & Success Green */
  --accent-foreground: #ffffff; /* white - Text on Accent/Success */

  --destructive: #ef4444; /* red-500 - Destructive & Error Red */
  --destructive-foreground: #ffffff; /* white - Text on Destructive/Error */

  --warning: #facc15; /* amber-400 - Warning Yellow/Orange */
  --warning-foreground: #1e293b; /* slate-800 - Text on Warning */

  --border: #cbd5e1; /* slate-300 - Standard Borders & Dividers */
  --input: #cbd5e1; /* slate-300 - Input border color */
  --ring: #0ea5e9; /* sky-500 - Focus Ring color */

  /* Unchanged chart and sidebar colors, pending specific guidelines */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0.002 247.839);
  --sidebar-foreground: oklch(0.13 0.028 261.692);
  --sidebar-primary: oklch(0.21 0.034 264.665);
  --sidebar-primary-foreground: oklch(0.985 0.002 247.839);
  --sidebar-accent: oklch(0.967 0.003 264.542);
  --sidebar-accent-foreground: oklch(0.21 0.034 264.665);
  --sidebar-border: oklch(0.928 0.006 264.531);
  --sidebar-ring: oklch(0.707 0.022 261.325);
}

.dark {
  --background: oklch(0.13 0.028 261.692);
  --foreground: oklch(0.985 0.002 247.839);
  --card: oklch(0.21 0.034 264.665);
  --card-foreground: oklch(0.985 0.002 247.839);
  --popover: oklch(0.21 0.034 264.665);
  --popover-foreground: oklch(0.985 0.002 247.839);
  --primary: oklch(0.928 0.006 264.531);
  --primary-foreground: oklch(0.21 0.034 264.665);
  --secondary: oklch(0.278 0.033 256.848);
  --secondary-foreground: oklch(0.985 0.002 247.839);
  --muted: oklch(0.278 0.033 256.848);
  --muted-foreground: oklch(0.707 0.022 261.325);
  --accent: oklch(0.278 0.033 256.848);
  --accent-foreground: oklch(0.985 0.002 247.839);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.551 0.027 264.364);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.21 0.034 264.665);
  --sidebar-foreground: oklch(0.985 0.002 247.839);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0.002 247.839);
  --sidebar-accent: oklch(0.278 0.033 256.848);
  --sidebar-accent-foreground: oklch(0.985 0.002 247.839);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.551 0.027 264.364);
}

@layer base {
  html,
  body {
    /* @apply overflow-hidden; */ /* Guideline: No main page scrollbars - Removed to allow main layout scrolling */
  }
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-sans); /* Guideline: Font Inter (using existing --font-sans) */
  }
}
