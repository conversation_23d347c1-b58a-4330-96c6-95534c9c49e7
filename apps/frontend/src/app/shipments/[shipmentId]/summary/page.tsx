"use client";

import { useQuery } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import {
  ShipmentSummaryResponseDto,
  PalletWithPalletItems,
  PalletItemWithItem,
} from "@quildora/types";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Loader2, AlertCircle } from "lucide-react";

const fetchShipmentSummary = async (
  shipmentId: string,
  warehouseId: string,
  token: string | null
): Promise<ShipmentSummaryResponseDto> => {
  if (!token) throw new Error("Authentication token not found.");
  return fetchWithAuth(
    `/api/shipments/summary/${shipmentId}?warehouseId=${warehouseId}`,
    { token }
  );
};

export default function ShipmentSummaryPage() {
  const params = useParams();
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();
  const shipmentId = params.shipmentId as string;

  const {
    data: summary,
    error,
    isLoading,
  } = useQuery<ShipmentSummaryResponseDto, Error>({
    queryKey: ["shipmentSummary", shipmentId, currentWarehouse?.id, appToken],
    queryFn: () =>
      fetchShipmentSummary(shipmentId, currentWarehouse?.id || "", appToken),
    enabled: !!shipmentId && !!appToken && !!currentWarehouse?.id,
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
        <p className="ml-2">Loading Shipment Summary...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load shipment summary: {error.message}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!summary) {
    return null;
  }

  return (
    <div className="container mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Shipment Summary</CardTitle>
          <CardDescription>
            Reference #: {summary.referenceNumber}
          </CardDescription>
        </CardHeader>
      </Card>

      {Object.entries(summary.palletsByDestination).map(
        ([destination, pallets]) => (
          <Card key={destination}>
            <CardHeader>
              <CardTitle>Destination: {destination}</CardTitle>
              <CardDescription>
                {pallets.length} pallet(s) assigned to this destination.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {pallets.map((pallet: PalletWithPalletItems) => (
                <Card key={pallet.id} className="shadow-md">
                  <CardHeader>
                    <CardTitle className="text-lg">
                      Pallet: {pallet.barcode}
                    </CardTitle>
                    <CardDescription>Status: {pallet.status}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <h4 className="font-semibold mb-2">
                      Items on this Pallet:
                    </h4>
                    <ul className="list-disc pl-5 space-y-1">
                      {pallet.palletItems.map(
                        (palletItem: PalletItemWithItem) => (
                          <li key={palletItem.id}>
                            {palletItem.quantity} x {palletItem.item.name} (SKU:{" "}
                            {palletItem.item.sku})
                          </li>
                        )
                      )}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </CardContent>
          </Card>
        )
      )}
    </div>
  );
}
