'use client';

import React, { useEffect, useRef } from 'react';
import { useParams } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { PlacardDataResponseDto } from '@quildora/types';
import { useAuth } from '@/components/providers/auth-provider';
import { fetchWithAuth } from '@/lib/api';
import Placard from '@/components/placard/Placard';
import '@/styles/print.css';

const PlacardPrintPage = () => {
  const params = useParams();
  const { palletId } = params;
  const id = Array.isArray(palletId) ? palletId[0] : palletId;
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const printTriggered = useRef(false);

  const {
    data: placardData,
    isLoading,
    isError,
    error,
  } = useQuery<PlacardDataResponseDto, Error>({
    queryKey: ['placardData', id, appToken],
    queryFn: () => {
      const url = `/api/pallets/${id}/placard-data`;
      console.log('Fetching placard data from URL:', url);
      return fetchWithAuth(url, {
        token: appToken,
      });
    },
    enabled: !!id && !!appToken && !isAuthLoading,
  });

  useEffect(() => {
    if (placardData && !printTriggered.current) {
      printTriggered.current = true;
      // Delay printing slightly to ensure the component has rendered fully
      setTimeout(() => {
        window.print();
      }, 500);
    }
  }, [placardData]);

  if (isLoading || isAuthLoading) {
    return <div className="flex items-center justify-center h-screen">Loading placard...</div>;
  }

  if (isError) {
    return <div className="flex items-center justify-center h-screen">Error loading placard data: {error.message}</div>;
  }

  if (!placardData) {
    return <div className="flex items-center justify-center h-screen">No data available for this placard.</div>;
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div id="placard-print-area" className="w-full aspect-[11/8.5] shadow-2xl overflow-hidden">
        <Placard data={placardData} />
      </div>
    </div>
  );
};

export default PlacardPrintPage;
