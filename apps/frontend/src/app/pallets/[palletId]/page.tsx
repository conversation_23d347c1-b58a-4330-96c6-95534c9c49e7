"use client";

import React, { useEffect } from "react"; // Added useEffect
import { useParams } from "next/navigation";
import {
  useSuspenseQuery,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";
// UI imports now handled by reusable components
import { AddPalletItemDialog } from "@/components/pallets/AddPalletItemDialog";
import { EditPalletItemDialog } from "@/components/pallets/EditPalletItemDialog";
import { EditPalletDialog } from "@/components/pallets/EditPalletDialog"; // Added
import { MovePalletDialog } from "@/components/pallets/MovePalletDialog"; // Added
import { DeletePalletDialog } from "@/components/pallets/DeletePalletDialog"; // Added
import { AuditLogSection } from "@/components/pallets/AuditLogSection";
import { toast } from "sonner";
// Icon imports now handled by reusable components
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { fetchWithAuth } from "@/lib/api";
import { usePageTitle } from "@/components/providers/PageTitleContext";
// Destination hooks moved to reusable components
import { usePrintPlacard } from "@/hooks/usePrintPlacard";
import { usePalletDetailDialogs } from "@/hooks/usePalletDetailDialogs";
import { PalletHeader } from "@/components/pallets/PalletHeader";
import { PalletItemsTable } from "@/components/pallets/PalletItemsTable";
import { PalletDetailActions } from "@/components/pallets/PalletDetailActions";
// Router import removed - navigation moved to reusable components
import {
  PalletItem,
  Pallet as GlobalPalletType,
  PalletWithPalletItems,
} from "@quildora/types"; // Using shared types for consistency

// Using shared types from @quildora/types for consistency
// Using shared PalletWithPalletItems type from @quildora/types
// This includes all pallet properties plus nested palletItems with item details

// Function to fetch details for a single pallet
async function fetchPalletDetails(
  palletIdToFetch: string, // Renamed to avoid conflict with palletId in map
  warehouseId: string,
  appToken: string | null
): Promise<PalletWithPalletItems> {
  if (!appToken) {
    throw new Error("Authentication token not available.");
  }
  // Ensure appToken is passed to fetchWithAuth
  const palletData = (await fetchWithAuth(
    `/api/pallets/${palletIdToFetch}?warehouseId=${warehouseId}`,
    {
      token: appToken,
    }
  )) as Omit<PalletWithPalletItems, "palletItems"> & {
    palletItems: Omit<PalletItem, "palletId">[];
  }; // Type from API

  // Augment palletItems with palletId (handle case where palletItems might be null/undefined)
  const augmentedPalletItems = (palletData.palletItems || []).map((item) => ({
    ...item,
    palletId: palletIdToFetch, // Add the palletId to each item
  }));

  return {
    ...palletData,
    palletItems: augmentedPalletItems,
  } as PalletWithPalletItems;
}

// API function to remove an item from a pallet
async function removeItemFromPalletApi(
  palletId: string,
  itemId: string,
  warehouseId: string,
  appToken: string | null
): Promise<void> {
  if (!palletId || !itemId)
    throw new Error("Pallet ID and Item ID are required");

  // Ensure appToken is passed to fetchWithAuth
  await fetchWithAuth(
    `/api/pallets/${palletId}/items/${itemId}?warehouseId=${warehouseId}`,
    {
      method: "DELETE",
      token: appToken,
    }
  );
  // DELETE typically doesn't return content, or returns 204 No Content handled by fetchWithAuth
}

// --- Page Component --- //

export default function PalletDetailPage() {
  const params = useParams();
  const queryClient = useQueryClient();
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();
  const { setTitle } = usePageTitle(); // Added
  // Destination functionality moved to reusable components

  const palletId = params.palletId as string;
  const palletBarcode = params.palletBarcode as string;

  // Print placard functionality
  const { printSinglePlacard, isPrinting } = usePrintPlacard();

  // Dialog state management
  const {
    isEditDialogOpen,
    isMoveDialogOpen,
    isDeleteDialogOpen,
    isAddDialogOpen,
    itemToEdit,
    itemToRemove,
    isEditItemDialogOpen,
    isRemoveItemDialogOpen,
    handleEditClick,
    handleMoveClick,
    handleDeleteClick,
    handleAddItemClick,
    handleEditItemClick,
    handleRemoveItemClick,
    closeEditDialog,
    closeMoveDialog,
    closeDeleteDialog,
    closeAddDialog,
    closeEditItemDialog,
    closeRemoveItemDialog,
  } = usePalletDetailDialogs();

  console.log(`Rendering Pallet Detail page for ID: ${palletId}`);

  // Use Suspense query for instant data access without loading states
  const { data: pallet } = useSuspenseQuery<PalletWithPalletItems, Error>({
    queryKey: ["palletDetails", palletId, currentWarehouse?.id],
    queryFn: () =>
      fetchPalletDetails(palletId, currentWarehouse?.id || "", appToken),
  });

  useEffect(() => {
    if (pallet) {
      setTitle(`Pallet Details: ${pallet.barcode}`);
    } else if (palletBarcode) {
      setTitle(`Pallet Details: ${palletBarcode}`);
    } else {
      setTitle("Pallet Details");
    }
  }, [pallet, palletBarcode, setTitle]);

  const removeMutation = useMutation<void, Error, { itemId: string }>({
    mutationFn: ({ itemId }) =>
      removeItemFromPalletApi(
        palletId,
        itemId,
        currentWarehouse?.id || "",
        appToken
      ),
    onSuccess: () => {
      toast.success("Item removed successfully.");
      queryClient.invalidateQueries({ queryKey: ["palletDetails", palletId] });
      closeRemoveItemDialog();
    },
    onError: (error) => {
      toast.error(error.message || "Failed to remove item.");
      closeRemoveItemDialog();
    },
  });

  // No loading or error states needed - useSuspenseQuery handles these automatically

  if (!pallet) {
    return (
      <div className="container mx-auto p-4">Pallet data not available.</div>
    ); // Should ideally be caught by isError
  }

  // Handler functions now provided by usePalletDetailDialogs hook

  const handleConfirmRemove = () => {
    if (itemToRemove) {
      removeMutation.mutate({ itemId: itemToRemove.itemId });
    }
  };

  // Print placard handler - now uses reusable hook
  const handlePrintPlacard = () => {
    if (pallet) {
      // Convert PalletDetails to Pallet format for the hook
      const palletForPrint: GlobalPalletType = {
        id: pallet.id,
        label: pallet.barcode || pallet.id, // Use barcode as label, fallback to id
        status: pallet.status,
        locationId: pallet.locationId,
        location: pallet.location || undefined,
        dateCreated: pallet.dateCreated,
        lastMovedDate: pallet.lastMovedDate,
        shipToDestination: pallet.shipToDestination,
        destinationCode: null, // PalletDetails doesn't have destinationCode
      };
      printSinglePlacard(palletForPrint);
    }
  };

  const palletForDialogs: GlobalPalletType | null = pallet
    ? {
        id: pallet.id,
        label: pallet.barcode || pallet.id, // Use barcode as label, fallback to ID
        status: pallet.status,
        locationId: pallet.location?.id ?? pallet.locationId, // Ensure this aligns with GlobalPalletType (string | null)
        location: pallet.location === null ? undefined : pallet.location, // Handle null to undefined for location object
        description: pallet.description ?? undefined,
        barcode: pallet.barcode ?? undefined,
        shipToDestination: pallet.shipToDestination ?? undefined, // Added
        palletItems: pallet.palletItems, // Map from palletItems (local) to palletItems (global)
        dateCreated: pallet.dateCreated, // Ensure these are part of GlobalPalletType if used by dialogs
        lastMovedDate: pallet.lastMovedDate,
      }
    : null;

  return (
    <div className="container mx-auto p-4 space-y-6">
      {/* Header with pallet information */}
      <PalletHeader
        palletId={palletId}
        barcode={pallet.barcode}
        status={pallet.status}
        shipToDestination={pallet.shipToDestination}
        destinationCode={pallet.destinationCode}
        description={pallet.description}
        locationName={pallet.location?.name}
        dateCreated={pallet.dateCreated}
        lastMovedDate={pallet.lastMovedDate}
      />

      {/* Action buttons */}
      {pallet && (
        <PalletDetailActions
          onEdit={handleEditClick}
          onPrint={handlePrintPlacard}
          onMove={handleMoveClick}
          onDelete={handleDeleteClick}
          isPrinting={isPrinting}
        />
      )}
      {palletForDialogs && (
        <>
          <EditPalletDialog
            open={isEditDialogOpen}
            onOpenChange={(open) => {
              if (!open) closeEditDialog();
            }}
            pallet={palletForDialogs}
            onSuccess={() => {
              toast.success("Pallet updated successfully.");
              queryClient.invalidateQueries({
                queryKey: ["palletDetails", palletId],
              });
              queryClient.invalidateQueries({ queryKey: ["pallets"] }); // Also invalidate list view
              closeEditDialog();
            }}
          />
          <MovePalletDialog
            isOpen={isMoveDialogOpen} // Changed from open to isOpen
            onOpenChange={(open) => {
              if (!open) closeMoveDialog();
            }}
            pallet={palletForDialogs}
            onPalletMoved={() => {
              // Changed from onSuccess to onPalletMoved
              toast.success("Pallet moved successfully.");
              queryClient.invalidateQueries({
                queryKey: ["palletDetails", palletId],
              });
              queryClient.invalidateQueries({ queryKey: ["pallets"] });
              closeMoveDialog();
            }}
          />
          <DeletePalletDialog
            open={isDeleteDialogOpen}
            onOpenChange={(open) => {
              if (!open) closeDeleteDialog();
            }}
            pallet={palletForDialogs}
            onSuccess={() => {
              toast.success("Pallet deleted successfully.");
              queryClient.invalidateQueries({ queryKey: ["pallets"] });
              // After deletion, navigate away. Assuming useRouter hook is available or can be added.
              // For now, just invalidating. A redirect would be better UX.
              // e.g. router.push('/pallets');
              closeDeleteDialog(); // Close dialog
              // Potentially clear the pallet data on this page or redirect
              queryClient.setQueryData(["palletDetails", palletId], null); // Optimistically set data to null
            }}
          />
        </>
      )}
      {/* Pallet information now displayed in PalletHeader component */}
      {/* Pallet Items Table */}
      <PalletItemsTable
        palletItems={pallet.palletItems || []}
        onAddItem={handleAddItemClick}
        onEditItem={handleEditItemClick}
        onRemoveItem={handleRemoveItemClick}
      />

      {/* Audit Log Section */}
      <div className="mt-8">
        <AuditLogSection palletId={palletId} />
      </div>

      <AddPalletItemDialog
        open={isAddDialogOpen}
        onOpenChange={(open) => {
          if (!open) closeAddDialog();
        }}
        palletId={palletId}
        palletBarcode={pallet.barcode || undefined}
        onSuccess={() => {
          toast.success("Item added successfully.");
          queryClient.invalidateQueries({
            queryKey: ["palletDetails", palletId],
          });
          closeAddDialog();
        }}
      />
      <EditPalletItemDialog
        open={isEditItemDialogOpen}
        onOpenChange={(open) => {
          if (!open) closeEditItemDialog();
        }}
        palletId={palletId}
        palletItem={itemToEdit}
        onSuccess={() => {
          toast.success("Item updated successfully.");
          queryClient.invalidateQueries({
            queryKey: ["palletDetails", palletId],
          });
          closeEditItemDialog();
        }}
      />
      <AlertDialog
        open={isRemoveItemDialogOpen}
        onOpenChange={(open) => {
          if (!open) closeRemoveItemDialog();
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently remove the
              item{" "}
              <span className="font-semibold">
                {itemToRemove?.item?.name} (SKU:{" "}
                {itemToRemove?.item?.sku ?? "N/A"})
              </span>{" "}
              from this pallet.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={closeRemoveItemDialog}
              disabled={removeMutation.isPending}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmRemove}
              disabled={removeMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {removeMutation.isPending ? "Removing..." : "Remove Item"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
