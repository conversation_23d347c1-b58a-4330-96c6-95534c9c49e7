import { <PERSON>ada<PERSON> } from "next";
import React from "react";

// --- Generate Metadata --- //

type Props = {
  params: Promise<{ palletId: string }>;
};

// This can now live in the layout (Server Component)
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { palletId } = await params;
  // In a real app, you might fetch minimal data needed for title here.
  // For now, just use the ID.
  return {
    title: `Pallet ${palletId}`,
    description: `Details and items for pallet ${palletId}.`,
  };
}

// --- Layout Component --- //

export default function PalletDetailLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
