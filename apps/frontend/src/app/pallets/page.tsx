"use client";

import React, { useEffect } from "react";

import { But<PERSON> } from "@/components/ui/button";

// Import Dialogs
import { CreatePalletDialog } from "@/components/pallets/CreatePalletDialog";
import { EditPalletDialog } from "@/components/pallets/EditPalletDialog";
import { DeletePalletDialog } from "@/components/pallets/DeletePalletDialog";
import { MovePalletDialog } from "@/components/pallets/MovePalletDialog";
import { useRoleAccess } from "@/components/providers/auth-provider";
import { Role } from "@quildora/types";

import { usePalletsSuspense, useLocations, useDestinations } from "@/hooks/api";
import { usePageTitle } from "@/components/providers/PageTitleContext";
import { usePrintPlacard } from "@/hooks/usePrintPlacard";
import { usePalletDialogs } from "@/hooks/usePalletDialogs";
import { usePalletFiltering } from "@/hooks/usePalletFiltering";

import { PalletFilters } from "./PalletFilters";
import { PalletTable } from "@/components/pallets/PalletTable";

export default function PalletsPage() {
  const isAdmin = useRoleAccess(Role.TENANT_ADMIN);
  const isWarehouseAdmin = useRoleAccess(Role.WAREHOUSE_MANAGER);
  const isWarehouseMember = useRoleAccess(Role.WAREHOUSE_MEMBER);
  const { setTitle } = usePageTitle();

  // Filter state management
  const { filters, handleFilterChange, handleResetFilters, filterPallets } =
    usePalletFiltering();

  useEffect(() => {
    setTitle("Pallets");
  }, []); // Only run once on mount

  // Print placard functionality
  const { printSinglePlacard } = usePrintPlacard();

  // Fetch simple destination names for filter component
  const { data: destinationNames = [], isLoading: isLoadingDestinations } =
    useDestinations();

  // Dialog state management
  const {
    isCreateDialogOpen,
    isEditDialogOpen,
    isDeleteDialogOpen,
    isMoveDialogOpen,
    selectedPalletForEdit,
    selectedPalletForDelete,
    selectedPalletForMove,
    handleCreateClick,
    handleEditClick,
    handleDeleteClick,
    setIsCreateDialogOpen,
    setIsEditDialogOpen,
    setIsDeleteDialogOpen,
    setIsMoveDialogOpen,
    closeDeleteDialog,
    closeMoveDialog,
  } = usePalletDialogs();

  // Fetch data using warehouse-aware hooks
  const { data: locations = [], isLoading: isLoadingLocations } =
    useLocations();

  // Fetch all pallets data with suspense for instant loading (no server-side filtering)
  const { data: pallets = [] } = usePalletsSuspense();

  // Apply client-side filtering using the hook
  const filteredPallets = filterPallets(pallets);

  // Render the pallets table
  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-4">
        <div>
          {" "}
        </div>
        {(isAdmin || isWarehouseAdmin || isWarehouseMember) && (
          <Button onClick={handleCreateClick}>Create Pallet</Button>
        )}
      </div>

      <div className="mb-4">
        <PalletFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          onReset={handleResetFilters}
          locations={locations}
          isLoadingLocations={isLoadingLocations}
          destinations={destinationNames}
          isLoadingDestinations={isLoadingDestinations}
        />
      </div>

      <PalletTable
        pallets={filteredPallets}
        hasActions={isAdmin || isWarehouseAdmin || isWarehouseMember}
        onEditClick={handleEditClick}
        onDeleteClick={handleDeleteClick}
        onPrintPlacard={printSinglePlacard}
      />
      {/* Render Dialogs */}
      <CreatePalletDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
      />
      <EditPalletDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        pallet={selectedPalletForEdit}
      />
      {selectedPalletForDelete && (
        <DeletePalletDialog
          open={isDeleteDialogOpen} // Corrected from isOpen to open
          onOpenChange={setIsDeleteDialogOpen}
          pallet={selectedPalletForDelete}
          onSuccess={closeDeleteDialog}
        />
      )}
      {/* Added Move Pallet Dialog instance */}
      {selectedPalletForMove && (
        <MovePalletDialog
          isOpen={isMoveDialogOpen}
          onOpenChange={setIsMoveDialogOpen}
          pallet={selectedPalletForMove}
          onPalletMoved={closeMoveDialog}
        />
      )}
    </div>
  );
}
