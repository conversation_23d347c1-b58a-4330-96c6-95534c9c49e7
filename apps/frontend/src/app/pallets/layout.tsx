"use client";

import React from "react";
import { useDataPrefetching } from "@/hooks/useDataPrefetching";

// Note: Metadata export removed since this is now a client component
// The page title is handled by the PageTitleProvider

export default function PalletsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Prefetch critical data before components mount
  useDataPrefetching();

  return (
    <div className="container mx-auto max-w-7xl py-6 px-4 md:px-6">
      {/* <WarehouseBreadcrumb items={breadcrumbItems} className="mb-6" /> */}
      {children}
    </div>
  );
}
