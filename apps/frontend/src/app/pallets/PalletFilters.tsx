"use client";

import { useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Combobox, ComboboxOption } from "@/components/ui/combobox";
import { LocationCombobox } from "@/components/combobox/LocationCombobox";
import { Location } from "@quildora/types";
import { XIcon, Filter, Search } from "lucide-react";
// Removed unused imports - destinations now passed as props

interface PalletFiltersProps {
  filters: {
    shipToDestination: string;
    destinationCode: string;
    description: string;
    locationId: string;
    includeReleased: boolean;
  };
  onFilterChange: (filters: Partial<PalletFiltersProps["filters"]>) => void;
  onReset: () => void;
  locations: Location[];
  isLoadingLocations: boolean;
  destinations: string[];
  isLoadingDestinations: boolean;
}

export const PalletFilters = ({
  filters,
  onFilterChange,
  onReset,
  locations,
  isLoadingLocations,
  destinations,
  isLoadingDestinations,
}: PalletFiltersProps) => {
  // Convert destinations to combobox options
  const destinationOptions: ComboboxOption[] = useMemo(
    () => destinations.map((dest) => ({ value: dest, label: dest })),
    [destinations]
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    onFilterChange({ [name]: value });
  };

  const handleLocationChange = (location: Location | null) => {
    onFilterChange({ locationId: location?.id || "" });
  };

  const handleDestinationChange = (value: string) => {
    onFilterChange({ shipToDestination: value });
  };

  const handleIncludeReleasedChange = (checked: boolean) => {
    onFilterChange({ includeReleased: checked });
  };

  return (
    <div className="space-y-4 p-6 bg-gradient-to-r from-slate-50 to-slate-100 rounded-xl border border-slate-200 shadow-sm">
      {/* Header */}
      <div className="flex items-center gap-2 mb-4">
        <Filter className="h-5 w-5 text-slate-600" />
        <h3 className="text-lg font-semibold text-slate-800">Filter Pallets</h3>
      </div>

      {/* Filter Controls */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {/* Destination Autocomplete */}
        <div className="space-y-2">
          <Label
            htmlFor="destination"
            className="text-sm font-medium text-slate-700"
          >
            Ship-To Destination
          </Label>
          <Combobox
            options={destinationOptions}
            value={filters.shipToDestination}
            onChange={handleDestinationChange}
            placeholder="Select destination..."
            loading={isLoadingDestinations}
          />
        </div>

        {/* Destination Code Filter */}
        <div className="space-y-2">
          <Label
            htmlFor="destinationCode"
            className="text-sm font-medium text-slate-700"
          >
            Destination Code
          </Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              id="destinationCode"
              name="destinationCode"
              placeholder="Search by code..."
              value={filters.destinationCode}
              onChange={handleInputChange}
              className="pl-10 h-11 text-base"
            />
          </div>
        </div>

        {/* Description Filter */}
        <div className="space-y-2">
          <Label
            htmlFor="description"
            className="text-sm font-medium text-slate-700"
          >
            Description
          </Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              id="description"
              name="description"
              placeholder="Search description..."
              value={filters.description}
              onChange={handleInputChange}
              className="pl-10 h-11 text-base"
            />
          </div>
        </div>

        {/* Location Filter */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-slate-700">Location</Label>
          <LocationCombobox
            locations={locations}
            onLocationSelected={handleLocationChange}
            initialLocationId={filters.locationId}
            isLoading={isLoadingLocations}
            className="w-full"
          />
        </div>

        {/* Status Filter */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-slate-700">
            Status Options
          </Label>
          <div className="flex items-center space-x-3 h-11">
            <Checkbox
              id="includeReleased"
              checked={filters.includeReleased}
              onCheckedChange={handleIncludeReleasedChange}
              className="h-5 w-5"
            />
            <Label
              htmlFor="includeReleased"
              className="text-sm font-medium text-slate-700 cursor-pointer select-none"
            >
              Include Released Pallets
            </Label>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-4 border-t border-slate-200">
        <div className="text-sm text-slate-600">
          {/* Active filter count could go here */}
        </div>
        <Button
          onClick={onReset}
          variant="outline"
          size="sm"
          className="h-9 px-4 text-slate-700 border-slate-300 hover:bg-slate-100"
        >
          <XIcon className="h-4 w-4 mr-2" />
          Reset Filters
        </Button>
      </div>
    </div>
  );
};

// Component reverted to simple implementation without React.memo
