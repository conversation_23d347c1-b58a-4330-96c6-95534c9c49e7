import { useState } from "react";
import { Picklist, PicklistDestination } from "@/components/picking/types";

/**
 * Custom hook for picking operations
 * Handles picklist management, view navigation, and destination updates
 */
export const usePickingOperations = () => {
  const [selectedDestination, setSelectedDestination] = useState<string>("");
  const [currentView, setCurrentView] = useState<"destinations" | "picklist">(
    "destinations"
  );
  const [activePicklist, setActivePicklist] = useState<Picklist | null>(null);
  const [showCreatePicklist, setShowCreatePicklist] = useState(false);

  // Handler for creating a new picklist
  const handleCreatePicklist = (picklist: Picklist) => {
    setActivePicklist(picklist);
    setCurrentView("picklist");
  };

  // Handler for navigating back to destinations view
  const handleBackToDestinations = () => {
    setCurrentView("destinations");
    setActivePicklist(null);
  };

  // Handler for completing a picklist
  const handleCompletePicklist = () => {
    // Clean up the completed picklist and return to main view
    setCurrentView("destinations");
    setActivePicklist(null);
    setSelectedDestination("");
  };

  // Handler for updating destination status in active picklist
  const handleUpdateDestination = (
    destination: string,
    updates: Partial<PicklistDestination>
  ) => {
    if (!activePicklist) return;

    const updatedDestinations = activePicklist.destinations.map((dest) =>
      dest.destination === destination ? { ...dest, ...updates } : dest
    );

    const completedDestinations = updatedDestinations.filter(
      (d) => d.status === "picked" || d.status === "partial"
    ).length;

    setActivePicklist({
      ...activePicklist,
      destinations: updatedDestinations,
      completedDestinations,
    });
  };

  // Handler for opening create picklist modal
  const handleOpenCreatePicklist = () => {
    setShowCreatePicklist(true);
  };

  // Handler for closing create picklist modal
  const handleCloseCreatePicklist = () => {
    setShowCreatePicklist(false);
  };

  return {
    // State
    selectedDestination,
    currentView,
    activePicklist,
    showCreatePicklist,
    
    // Actions
    setSelectedDestination,
    handleCreatePicklist,
    handleBackToDestinations,
    handleCompletePicklist,
    handleUpdateDestination,
    handleOpenCreatePicklist,
    handleCloseCreatePicklist,
  };
};
