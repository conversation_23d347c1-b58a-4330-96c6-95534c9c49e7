import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { usePrintPlacard } from "@/hooks/usePrintPlacard";

/**
 * Custom hook for receiving summary page operations
 * Handles location updates and placard printing functionality
 */
export const useReceivingSummary = (shipmentId: string | undefined) => {
  const { appToken } = useAuth();
  const queryClient = useQueryClient();
  const { printPlacardsByDestination } = usePrintPlacard();
  const [isUpdatingLocation, setIsUpdatingLocation] = useState<string | null>(null);

  // API function for updating pallet location
  const updatePalletLocation = async (
    palletId: string,
    locationId: string
  ): Promise<void> => {
    return fetchWithAuth(`/api/pallets/${palletId}/move`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ newLocationId: locationId }),
      token: appToken,
    });
  };

  // Mutation for updating pallet location
  const updateLocationMutation = useMutation({
    mutationFn: ({
      palletId,
      locationId,
    }: {
      palletId: string;
      locationId: string;
    }) => updatePalletLocation(palletId, locationId),
    onMutate: ({ palletId }) => {
      setIsUpdatingLocation(palletId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["shipmentSummary", shipmentId],
      });
      toast.success("Location assigned successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to assign location");
    },
    onSettled: () => {
      setIsUpdatingLocation(null);
    },
  });

  // Handler for location assignment
  const handleLocationAssign = (palletId: string, locationId: string) => {
    updateLocationMutation.mutate({ palletId, locationId });
  };

  // Handler for bulk placard printing
  const handleBulkPrint = async (destination: string) => {
    if (!destination) {
      toast.error("No destination specified for printing");
      return;
    }

    await printPlacardsByDestination(destination);
  };

  return {
    handleLocationAssign,
    handleBulkPrint,
    isUpdatingLocation,
    isPrintingPlacards: false, // Handled by usePrintPlacard hook
  };
};
