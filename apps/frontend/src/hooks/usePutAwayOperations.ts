import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";

/**
 * Custom hook for put-away operations
 * Handles pallet movement and shipment completion functionality
 */
export const usePutAwayOperations = (
  shipmentId: string | undefined,
  poNumber: string | undefined
) => {
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();
  const queryClient = useQueryClient();
  const [movingPallets, setMovingPallets] = useState<Set<string>>(new Set());
  const [completedPallets, setCompletedPallets] = useState<Set<string>>(
    new Set()
  );

  // API function for moving pallets
  const movePallet = async (
    palletId: string,
    newLocationId: string | undefined
  ): Promise<void> => {
    const body = newLocationId ? { newLocationId } : {};
    return fetchWithAuth(`/api/pallets/${palletId}/move`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(body),
      token: appToken,
    });
  };

  // API function for completing shipment
  const completeShipment = async (shipmentId: string): Promise<void> => {
    return fetchWithAuth(
      `/api/shipments/${shipmentId}?warehouseId=${currentWarehouse?.id}`,
      {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ status: "Received" }),
        token: appToken,
      }
    );
  };

  // Mutation for moving pallets
  const movePalletMutation = useMutation({
    mutationFn: ({
      palletId,
      newLocationId,
    }: {
      palletId: string;
      newLocationId?: string;
    }) => movePallet(palletId, newLocationId),
    onMutate: ({ palletId }) => {
      setMovingPallets((prev) => new Set(prev).add(palletId));
    },
    onSuccess: (_, { palletId }) => {
      setCompletedPallets((prev) => new Set(prev).add(palletId));
      queryClient.invalidateQueries({
        queryKey: ["shipmentSummary", shipmentId],
      });
      toast.success("Pallet moved successfully");
    },
    onError: (error: Error, { palletId }) => {
      toast.error(error.message || "Failed to move pallet");
      setMovingPallets((prev) => {
        const newSet = new Set(prev);
        newSet.delete(palletId);
        return newSet;
      });
    },
    onSettled: (_, __, { palletId }) => {
      setMovingPallets((prev) => {
        const newSet = new Set(prev);
        newSet.delete(palletId);
        return newSet;
      });
    },
  });

  // Mutation for completing shipment
  const completeShipmentMutation = useMutation({
    mutationFn: (shipmentId: string) => completeShipment(shipmentId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["shipmentSummary", shipmentId],
      });
      queryClient.invalidateQueries({ queryKey: ["shipment", poNumber] });
      toast.success("Shipment marked as received");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to complete shipment");
    },
  });

  // Handler for moving pallets
  const handleMovePallet = (palletId: string, hasLocation: boolean) => {
    if (hasLocation) {
      // Confirm move - no new location needed
      movePalletMutation.mutate({ palletId });
    } else {
      // Move to assigned location - this should not happen if location is pre-assigned
      toast.error("Pallet needs a storage location assignment first");
    }
  };

  // Handler for completing shipment
  const handleCompleteShipment = (shipmentId: string) => {
    completeShipmentMutation.mutate(shipmentId);
  };

  return {
    handleMovePallet,
    handleCompleteShipment,
    movingPallets,
    completedPallets,
    isCompletingShipment: completeShipmentMutation.isPending,
  };
};
