import { useState, useMemo, useCallback } from "react";
import { Pallet } from "@quildora/types";

export interface PalletFilters {
  shipToDestination: string;
  destinationCode: string;
  description: string;
  locationId: string;
  includeReleased: boolean;
}

/**
 * Custom hook for managing pallet filtering logic
 * Provides filter state management and client-side filtering functionality
 */
export const usePalletFiltering = () => {
  // Initial filter state
  const initialFilters = useMemo(
    () => ({
      shipToDestination: "",
      destinationCode: "",
      description: "",
      locationId: "",
      includeReleased: false,
    }),
    []
  );

  const [filters, setFilters] = useState<PalletFilters>(initialFilters);

  // Filter change handler
  const handleFilterChange = useCallback((newFilters: Partial<PalletFilters>) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
  }, []);

  // Reset filters handler
  const handleResetFilters = useCallback(() => {
    setFilters(initialFilters);
  }, [initialFilters]);

  // Client-side filtering function
  const filterPallets = useCallback((pallets: Pallet[]) => {
    return pallets.filter((pallet: Pallet) => {
      // Early return for status filter to avoid unnecessary processing
      if (!filters.includeReleased && pallet.status === "Released") {
        return false;
      }

      // Early return for location filter
      if (filters.locationId && pallet.locationId !== filters.locationId) {
        return false;
      }

      // Ship to destination filter
      if (filters.shipToDestination) {
        if (
          !pallet.shipToDestination ||
          !pallet.shipToDestination
            .toLowerCase()
            .includes(filters.shipToDestination.toLowerCase())
        ) {
          return false;
        }
      }

      // Description filter
      if (filters.description) {
        if (
          !pallet.description ||
          !pallet.description
            .toLowerCase()
            .includes(filters.description.toLowerCase())
        ) {
          return false;
        }
      }

      // Destination code filter - using simple pallet.destinationCode field
      if (filters.destinationCode) {
        if (
          !pallet.destinationCode ||
          !pallet.destinationCode
            .toLowerCase()
            .includes(filters.destinationCode.toLowerCase())
        ) {
          return false;
        }
      }

      return true;
    });
  }, [filters]);

  return {
    filters,
    handleFilterChange,
    handleResetFilters,
    filterPallets,
  };
};
