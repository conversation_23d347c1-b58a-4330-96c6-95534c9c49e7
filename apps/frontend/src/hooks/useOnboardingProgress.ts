"use client";

import { useState, useCallback, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { OnboardingStep } from "@quildora/types";

interface OnboardingProgressState {
  currentStep: OnboardingStep;
  completedSteps: OnboardingStep[];
  sessionId: string | null;
  canProceed: boolean;
  canGoBack: boolean;
}

interface StepValidation {
  isValid: boolean;
  errors: string[];
}

interface UseOnboardingProgressOptions {
  initialStep?: OnboardingStep;
  sessionId?: string | null;
  validateStep?: (
    step: OnboardingStep
  ) => Promise<StepValidation> | StepValidation;
  onStepChange?: (step: OnboardingStep) => void;
}

const STEP_ORDER: OnboardingStep[] = [
  'business_info',
  'admin_account',
  'warehouse_setup',
  'team_setup',
  'completion'
];

const STEP_ROUTES = {
  business_info: '/auth/signup/business',
  admin_account: '/auth/signup/business/account',
  warehouse_setup: '/auth/signup/business/warehouse',
  team_setup: '/auth/signup/business/team',
  completion: '/auth/signup/business/complete',
} as const;

const JOIN_STEP_ROUTES = {
  invitation_code: '/auth/signup/join',
  join_account: '/auth/signup/join/account',
  completion: '/auth/signup/join/complete',
} as const;

export function useOnboardingProgress(options: UseOnboardingProgressOptions = {}) {
  const router = useRouter();
  const pathname = usePathname();

  const [state, setState] = useState<OnboardingProgressState>(() => {
    const currentStep = getCurrentStepFromPath(pathname) || options.initialStep || 'business_info';
    return {
      currentStep,
      completedSteps: [],
      sessionId: options.sessionId || null,
      canProceed: false,
      canGoBack: getStepIndex(currentStep) > 0,
    };
  });

  const [isValidating, setIsValidating] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Update current step when pathname changes
  useEffect(() => {
    const stepFromPath = getCurrentStepFromPath(pathname);
    if (stepFromPath && stepFromPath !== state.currentStep) {
      setState(prev => ({
        ...prev,
        currentStep: stepFromPath,
        canGoBack: getStepIndex(stepFromPath) > 0,
      }));

      if (options.onStepChange) {
        options.onStepChange(stepFromPath);
      }
    }
  }, [pathname, state.currentStep, options]);

  // Validate current step
  const validateCurrentStep = useCallback(async (): Promise<StepValidation> => {
    if (!options.validateStep) {
      return { isValid: true, errors: [] };
    }

    setIsValidating(true);
    try {
      const result = await options.validateStep(state.currentStep);
      setValidationErrors(result.errors);
      setState(prev => ({ ...prev, canProceed: result.isValid }));
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Validation failed';
      setValidationErrors([errorMessage]);
      setState(prev => ({ ...prev, canProceed: false }));
      return { isValid: false, errors: [errorMessage] };
    } finally {
      setIsValidating(false);
    }
  }, [state.currentStep, options]);

  // Navigate to specific step
  const goToStep = useCallback((step: OnboardingStep, force = false) => {
    const stepIndex = getStepIndex(step);
    const currentIndex = getStepIndex(state.currentStep);

    // Prevent skipping ahead unless forced
    if (!force && stepIndex > currentIndex + 1) {
      console.warn(`Cannot skip to step ${step} from ${state.currentStep}`);
      return false;
    }

    const route = STEP_ROUTES[step];
    if (route) {
      router.push(route);
      return true;
    }

    return false;
  }, [state.currentStep, router]);

  // Navigate to next step
  const goToNextStep = useCallback(async () => {
    // Validate current step first
    const validation = await validateCurrentStep();
    if (!validation.isValid) {
      return false;
    }

    const currentIndex = getStepIndex(state.currentStep);
    const nextStep = STEP_ORDER[currentIndex + 1];

    if (nextStep) {
      // Mark current step as completed
      setState(prev => ({
        ...prev,
        completedSteps: [...prev.completedSteps.filter(s => s !== state.currentStep), state.currentStep],
      }));

      return goToStep(nextStep, true);
    }

    return false;
  }, [state.currentStep, validateCurrentStep, goToStep]);

  // Navigate to previous step
  const goToPreviousStep = useCallback(() => {
    const currentIndex = getStepIndex(state.currentStep);
    const previousStep = STEP_ORDER[currentIndex - 1];

    if (previousStep) {
      return goToStep(previousStep, true);
    }

    return false;
  }, [state.currentStep, goToStep]);

  // Mark step as completed
  const markStepCompleted = useCallback((step: OnboardingStep) => {
    setState(prev => ({
      ...prev,
      completedSteps: [...prev.completedSteps.filter(s => s !== step), step],
    }));
  }, []);

  // Update session ID
  const updateSessionId = useCallback((sessionId: string) => {
    setState(prev => ({ ...prev, sessionId }));
  }, []);

  // Get progress percentage
  const getProgressPercentage = useCallback(() => {
    const currentIndex = getStepIndex(state.currentStep);
    const totalSteps = STEP_ORDER.length;
    return Math.round(((currentIndex + 1) / totalSteps) * 100);
  }, [state.currentStep]);

  // Check if step is completed
  const isStepCompleted = useCallback((step: OnboardingStep) => {
    return state.completedSteps.includes(step);
  }, [state.completedSteps]);

  // Get step info
  const getStepInfo = useCallback((step: OnboardingStep) => {
    const index = getStepIndex(step);
    const currentIndex = getStepIndex(state.currentStep);

    return {
      order: index + 1,
      total: STEP_ORDER.length,
      isActive: step === state.currentStep,
      isCompleted: isStepCompleted(step),
      isAccessible: index <= currentIndex + 1,
      route: STEP_ROUTES[step],
    };
  }, [state.currentStep, isStepCompleted]);

  return {
    // State
    currentStep: state.currentStep,
    completedSteps: state.completedSteps,
    sessionId: state.sessionId,
    canProceed: state.canProceed,
    canGoBack: state.canGoBack,
    isValidating,
    validationErrors,

    // Actions
    goToStep,
    goToNextStep,
    goToPreviousStep,
    markStepCompleted,
    updateSessionId,
    validateCurrentStep,

    // Computed
    getProgressPercentage,
    isStepCompleted,
    getStepInfo,

    // Constants
    stepOrder: STEP_ORDER,
    stepRoutes: STEP_ROUTES,
  };
}

// Helper functions
function getCurrentStepFromPath(pathname: string): OnboardingStep | null {
  // Business signup flow
  if (pathname === '/auth/signup/business') return 'business_info';
  if (pathname === '/auth/signup/business/account') return 'admin_account';
  if (pathname === '/auth/signup/business/warehouse') return 'warehouse_setup';
  if (pathname === '/auth/signup/business/team') return 'team_setup';
  if (pathname === '/auth/signup/business/complete') return 'completion';

  // Join flow - map to equivalent steps
  if (pathname === '/auth/signup/join') return 'admin_account'; // Join starts at account creation
  if (pathname === '/auth/signup/join/account') return 'admin_account';
  if (pathname === '/auth/signup/join/complete') return 'completion';

  return null;
}

function getStepIndex(step: OnboardingStep): number {
  return STEP_ORDER.indexOf(step);
}

// Export types
export type { OnboardingProgressState, StepValidation, UseOnboardingProgressOptions };