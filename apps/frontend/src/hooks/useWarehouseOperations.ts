"use client";

import { useCallback, useMemo } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import {
  useLocations,
  useItems,
  usePallets,
  useShipments,
  usePurchaseOrders,
} from "@/hooks/api";
import {
  LocationFilters,
  PalletFilters,
  LocationCategory,
} from "@quildora/types";
import { toast } from "sonner";

/**
 * Simplified warehouse operations hook that follows React hooks rules
 * Provides essential warehouse data and operations without complex nested hooks
 */
export function useWarehouseOperations() {
  const queryClient = useQueryClient();
  const { currentWarehouse } = useWarehouse();
  const { appToken } = useAuth();

  // Core data fetching
  const {
    data: locations = [],
    isLoading: isLoadingLocations,
    refetch: refetchLocations,
  } = useLocations();

  const {
    data: items = [],
    isLoading: isLoadingItems,
    refetch: refetchItems,
  } = useItems();

  const {
    data: pallets = [],
    isLoading: isLoadingPallets,
    refetch: refetchPallets,
  } = usePallets();

  const {
    data: shipmentsData,
    isLoading: isLoadingShipments,
    refetch: refetchShipments,
  } = useShipments();

  const shipments = shipmentsData?.shipments || [];

  const {
    data: purchaseOrdersData,
    isLoading: isLoadingPOs,
    refetch: refetchPOs,
  } = usePurchaseOrders();

  const purchaseOrders = purchaseOrdersData?.purchaseOrders || [];

  // Computed warehouse statistics
  const warehouseStats = useMemo(() => {
    if (!currentWarehouse) return null;

    return {
      warehouse: {
        id: currentWarehouse.id,
        name: currentWarehouse.name,
      },
      locations: {
        total: locations.length,
        receiving: locations.filter(
          (l) => l.category === LocationCategory.Receiving
        ).length,
        storage: locations.filter(
          (l) => l.category === LocationCategory.Storage
        ).length,
        shipping: locations.filter(
          (l) => l.category === LocationCategory.Shipping
        ).length,
      },
      pallets: {
        total: pallets.length,
        available: pallets.filter((p) => p.status === "available").length,
        inTransit: pallets.filter((p) => p.status === "in_transit").length,
        shipped: pallets.filter((p) => p.status === "shipped").length,
      },
      items: {
        total: items.length,
        active: items.filter((i) => i.status === "ACTIVE").length,
        lowStock: items.filter(
          (i) =>
            i.lowStockThreshold &&
            (i as any).currentStock <= i.lowStockThreshold
        ).length,
      },
      operations: {
        activeShipments: shipments.filter((s) => s.status === "processing")
          .length,
        pendingPOs: purchaseOrders.filter((po) => po.status === "pending")
          .length,
        receivingPOs: purchaseOrders.filter((po) => po.status === "processing")
          .length,
      },
    };
  }, [currentWarehouse, locations, pallets, items, shipments, purchaseOrders]);

  // Refresh all warehouse data
  const refreshAll = useCallback(async () => {
    if (!currentWarehouse) return;

    try {
      await Promise.all([
        refetchLocations(),
        refetchItems(),
        refetchPallets(),
        refetchShipments(),
        refetchPOs(),
      ]);

      // Also invalidate all warehouse-scoped queries
      queryClient.invalidateQueries({
        queryKey: ["locations", currentWarehouse.id],
      });
      queryClient.invalidateQueries({
        queryKey: ["pallets", currentWarehouse.id],
      });
      queryClient.invalidateQueries({
        queryKey: ["items", currentWarehouse.id],
      });

      toast.success("Warehouse data refreshed");
    } catch (error) {
      toast.error("Failed to refresh warehouse data");
      throw error;
    }
  }, [
    currentWarehouse,
    queryClient,
    refetchLocations,
    refetchItems,
    refetchPallets,
    refetchShipments,
    refetchPOs,
  ]);

  // Simple filter functions (no nested hooks)
  const getFilteredPallets = useCallback(
    (filters: PalletFilters) => {
      return pallets.filter((pallet) => {
        if (filters.status && pallet.status !== filters.status) return false;
        if (filters.locationId && pallet.location?.id !== filters.locationId)
          return false;
        if (
          filters.search &&
          !pallet.barcode?.toLowerCase().includes(filters.search.toLowerCase())
        )
          return false;
        if (
          filters.shipToDestination &&
          pallet.shipToDestination !== filters.shipToDestination
        )
          return false;
        return true;
      });
    },
    [pallets]
  );

  const getFilteredLocations = useCallback(
    (filters: LocationFilters) => {
      return locations.filter((location) => {
        if (filters.category && location.category !== filters.category)
          return false;
        if (filters.type && location.locationType !== filters.type)
          return false;
        if (
          filters.search &&
          !location.name.toLowerCase().includes(filters.search.toLowerCase())
        )
          return false;
        return true;
      });
    },
    [locations]
  );

  const searchPallets = useCallback(
    (query: string) => {
      const lowercaseQuery = query.toLowerCase();
      return pallets.filter(
        (pallet) =>
          pallet.barcode?.toLowerCase().includes(lowercaseQuery) ||
          pallet.description?.toLowerCase().includes(lowercaseQuery) ||
          pallet.shipToDestination?.toLowerCase().includes(lowercaseQuery)
      );
    },
    [pallets]
  );

  const searchItems = useCallback(
    (query: string) => {
      const lowercaseQuery = query.toLowerCase();
      return items.filter(
        (item) =>
          item.name.toLowerCase().includes(lowercaseQuery) ||
          item.sku?.toLowerCase().includes(lowercaseQuery) ||
          item.description?.toLowerCase().includes(lowercaseQuery)
      );
    },
    [items]
  );

  // Loading states
  const isLoading =
    isLoadingLocations ||
    isLoadingItems ||
    isLoadingPallets ||
    isLoadingShipments ||
    isLoadingPOs;

  const loadingStates = {
    isLoading,
    isLoadingLocations,
    isLoadingItems,
    isLoadingPallets,
    isLoadingShipments,
    isLoadingPOs,
  };

  return {
    // Data
    locations,
    items,
    pallets,
    shipments,
    purchaseOrders,

    // Statistics
    stats: warehouseStats,

    // Loading states
    ...loadingStates,

    // Operations
    refreshAll,
    getFilteredPallets,
    getFilteredLocations,
    searchPallets,
    searchItems,

    // Utilities
    canPerformOperations: !!currentWarehouse && !!appToken,
    currentWarehouse,
  };
}

/**
 * Hook for warehouse dashboard data
 * Optimized for dashboard display with minimal re-renders
 */
export function useWarehouseDashboard() {
  const { stats, isLoading, canPerformOperations } = useWarehouseOperations();

  // Dashboard-specific computed values
  const dashboardData = useMemo(() => {
    if (!stats) return null;

    return {
      summary: {
        totalPallets: stats.pallets.total,
        availablePallets: stats.pallets.available,
        totalLocations: stats.locations.total,
        activeItems: stats.items.active,
      },
      alerts: {
        lowStockItems: stats.items.lowStock,
        pendingShipments: stats.operations.activeShipments,
        pendingReceiving: stats.operations.receivingPOs,
      },
      utilization: {
        palletUtilization:
          stats.pallets.total > 0
            ? Math.round((stats.pallets.available / stats.pallets.total) * 100)
            : 0,
        locationUtilization:
          stats.locations.total > 0
            ? Math.round(
                ((stats.locations.total - stats.locations.storage) /
                  stats.locations.total) *
                  100
              )
            : 0,
      },
    };
  }, [stats]);

  return {
    data: dashboardData,
    isLoading,
    canPerformOperations,
    warehouse: stats?.warehouse,
  };
}
