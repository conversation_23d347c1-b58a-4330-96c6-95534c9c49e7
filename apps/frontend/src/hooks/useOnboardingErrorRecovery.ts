'use client';

import { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

export interface ErrorRecoveryState {
  hasError: boolean;
  errorType: 'network' | 'validation' | 'authentication' | 'unknown';
  errorMessage: string;
  retryCount: number;
  canRetry: boolean;
  lastAction?: string;
}

export interface RecoveryOptions {
  maxRetries?: number;
  autoRetryDelay?: number;
  enableAutoRetry?: boolean;
  onError?: (error: Error, context?: any) => void;
  onRecovery?: () => void;
}

export function useOnboardingErrorRecovery(options: RecoveryOptions = {}) {
  const {
    maxRetries = 3,
    autoRetryDelay = 2000,
    enableAutoRetry = false,
    onError,
    onRecovery,
  } = options;

  const router = useRouter();
  const [errorState, setErrorState] = useState<ErrorRecoveryState>({
    hasError: false,
    errorType: 'unknown',
    errorMessage: '',
    retryCount: 0,
    canRetry: true,
  });

  // Auto-retry mechanism
  useEffect(() => {
    if (
      enableAutoRetry &&
      errorState.hasError &&
      errorState.canRetry &&
      errorState.retryCount < maxRetries
    ) {
      const timer = setTimeout(() => {
        handleRetry();
      }, autoRetryDelay);

      return () => clearTimeout(timer);
    }
  }, [errorState, enableAutoRetry, maxRetries, autoRetryDelay]);

  const determineErrorType = (error: Error): ErrorRecoveryState['errorType'] => {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return 'network';
    }
    if (message.includes('validation') || message.includes('invalid')) {
      return 'validation';
    }
    if (message.includes('unauthorized') || message.includes('authentication')) {
      return 'authentication';
    }
    
    return 'unknown';
  };

  const handleError = useCallback((error: Error, context?: any) => {
    const errorType = determineErrorType(error);
    const canRetry = errorType !== 'validation' && errorState.retryCount < maxRetries;

    setErrorState(prev => ({
      hasError: true,
      errorType,
      errorMessage: error.message,
      retryCount: prev.retryCount,
      canRetry,
      lastAction: context?.action,
    }));

    // Call custom error handler
    onError?.(error, context);

    // Show appropriate toast message
    const toastMessage = getErrorToastMessage(errorType, error.message);
    toast.error(toastMessage);

    // Log error for debugging
    console.error('Onboarding error:', {
      error: error.message,
      type: errorType,
      context,
      retryCount: errorState.retryCount,
    });
  }, [errorState.retryCount, maxRetries, onError]);

  const handleRetry = useCallback(async () => {
    if (!errorState.canRetry || errorState.retryCount >= maxRetries) {
      return false;
    }

    setErrorState(prev => ({
      ...prev,
      retryCount: prev.retryCount + 1,
    }));

    try {
      // Clear error state temporarily
      setErrorState(prev => ({
        ...prev,
        hasError: false,
      }));

      // Call recovery callback
      onRecovery?.();

      toast.success('Retrying...');
      return true;
    } catch (error) {
      // If retry fails, restore error state
      setErrorState(prev => ({
        ...prev,
        hasError: true,
      }));
      return false;
    }
  }, [errorState, maxRetries, onRecovery]);

  const clearError = useCallback(() => {
    setErrorState({
      hasError: false,
      errorType: 'unknown',
      errorMessage: '',
      retryCount: 0,
      canRetry: true,
    });
  }, []);

  const resetRetryCount = useCallback(() => {
    setErrorState(prev => ({
      ...prev,
      retryCount: 0,
      canRetry: true,
    }));
  }, []);

  // Recovery strategies for different error types
  const recoverFromNetworkError = useCallback(async () => {
    // Check network connectivity
    if (!navigator.onLine) {
      toast.error('No internet connection. Please check your network and try again.');
      return false;
    }

    // Attempt to retry the last action
    return handleRetry();
  }, [handleRetry]);

  const recoverFromAuthError = useCallback(() => {
    // Clear any stored auth tokens
    localStorage.removeItem('quildora_auth_token');
    localStorage.removeItem('quildora_onboarding_session');
    
    // Redirect to sign in
    toast.error('Authentication expired. Please sign in again.');
    router.push('/auth/signin');
  }, [router]);

  const recoverFromValidationError = useCallback((errors: string[]) => {
    // Show validation errors to user
    toast.error('Please fix the form errors and try again.');
    
    // Don't auto-retry validation errors
    setErrorState(prev => ({
      ...prev,
      canRetry: false,
    }));
  }, []);

  const recoverFromUnknownError = useCallback(() => {
    // For unknown errors, try a general recovery
    if (errorState.retryCount < maxRetries) {
      return handleRetry();
    } else {
      // If max retries reached, offer to restart onboarding
      toast.error('Multiple errors occurred. You may need to restart the onboarding process.');
      return false;
    }
  }, [errorState.retryCount, maxRetries, handleRetry]);

  // Main recovery function
  const recover = useCallback(async () => {
    switch (errorState.errorType) {
      case 'network':
        return recoverFromNetworkError();
      case 'authentication':
        recoverFromAuthError();
        return true;
      case 'validation':
        recoverFromValidationError([errorState.errorMessage]);
        return false;
      case 'unknown':
      default:
        return recoverFromUnknownError();
    }
  }, [
    errorState.errorType,
    errorState.errorMessage,
    recoverFromNetworkError,
    recoverFromAuthError,
    recoverFromValidationError,
    recoverFromUnknownError,
  ]);

  // Navigation recovery helpers
  const goToPreviousStep = useCallback(() => {
    clearError();
    router.back();
  }, [clearError, router]);

  const restartOnboarding = useCallback(() => {
    // Clear all onboarding data
    localStorage.removeItem('quildora_onboarding_session');
    clearError();
    
    // Redirect to start
    toast.info('Starting fresh onboarding process...');
    router.push('/auth/signup/business');
  }, [clearError, router]);

  const goToSupport = useCallback(() => {
    // Open support page or email
    window.open('mailto:<EMAIL>?subject=Onboarding Error&body=' + 
      encodeURIComponent(`Error: ${errorState.errorMessage}\nType: ${errorState.errorType}`));
  }, [errorState]);

  return {
    errorState,
    handleError,
    handleRetry,
    clearError,
    resetRetryCount,
    recover,
    goToPreviousStep,
    restartOnboarding,
    goToSupport,
    
    // Convenience getters
    hasError: errorState.hasError,
    canRetry: errorState.canRetry,
    errorType: errorState.errorType,
    errorMessage: errorState.errorMessage,
    retryCount: errorState.retryCount,
    maxRetriesReached: errorState.retryCount >= maxRetries,
  };
}

// Helper function to get user-friendly error messages
function getErrorToastMessage(errorType: ErrorRecoveryState['errorType'], originalMessage: string): string {
  switch (errorType) {
    case 'network':
      return 'Connection problem. Please check your internet and try again.';
    case 'authentication':
      return 'Your session has expired. Please sign in again.';
    case 'validation':
      return 'Please check your input and try again.';
    case 'unknown':
    default:
      return 'Something went wrong. Please try again.';
  }
}

// Hook for handling specific onboarding step errors
export function useStepErrorRecovery(stepName: string) {
  const baseRecovery = useOnboardingErrorRecovery({
    maxRetries: 2,
    enableAutoRetry: false,
    onError: (error, context) => {
      // Log step-specific error
      console.error(`Error in ${stepName} step:`, error, context);
    },
  });

  const handleStepError = useCallback((error: Error, action?: string) => {
    baseRecovery.handleError(error, { step: stepName, action });
  }, [baseRecovery, stepName]);

  const retryStep = useCallback(async () => {
    // Step-specific retry logic
    toast.info(`Retrying ${stepName} step...`);
    return baseRecovery.handleRetry();
  }, [baseRecovery, stepName]);

  return {
    ...baseRecovery,
    handleStepError,
    retryStep,
  };
}

// Hook for form-specific error recovery
export function useFormErrorRecovery() {
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFormError = useCallback((fieldErrors: Record<string, string>) => {
    setFormErrors(fieldErrors);
    
    // Show summary toast
    const errorCount = Object.keys(fieldErrors).length;
    toast.error(`Please fix ${errorCount} form error${errorCount > 1 ? 's' : ''}`);
  }, []);

  const clearFormErrors = useCallback(() => {
    setFormErrors({});
  }, []);

  const clearFieldError = useCallback((fieldName: string) => {
    setFormErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fieldName];
      return newErrors;
    });
  }, []);

  const hasFormErrors = Object.keys(formErrors).length > 0;
  const getFieldError = (fieldName: string) => formErrors[fieldName];

  return {
    formErrors,
    hasFormErrors,
    isSubmitting,
    setIsSubmitting,
    handleFormError,
    clearFormErrors,
    clearFieldError,
    getFieldError,
  };
}
