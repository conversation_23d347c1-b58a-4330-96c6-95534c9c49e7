"use client";

import { useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { fetchWithAuth } from "@/lib/api";
import { getWarehouseQueryConfig } from "@/lib/query-config";

/**
 * Simple data prefetching hook
 * Prefetches critical warehouse data to eliminate loading spinners
 */
export function useDataPrefetching() {
  const queryClient = useQueryClient();
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  useEffect(() => {
    if (isAuthLoading || !appToken || !currentWarehouse) {
      return;
    }

    const warehouseId = currentWarehouse.id;

    // Simple prefetch function
    const prefetchCriticalData = () => {
      // Prefetch pallets (most accessed)
      queryClient.prefetchQuery({
        queryKey: ["pallets", warehouseId, {}, appToken],
        queryFn: () => {
          const params = new URLSearchParams({ warehouseId });
          return fetchWithAuth(`/api/pallets?${params}`, { token: appToken });
        },
        ...getWarehouseQueryConfig("pallets"),
      });

      // Prefetch locations
      queryClient.prefetchQuery({
        queryKey: ["locations", warehouseId, {}, appToken],
        queryFn: () => {
          const params = new URLSearchParams({ warehouseId });
          return fetchWithAuth(`/api/locations?${params}`, { token: appToken });
        },
        ...getWarehouseQueryConfig("locations"),
      });
    };

    // Prefetch after a short delay
    const timeoutId = setTimeout(prefetchCriticalData, 50);
    return () => clearTimeout(timeoutId);
  }, [queryClient, appToken, currentWarehouse, isAuthLoading]);
}

/**
 * Hook to prefetch data for specific routes
 */
export function useRoutePrefetching(route: string) {
  const queryClient = useQueryClient();
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  useEffect(() => {
    if (isAuthLoading || !appToken || !currentWarehouse) {
      return;
    }

    const warehouseId = currentWarehouse.id;

    // Route-specific prefetching
    switch (route) {
      case "/pallets":
        // Prefetch pallet-related data
        queryClient.prefetchQuery({
          queryKey: ["pallets", warehouseId, {}, appToken],
          queryFn: async () => {
            const searchParams = new URLSearchParams();
            searchParams.append("warehouseId", warehouseId);
            return fetchWithAuth(`/api/pallets?${searchParams.toString()}`, {
              token: appToken,
            });
          },
          ...getWarehouseQueryConfig("pallets"),
        });
        break;

      case "/locations":
        // Prefetch location-related data
        queryClient.prefetchQuery({
          queryKey: ["locations", warehouseId, {}, appToken],
          queryFn: async () => {
            const searchParams = new URLSearchParams();
            searchParams.append("warehouseId", warehouseId);
            return fetchWithAuth(`/api/locations?${searchParams.toString()}`, {
              token: appToken,
            });
          },
          ...getWarehouseQueryConfig("locations"),
        });
        break;

      case "/receiving":
        // Prefetch receiving-related data
        queryClient.prefetchQuery({
          queryKey: ["shipments", warehouseId, {}, appToken],
          queryFn: async () => {
            const searchParams = new URLSearchParams();
            searchParams.append("warehouseId", warehouseId);
            return fetchWithAuth(`/api/shipments?${searchParams.toString()}`, {
              token: appToken,
            });
          },
          ...getWarehouseQueryConfig("shipments"),
        });

        queryClient.prefetchQuery({
          queryKey: ["purchaseOrders", warehouseId, {}, appToken],
          queryFn: async () => {
            const searchParams = new URLSearchParams();
            searchParams.append("warehouseId", warehouseId);
            return fetchWithAuth(
              `/api/purchase-orders?${searchParams.toString()}`,
              { token: appToken }
            );
          },
          ...getWarehouseQueryConfig("purchaseOrders"),
        });
        break;

      case "/picking":
        // Prefetch picking-related data
        queryClient.prefetchQuery({
          queryKey: ["pallets", warehouseId, { status: "available" }, appToken],
          queryFn: async () => {
            const searchParams = new URLSearchParams();
            searchParams.append("warehouseId", warehouseId);
            searchParams.append("status", "available");
            return fetchWithAuth(`/api/pallets?${searchParams.toString()}`, {
              token: appToken,
            });
          },
          ...getWarehouseQueryConfig("pallets"),
        });
        break;

      default:
        // Default prefetching for common data
        queryClient.prefetchQuery({
          queryKey: ["pallets", warehouseId, {}, appToken],
          queryFn: async () => {
            const searchParams = new URLSearchParams();
            searchParams.append("warehouseId", warehouseId);
            return fetchWithAuth(`/api/pallets?${searchParams.toString()}`, {
              token: appToken,
            });
          },
          ...getWarehouseQueryConfig("pallets"),
        });
        break;
    }
  }, [queryClient, appToken, currentWarehouse, isAuthLoading, route]);
}

/**
 * Hook to check if critical data is already cached
 * Helps determine if we should show loading states
 */
export function useDataCacheStatus() {
  const queryClient = useQueryClient();
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  if (!appToken || !currentWarehouse) {
    return {
      hasCachedPallets: false,
      hasCachedLocations: false,
      hasCachedItems: false,
      hasAnyCachedData: false,
    };
  }

  const warehouseId = currentWarehouse.id;

  const hasCachedPallets = !!queryClient.getQueryData([
    "pallets",
    warehouseId,
    {},
    appToken,
  ]);
  const hasCachedLocations = !!queryClient.getQueryData([
    "locations",
    warehouseId,
    {},
    appToken,
  ]);
  const hasCachedItems = !!queryClient.getQueryData([
    "items",
    warehouseId,
    {},
    appToken,
  ]);

  return {
    hasCachedPallets,
    hasCachedLocations,
    hasCachedItems,
    hasAnyCachedData: hasCachedPallets || hasCachedLocations || hasCachedItems,
  };
}

/**
 * Hook to warm up the cache for a specific warehouse
 * Used when switching warehouses
 */
export function useWarehouseCacheWarming() {
  const queryClient = useQueryClient();
  const { appToken } = useAuth();

  const warmUpWarehouse = async (warehouseId: string) => {
    if (!appToken) return;

    // Warm up critical data for the warehouse
    const warmUpPromises = [
      queryClient.prefetchQuery({
        queryKey: ["pallets", warehouseId, {}, appToken],
        queryFn: async () => {
          const searchParams = new URLSearchParams();
          searchParams.append("warehouseId", warehouseId);
          return fetchWithAuth(`/api/pallets?${searchParams.toString()}`, {
            token: appToken,
          });
        },
        ...getWarehouseQueryConfig("pallets"),
      }),

      queryClient.prefetchQuery({
        queryKey: ["locations", warehouseId, {}, appToken],
        queryFn: async () => {
          const searchParams = new URLSearchParams();
          searchParams.append("warehouseId", warehouseId);
          return fetchWithAuth(`/api/locations?${searchParams.toString()}`, {
            token: appToken,
          });
        },
        ...getWarehouseQueryConfig("locations"),
      }),

      queryClient.prefetchQuery({
        queryKey: ["items", warehouseId, {}, appToken],
        queryFn: async () => {
          const searchParams = new URLSearchParams();
          searchParams.append("warehouseId", warehouseId);
          return fetchWithAuth(`/api/items?${searchParams.toString()}`, {
            token: appToken,
          });
        },
        ...getWarehouseQueryConfig("items"),
      }),
    ];

    await Promise.all(warmUpPromises);
  };

  return { warmUpWarehouse };
}
