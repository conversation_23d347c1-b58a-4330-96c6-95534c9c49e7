"use client";

import { useState, useCallback, useMemo } from "react";
import { z } from "zod";
import {
  OnboardingStep,
  BusinessInfo,
  AdminAccount,
  WarehouseSetup,
  TeamSetup,
} from "@quildora/types";

// Validation schemas for each onboarding step
export const businessInfoSchema = z.object({
  companyName: z
    .string()
    .min(1, "Company name is required")
    .min(2, "Company name must be at least 2 characters")
    .max(100, "Company name must be less than 100 characters"),
  industry: z.string().optional(),
  companySize: z.string().optional(),
  primaryUseCase: z.string().optional(),
});

export const adminAccountSchema = z.object({
  fullName: z
    .string()
    .min(1, "Full name is required")
    .min(2, "Full name must be at least 2 characters")
    .max(100, "Full name must be less than 100 characters"),
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .max(128, "Password must be less than 128 characters")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/[0-9]/, "Password must contain at least one number"),
  phoneNumber: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^\+?[\d\s\-\(\)]+$/.test(val),
      "Please enter a valid phone number"
    ),
});

export const warehouseSetupSchema = z.object({
  warehouseName: z
    .string()
    .min(1, "Warehouse name is required")
    .min(2, "Warehouse name must be at least 2 characters")
    .max(100, "Warehouse name must be less than 100 characters"),
  address: z
    .string()
    .max(500, "Address must be less than 500 characters")
    .optional(),
  warehouseType: z.string().optional(),
  expectedVolume: z.string().optional(),
});

export const teamSetupSchema = z.object({
  inviteEmails: z.array(z.string().email("Please enter valid email addresses")),
  defaultRole: z.enum(["WAREHOUSE_MEMBER", "WAREHOUSE_MANAGER"]),
});

// Combined schema for complete validation
export const completeOnboardingSchema = businessInfoSchema
  .merge(adminAccountSchema)
  .merge(warehouseSetupSchema)
  .merge(teamSetupSchema.partial());

// Schema map for easy access
const STEP_SCHEMAS = {
  business_info: businessInfoSchema,
  admin_account: adminAccountSchema,
  warehouse_setup: warehouseSetupSchema,
  team_setup: teamSetupSchema,
  completion: z.object({}), // No validation needed for completion step
} as const;

// Type for validation errors
export interface ValidationErrors {
  [key: string]: string;
}

// Type for field validation result
export interface FieldValidationResult {
  isValid: boolean;
  error?: string;
}

// Type for step validation result
export interface StepValidationResult {
  isValid: boolean;
  errors: ValidationErrors;
  validFields: string[];
  invalidFields: string[];
}

export interface UseOnboardingValidationReturn {
  // Current validation state
  errors: ValidationErrors;
  isValid: boolean;
  validFields: string[];
  invalidFields: string[];

  // Field-level validation
  validateField: (field: string, value: any) => FieldValidationResult;
  clearFieldError: (field: string) => void;
  setFieldError: (field: string, error: string) => void;

  // Step-level validation
  validateStep: (step: OnboardingStep, data: any) => StepValidationResult;
  validateCurrentData: (step: OnboardingStep, data: any) => boolean;

  // Utility functions
  clearAllErrors: () => void;
  hasErrors: () => boolean;
  getFieldError: (field: string) => string | undefined;
  getSchema: (step: OnboardingStep) => z.ZodSchema;
}

export function useOnboardingValidation(
  initialStep: OnboardingStep = "business_info"
): UseOnboardingValidationReturn {
  const [errors, setErrors] = useState<ValidationErrors>({});

  // Get schema for a specific step
  const getSchema = useCallback((step: OnboardingStep): z.ZodSchema => {
    return STEP_SCHEMAS[step] || z.object({});
  }, []);

  // Derived state
  const isValid = useMemo(() => Object.keys(errors).length === 0, [errors]);
  const validFields = useMemo(() => {
    // This would need to be tracked separately for a complete implementation
    // For now, we'll return an empty array
    return [];
  }, []);
  const invalidFields = useMemo(() => Object.keys(errors), [errors]);

  // Field-level validation
  const validateField = useCallback(
    (field: string, value: any): FieldValidationResult => {
      try {
        // Get the current step schema (this would need to be passed in or tracked)
        // For now, we'll try to validate against all schemas to find the field
        let fieldSchema: z.ZodSchema | null = null;

        for (const schema of Object.values(STEP_SCHEMAS)) {
          try {
            const shape = (schema as any)._def?.shape;
            if (shape && shape[field]) {
              fieldSchema = shape[field];
              break;
            }
          } catch {
            // Continue to next schema
          }
        }

        if (!fieldSchema) {
          return { isValid: true }; // If we can't find the field, assume it's valid
        }

        fieldSchema.parse(value);

        // Clear any existing error for this field
        setErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors[field];
          return newErrors;
        });

        return { isValid: true };
      } catch (error) {
        if (error instanceof z.ZodError) {
          const errorMessage = error.errors[0]?.message || "Invalid value";

          // Set error for this field
          setErrors((prev) => ({
            ...prev,
            [field]: errorMessage,
          }));

          return { isValid: false, error: errorMessage };
        }

        return { isValid: false, error: "Validation error" };
      }
    },
    []
  );

  // Clear field error
  const clearFieldError = useCallback((field: string) => {
    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  // Set field error
  const setFieldError = useCallback((field: string, error: string) => {
    setErrors((prev) => ({
      ...prev,
      [field]: error,
    }));
  }, []);

  // Step-level validation
  const validateStep = useCallback(
    (step: OnboardingStep, data: any): StepValidationResult => {
      const schema = getSchema(step);

      try {
        schema.parse(data);

        // Clear all errors if validation passes
        setErrors({});

        return {
          isValid: true,
          errors: {},
          validFields: Object.keys(data),
          invalidFields: [],
        };
      } catch (error) {
        if (error instanceof z.ZodError) {
          const validationErrors: ValidationErrors = {};
          const invalidFieldsList: string[] = [];

          error.errors.forEach((err) => {
            const field = err.path.join(".");
            validationErrors[field] = err.message;
            invalidFieldsList.push(field);
          });

          setErrors(validationErrors);

          return {
            isValid: false,
            errors: validationErrors,
            validFields: Object.keys(data).filter(
              (field) => !invalidFieldsList.includes(field)
            ),
            invalidFields: invalidFieldsList,
          };
        }

        return {
          isValid: false,
          errors: { general: "Validation failed" },
          validFields: [],
          invalidFields: Object.keys(data),
        };
      }
    },
    [getSchema]
  );

  // Validate current data (returns boolean for quick checks)
  const validateCurrentData = useCallback(
    (step: OnboardingStep, data: any): boolean => {
      const result = validateStep(step, data);
      return result.isValid;
    },
    [validateStep]
  );

  // Utility functions
  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  const hasErrors = useCallback((): boolean => {
    return Object.keys(errors).length > 0;
  }, [errors]);

  const getFieldError = useCallback(
    (field: string): string | undefined => {
      return errors[field];
    },
    [errors]
  );

  return {
    // Current validation state
    errors,
    isValid,
    validFields,
    invalidFields,

    // Field-level validation
    validateField,
    clearFieldError,
    setFieldError,

    // Step-level validation
    validateStep,
    validateCurrentData,

    // Utility functions
    clearAllErrors,
    hasErrors,
    getFieldError,
    getSchema,
  };
}
