"use client";

import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";

export interface AppLoadingState {
  isLoading: boolean;
  isReady: boolean;
  loadingMessage: string;
  progress: number;
  hasError: boolean;
  error: string | null;
  canRetry: boolean;
  retry: () => void;
}

/**
 * Unified loading state hook that combines auth and warehouse loading
 * Provides a single loading state instead of sequential auth → warehouse loading
 */
export function useAppLoading(): AppLoadingState {
  const { isLoading: authLoading, appUser, appToken } = useAuth();

  const {
    isLoadingWarehouses,
    warehouseError,
    currentWarehouse,
    accessibleWarehouses,
    retryLoadWarehouses,
  } = useWarehouse();

  // Calculate overall loading state
  const isAuthReady = !authLoading && !!appUser && !!appToken;
  const isWarehouseReady =
    !isLoadingWarehouses && accessibleWarehouses.length > 0;

  // App is loading if either auth or warehouses are loading
  const isLoading = authLoading || (isAuthReady && isLoadingWarehouses);

  // App is ready when both auth and warehouses are ready
  const isReady = isAuthReady && isWarehouseReady;

  // Use actual loading states instead of simulated progress
  const progress = isReady ? 100 : 0;

  // Determine loading message
  let loadingMessage = "Initializing...";
  if (authLoading) {
    loadingMessage = "Authenticating...";
  } else if (isAuthReady && isLoadingWarehouses) {
    loadingMessage = "Loading workspace...";
  } else if (isReady) {
    loadingMessage = "Ready";
  }

  // Error handling
  const hasError = !!warehouseError;
  const error = warehouseError;
  const canRetry = hasError && !!retryLoadWarehouses;
  const retry = retryLoadWarehouses || (() => {});

  return {
    isLoading,
    isReady,
    loadingMessage,
    progress,
    hasError,
    error,
    canRetry,
    retry,
  };
}
