import { useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { toast } from "sonner";
import { DestinationResponse } from "@quildora/types";
import { useDestinationsWithCodes } from "./api/useWarehouseData";

interface UseDestinationAutoPopulationProps {
  form: UseFormReturn<any>;
  onSuccess?: (message: string) => void;
}

interface UseDestinationAutoPopulationReturn {
  handleDestinationSelect: (destination: DestinationResponse | null) => void;
  handleDestinationCodeLookup: (code: string) => Promise<void>;
  isLookingUp: boolean;
}

/**
 * Custom hook for bidirectional destination auto-population
 * Supports both name → code and code → name workflows
 */
export function useDestinationAutoPopulation({
  form,
  onSuccess,
}: UseDestinationAutoPopulationProps): UseDestinationAutoPopulationReturn {
  const [isLookingUp, setIsLookingUp] = useState(false);
  const { data: destinations = [] } = useDestinationsWithCodes();

  /**
   * Handle destination selection from autocomplete (Name → Code workflow)
   * When user selects a destination name, auto-populate the code if available
   */
  const handleDestinationSelect = (destination: DestinationResponse | null) => {
    if (destination?.code) {
      form.setValue("destinationCode", destination.code);
      const message = `Auto-filled destination code: ${destination.code}`;

      toast.success(message, {
        description: `Code for ${destination.name}`,
        duration: 2000,
      });

      if (onSuccess) {
        onSuccess(message);
      }
    }
  };

  /**
   * Handle destination code lookup (Code → Name workflow)
   * When user enters a code, lookup and auto-populate the name if found
   */
  const handleDestinationCodeLookup = async (code: string): Promise<void> => {
    if (!code.trim()) return;

    // Validate code format (numbers only)
    if (!/^\d+$/.test(code.trim())) {
      return; // Let form validation handle the error message
    }

    setIsLookingUp(true);

    try {
      // Search for destination by code in the current destinations
      const matchingDestination = destinations.find(
        (dest) => dest.code === code.trim()
      );

      if (matchingDestination) {
        // Auto-populate the destination name
        form.setValue("shipToDestination", matchingDestination.name);

        const message = `Auto-filled destination: ${matchingDestination.name}`;

        toast.success(message, {
          description: `Found destination for code ${code}`,
          duration: 2000,
        });

        if (onSuccess) {
          onSuccess(message);
        }
      } else {
        // Code not found - this is not an error, user might be creating new destination
        // No toast needed, just let them continue
      }
    } catch (error) {
      console.error("Error looking up destination by code:", error);
      toast.error("Failed to lookup destination", {
        description: "Please try again or enter manually",
        duration: 3000,
      });
    } finally {
      setIsLookingUp(false);
    }
  };

  return {
    handleDestinationSelect,
    handleDestinationCodeLookup,
    isLookingUp,
  };
}

/**
 * Utility function to create destination code input handler with debouncing
 * This can be used with onBlur or onChange events
 */
export function createDestinationCodeHandler(
  handleDestinationCodeLookup: (code: string) => Promise<void>,
  debounceMs: number = 500
) {
  let timeoutId: NodeJS.Timeout;

  return (code: string) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      handleDestinationCodeLookup(code);
    }, debounceMs);
  };
}

/**
 * Utility function to format destination display name
 * Handles both destinations with and without codes
 */
export function formatDestinationDisplay(
  destination: DestinationResponse
): string {
  if (destination.code) {
    return `${destination.name} (${destination.code})`;
  }
  return destination.name;
}

/**
 * Utility function to validate destination code format
 * Returns true if code is valid (numbers only) or empty
 */
export function isValidDestinationCode(code: string): boolean {
  if (!code.trim()) return true; // Empty is valid (optional field)
  return /^\d+$/.test(code.trim());
}
