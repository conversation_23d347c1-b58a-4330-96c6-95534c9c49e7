"use client";

import { use<PERSON><PERSON>back, useMemo } from "react";
import { UseFormReturn } from "react-hook-form";
import { toast } from "sonner";

/**
 * Simplified form handlers that follow React hooks rules
 * Provides essential form utilities without complex nested hooks
 */
export function useFormHandlers<T extends Record<string, any>>(
  form: UseFormReturn<T>,
  options?: {
    onSuccess?: (data: T) => void;
    onError?: (error: Error) => void;
    resetOnSuccess?: boolean;
  }
) {
  const { onSuccess, onError, resetOnSuccess = true } = options || {};

  // Handle form submission with error handling
  const handleSubmit = form.handleSubmit(
    async (data: T) => {
      try {
        await onSuccess?.(data);
        if (resetOnSuccess) {
          form.reset();
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "An error occurred";
        toast.error(errorMessage);
        onError?.(error as Error);
      }
    },
    (errors) => {
      // Handle validation errors
      const firstError = Object.values(errors)[0];
      if (firstError?.message) {
        toast.error(String(firstError.message));
      } else {
        toast.error("Please check the form for errors");
      }
    }
  );

  // Reset form with confirmation
  const handleReset = useCallback(() => {
    form.reset();
    toast.success("Form reset");
  }, [form]);

  // Simple field operations
  const clearField = useCallback(
    (fieldName: string) => {
      form.setValue(fieldName as any, "" as any);
    },
    [form]
  );

  const setFields = useCallback(
    (fields: Record<string, any>) => {
      Object.entries(fields).forEach(([key, value]) => {
        form.setValue(key as any, value);
      });
    },
    [form]
  );

  // Form state helpers
  const formState = useMemo(
    () => ({
      isValid: form.formState.isValid,
      isDirty: form.formState.isDirty,
      isSubmitting: form.formState.isSubmitting,
      errors: form.formState.errors,
    }),
    [form.formState]
  );

  return {
    handleSubmit,
    handleReset,
    clearField,
    setFields,
    state: formState,
    form,
  };
}
