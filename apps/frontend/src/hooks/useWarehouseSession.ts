"use client";

import { useCallback, useEffect, useState } from "react";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { warehousePersistence, getWarehousePreferences } from "@/lib/warehouse-persistence";

/**
 * Hook for managing warehouse session state and persistence
 * Provides utilities for session management, validation, and restoration
 */
export function useWarehouseSession() {
  const { 
    currentWarehouse, 
    accessibleWarehouses, 
    isLoadingWarehouses,
    setCurrentWarehouse 
  } = useWarehouse();
  
  const [sessionStartTime] = useState(() => Date.now());
  const [lastWarehouseChange, setLastWarehouseChange] = useState<number | null>(null);

  // Track warehouse changes
  useEffect(() => {
    if (currentWarehouse) {
      setLastWarehouseChange(Date.now());
    }
  }, [currentWarehouse]);

  /**
   * Get session information
   */
  const getSessionInfo = useCallback(() => {
    const preferences = getWarehousePreferences();
    const sessionDuration = Date.now() - sessionStartTime;
    const timeSinceLastChange = lastWarehouseChange 
      ? Date.now() - lastWarehouseChange 
      : null;

    return {
      sessionStartTime,
      sessionDuration,
      lastWarehouseChange,
      timeSinceLastChange,
      currentWarehouse,
      warehouseCount: accessibleWarehouses.length,
      preferences,
      hasValidWarehouse: !!currentWarehouse && accessibleWarehouses.some(w => w.id === currentWarehouse.id),
    };
  }, [sessionStartTime, lastWarehouseChange, currentWarehouse, accessibleWarehouses]);

  /**
   * Validate current warehouse selection
   */
  const validateWarehouseSelection = useCallback(() => {
    if (!currentWarehouse) {
      return {
        isValid: false,
        reason: 'No warehouse selected',
        canRestore: accessibleWarehouses.length > 0,
      };
    }

    const isAccessible = accessibleWarehouses.some(w => w.id === currentWarehouse.id);
    if (!isAccessible) {
      return {
        isValid: false,
        reason: 'Selected warehouse is no longer accessible',
        canRestore: accessibleWarehouses.length > 0,
      };
    }

    return {
      isValid: true,
      reason: 'Warehouse selection is valid',
      canRestore: false,
    };
  }, [currentWarehouse, accessibleWarehouses]);

  /**
   * Restore warehouse selection from persistence
   */
  const restoreWarehouseSelection = useCallback(() => {
    if (isLoadingWarehouses || accessibleWarehouses.length === 0) {
      return false;
    }

    const savedWarehouse = warehousePersistence.findSavedWarehouse(accessibleWarehouses);
    if (savedWarehouse && savedWarehouse.id !== currentWarehouse?.id) {
      setCurrentWarehouse(savedWarehouse);
      return true;
    }

    return false;
  }, [isLoadingWarehouses, accessibleWarehouses, currentWarehouse, setCurrentWarehouse]);

  /**
   * Clear warehouse selection and persistence
   */
  const clearWarehouseSelection = useCallback(() => {
    setCurrentWarehouse(null);
    warehousePersistence.clearSavedWarehouse();
  }, [setCurrentWarehouse]);

  /**
   * Switch to a specific warehouse with validation
   */
  const switchToWarehouse = useCallback((warehouseId: string) => {
    const targetWarehouse = accessibleWarehouses.find(w => w.id === warehouseId);
    
    if (!targetWarehouse) {
      console.warn(`Warehouse ${warehouseId} not found in accessible warehouses`);
      return false;
    }

    setCurrentWarehouse(targetWarehouse);
    return true;
  }, [accessibleWarehouses, setCurrentWarehouse]);

  /**
   * Get warehouse switching recommendations
   */
  const getWarehouseSuggestions = useCallback(() => {
    const preferences = getWarehousePreferences();
    const suggestions = [];

    // Suggest restoring saved warehouse if different from current
    const savedWarehouse = warehousePersistence.findSavedWarehouse(accessibleWarehouses);
    if (savedWarehouse && savedWarehouse.id !== currentWarehouse?.id) {
      suggestions.push({
        type: 'restore' as const,
        warehouse: savedWarehouse,
        reason: 'Previously selected warehouse available',
      });
    }

    // Suggest first warehouse if none selected
    if (!currentWarehouse && accessibleWarehouses.length > 0) {
      suggestions.push({
        type: 'default' as const,
        warehouse: accessibleWarehouses[0],
        reason: 'Select a warehouse to access all features',
      });
    }

    // Suggest enabling auto-select if disabled but warehouse is remembered
    if (!preferences.autoSelectLastWarehouse && preferences.lastSelectedWarehouseId) {
      suggestions.push({
        type: 'preference' as const,
        warehouse: null,
        reason: 'Enable auto-select to automatically restore your warehouse selection',
      });
    }

    return suggestions;
  }, [currentWarehouse, accessibleWarehouses]);

  /**
   * Check if warehouse selection is required for current route
   */
  const isWarehouseRequired = useCallback((pathname?: string) => {
    const currentPath = pathname || (typeof window !== 'undefined' ? window.location.pathname : '');
    
    // Warehouse-dependent routes
    const warehouseDependentRoutes = [
      '/',
      '/pallets',
      '/move',
      '/picking',
      '/items',
      '/receiving',
    ];

    return warehouseDependentRoutes.some(route => 
      currentPath === route || currentPath.startsWith(`${route}/`)
    );
  }, []);

  /**
   * Get warehouse session statistics
   */
  const getSessionStats = useCallback(() => {
    const sessionInfo = getSessionInfo();
    const validation = validateWarehouseSelection();
    const suggestions = getWarehouseSuggestions();

    return {
      ...sessionInfo,
      validation,
      suggestions,
      isWarehouseRequired: isWarehouseRequired(),
      needsWarehouseSelection: !validation.isValid && isWarehouseRequired(),
    };
  }, [getSessionInfo, validateWarehouseSelection, getWarehouseSuggestions, isWarehouseRequired]);

  return {
    // Session info
    getSessionInfo,
    getSessionStats,
    
    // Validation
    validateWarehouseSelection,
    isWarehouseRequired,
    
    // Actions
    restoreWarehouseSelection,
    clearWarehouseSelection,
    switchToWarehouse,
    
    // Suggestions
    getWarehouseSuggestions,
    
    // State
    sessionStartTime,
    lastWarehouseChange,
  };
}
