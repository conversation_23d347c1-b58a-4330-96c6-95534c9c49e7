"use client";

import { useCallback, useMemo } from "react";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { Role } from "@quildora/types";

interface RoutePermission {
  canAccess: boolean;
  reason?: string;
  missingRequirements?: string[];
}

interface UseRouteGuardReturn {
  checkRouteAccess: (
    requireWarehouse?: boolean,
    requiredRole?: Role,
    allowedRoles?: Role[]
  ) => RoutePermission;
  canAccessWarehouseRoutes: boolean;
  canAccessAdminRoutes: boolean;
  canAccessManagerRoutes: boolean;
  hasRole: (role: Role) => boolean;
  hasAnyRole: (roles: Role[]) => boolean;
  hasWarehouseRole: (warehouseId: string, role: Role) => boolean;
  currentUserRole: Role | null;
  isLoading: boolean;
}

/**
 * Hook for checking route permissions and access control
 */
export function useRouteGuard(): UseRouteGuardReturn {
  const { appUser, isLoading: authLoading } = useAuth();
  const { currentWarehouse, accessibleWarehouses, isLoadingWarehouses } =
    useWarehouse();

  const isLoading = authLoading || isLoadingWarehouses;

  // Role hierarchy for comparison
  const roleHierarchy = useMemo(
    () => ({
      [Role.TENANT_ADMIN]: 3,
      [Role.WAREHOUSE_MANAGER]: 2,
      [Role.WAREHOUSE_MEMBER]: 1,
    }),
    []
  );

  // Check if user has a specific role or higher
  const hasRole = useCallback(
    (role: Role): boolean => {
      if (!appUser) return false;
      return roleHierarchy[appUser.role] >= roleHierarchy[role];
    },
    [appUser, roleHierarchy]
  );

  // Check if user has any of the specified roles
  const hasAnyRole = useCallback(
    (roles: Role[]): boolean => {
      return roles.some((role) => hasRole(role));
    },
    [hasRole]
  );

  // Check if user has a specific role in a warehouse
  const hasWarehouseRole = useCallback(
    (warehouseId: string, role: Role): boolean => {
      if (!appUser) return false;

      // Tenant admins have access to all warehouses
      if (appUser.role === Role.TENANT_ADMIN) return true;

      // Check warehouse-specific role
      if (appUser.warehouseUsers && appUser.warehouseUsers.length > 0) {
        const warehouseUser = appUser.warehouseUsers.find(
          (wu) => wu.warehouseId === warehouseId
        );
        if (warehouseUser) {
          return roleHierarchy[warehouseUser.role] >= roleHierarchy[role];
        }
      }

      // Fallback to global role
      return hasRole(role);
    },
    [appUser, roleHierarchy, hasRole]
  );

  // Check route access with detailed feedback
  const checkRouteAccess = useCallback(
    (
      requireWarehouse = false,
      requiredRole?: Role,
      allowedRoles?: Role[]
    ): RoutePermission => {
      const missingRequirements: string[] = [];

      // Check authentication
      if (!appUser) {
        return {
          canAccess: false,
          reason: "Authentication required",
          missingRequirements: ["authentication"],
        };
      }

      // Check role requirements
      if (requiredRole && !hasRole(requiredRole)) {
        missingRequirements.push(`role: ${requiredRole} or higher`);
      }

      if (allowedRoles && !hasAnyRole(allowedRoles)) {
        missingRequirements.push(`role: ${allowedRoles.join(" or ")}`);
      }

      // Check warehouse requirements
      if (requireWarehouse) {
        if (accessibleWarehouses.length === 0) {
          missingRequirements.push("warehouse access");
        } else if (!currentWarehouse) {
          missingRequirements.push("warehouse selection");
        } else if (
          requiredRole &&
          !hasWarehouseRole(currentWarehouse.id, requiredRole)
        ) {
          missingRequirements.push(`warehouse role: ${requiredRole} or higher`);
        }
      }

      const canAccess = missingRequirements.length === 0;

      return {
        canAccess,
        reason: canAccess
          ? undefined
          : `Missing: ${missingRequirements.join(", ")}`,
        missingRequirements: canAccess ? undefined : missingRequirements,
      };
    },
    [
      appUser,
      hasRole,
      hasAnyRole,
      hasWarehouseRole,
      accessibleWarehouses,
      currentWarehouse,
    ]
  );

  // Convenience computed properties
  const canAccessWarehouseRoutes = useMemo(() => {
    return checkRouteAccess(true, Role.WAREHOUSE_MEMBER).canAccess;
  }, [checkRouteAccess]);

  const canAccessAdminRoutes = useMemo(() => {
    return checkRouteAccess(false, Role.TENANT_ADMIN).canAccess;
  }, [checkRouteAccess]);

  const canAccessManagerRoutes = useMemo(() => {
    return checkRouteAccess(false, Role.WAREHOUSE_MANAGER).canAccess;
  }, [checkRouteAccess]);

  const currentUserRole = appUser?.role || null;

  return {
    checkRouteAccess,
    canAccessWarehouseRoutes,
    canAccessAdminRoutes,
    canAccessManagerRoutes,
    hasRole,
    hasAnyRole,
    hasWarehouseRole,
    currentUserRole,
    isLoading,
  };
}
