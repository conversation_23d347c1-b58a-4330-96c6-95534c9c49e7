import { useState } from "react";
import { toast } from "sonner";
import { Pallet } from "@quildora/types";
import { useAuth } from "@/components/providers/auth-provider";

/**
 * Custom hook for handling pallet placard printing functionality
 * Provides both single pallet and batch printing capabilities
 * Includes popup blocker workaround and error handling
 */
export const usePrintPlacard = () => {
  const [isPrinting, setIsPrinting] = useState(false);
  const { appToken } = useAuth();

  /**
   * Print placard for a single pallet using the correct backend API endpoint
   * @param pallet - The pallet to print placard for
   */
  const printSinglePlacard = async (pallet: Pallet) => {
    if (isPrinting) return;

    const palletId = pallet.id;
    if (!palletId || !appToken) {
      toast.error("Cannot print placard: Missing pallet ID or authentication.");
      return;
    }

    setIsPrinting(true);

    // Open the window immediately to avoid popup blockers
    const newWindow = window.open("", "_blank");

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/pallets/placards/print?palletId=${palletId}`,
        {
          headers: {
            Authorization: `Bearer ${appToken}`,
          },
        }
      );

      if (!response.ok) {
        const errorData = await response
          .json()
          .catch(() => ({ message: "Failed to fetch pallet placard PDF." }));
        throw new Error(errorData.message);
      }

      const blob = await response.blob();
      console.log("PDF blob size:", blob.size, "type:", blob.type);

      if (blob.size === 0) {
        throw new Error("Received empty PDF file");
      }

      const url = window.URL.createObjectURL(blob);
      console.log("Created blob URL:", url);

      if (newWindow && !newWindow.closed) {
        // Load the PDF into the already opened window
        newWindow.location.href = url;
        toast.success("Placard PDF opened in a new tab.");
      } else {
        // Fallback: try to download the file instead
        const link = document.createElement("a");
        link.href = url;
        link.download = `placard-${palletId}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        toast.success("Placard PDF downloaded.");
      }

      // Clean up the URL after a delay
      setTimeout(() => window.URL.revokeObjectURL(url), 1000);
    } catch (error: unknown) {
      // Close the window if there was an error
      if (newWindow && !newWindow.closed) {
        newWindow.close();
      }

      console.error("Error printing placard:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to print pallet placard."
      );
    } finally {
      setIsPrinting(false);
    }
  };

  /**
   * Print placards by destination using the correct backend API endpoint
   * @param destination - The destination to print placards for
   */
  const printPlacardsByDestination = async (destination: string) => {
    if (isPrinting) return;

    if (!appToken) {
      toast.error("Cannot print placards: Missing authentication.");
      return;
    }

    setIsPrinting(true);

    // Open the window immediately to avoid popup blockers
    const newWindow = window.open("", "_blank");

    try {
      const params = new URLSearchParams({ destination });
      const response = await fetch(
        `${
          process.env.NEXT_PUBLIC_API_BASE_URL
        }/api/pallets/placards/print?${params.toString()}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${appToken}`,
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({
          message: `Failed to generate placards: ${response.statusText}`,
        }));
        throw new Error(errorData.message);
      }

      // Create a blob from the PDF response and open it in the pre-opened window
      const blob = await response.blob();
      console.log("PDF blob size:", blob.size, "type:", blob.type);

      if (blob.size === 0) {
        throw new Error("Received empty PDF file");
      }

      const url = window.URL.createObjectURL(blob);
      console.log("Created blob URL:", url);

      if (newWindow && !newWindow.closed) {
        newWindow.location.href = url;
        toast.success("Placards opened in new tab for printing");
      } else {
        // Fallback if window was closed or blocked
        const link = document.createElement("a");
        link.href = url;
        link.download = `placards-${destination}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        toast.success("Placards PDF downloaded.");
      }

      // Clean up the object URL after a short delay
      setTimeout(() => window.URL.revokeObjectURL(url), 1000);
    } catch (error: unknown) {
      // Close the window if there was an error
      if (newWindow && !newWindow.closed) {
        newWindow.close();
      }

      console.error("Error printing placards:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to generate placards"
      );
    } finally {
      setIsPrinting(false);
    }
  };

  return {
    printSinglePlacard,
    printPlacardsByDestination,
    isPrinting,
  };
};
