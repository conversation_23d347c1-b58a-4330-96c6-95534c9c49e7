/**
 * Smart warehouse hook with optimized loading states
 * Provides context-aware loading and caching for warehouse operations
 */

import { useCallback, useMemo } from "react";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { useAuth } from "@/components/providers/auth-provider";
import { getCachedWarehouseValidation } from "@/lib/warehouse-validation-cache";

interface SmartWarehouseState {
  // Core warehouse state
  currentWarehouse: ReturnType<typeof useWarehouse>["currentWarehouse"];
  accessibleWarehouses: ReturnType<typeof useWarehouse>["accessibleWarehouses"];
  
  // Smart loading states
  isInitialLoading: boolean;
  isWarehouseSwitching: boolean;
  isRefreshing: boolean;
  hasValidCache: boolean;
  
  // Optimized loading flags
  shouldShowLoadingSpinner: boolean;
  shouldBlockNavigation: boolean;
  shouldShowOptimisticUI: boolean;
  
  // Actions
  setCurrentWarehouse: ReturnType<typeof useWarehouse>["setCurrentWarehouse"];
  refreshWarehouses: ReturnType<typeof useWarehouse>["refreshWarehouses"];
  
  // Enhanced utilities
  hasWarehouseAccess: ReturnType<typeof useWarehouse>["hasWarehouseAccess"];
  getUserWarehouseRole: ReturnType<typeof useWarehouse>["getUserWarehouseRole"];
  canManageWarehouse: ReturnType<typeof useWarehouse>["canManageWarehouse"];
  
  // Smart loading utilities
  getLoadingState: (operation?: string) => {
    isLoading: boolean;
    loadingType: "initial" | "refresh" | "switch" | "operation";
    context: "warehouse" | "general";
    message: string;
  };
}

/**
 * Enhanced warehouse hook with smart loading states
 */
export function useSmartWarehouse(): SmartWarehouseState {
  const { appUser } = useAuth();
  const {
    currentWarehouse,
    accessibleWarehouses,
    isLoadingWarehouses,
    isChangingWarehouse,
    warehouseError,
    setCurrentWarehouse,
    refreshWarehouses,
    hasWarehouseAccess,
    getUserWarehouseRole,
    canManageWarehouse,
  } = useWarehouse();

  // Determine cache validity
  const hasValidCache = useMemo(() => {
    if (!appUser || accessibleWarehouses.length === 0) return false;
    
    // Check if we have cached validation for current warehouse
    if (currentWarehouse) {
      const cached = getCachedWarehouseValidation(currentWarehouse.id, appUser.id);
      return !!cached;
    }
    
    // If we have warehouses loaded, consider cache valid
    return accessibleWarehouses.length > 0;
  }, [accessibleWarehouses, currentWarehouse, appUser]);

  // Determine loading states
  const isInitialLoading = useMemo(() => {
    return isLoadingWarehouses && !hasValidCache && accessibleWarehouses.length === 0;
  }, [isLoadingWarehouses, hasValidCache, accessibleWarehouses.length]);

  const isWarehouseSwitching = useMemo(() => {
    return isChangingWarehouse;
  }, [isChangingWarehouse]);

  const isRefreshing = useMemo(() => {
    return isLoadingWarehouses && hasValidCache;
  }, [isLoadingWarehouses, hasValidCache]);

  // Optimized loading flags
  const shouldShowLoadingSpinner = useMemo(() => {
    // Show spinner only for initial load or warehouse switching
    return isInitialLoading || isWarehouseSwitching;
  }, [isInitialLoading, isWarehouseSwitching]);

  const shouldBlockNavigation = useMemo(() => {
    // Block navigation only during initial load or critical operations
    return isInitialLoading || (isWarehouseSwitching && !hasValidCache);
  }, [isInitialLoading, isWarehouseSwitching, hasValidCache]);

  const shouldShowOptimisticUI = useMemo(() => {
    // Show optimistic UI when we have cache but are refreshing
    return hasValidCache && (isRefreshing || isLoadingWarehouses);
  }, [hasValidCache, isRefreshing, isLoadingWarehouses]);

  // Smart loading state getter
  const getLoadingState = useCallback((operation?: string) => {
    if (isInitialLoading) {
      return {
        isLoading: true,
        loadingType: "initial" as const,
        context: "warehouse" as const,
        message: "Loading workspace...",
      };
    }

    if (isWarehouseSwitching) {
      return {
        isLoading: true,
        loadingType: "switch" as const,
        context: "warehouse" as const,
        message: "Switching warehouse...",
      };
    }

    if (isRefreshing) {
      return {
        isLoading: true,
        loadingType: "refresh" as const,
        context: "warehouse" as const,
        message: "Refreshing warehouse data...",
      };
    }

    if (operation) {
      return {
        isLoading: false,
        loadingType: "operation" as const,
        context: "warehouse" as const,
        message: `Processing ${operation}...`,
      };
    }

    return {
      isLoading: false,
      loadingType: "initial" as const,
      context: "general" as const,
      message: "",
    };
  }, [isInitialLoading, isWarehouseSwitching, isRefreshing]);

  return {
    // Core state
    currentWarehouse,
    accessibleWarehouses,
    
    // Smart loading states
    isInitialLoading,
    isWarehouseSwitching,
    isRefreshing,
    hasValidCache,
    
    // Optimized loading flags
    shouldShowLoadingSpinner,
    shouldBlockNavigation,
    shouldShowOptimisticUI,
    
    // Actions
    setCurrentWarehouse,
    refreshWarehouses,
    
    // Utilities
    hasWarehouseAccess,
    getUserWarehouseRole,
    canManageWarehouse,
    
    // Smart loading utilities
    getLoadingState,
  };
}

/**
 * Hook for warehouse-specific operations with smart loading
 */
export function useWarehouseOperation(operationType: string) {
  const smartWarehouse = useSmartWarehouse();
  
  const getOperationLoadingState = useCallback(() => {
    return smartWarehouse.getLoadingState(operationType);
  }, [smartWarehouse, operationType]);

  return {
    ...smartWarehouse,
    getOperationLoadingState,
    isOperationReady: !smartWarehouse.shouldBlockNavigation,
  };
}

/**
 * Hook for warehouse navigation with smart loading
 */
export function useWarehouseNavigation() {
  const smartWarehouse = useSmartWarehouse();
  
  const canNavigate = useCallback((requiresWarehouse = false) => {
    if (!requiresWarehouse) return true;
    
    // Can navigate if we have a warehouse and not blocking
    return !!smartWarehouse.currentWarehouse && !smartWarehouse.shouldBlockNavigation;
  }, [smartWarehouse.currentWarehouse, smartWarehouse.shouldBlockNavigation]);

  const getNavigationLoadingState = useCallback(() => {
    if (smartWarehouse.shouldBlockNavigation) {
      return smartWarehouse.getLoadingState();
    }
    
    return {
      isLoading: false,
      loadingType: "initial" as const,
      context: "general" as const,
      message: "",
    };
  }, [smartWarehouse]);

  return {
    canNavigate,
    getNavigationLoadingState,
    shouldShowOptimisticUI: smartWarehouse.shouldShowOptimisticUI,
    currentWarehouse: smartWarehouse.currentWarehouse,
    accessibleWarehouses: smartWarehouse.accessibleWarehouses,
  };
}

/**
 * Hook for warehouse data with smart caching
 */
export function useWarehouseData<T>(
  queryKey: string,
  fetcher: () => Promise<T>,
  options: {
    enabled?: boolean;
    staleTime?: number;
    cacheTime?: number;
  } = {}
) {
  const smartWarehouse = useSmartWarehouse();
  
  // This would integrate with React Query for actual data fetching
  // For now, return the smart loading state
  return {
    data: null as T | null,
    isLoading: smartWarehouse.shouldShowLoadingSpinner,
    error: null,
    refetch: () => Promise.resolve(),
    hasValidCache: smartWarehouse.hasValidCache,
    shouldShowOptimisticUI: smartWarehouse.shouldShowOptimisticUI,
  };
}
