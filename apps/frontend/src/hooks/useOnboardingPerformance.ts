'use client';

import { useCallback, useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';

interface PerformanceMetrics {
  stepLoadTime: number;
  formSubmissionTime: number;
  apiResponseTime: number;
  totalOnboardingTime: number;
  errorCount: number;
  retryCount: number;
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

class OnboardingCache {
  private cache = new Map<string, CacheEntry<any>>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes

  set<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    const isExpired = Date.now() - entry.timestamp > entry.ttl;
    if (isExpired) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  clear(): void {
    this.cache.clear();
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  // Get cache statistics
  getStats() {
    const entries = Array.from(this.cache.entries());
    const expired = entries.filter(([_, entry]) => 
      Date.now() - entry.timestamp > entry.ttl
    ).length;

    return {
      total: this.cache.size,
      active: this.cache.size - expired,
      expired,
    };
  }
}

// Global cache instance
const onboardingCache = new OnboardingCache();

export function useOnboardingPerformance() {
  const router = useRouter();
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    stepLoadTime: 0,
    formSubmissionTime: 0,
    apiResponseTime: 0,
    totalOnboardingTime: 0,
    errorCount: 0,
    retryCount: 0,
  });

  const stepStartTime = useRef<number>(0);
  const onboardingStartTime = useRef<number>(0);
  const formSubmissionStart = useRef<number>(0);
  const apiCallStart = useRef<number>(0);

  // Initialize onboarding timing
  useEffect(() => {
    if (onboardingStartTime.current === 0) {
      onboardingStartTime.current = Date.now();
    }
  }, []);

  // Track step load time
  const trackStepStart = useCallback(() => {
    stepStartTime.current = Date.now();
  }, []);

  const trackStepComplete = useCallback(() => {
    if (stepStartTime.current > 0) {
      const loadTime = Date.now() - stepStartTime.current;
      setMetrics(prev => ({
        ...prev,
        stepLoadTime: loadTime,
      }));
    }
  }, []);

  // Track form submission performance
  const trackFormSubmissionStart = useCallback(() => {
    formSubmissionStart.current = Date.now();
  }, []);

  const trackFormSubmissionComplete = useCallback(() => {
    if (formSubmissionStart.current > 0) {
      const submissionTime = Date.now() - formSubmissionStart.current;
      setMetrics(prev => ({
        ...prev,
        formSubmissionTime: submissionTime,
      }));
    }
  }, []);

  // Track API call performance
  const trackApiCallStart = useCallback(() => {
    apiCallStart.current = Date.now();
  }, []);

  const trackApiCallComplete = useCallback(() => {
    if (apiCallStart.current > 0) {
      const responseTime = Date.now() - apiCallStart.current;
      setMetrics(prev => ({
        ...prev,
        apiResponseTime: responseTime,
      }));
    }
  }, []);

  // Track errors and retries
  const trackError = useCallback(() => {
    setMetrics(prev => ({
      ...prev,
      errorCount: prev.errorCount + 1,
    }));
  }, []);

  const trackRetry = useCallback(() => {
    setMetrics(prev => ({
      ...prev,
      retryCount: prev.retryCount + 1,
    }));
  }, []);

  // Calculate total onboarding time
  const getTotalOnboardingTime = useCallback(() => {
    if (onboardingStartTime.current > 0) {
      return Date.now() - onboardingStartTime.current;
    }
    return 0;
  }, []);

  // Performance optimization helpers
  const preloadNextStep = useCallback((nextStepPath: string) => {
    // Preload the next step's route
    router.prefetch(nextStepPath);
  }, [router]);

  const optimizeFormSubmission = useCallback(async (
    submitFunction: () => Promise<any>,
    options: {
      showLoadingState?: boolean;
      optimisticUpdate?: () => void;
      rollbackUpdate?: () => void;
    } = {}
  ) => {
    const { showLoadingState = true, optimisticUpdate, rollbackUpdate } = options;

    trackFormSubmissionStart();

    try {
      // Apply optimistic update if provided
      if (optimisticUpdate) {
        optimisticUpdate();
      }

      const result = await submitFunction();
      trackFormSubmissionComplete();
      return result;
    } catch (error) {
      // Rollback optimistic update on error
      if (rollbackUpdate) {
        rollbackUpdate();
      }
      trackError();
      throw error;
    }
  }, [trackFormSubmissionStart, trackFormSubmissionComplete, trackError]);

  // Cache management
  const cacheData = useCallback(<T>(key: string, data: T, ttl?: number) => {
    onboardingCache.set(key, data, ttl);
  }, []);

  const getCachedData = useCallback(<T>(key: string): T | null => {
    return onboardingCache.get<T>(key);
  }, []);

  const clearCache = useCallback(() => {
    onboardingCache.clear();
  }, []);

  // Performance monitoring
  const getPerformanceReport = useCallback(() => {
    const totalTime = getTotalOnboardingTime();
    const cacheStats = onboardingCache.getStats();

    return {
      metrics: {
        ...metrics,
        totalOnboardingTime: totalTime,
      },
      cache: cacheStats,
      performance: {
        averageStepTime: metrics.stepLoadTime,
        averageApiTime: metrics.apiResponseTime,
        errorRate: metrics.errorCount > 0 ? (metrics.errorCount / (metrics.errorCount + 1)) * 100 : 0,
        retryRate: metrics.retryCount > 0 ? (metrics.retryCount / (metrics.retryCount + 1)) * 100 : 0,
      },
      recommendations: generatePerformanceRecommendations(metrics, totalTime),
    };
  }, [metrics, getTotalOnboardingTime]);

  // Log performance metrics (for development)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const logMetrics = () => {
        const report = getPerformanceReport();
        console.group('🚀 Onboarding Performance Report');
        console.table(report.metrics);
        console.log('Cache Stats:', report.cache);
        console.log('Performance Analysis:', report.performance);
        if (report.recommendations.length > 0) {
          console.warn('Recommendations:', report.recommendations);
        }
        console.groupEnd();
      };

      // Log every 30 seconds in development
      const interval = setInterval(logMetrics, 30000);
      return () => clearInterval(interval);
    }
  }, [getPerformanceReport]);

  return {
    metrics,
    trackStepStart,
    trackStepComplete,
    trackFormSubmissionStart,
    trackFormSubmissionComplete,
    trackApiCallStart,
    trackApiCallComplete,
    trackError,
    trackRetry,
    getTotalOnboardingTime,
    preloadNextStep,
    optimizeFormSubmission,
    cacheData,
    getCachedData,
    clearCache,
    getPerformanceReport,
  };
}

// Generate performance recommendations
function generatePerformanceRecommendations(
  metrics: PerformanceMetrics,
  totalTime: number
): string[] {
  const recommendations: string[] = [];

  if (metrics.stepLoadTime > 2000) {
    recommendations.push('Step load time is slow. Consider optimizing component rendering.');
  }

  if (metrics.apiResponseTime > 3000) {
    recommendations.push('API response time is slow. Consider implementing request caching.');
  }

  if (metrics.formSubmissionTime > 1000) {
    recommendations.push('Form submission is slow. Consider optimistic updates.');
  }

  if (metrics.errorCount > 2) {
    recommendations.push('High error count detected. Review error handling and validation.');
  }

  if (metrics.retryCount > 3) {
    recommendations.push('High retry count. Consider improving error recovery mechanisms.');
  }

  if (totalTime > 10 * 60 * 1000) { // 10 minutes
    recommendations.push('Onboarding is taking too long. Consider simplifying the process.');
  }

  return recommendations;
}

// Hook for step-specific performance optimization
export function useStepPerformance(stepName: string) {
  const basePerformance = useOnboardingPerformance();

  const trackStepSpecificMetric = useCallback((metricName: string, value: number) => {
    basePerformance.cacheData(`step_${stepName}_${metricName}`, value);
  }, [basePerformance, stepName]);

  const getStepMetrics = useCallback(() => {
    return {
      loadTime: basePerformance.getCachedData<number>(`step_${stepName}_loadTime`) || 0,
      submissionTime: basePerformance.getCachedData<number>(`step_${stepName}_submissionTime`) || 0,
      errorCount: basePerformance.getCachedData<number>(`step_${stepName}_errorCount`) || 0,
    };
  }, [basePerformance, stepName]);

  return {
    ...basePerformance,
    trackStepSpecificMetric,
    getStepMetrics,
  };
}

// Hook for form performance optimization
export function useFormPerformance() {
  const [isOptimizing, setIsOptimizing] = useState(false);
  const performance = useOnboardingPerformance();

  const optimizedSubmit = useCallback(async (
    submitFn: () => Promise<any>,
    options: {
      optimisticUpdate?: () => void;
      rollbackUpdate?: () => void;
      successMessage?: string;
    } = {}
  ) => {
    setIsOptimizing(true);
    
    try {
      const result = await performance.optimizeFormSubmission(submitFn, {
        optimisticUpdate: options.optimisticUpdate,
        rollbackUpdate: options.rollbackUpdate,
      });
      
      if (options.successMessage) {
        // Show success message after a brief delay for better UX
        setTimeout(() => {
          console.log(options.successMessage);
        }, 100);
      }
      
      return result;
    } finally {
      setIsOptimizing(false);
    }
  }, [performance]);

  return {
    isOptimizing,
    optimizedSubmit,
    ...performance,
  };
}
