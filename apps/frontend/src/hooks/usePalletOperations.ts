"use client";

import { useCallback, useMemo } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import {
  usePallets,
  usePallet,
  useCreatePallet,
  useUpdatePallet,
  useMovePallet,
} from "@/hooks/api";
import { PalletFilters, Pallet } from "@quildora/types";
import { toast } from "sonner";

/**
 * Unified hook for pallet operations
 * Consolidates common pallet-related functionality
 */
export function usePalletOperations(filters?: PalletFilters) {
  const queryClient = useQueryClient();
  const { currentWarehouse } = useWarehouse();
  const { appToken } = useAuth();

  // Data fetching
  const {
    data: pallets = [],
    isLoading: isLoadingPallets,
    error: palletsError,
    refetch: refetchPallets,
  } = usePallets(filters);

  // Mutations
  const createPalletMutation = useCreatePallet();
  const updatePalletMutation = useUpdatePallet();
  const movePalletMutation = useMovePallet();

  // Create pallet with optimistic updates
  const createPallet = useCallback(
    async (palletData: any) => {
      try {
        const result = await createPalletMutation.mutateAsync(palletData);
        toast.success("Pallet created successfully");
        return result;
      } catch (error) {
        toast.error("Failed to create pallet");
        throw error;
      }
    },
    [createPalletMutation]
  );

  // Update pallet with optimistic updates
  const updatePallet = useCallback(
    async (palletId: string, updates: Partial<Pallet>) => {
      try {
        const result = await updatePalletMutation.mutateAsync({
          id: palletId,
          data: updates,
        });
        toast.success("Pallet updated successfully");
        return result;
      } catch (error) {
        toast.error("Failed to update pallet");
        throw error;
      }
    },
    [updatePalletMutation]
  );

  // Move pallet with optimistic updates
  const movePallet = useCallback(
    async (palletId: string, newLocationId: string) => {
      try {
        const result = await movePalletMutation.mutateAsync({
          palletId,
          locationId: newLocationId,
        });
        toast.success("Pallet moved successfully");
        return result;
      } catch (error) {
        toast.error("Failed to move pallet");
        throw error;
      }
    },
    [movePalletMutation]
  );

  // Bulk operations
  const bulkMovePallets = useCallback(
    async (palletIds: string[], newLocationId: string) => {
      try {
        const promises = palletIds.map((id) =>
          movePalletMutation.mutateAsync({
            palletId: id,
            locationId: newLocationId,
          })
        );
        await Promise.all(promises);
        toast.success(`${palletIds.length} pallets moved successfully`);
      } catch (error) {
        toast.error("Failed to move some pallets");
        throw error;
      }
    },
    [movePalletMutation]
  );

  // Refresh data
  const refreshPallets = useCallback(() => {
    refetchPallets();
    // Also invalidate related queries
    queryClient.invalidateQueries({
      queryKey: ["pallets", currentWarehouse?.id],
    });
  }, [refetchPallets, queryClient, currentWarehouse?.id]);

  // Computed values
  const stats = useMemo(() => {
    if (!pallets.length) return null;

    return {
      total: pallets.length,
      available: pallets.filter((p) => p.status === "available").length,
      inTransit: pallets.filter((p) => p.status === "in_transit").length,
      shipped: pallets.filter((p) => p.status === "shipped").length,
    };
  }, [pallets]);

  // Loading states
  const isLoading =
    isLoadingPallets ||
    createPalletMutation.isPending ||
    updatePalletMutation.isPending ||
    movePalletMutation.isPending;

  return {
    // Data
    pallets,
    stats,

    // Loading states
    isLoading,
    isLoadingPallets,
    isCreating: createPalletMutation.isPending,
    isUpdating: updatePalletMutation.isPending,
    isMoving: movePalletMutation.isPending,

    // Errors
    error: palletsError,

    // Operations
    createPallet,
    updatePallet,
    movePallet,
    bulkMovePallets,
    refreshPallets,

    // Utilities
    canPerformOperations: !!currentWarehouse && !!appToken,
  };
}

/**
 * Hook for single pallet operations
 */
export function usePalletDetail(palletId: string | null) {
  const { data: pallet, isLoading, error, refetch } = usePallet(palletId);

  const updatePalletMutation = useUpdatePallet();
  const movePalletMutation = useMovePallet();

  const updatePallet = useCallback(
    async (updates: Partial<Pallet>) => {
      if (!palletId) throw new Error("No pallet ID provided");

      try {
        const result = await updatePalletMutation.mutateAsync({
          id: palletId,
          data: updates,
        });
        toast.success("Pallet updated successfully");
        return result;
      } catch (error) {
        toast.error("Failed to update pallet");
        throw error;
      }
    },
    [palletId, updatePalletMutation]
  );

  const movePallet = useCallback(
    async (newLocationId: string) => {
      if (!palletId) throw new Error("No pallet ID provided");

      try {
        const result = await movePalletMutation.mutateAsync({
          palletId,
          locationId: newLocationId,
        });
        toast.success("Pallet moved successfully");
        return result;
      } catch (error) {
        toast.error("Failed to move pallet");
        throw error;
      }
    },
    [palletId, movePalletMutation]
  );

  return {
    pallet,
    isLoading:
      isLoading ||
      updatePalletMutation.isPending ||
      movePalletMutation.isPending,
    error,
    updatePallet,
    movePallet,
    refresh: refetch,
  };
}
