// Warehouse-aware API hooks
export {
  usePallets,
  useLocations,
  useItems,
  usePallet,
  useLocation,
  useDestinations,
  useDestinationsWithCodes,
  useDestinationByCode,
  useDestinationsByName,
  useWarehouses,
  useCreatePallet,
  useUpdatePallet,
  useMovePallet,
  // Suspense-enabled hooks for instant loading
  usePalletsSuspense,
  useLocationsSuspense,
  useItemsSuspense,
  usePalletSuspense,
} from "./useWarehouseData";

export {
  useShipments,
  usePurchaseOrders,
  useAuditLogs,
  useReceivingLocations,
  useShippingLocations,
  useStorageLocations,
  useCompleteShipment,
  useUpdatePurchaseOrderStatus,
} from "./useWarehouseOperations";

export { useWarehouseSync, useWarehouseQueryUtils } from "./useWarehouseSync";

// Re-export common types for convenience
export type {
  Pallet,
  Location,
  Item,
  Warehouse,
  Shipment,
  PurchaseOrder,
  AuditLog,
  LocationCategory,
  LocationType,
} from "@quildora/types";
