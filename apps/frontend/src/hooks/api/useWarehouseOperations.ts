"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { fetchWithAuth } from "@/lib/api";
import { getWarehouseQueryConfig } from "@/lib/query-config";

// Import types from shared package
import {
  Shipment,
  PurchaseOrder,
  AuditLog,
  LocationCategory,
  ShipmentFilters,
  PurchaseOrderFilters,
  AuditLogFilters,
} from "@quildora/types";

/**
 * Hook to fetch shipments for the current warehouse
 */
export function useShipments(filters?: ShipmentFilters) {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useQuery<
    { shipments: Shipment[]; total: number; page: number; limit: number },
    Error
  >({
    queryKey: ["shipments", currentWarehouse?.id, filters, appToken],
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");

      const searchParams = new URLSearchParams();
      searchParams.append("warehouseId", currentWarehouse.id);

      if (filters?.status) searchParams.append("status", filters.status);
      if (filters?.page) searchParams.append("page", filters.page.toString());
      if (filters?.limit)
        searchParams.append("limit", filters.limit.toString());
      if (filters?.sortBy) searchParams.append("sortBy", filters.sortBy);
      if (filters?.sortOrder)
        searchParams.append("sortOrder", filters.sortOrder);

      const url = `/api/shipments${
        searchParams.toString() ? `?${searchParams.toString()}` : ""
      }`;
      return fetchWithAuth(url, { token: appToken });
    },
    enabled: !isAuthLoading && !!appToken && !!currentWarehouse,
    ...getWarehouseQueryConfig("shipments"),
  });
}

/**
 * Hook to fetch purchase orders for the current warehouse
 */
export function usePurchaseOrders(filters?: PurchaseOrderFilters) {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useQuery<
    {
      purchaseOrders: PurchaseOrder[];
      total: number;
      page: number;
      limit: number;
    },
    Error
  >({
    queryKey: ["purchaseOrders", currentWarehouse?.id, filters, appToken],
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");

      const searchParams = new URLSearchParams();
      searchParams.append("warehouseId", currentWarehouse.id);

      if (filters?.status) searchParams.append("status", filters.status);
      if (filters?.search) searchParams.append("search", filters.search);
      if (filters?.page) searchParams.append("page", filters.page.toString());
      if (filters?.limit)
        searchParams.append("limit", filters.limit.toString());

      const url = `/api/purchase-orders${
        searchParams.toString() ? `?${searchParams.toString()}` : ""
      }`;
      return fetchWithAuth(url, { token: appToken });
    },
    enabled: !isAuthLoading && !!appToken && !!currentWarehouse,
    ...getWarehouseQueryConfig("purchaseOrders"),
  });
}

/**
 * Hook to fetch audit logs for the current warehouse
 */
export function useAuditLogs(filters?: AuditLogFilters) {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useQuery<
    { auditLogs: AuditLog[]; total: number; page: number; limit: number },
    Error
  >({
    queryKey: ["auditLogs", currentWarehouse?.id, filters, appToken],
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");

      const searchParams = new URLSearchParams();
      searchParams.append("warehouseId", currentWarehouse.id);

      if (filters?.entityType)
        searchParams.append("entityType", filters.entityType);
      if (filters?.entityId) searchParams.append("entityId", filters.entityId);
      if (filters?.userId) searchParams.append("userId", filters.userId);
      if (filters?.action) searchParams.append("action", filters.action);
      if (filters?.startDate)
        searchParams.append("startDate", filters.startDate);
      if (filters?.endDate) searchParams.append("endDate", filters.endDate);
      if (filters?.page) searchParams.append("page", filters.page.toString());
      if (filters?.limit)
        searchParams.append("limit", filters.limit.toString());

      const url = `/api/audit-logs${
        searchParams.toString() ? `?${searchParams.toString()}` : ""
      }`;
      return fetchWithAuth(url, { token: appToken });
    },
    enabled: !isAuthLoading && !!appToken && !!currentWarehouse,
    ...getWarehouseQueryConfig("auditLogs"),
  });
}

/**
 * Hook to fetch receiving locations for the current warehouse
 */
export function useReceivingLocations() {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useQuery({
    queryKey: [
      "locations",
      currentWarehouse?.id,
      { category: LocationCategory.Receiving },
      appToken,
    ],
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");

      const searchParams = new URLSearchParams();
      searchParams.append("warehouseId", currentWarehouse.id);
      searchParams.append("category", LocationCategory.Receiving);

      return fetchWithAuth(`/api/locations?${searchParams.toString()}`, {
        token: appToken,
      });
    },
    enabled: !isAuthLoading && !!appToken && !!currentWarehouse,
    ...getWarehouseQueryConfig("locations"),
  });
}

/**
 * Hook to fetch shipping locations for the current warehouse
 */
export function useShippingLocations() {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useQuery({
    queryKey: [
      "locations",
      currentWarehouse?.id,
      { category: LocationCategory.Shipping },
      appToken,
    ],
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");

      const searchParams = new URLSearchParams();
      searchParams.append("warehouseId", currentWarehouse.id);
      searchParams.append("category", LocationCategory.Shipping);

      return fetchWithAuth(`/api/locations?${searchParams.toString()}`, {
        token: appToken,
      });
    },
    enabled: !isAuthLoading && !!appToken && !!currentWarehouse,
    ...getWarehouseQueryConfig("locations"),
  });
}

/**
 * Hook to fetch storage locations for the current warehouse
 */
export function useStorageLocations() {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useQuery({
    queryKey: [
      "locations",
      currentWarehouse?.id,
      { category: LocationCategory.Storage },
      appToken,
    ],
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");

      const searchParams = new URLSearchParams();
      searchParams.append("warehouseId", currentWarehouse.id);
      searchParams.append("category", LocationCategory.Storage);

      return fetchWithAuth(`/api/locations?${searchParams.toString()}`, {
        token: appToken,
      });
    },
    enabled: !isAuthLoading && !!appToken && !!currentWarehouse,
    ...getWarehouseQueryConfig("locations"),
  });
}

/**
 * Mutation hook for completing shipments
 */
export function useCompleteShipment() {
  const queryClient = useQueryClient();
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useMutation<Shipment, Error, { shipmentId: string }>({
    mutationFn: async ({ shipmentId }) => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");

      return fetchWithAuth(
        `/api/shipments/${shipmentId}/complete?warehouseId=${currentWarehouse.id}`,
        {
          method: "POST",
          token: appToken,
        }
      );
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: ["shipments", currentWarehouse?.id],
      });
      queryClient.invalidateQueries({
        queryKey: ["pallets", currentWarehouse?.id],
      });
      queryClient.invalidateQueries({
        queryKey: ["purchaseOrders", currentWarehouse?.id],
      });
    },
  });
}

/**
 * Mutation hook for updating purchase order status
 */
export function useUpdatePurchaseOrderStatus() {
  const queryClient = useQueryClient();
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useMutation<PurchaseOrder, Error, { poId: string; status: string }>({
    mutationFn: async ({ poId, status }) => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");

      return fetchWithAuth(
        `/api/purchase-orders/${poId}/status?warehouseId=${currentWarehouse.id}`,
        {
          method: "PATCH",
          token: appToken,
          body: JSON.stringify({ status }),
        }
      );
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: ["purchaseOrders", currentWarehouse?.id],
      });
      queryClient.invalidateQueries({
        queryKey: ["shipments", currentWarehouse?.id],
      });
    },
  });
}
