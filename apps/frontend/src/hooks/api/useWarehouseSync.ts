"use client";

import { useEffect, useRef } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useWarehouse } from "@/components/providers/warehouse-provider";

/**
 * Hook that automatically invalidates warehouse-scoped queries when the warehouse changes
 * This ensures that data is refetched when users switch between warehouses
 */
export function useWarehouseSync() {
  const queryClient = useQueryClient();
  const { currentWarehouse } = useWarehouse();
  const previousWarehouseId = useRef<string | null>(null);

  useEffect(() => {
    // Skip on initial mount when previousWarehouseId is not set
    if (previousWarehouseId.current === null) {
      previousWarehouseId.current = currentWarehouse?.id || null;
      return;
    }

    // If warehouse has changed, invalidate all warehouse-scoped queries
    if (previousWarehouseId.current !== currentWarehouse?.id) {
      console.log(
        `[WarehouseSync] Warehouse changed from ${previousWarehouseId.current} to ${currentWarehouse?.id}. Invalidating queries.`
      );

      // Invalidate all warehouse-scoped data queries
      const warehouseQueriesToInvalidate = [
        "pallets",
        "locations", 
        "items",
        "pallet",
        "location",
        "warehouseItems",
        "shipments",
        "purchaseOrders",
        "auditLogs"
      ];

      warehouseQueriesToInvalidate.forEach(queryKey => {
        queryClient.invalidateQueries({ 
          queryKey: [queryKey],
          exact: false // This will invalidate all queries that start with this key
        });
      });

      // Update the ref to the new warehouse ID
      previousWarehouseId.current = currentWarehouse?.id || null;
    }
  }, [currentWarehouse?.id, queryClient]);

  return {
    currentWarehouseId: currentWarehouse?.id,
    isWarehouseSelected: !!currentWarehouse,
  };
}

/**
 * Hook that provides warehouse-aware query invalidation utilities
 */
export function useWarehouseQueryUtils() {
  const queryClient = useQueryClient();
  const { currentWarehouse } = useWarehouse();

  const invalidateWarehouseQueries = (queryKeys: string[]) => {
    if (!currentWarehouse) return;

    queryKeys.forEach(queryKey => {
      queryClient.invalidateQueries({ 
        queryKey: [queryKey, currentWarehouse.id],
        exact: false
      });
    });
  };

  const invalidateAllWarehouseData = () => {
    if (!currentWarehouse) return;

    const allWarehouseQueries = [
      "pallets",
      "locations", 
      "items",
      "pallet",
      "location",
      "warehouseItems",
      "shipments",
      "purchaseOrders",
      "auditLogs"
    ];

    invalidateWarehouseQueries(allWarehouseQueries);
  };

  const prefetchWarehouseData = async () => {
    if (!currentWarehouse) return;

    // Prefetch commonly used data for the current warehouse
    const prefetchPromises = [
      queryClient.prefetchQuery({
        queryKey: ["pallets", currentWarehouse.id],
        staleTime: 30000, // 30 seconds
      }),
      queryClient.prefetchQuery({
        queryKey: ["locations", currentWarehouse.id],
        staleTime: 300000, // 5 minutes (locations change less frequently)
      }),
    ];

    try {
      await Promise.all(prefetchPromises);
      console.log(`[WarehouseSync] Prefetched data for warehouse ${currentWarehouse.id}`);
    } catch (error) {
      console.warn(`[WarehouseSync] Failed to prefetch data for warehouse ${currentWarehouse.id}:`, error);
    }
  };

  return {
    invalidateWarehouseQueries,
    invalidateAllWarehouseData,
    prefetchWarehouseData,
    currentWarehouseId: currentWarehouse?.id,
  };
}
