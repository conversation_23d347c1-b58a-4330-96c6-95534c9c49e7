"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/components/providers/auth-provider";
import { fetchWithAuth } from "@/lib/api";
import { FrontendAppUser } from "@quildora/types";

interface UpdateProfileData {
  name?: string;
}

/**
 * Hook for updating user profile
 */
export function useUpdateProfile() {
  const queryClient = useQueryClient();
  const { appToken } = useAuth();

  return useMutation<FrontendAppUser, Error, UpdateProfileData>({
    mutationFn: async (profileData: UpdateProfileData) => {
      if (!appToken) throw new Error("Authentication token not available");
      return fetchWithAuth("/api/users/profile", {
        method: "PATCH",
        token: appToken,
        body: JSON.stringify(profileData),
      });
    },
    onSuccess: (updatedUser) => {
      // Update the auth context by invalidating auth-related queries
      // This will trigger a refetch of the current user data
      queryClient.invalidateQueries({ queryKey: ["auth"] });
    },
  });
}
