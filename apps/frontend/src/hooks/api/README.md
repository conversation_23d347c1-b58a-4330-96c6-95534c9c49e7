# Warehouse-Aware API Hooks

This directory contains React Query hooks that are warehouse-scoped and automatically handle warehouse context in API calls.

## Overview

All hooks in this directory are designed to work with the warehouse-scoped access control system. They automatically:

- Include the current warehouse ID in API calls
- Invalidate and refetch data when the warehouse changes
- Handle authentication and warehouse selection states
- Provide proper TypeScript types

## Core Hooks

### Data Fetching Hooks

#### `usePallets(filters?)`
Fetches pallets for the current warehouse with optional filtering.

```typescript
import { usePallets } from '@/hooks/api';

function PalletsPage() {
  const { data: pallets, isLoading, error } = usePallets({
    status: 'available',
    search: 'ABC123'
  });

  // pallets will automatically update when warehouse changes
}
```

#### `useLocations(filters?)`
Fetches locations for the current warehouse.

```typescript
import { useLocations } from '@/hooks/api';

function LocationsPage() {
  const { data: locations, isLoading } = useLocations({
    category: LocationCategory.Storage
  });
}
```

#### `useItems(filters?)`
Fetches items with warehouse stock information.

```typescript
import { useItems } from '@/hooks/api';

function ItemsPage() {
  const { data: items, isLoading } = useItems({
    inStock: true,
    search: 'widget'
  });
}
```

### Single Entity Hooks

#### `usePallet(palletId)`
Fetches a single pallet by ID.

```typescript
import { usePallet } from '@/hooks/api';

function PalletDetail({ palletId }: { palletId: string }) {
  const { data: pallet, isLoading } = usePallet(palletId);
}
```

#### `useLocation(locationId)`
Fetches a single location by ID.

```typescript
import { useLocation } from '@/hooks/api';

function LocationDetail({ locationId }: { locationId: string }) {
  const { data: location, isLoading } = useLocation(locationId);
}
```

### Specialized Location Hooks

#### `useReceivingLocations()`
Fetches only receiving locations for the current warehouse.

```typescript
import { useReceivingLocations } from '@/hooks/api';

function ReceivingForm() {
  const { data: receivingLocations } = useReceivingLocations();
}
```

#### `useShippingLocations()`
Fetches only shipping locations for the current warehouse.

#### `useStorageLocations()`
Fetches only storage locations for the current warehouse.

### Operations Hooks

#### `useShipments(filters?)`
Fetches shipments for the current warehouse with pagination.

```typescript
import { useShipments } from '@/hooks/api';

function ShipmentsPage() {
  const { data, isLoading } = useShipments({
    status: 'processing',
    page: 1,
    limit: 20
  });
  
  // data.shipments, data.total, data.page, data.limit
}
```

#### `usePurchaseOrders(filters?)`
Fetches purchase orders for the current warehouse.

#### `useAuditLogs(filters?)`
Fetches audit logs for the current warehouse.

## Mutation Hooks

### `useCreatePallet()`
Creates a new pallet in the current warehouse.

```typescript
import { useCreatePallet } from '@/hooks/api';

function CreatePalletForm() {
  const createPallet = useCreatePallet();

  const handleSubmit = (data) => {
    createPallet.mutate(data, {
      onSuccess: (newPallet) => {
        console.log('Pallet created:', newPallet);
      }
    });
  };
}
```

### `useUpdatePallet()`
Updates an existing pallet.

```typescript
import { useUpdatePallet } from '@/hooks/api';

function EditPalletForm({ palletId }: { palletId: string }) {
  const updatePallet = useUpdatePallet();

  const handleSubmit = (data) => {
    updatePallet.mutate({ id: palletId, data }, {
      onSuccess: (updatedPallet) => {
        console.log('Pallet updated:', updatedPallet);
      }
    });
  };
}
```

### `useMovePallet()`
Moves a pallet to a different location.

```typescript
import { useMovePallet } from '@/hooks/api';

function MovePalletForm() {
  const movePallet = useMovePallet();

  const handleMove = (palletId: string, locationId: string) => {
    movePallet.mutate({ palletId, locationId });
  };
}
```

## Synchronization Hooks

### `useWarehouseSync()`
Automatically handles query invalidation when warehouse changes. This is already integrated into the app layout.

### `useWarehouseQueryUtils()`
Provides utilities for manual query management.

```typescript
import { useWarehouseQueryUtils } from '@/hooks/api';

function SomeComponent() {
  const { 
    invalidateWarehouseQueries, 
    invalidateAllWarehouseData,
    prefetchWarehouseData 
  } = useWarehouseQueryUtils();

  const handleRefresh = () => {
    invalidateWarehouseQueries(['pallets', 'locations']);
  };

  const handleWarehouseSwitch = async () => {
    await prefetchWarehouseData();
  };
}
```

## Migration from Old Hooks

### Before (Old Pattern)
```typescript
// Old way - manual warehouse handling
const { data: pallets } = useQuery({
  queryKey: ["pallets", appToken],
  queryFn: () => fetchWithAuth(`/api/pallets`, { token: appToken }),
  enabled: !isAuthLoading,
});
```

### After (New Pattern)
```typescript
// New way - automatic warehouse context
import { usePallets } from '@/hooks/api';

const { data: pallets } = usePallets();
```

## Key Benefits

1. **Automatic Warehouse Context**: All API calls include the current warehouse ID
2. **Automatic Refetching**: Data refreshes when warehouse selection changes
3. **Type Safety**: Full TypeScript support with shared types
4. **Consistent Patterns**: All hooks follow the same conventions
5. **Error Handling**: Built-in error states for missing auth/warehouse
6. **Performance**: Optimized query keys and caching strategies

## Requirements

- Must be used within `WarehouseProvider` context
- Must be used within `AuthProvider` context  
- Must be used within `ReactQueryProvider` context
- A warehouse must be selected for most hooks to work

## Error States

All hooks handle these error states automatically:
- Missing authentication token
- No warehouse selected
- API errors
- Network failures

The hooks will show loading states until auth and warehouse context are ready.
