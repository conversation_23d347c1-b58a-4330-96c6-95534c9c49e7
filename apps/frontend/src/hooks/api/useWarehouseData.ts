"use client";

import { useState, useEffect } from "react";
import {
  useQuery,
  useSuspenseQuery,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";
import { useAuth } from "@/components/providers/auth-provider";
import { useWarehouse } from "@/components/providers/warehouse-provider";
import { fetchWithAuth } from "@/lib/api";
import { getWarehouseQueryConfig } from "@/lib/query-config";

// Import types from shared package
import {
  Pallet,
  Location,
  Item,
  Warehouse,
  PalletFilters,
  LocationFilters,
  DestinationResponse,
} from "@quildora/types";

// Warehouse-aware API hooks

/**
 * Hook to fetch pallets for the current warehouse
 */
export function usePallets(filters?: PalletFilters) {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useQuery<Pallet[], Error>({
    queryKey: ["pallets", currentWarehouse?.id, filters, appToken],
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");

      const searchParams = new URLSearchParams();
      searchParams.append("warehouseId", currentWarehouse.id);

      if (filters?.status) searchParams.append("status", filters.status);
      if (filters?.locationId)
        searchParams.append("locationId", filters.locationId);
      if (filters?.search) searchParams.append("search", filters.search);
      if (filters?.shipToDestination)
        searchParams.append("shipToDestination", filters.shipToDestination);

      const url = `/api/pallets${
        searchParams.toString() ? `?${searchParams.toString()}` : ""
      }`;
      return fetchWithAuth(url, { token: appToken });
    },
    enabled: !isAuthLoading && !!appToken && !!currentWarehouse,
    retry: 1,
  });
}

/**
 * Hook to fetch locations for the current warehouse
 */
export function useLocations(filters?: LocationFilters) {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useQuery<Location[], Error>({
    queryKey: ["locations", currentWarehouse?.id, filters, appToken],
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");

      const searchParams = new URLSearchParams();
      searchParams.append("warehouseId", currentWarehouse.id);

      if (filters?.category) searchParams.append("category", filters.category);
      if (filters?.type) searchParams.append("type", filters.type);
      if (filters?.search) searchParams.append("search", filters.search);

      const url = `/api/locations${
        searchParams.toString() ? `?${searchParams.toString()}` : ""
      }`;
      return fetchWithAuth(url, { token: appToken });
    },
    enabled: !isAuthLoading && !!appToken && !!currentWarehouse,
    retry: 1,
  });
}

/**
 * Hook to fetch items with warehouse stock information
 */
export function useItems(filters?: {
  search?: string;
  category?: string;
  inStock?: boolean;
}) {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useQuery<Item[], Error>({
    queryKey: ["items", currentWarehouse?.id, filters, appToken],
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");

      const searchParams = new URLSearchParams();
      searchParams.append("warehouseId", currentWarehouse.id);

      if (filters?.search) searchParams.append("search", filters.search);
      if (filters?.category) searchParams.append("category", filters.category);
      if (filters?.inStock !== undefined)
        searchParams.append("inStock", filters.inStock.toString());

      const url = `/api/items${
        searchParams.toString() ? `?${searchParams.toString()}` : ""
      }`;
      return fetchWithAuth(url, { token: appToken });
    },
    enabled: !isAuthLoading && !!appToken && !!currentWarehouse,
    ...getWarehouseQueryConfig("items"),
  });
}

/**
 * Hook to fetch a single pallet by ID
 */
export function usePallet(palletId: string | null) {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useQuery<Pallet, Error>({
    queryKey: ["pallet", palletId, currentWarehouse?.id, appToken],
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");
      if (!palletId) throw new Error("Pallet ID is required");

      return fetchWithAuth(
        `/api/pallets/${palletId}?warehouseId=${currentWarehouse.id}`,
        {
          token: appToken,
        }
      );
    },
    enabled: !isAuthLoading && !!appToken && !!currentWarehouse && !!palletId,
    ...getWarehouseQueryConfig("pallet"),
  });
}

/**
 * Hook to fetch a single location by ID
 */
export function useLocation(locationId: string | null) {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useQuery<Location, Error>({
    queryKey: ["location", locationId, currentWarehouse?.id, appToken],
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");
      if (!locationId) throw new Error("Location ID is required");

      return fetchWithAuth(
        `/api/locations/${locationId}?warehouseId=${currentWarehouse.id}`,
        {
          token: appToken,
        }
      );
    },
    enabled: !isAuthLoading && !!appToken && !!currentWarehouse && !!locationId,
    ...getWarehouseQueryConfig("locations"),
  });
}

/**
 * Hook to fetch destinations for the current warehouse
 */
export function useDestinations() {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useQuery<string[], Error>({
    queryKey: ["destinations", currentWarehouse?.id, appToken],
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");

      const searchParams = new URLSearchParams();
      searchParams.append("warehouseId", currentWarehouse.id);

      return fetchWithAuth(
        `/api/pallets/destinations?${searchParams.toString()}`,
        {
          token: appToken,
        }
      );
    },
    enabled: !isAuthLoading && !!appToken && !!currentWarehouse,
    ...getWarehouseQueryConfig("destinations"),
  });
}

/**
 * Hook to fetch destinations with codes for the current warehouse
 */
export function useDestinationsWithCodes() {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useQuery<DestinationResponse[], Error>({
    queryKey: ["destinations-with-codes", currentWarehouse?.id, appToken],
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");

      const searchParams = new URLSearchParams();
      searchParams.append("warehouseId", currentWarehouse.id);

      return fetchWithAuth(
        `/api/pallets/destinations/with-codes?${searchParams.toString()}`,
        {
          token: appToken,
        }
      );
    },
    enabled: !isAuthLoading && !!appToken && !!currentWarehouse,
    ...getWarehouseQueryConfig("destinations"),
  });
}

/**
 * Suspense hook to fetch destinations with codes for the current warehouse
 */
export function useDestinationsWithCodesSuspense() {
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useSuspenseQuery<DestinationResponse[], Error>({
    queryKey: ["destinations-with-codes", currentWarehouse?.id, appToken],
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");

      const searchParams = new URLSearchParams();
      searchParams.append("warehouseId", currentWarehouse.id);

      return fetchWithAuth(
        `/api/pallets/destinations/with-codes?${searchParams.toString()}`,
        {
          token: appToken,
        }
      );
    },
    ...getWarehouseQueryConfig("destinations"),
  });
}

/**
 * Hook to find destination by code
 */
export function useDestinationByCode(code: string | null) {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useQuery<DestinationResponse | null, Error>({
    queryKey: ["destination-by-code", code, currentWarehouse?.id, appToken],
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");
      if (!code) return null;

      const searchParams = new URLSearchParams();
      searchParams.append("warehouseId", currentWarehouse.id);

      return fetchWithAuth(
        `/api/pallets/destinations/by-code/${encodeURIComponent(
          code
        )}?${searchParams.toString()}`,
        {
          token: appToken,
        }
      );
    },
    enabled: !isAuthLoading && !!appToken && !!currentWarehouse && !!code,
    ...getWarehouseQueryConfig("destinations"),
  });
}

/**
 * Suspense hook to find destination by code
 */
export function useDestinationByCodeSuspense(code: string | null) {
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useSuspenseQuery<DestinationResponse | null, Error>({
    queryKey: ["destination-by-code", code, currentWarehouse?.id, appToken],
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");
      if (!code) return null;

      const searchParams = new URLSearchParams();
      searchParams.append("warehouseId", currentWarehouse.id);

      return fetchWithAuth(
        `/api/pallets/destinations/by-code/${encodeURIComponent(
          code
        )}?${searchParams.toString()}`,
        {
          token: appToken,
        }
      );
    },
    ...getWarehouseQueryConfig("destinations"),
  });
}


/**
 * Hook to search destinations by name with debouncing
 */
export function useDestinationsByName(
  nameQuery: string,
  debounceMs: number = 300
) {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();
  const [debouncedQuery, setDebouncedQuery] = useState(nameQuery);

  // Debounce the search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(nameQuery);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [nameQuery, debounceMs]);

  return useQuery<DestinationResponse[], Error>({
    queryKey: [
      "destinations-by-name",
      debouncedQuery,
      currentWarehouse?.id,
      appToken,
    ],
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");
      if (!debouncedQuery.trim()) return [];

      const searchParams = new URLSearchParams();
      searchParams.append("q", debouncedQuery.trim());
      searchParams.append("warehouseId", currentWarehouse.id);

      return fetchWithAuth(
        `/api/pallets/destinations/by-name?${searchParams.toString()}`,
        {
          token: appToken,
        }
      );
    },
    enabled:
      !isAuthLoading &&
      !!appToken &&
      !!currentWarehouse &&
      debouncedQuery.trim().length >= 2,
    ...getWarehouseQueryConfig("destinations"),
  });
}

/**
 * Hook to fetch warehouses (for warehouse selection)
 */
export function useWarehouses() {
  const { appToken, isLoading: isAuthLoading } = useAuth();

  return useQuery<Warehouse[], Error>({
    queryKey: ["warehouses", appToken],
    queryFn: async () => {
      if (!appToken) throw new Error("Authentication token not available");
      return fetchWithAuth("/api/warehouses", { token: appToken });
    },
    enabled: !isAuthLoading && !!appToken,
    ...getWarehouseQueryConfig("userWarehouses"),
  });
}

/**
 * Mutation hook for creating pallets
 */
export function useCreatePallet() {
  const queryClient = useQueryClient();
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useMutation<Pallet, Error, any>({
    mutationFn: async (palletData) => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");

      // Include warehouse context in headers
      return fetchWithAuth("/api/pallets", {
        method: "POST",
        token: appToken,
        headers: {
          "x-warehouse-id": currentWarehouse.id,
        },
        body: JSON.stringify(palletData),
      });
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: ["pallets", currentWarehouse?.id],
      });
      queryClient.invalidateQueries({
        queryKey: ["locations", currentWarehouse?.id],
      });
      // Invalidate destination caches to ensure new destinations appear immediately
      queryClient.invalidateQueries({
        queryKey: ["destinations", currentWarehouse?.id],
      });
      queryClient.invalidateQueries({
        queryKey: ["destinations-with-codes", currentWarehouse?.id],
      });
    },
  });
}

/**
 * Mutation hook for updating pallets
 */
export function useUpdatePallet() {
  const queryClient = useQueryClient();
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useMutation<Pallet, Error, { id: string; data: any }>({
    mutationFn: async ({ id, data }) => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");

      return fetchWithAuth(
        `/api/pallets/${id}?warehouseId=${currentWarehouse.id}`,
        {
          method: "PATCH",
          token: appToken,
          body: JSON.stringify(data),
        }
      );
    },
    onSuccess: (_, { id }) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: ["pallets", currentWarehouse?.id],
      });
      queryClient.invalidateQueries({
        queryKey: ["pallet", id, currentWarehouse?.id],
      });
      queryClient.invalidateQueries({
        queryKey: ["locations", currentWarehouse?.id],
      });
    },
  });
}

/**
 * Mutation hook for moving pallets
 */
export function useMovePallet() {
  const queryClient = useQueryClient();
  const { appToken } = useAuth();
  const { currentWarehouse } = useWarehouse();

  return useMutation<Pallet, Error, { palletId: string; locationId: string }>({
    mutationFn: async ({ palletId, locationId }) => {
      if (!appToken) throw new Error("Authentication token not available");
      if (!currentWarehouse) throw new Error("No warehouse selected");

      return fetchWithAuth(
        `/api/pallets/${palletId}/move?warehouseId=${currentWarehouse.id}`,
        {
          method: "POST",
          token: appToken,
          body: JSON.stringify({ locationId }),
        }
      );
    },
    onSuccess: (_, { palletId }) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: ["pallets", currentWarehouse?.id],
      });
      queryClient.invalidateQueries({
        queryKey: ["pallet", palletId, currentWarehouse?.id],
      });
      queryClient.invalidateQueries({
        queryKey: ["locations", currentWarehouse?.id],
      });
    },
  });
}

/**
 * Suspense-enabled hooks for instant loading without spinners
 * These hooks throw promises during loading, preventing re-renders
 */

/**
 * Hook to fetch pallets with Suspense (no loading states)
 */
export function usePalletsSuspense(filters: PalletFilters = {}) {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  if (isAuthLoading || !appToken || !currentWarehouse) {
    throw new Promise(() => {}); // Suspend until ready
  }

  const searchParams = new URLSearchParams();
  searchParams.append("warehouseId", currentWarehouse.id);

  // Add filters to search params
  if (filters.status) searchParams.append("status", filters.status);
  if (filters.locationId) searchParams.append("locationId", filters.locationId);
  if (filters.search) searchParams.append("search", filters.search);
  if (filters.shipToDestination)
    searchParams.append("shipToDestination", filters.shipToDestination);

  return useSuspenseQuery({
    queryKey: ["pallets", currentWarehouse.id, filters, appToken],
    queryFn: async () => {
      return fetchWithAuth(`/api/pallets?${searchParams.toString()}`, {
        token: appToken,
      });
    },
    ...getWarehouseQueryConfig("pallets"),
  });
}

/**
 * Hook to fetch locations with Suspense (no loading states)
 */
export function useLocationsSuspense(filters: LocationFilters = {}) {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  if (isAuthLoading || !appToken || !currentWarehouse) {
    throw new Promise(() => {}); // Suspend until ready
  }

  const searchParams = new URLSearchParams();
  searchParams.append("warehouseId", currentWarehouse.id);

  // Add filters to search params
  if (filters.category) searchParams.append("category", filters.category);
  if (filters.type) searchParams.append("type", filters.type);
  if (filters.search) searchParams.append("search", filters.search);

  return useSuspenseQuery({
    queryKey: ["locations", currentWarehouse.id, filters, appToken],
    queryFn: async () => {
      return fetchWithAuth(`/api/locations?${searchParams.toString()}`, {
        token: appToken,
      });
    },
    ...getWarehouseQueryConfig("locations"),
  });
}

/**
 * Hook to fetch items with Suspense (no loading states)
 */
export function useItemsSuspense() {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  if (isAuthLoading || !appToken || !currentWarehouse) {
    throw new Promise(() => {}); // Suspend until ready
  }

  const searchParams = new URLSearchParams();
  searchParams.append("warehouseId", currentWarehouse.id);

  return useSuspenseQuery({
    queryKey: ["items", currentWarehouse.id, {}, appToken],
    queryFn: async () => {
      return fetchWithAuth(`/api/items?${searchParams.toString()}`, {
        token: appToken,
      });
    },
    ...getWarehouseQueryConfig("items"),
  });
}

/**
 * Hook to fetch single pallet with Suspense (no loading states)
 */
export function usePalletSuspense(palletId: string) {
  const { appToken, isLoading: isAuthLoading } = useAuth();
  const { currentWarehouse } = useWarehouse();

  if (isAuthLoading || !appToken || !currentWarehouse || !palletId) {
    throw new Promise(() => {}); // Suspend until ready
  }

  return useSuspenseQuery({
    queryKey: ["pallet", palletId, currentWarehouse.id, appToken],
    queryFn: async () => {
      return fetchWithAuth(`/api/pallets/${palletId}`, { token: appToken });
    },
    ...getWarehouseQueryConfig("pallets"),
  });
}
