/**
 * Warehouse validation hook with caching
 * Provides cached warehouse validation to avoid redundant API calls
 */

import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/components/providers/auth-provider";
import { fetchWithAuth } from "@/lib/api";
import {
  getCachedWarehouseValidation,
  cacheWarehouseValidation,
  cleanupExpiredWarehouseValidation,
} from "@/lib/warehouse-validation-cache";
import { warehouseKeys, batchKeys } from "@/lib/query-keys";
import { Role } from "@quildora/types";

interface WarehouseValidationResult {
  hasAccess: boolean;
  userRole: Role | null;
  isManager: boolean;
  isAdmin: boolean;
}

interface UseWarehouseValidationOptions {
  warehouseId?: string;
  enabled?: boolean;
}

/**
 * Hook for validating warehouse access with caching
 */
export function useWarehouseValidation({
  warehouseId,
  enabled = true,
}: UseWarehouseValidationOptions = {}) {
  const { appUser, appToken } = useAuth();

  return useQuery<WarehouseValidationResult, Error>({
    queryKey: warehouseKeys.validation(warehouseId || "", appUser?.id || ""),
    queryFn: async () => {
      if (!warehouseId || !appUser || !appToken) {
        throw new Error("Missing required parameters for warehouse validation");
      }

      // Clean up expired cache entries
      cleanupExpiredWarehouseValidation();

      // Check cache first
      const cached = getCachedWarehouseValidation(warehouseId, appUser.id);
      if (cached) {
        return {
          hasAccess: cached.hasAccess,
          userRole: cached.userRole,
          isManager: cached.isManager,
          isAdmin: cached.isAdmin,
        };
      }

      // If not cached, validate with API
      try {
        const response = await fetchWithAuth(
          `/api/warehouses/${warehouseId}/validate-access`,
          { token: appToken }
        );

        const result: WarehouseValidationResult = {
          hasAccess: response.hasAccess,
          userRole: response.userRole,
          isManager: response.isManager,
          isAdmin: response.isAdmin,
        };

        // Cache the result
        cacheWarehouseValidation(
          warehouseId,
          appUser.id,
          result.hasAccess,
          result.userRole,
          result.isManager,
          result.isAdmin
        );

        return result;
      } catch (error) {
        // If API fails, fall back to client-side validation
        const hasAccess = validateWarehouseAccessClientSide(
          warehouseId,
          appUser
        );
        const userRole = getUserWarehouseRoleClientSide(warehouseId, appUser);
        const isManager =
          userRole === Role.WAREHOUSE_MANAGER || userRole === Role.TENANT_ADMIN;
        const isAdmin = userRole === Role.TENANT_ADMIN;

        const result: WarehouseValidationResult = {
          hasAccess,
          userRole,
          isManager,
          isAdmin,
        };

        // Cache the client-side result (shorter duration)
        cacheWarehouseValidation(
          warehouseId,
          appUser.id,
          result.hasAccess,
          result.userRole,
          result.isManager,
          result.isAdmin
        );

        return result;
      }
    },
    enabled: enabled && !!warehouseId && !!appUser && !!appToken,
    staleTime: 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
    refetchOnWindowFocus: false,
  });
}

/**
 * Client-side warehouse access validation (fallback)
 */
function validateWarehouseAccessClientSide(
  warehouseId: string,
  appUser: any
): boolean {
  if (!appUser) return false;

  // TENANT_ADMIN has access to all warehouses in their tenant
  if (appUser.role === Role.TENANT_ADMIN) return true;

  // Check if user has specific warehouse access
  return (
    appUser.warehouseUsers?.some((wu: any) => wu.warehouseId === warehouseId) ||
    false
  );
}

/**
 * Client-side warehouse role determination (fallback)
 */
function getUserWarehouseRoleClientSide(
  warehouseId: string,
  appUser: any
): Role | null {
  if (!appUser) return null;

  // TENANT_ADMIN has admin access to all warehouses
  if (appUser.role === Role.TENANT_ADMIN) return Role.TENANT_ADMIN;

  // Find specific warehouse role
  const warehouseUser = appUser.warehouseUsers?.find(
    (wu: any) => wu.warehouseId === warehouseId
  );
  return warehouseUser?.role || null;
}

/**
 * Hook for batch warehouse validation (multiple warehouses)
 */
export function useBatchWarehouseValidation(warehouseIds: string[]) {
  const { appUser, appToken } = useAuth();

  return useQuery<Record<string, WarehouseValidationResult>, Error>({
    queryKey: batchKeys.warehouseValidation(warehouseIds, appUser?.id || ""),
    queryFn: async () => {
      if (!appUser || !appToken || warehouseIds.length === 0) {
        return {};
      }

      const results: Record<string, WarehouseValidationResult> = {};

      // Check cache for each warehouse
      for (const warehouseId of warehouseIds) {
        const cached = getCachedWarehouseValidation(warehouseId, appUser.id);
        if (cached) {
          results[warehouseId] = {
            hasAccess: cached.hasAccess,
            userRole: cached.userRole,
            isManager: cached.isManager,
            isAdmin: cached.isAdmin,
          };
        }
      }

      // Get uncached warehouse IDs
      const uncachedIds = warehouseIds.filter((id) => !results[id]);

      if (uncachedIds.length > 0) {
        try {
          // Batch validate uncached warehouses
          const response = await fetchWithAuth(
            `/api/warehouses/batch-validate-access`,
            {
              method: "POST",
              body: JSON.stringify({ warehouseIds: uncachedIds }),
              token: appToken,
            }
          );

          // Process and cache results
          for (const [warehouseId, validation] of Object.entries(response)) {
            const result = validation as WarehouseValidationResult;
            results[warehouseId] = result;

            // Cache the result
            cacheWarehouseValidation(
              warehouseId,
              appUser.id,
              result.hasAccess,
              result.userRole,
              result.isManager,
              result.isAdmin
            );
          }
        } catch (error) {
          // Fall back to client-side validation for uncached warehouses
          for (const warehouseId of uncachedIds) {
            const hasAccess = validateWarehouseAccessClientSide(
              warehouseId,
              appUser
            );
            const userRole = getUserWarehouseRoleClientSide(
              warehouseId,
              appUser
            );
            const isManager =
              userRole === Role.WAREHOUSE_MANAGER ||
              userRole === Role.TENANT_ADMIN;
            const isAdmin = userRole === Role.TENANT_ADMIN;

            results[warehouseId] = {
              hasAccess,
              userRole,
              isManager,
              isAdmin,
            };

            // Cache the client-side result
            cacheWarehouseValidation(
              warehouseId,
              appUser.id,
              hasAccess,
              userRole,
              isManager,
              isAdmin
            );
          }
        }
      }

      return results;
    },
    enabled: !!appUser && !!appToken && warehouseIds.length > 0,
    staleTime: 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
    refetchOnWindowFocus: false,
  });
}
