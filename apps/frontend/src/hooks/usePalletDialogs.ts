import { useState } from "react";
import { Pallet } from "@quildora/types";

/**
 * Custom hook for managing pallet dialog states and handlers
 * Centralizes all dialog-related state management for better organization
 */
export const usePalletDialogs = () => {
  // Dialog open/close states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isMoveDialogOpen, setIsMoveDialogOpen] = useState(false);

  // Selected pallet states for dialogs
  const [selectedPalletForEdit, setSelectedPalletForEdit] = useState<Pallet | null>(null);
  const [selectedPalletForDelete, setSelectedPalletForDelete] = useState<Pallet | null>(null);
  const [selectedPalletForMove, setSelectedPalletForMove] = useState<Pallet | null>(null);

  // Handler functions
  const handleCreateClick = () => {
    setIsCreateDialogOpen(true);
  };

  const handleEditClick = (pallet: Pallet) => {
    setSelectedPalletForEdit(pallet);
    setIsEditDialogOpen(true);
  };

  const handleDeleteClick = (pallet: Pallet) => {
    setSelectedPalletForDelete(pallet);
    setIsDeleteDialogOpen(true);
  };

  const handleMoveClick = (pallet: Pallet) => {
    setSelectedPalletForMove(pallet);
    setIsMoveDialogOpen(true);
  };

  // Close handlers with cleanup
  const closeCreateDialog = () => {
    setIsCreateDialogOpen(false);
  };

  const closeEditDialog = () => {
    setIsEditDialogOpen(false);
    setSelectedPalletForEdit(null);
  };

  const closeDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setSelectedPalletForDelete(null);
  };

  const closeMoveDialog = () => {
    setIsMoveDialogOpen(false);
    setSelectedPalletForMove(null);
  };

  return {
    // Dialog states
    isCreateDialogOpen,
    isEditDialogOpen,
    isDeleteDialogOpen,
    isMoveDialogOpen,
    
    // Selected pallets
    selectedPalletForEdit,
    selectedPalletForDelete,
    selectedPalletForMove,
    
    // Action handlers
    handleCreateClick,
    handleEditClick,
    handleDeleteClick,
    handleMoveClick,
    
    // Close handlers
    closeCreateDialog,
    closeEditDialog,
    closeDeleteDialog,
    closeMoveDialog,
    
    // Direct state setters (for dialog components)
    setIsCreateDialogOpen,
    setIsEditDialogOpen,
    setIsDeleteDialogOpen,
    setIsMoveDialogOpen,
  };
};
