import { useState, useCallback } from "react";
import { PalletItem } from "@quildora/types";

/**
 * Custom hook for managing dialog states in the pallet detail page
 * Handles both pallet-level dialogs (Edit, Move, Delete) and item-level dialogs (Add, Edit, Remove)
 */
export const usePalletDetailDialogs = () => {
  // Pallet-level dialog states
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isMoveDialogOpen, setIsMoveDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Item-level dialog states
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [itemToEdit, setItemToEdit] = useState<PalletItem | null>(null);
  const [itemToRemove, setItemToRemove] = useState<PalletItem | null>(null);

  // Pallet-level dialog handlers
  const handleEditClick = useCallback(() => {
    setIsEditDialogOpen(true);
  }, []);

  const handleMoveClick = useCallback(() => {
    setIsMoveDialogOpen(true);
  }, []);

  const handleDeleteClick = useCallback(() => {
    setIsDeleteDialogOpen(true);
  }, []);

  const closeEditDialog = useCallback(() => {
    setIsEditDialogOpen(false);
  }, []);

  const closeMoveDialog = useCallback(() => {
    setIsMoveDialogOpen(false);
  }, []);

  const closeDeleteDialog = useCallback(() => {
    setIsDeleteDialogOpen(false);
  }, []);

  // Item-level dialog handlers
  const handleAddItemClick = useCallback(() => {
    setIsAddDialogOpen(true);
  }, []);

  const handleEditItemClick = useCallback((item: PalletItem) => {
    setItemToEdit(item);
  }, []);

  const handleRemoveItemClick = useCallback((item: PalletItem) => {
    setItemToRemove(item);
  }, []);

  const closeAddDialog = useCallback(() => {
    setIsAddDialogOpen(false);
  }, []);

  const closeEditItemDialog = useCallback(() => {
    setItemToEdit(null);
  }, []);

  const closeRemoveItemDialog = useCallback(() => {
    setItemToRemove(null);
  }, []);

  return {
    // Pallet-level dialog states
    isEditDialogOpen,
    isMoveDialogOpen,
    isDeleteDialogOpen,
    
    // Pallet-level dialog handlers
    handleEditClick,
    handleMoveClick,
    handleDeleteClick,
    closeEditDialog,
    closeMoveDialog,
    closeDeleteDialog,
    
    // Item-level dialog states
    isAddDialogOpen,
    itemToEdit,
    itemToRemove,
    
    // Item-level dialog handlers
    handleAddItemClick,
    handleEditItemClick,
    handleRemoveItemClick,
    closeAddDialog,
    closeEditItemDialog,
    closeRemoveItemDialog,
    
    // Computed states for convenience
    isEditItemDialogOpen: itemToEdit !== null,
    isRemoveItemDialogOpen: itemToRemove !== null,
  };
};
