/**
 * Comprehensive Integration Tests for Quildora Onboarding System
 * 
 * These tests verify the complete onboarding flow from start to finish,
 * including error handling, performance, and accessibility.
 */

import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import '@testing-library/jest-dom';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock Next.js router
const mockPush = jest.fn();
const mockPrefetch = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    prefetch: mockPrefetch,
    back: jest.fn(),
  }),
  usePathname: () => '/auth/signup/business',
}));

// Mock authentication
jest.mock('@/components/providers/auth-provider', () => ({
  useAuth: () => ({
    appUser: null,
    appToken: null,
    isLoading: false,
  }),
}));

// Test data
const validBusinessInfo = {
  companyName: 'Test Company Inc.',
  industry: 'Technology',
  companySize: '10-50',
};

const validAdminAccount = {
  email: '<EMAIL>',
  fullName: 'John Admin',
  password: 'SecurePassword123!',
  confirmPassword: 'SecurePassword123!',
};

const validWarehouseSetup = {
  warehouseName: 'Main Warehouse',
  warehouseType: 'DISTRIBUTION',
  address: '123 Business St',
  city: 'Business City',
  state: 'BC',
  zipCode: '12345',
  country: 'US',
};

describe('Onboarding Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Clear localStorage
    localStorage.clear();
  });

  describe('Complete Onboarding Flow', () => {
    it('should complete the entire onboarding process successfully', async () => {
      const user = userEvent.setup();

      // Mock API responses
      global.fetch = jest.fn()
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, sessionId: 'test-session' }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, warehouseId: 'test-warehouse' }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, userId: 'test-user' }),
        });

      // Start onboarding flow
      const { container } = render(<OnboardingFlow />);

      // Step 1: Business Information
      await user.type(screen.getByLabelText(/company name/i), validBusinessInfo.companyName);
      await user.selectOptions(screen.getByLabelText(/industry/i), validBusinessInfo.industry);
      await user.selectOptions(screen.getByLabelText(/company size/i), validBusinessInfo.companySize);
      
      await user.click(screen.getByRole('button', { name: /continue/i }));

      // Wait for navigation to admin account step
      await waitFor(() => {
        expect(screen.getByText(/admin account/i)).toBeInTheDocument();
      });

      // Step 2: Admin Account
      await user.type(screen.getByLabelText(/email/i), validAdminAccount.email);
      await user.type(screen.getByLabelText(/full name/i), validAdminAccount.fullName);
      await user.type(screen.getByLabelText(/^password/i), validAdminAccount.password);
      await user.type(screen.getByLabelText(/confirm password/i), validAdminAccount.confirmPassword);
      
      await user.click(screen.getByRole('button', { name: /continue/i }));

      // Wait for navigation to warehouse setup
      await waitFor(() => {
        expect(screen.getByText(/warehouse setup/i)).toBeInTheDocument();
      });

      // Step 3: Warehouse Setup
      await user.type(screen.getByLabelText(/warehouse name/i), validWarehouseSetup.warehouseName);
      await user.selectOptions(screen.getByLabelText(/warehouse type/i), validWarehouseSetup.warehouseType);
      await user.type(screen.getByLabelText(/address/i), validWarehouseSetup.address);
      await user.type(screen.getByLabelText(/city/i), validWarehouseSetup.city);
      await user.type(screen.getByLabelText(/state/i), validWarehouseSetup.state);
      await user.type(screen.getByLabelText(/zip code/i), validWarehouseSetup.zipCode);
      await user.selectOptions(screen.getByLabelText(/country/i), validWarehouseSetup.country);
      
      await user.click(screen.getByRole('button', { name: /continue/i }));

      // Wait for navigation to team setup
      await waitFor(() => {
        expect(screen.getByText(/team setup/i)).toBeInTheDocument();
      });

      // Step 4: Skip team setup
      await user.click(screen.getByRole('button', { name: /skip/i }));

      // Wait for completion
      await waitFor(() => {
        expect(screen.getByText(/welcome to quildora/i)).toBeInTheDocument();
      });

      // Verify completion screen
      expect(screen.getByText(/🎉/)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /enter your dashboard/i })).toBeInTheDocument();

      // Check accessibility
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should handle errors gracefully and allow recovery', async () => {
      const user = userEvent.setup();

      // Mock API failure then success
      global.fetch = jest.fn()
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, sessionId: 'test-session' }),
        });

      render(<OnboardingFlow />);

      // Fill business information
      await user.type(screen.getByLabelText(/company name/i), validBusinessInfo.companyName);
      await user.selectOptions(screen.getByLabelText(/industry/i), validBusinessInfo.industry);
      await user.selectOptions(screen.getByLabelText(/company size/i), validBusinessInfo.companySize);
      
      // Submit and expect error
      await user.click(screen.getByRole('button', { name: /continue/i }));

      // Wait for error message
      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });

      // Retry should work
      await user.click(screen.getByRole('button', { name: /try again/i }));

      // Should proceed to next step
      await waitFor(() => {
        expect(screen.getByText(/admin account/i)).toBeInTheDocument();
      });
    });

    it('should validate form inputs and show appropriate errors', async () => {
      const user = userEvent.setup();

      render(<OnboardingFlow />);

      // Try to continue without filling required fields
      await user.click(screen.getByRole('button', { name: /continue/i }));

      // Should show validation errors
      await waitFor(() => {
        expect(screen.getByText(/company name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/industry is required/i)).toBeInTheDocument();
        expect(screen.getByText(/company size is required/i)).toBeInTheDocument();
      });

      // Fill invalid email in admin step
      await user.type(screen.getByLabelText(/company name/i), validBusinessInfo.companyName);
      await user.selectOptions(screen.getByLabelText(/industry/i), validBusinessInfo.industry);
      await user.selectOptions(screen.getByLabelText(/company size/i), validBusinessInfo.companySize);
      await user.click(screen.getByRole('button', { name: /continue/i }));

      // Navigate to admin step
      await waitFor(() => {
        expect(screen.getByText(/admin account/i)).toBeInTheDocument();
      });

      // Enter invalid email
      await user.type(screen.getByLabelText(/email/i), 'invalid-email');
      await user.click(screen.getByRole('button', { name: /continue/i }));

      // Should show email validation error
      await waitFor(() => {
        expect(screen.getByText(/valid email address/i)).toBeInTheDocument();
      });
    });

    it('should preserve progress when navigating between steps', async () => {
      const user = userEvent.setup();

      render(<OnboardingFlow />);

      // Fill business information
      await user.type(screen.getByLabelText(/company name/i), validBusinessInfo.companyName);
      await user.selectOptions(screen.getByLabelText(/industry/i), validBusinessInfo.industry);
      await user.selectOptions(screen.getByLabelText(/company size/i), validBusinessInfo.companySize);
      
      await user.click(screen.getByRole('button', { name: /continue/i }));

      // Navigate to admin step
      await waitFor(() => {
        expect(screen.getByText(/admin account/i)).toBeInTheDocument();
      });

      // Go back to business step
      await user.click(screen.getByRole('button', { name: /back/i }));

      // Verify data is preserved
      await waitFor(() => {
        expect(screen.getByDisplayValue(validBusinessInfo.companyName)).toBeInTheDocument();
      });
    });
  });

  describe('Mobile Responsiveness', () => {
    it('should render correctly on mobile devices', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      const { container } = render(<OnboardingFlow />);

      // Check mobile-specific elements
      expect(screen.getByTestId('mobile-progress')).toBeInTheDocument();
      expect(screen.getByTestId('mobile-header')).toBeInTheDocument();

      // Check touch target sizes
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        const styles = window.getComputedStyle(button);
        const minHeight = parseInt(styles.minHeight);
        expect(minHeight).toBeGreaterThanOrEqual(44); // 44px minimum touch target
      });

      // Check accessibility
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Performance', () => {
    it('should preload next steps for better performance', async () => {
      render(<OnboardingFlow />);

      // Check that router.prefetch was called for next step
      await waitFor(() => {
        expect(mockPrefetch).toHaveBeenCalledWith('/auth/signup/business/admin');
      });
    });

    it('should lazy load components to reduce initial bundle size', async () => {
      const { container } = render(<OnboardingFlow />);

      // Check that loading fallback is shown initially
      expect(screen.getByText(/loading/i)).toBeInTheDocument();

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByText(/business information/i)).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();

      render(<OnboardingFlow />);

      // Tab through form elements
      await user.tab();
      expect(screen.getByLabelText(/company name/i)).toHaveFocus();

      await user.tab();
      expect(screen.getByLabelText(/industry/i)).toHaveFocus();

      await user.tab();
      expect(screen.getByLabelText(/company size/i)).toHaveFocus();

      await user.tab();
      expect(screen.getByRole('button', { name: /continue/i })).toHaveFocus();
    });

    it('should announce errors to screen readers', async () => {
      const user = userEvent.setup();

      render(<OnboardingFlow />);

      // Submit form without required fields
      await user.click(screen.getByRole('button', { name: /continue/i }));

      // Check that error has proper ARIA attributes
      await waitFor(() => {
        const errorMessage = screen.getByText(/company name is required/i);
        expect(errorMessage).toHaveAttribute('role', 'alert');
        expect(errorMessage).toHaveAttribute('aria-live', 'polite');
      });
    });

    it('should have proper heading hierarchy', () => {
      render(<OnboardingFlow />);

      const headings = screen.getAllByRole('heading');
      
      // Check that headings follow proper hierarchy (h1, h2, h3, etc.)
      let previousLevel = 0;
      headings.forEach(heading => {
        const level = parseInt(heading.tagName.charAt(1));
        expect(level).toBeLessThanOrEqual(previousLevel + 1);
        previousLevel = level;
      });
    });
  });

  describe('Error Boundaries', () => {
    it('should catch and handle component errors gracefully', () => {
      // Mock console.error to avoid noise in test output
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // Component that throws an error
      const ThrowError = () => {
        throw new Error('Test error');
      };

      render(
        <OnboardingErrorBoundary>
          <ThrowError />
        </OnboardingErrorBoundary>
      );

      // Should show error fallback
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();

      consoleSpy.mockRestore();
    });
  });
});

// Mock components for testing
function OnboardingFlow() {
  return (
    <div>
      <div data-testid="mobile-progress">Mobile Progress</div>
      <div data-testid="mobile-header">Mobile Header</div>
      <h1>Business Information</h1>
      <form>
        <label htmlFor="companyName">Company Name *</label>
        <input id="companyName" type="text" required />
        
        <label htmlFor="industry">Industry *</label>
        <select id="industry" required>
          <option value="">Select Industry</option>
          <option value="Technology">Technology</option>
        </select>
        
        <label htmlFor="companySize">Company Size *</label>
        <select id="companySize" required>
          <option value="">Select Size</option>
          <option value="10-50">10-50</option>
        </select>
        
        <button type="submit">Continue</button>
        <button type="button">Back</button>
      </form>
      <div>Loading...</div>
    </div>
  );
}

function OnboardingErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <div>
      <div>Something went wrong</div>
      <button>Try Again</button>
      {children}
    </div>
  );
}
