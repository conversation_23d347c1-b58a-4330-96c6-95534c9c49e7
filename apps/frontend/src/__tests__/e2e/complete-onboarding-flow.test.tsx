import { test, expect } from '@playwright/test';

test.describe('Complete Onboarding Flow E2E', () => {
  test.beforeEach(async ({ page }) => {
    // Start from the welcome page
    await page.goto('/auth/welcome');
  });

  test('should complete full business onboarding flow', async ({ page }) => {
    // Step 1: Start business signup
    await page.click('[data-testid="start-business-signup"]');
    await expect(page).toHaveURL('/auth/signup/business');

    // Step 2: Fill business information
    await page.fill('[data-testid="company-name"]', 'E2E Test Company');
    await page.selectOption('[data-testid="industry"]', 'Technology');
    await page.selectOption('[data-testid="company-size"]', '10-50');
    
    // Verify progress indicator
    await expect(page.locator('[data-testid="progress-step-1"]')).toHaveClass(/active/);
    
    await page.click('[data-testid="continue-button"]');
    
    // Should navigate to admin account step
    await expect(page).toHaveURL('/auth/signup/admin');
    await expect(page.locator('[data-testid="progress-step-2"]')).toHaveClass(/active/);

    // Step 3: Create admin account
    await page.fill('[data-testid="admin-email"]', '<EMAIL>');
    await page.fill('[data-testid="admin-name"]', 'E2E Test Admin');
    await page.fill('[data-testid="admin-password"]', 'SecurePassword123!');
    await page.fill('[data-testid="confirm-password"]', 'SecurePassword123!');
    
    await page.click('[data-testid="continue-button"]');
    
    // Should navigate to warehouse setup
    await expect(page).toHaveURL('/auth/signup/warehouse');
    await expect(page.locator('[data-testid="progress-step-3"]')).toHaveClass(/active/);

    // Step 4: Setup warehouse
    await page.fill('[data-testid="warehouse-name"]', 'E2E Test Warehouse');
    await page.selectOption('[data-testid="warehouse-type"]', 'DISTRIBUTION');
    await page.fill('[data-testid="address"]', '123 E2E Test Street');
    await page.fill('[data-testid="city"]', 'Test City');
    await page.fill('[data-testid="state"]', 'TS');
    await page.fill('[data-testid="zip-code"]', '12345');
    await page.selectOption('[data-testid="country"]', 'US');
    
    await page.click('[data-testid="continue-button"]');
    
    // Should navigate to team setup (optional)
    await expect(page).toHaveURL('/auth/signup/team');
    await expect(page.locator('[data-testid="progress-step-4"]')).toHaveClass(/active/);

    // Step 5: Skip team setup for now
    await page.click('[data-testid="skip-team-setup"]');
    
    // Should navigate to completion
    await expect(page).toHaveURL('/auth/signup/complete');
    await expect(page.locator('[data-testid="progress-step-5"]')).toHaveClass(/active/);

    // Step 6: Complete onboarding
    await page.click('[data-testid="complete-onboarding"]');
    
    // Should show success and redirect to dashboard
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="confetti"]')).toBeVisible();
    
    // Wait for redirect
    await page.waitForURL('/dashboard');
    
    // Verify user is logged in and has access to warehouse
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    await expect(page.locator('[data-testid="warehouse-selector"]')).toContainText('E2E Test Warehouse');
  });

  test('should handle team invitation flow', async ({ page }) => {
    // Complete initial onboarding first (abbreviated)
    await page.goto('/auth/signup/business');
    await page.fill('[data-testid="company-name"]', 'Team Test Company');
    await page.selectOption('[data-testid="industry"]', 'Technology');
    await page.selectOption('[data-testid="company-size"]', '10-50');
    await page.click('[data-testid="continue-button"]');
    
    await page.fill('[data-testid="admin-email"]', '<EMAIL>');
    await page.fill('[data-testid="admin-name"]', 'Team Admin');
    await page.fill('[data-testid="admin-password"]', 'SecurePassword123!');
    await page.fill('[data-testid="confirm-password"]', 'SecurePassword123!');
    await page.click('[data-testid="continue-button"]');
    
    await page.fill('[data-testid="warehouse-name"]', 'Team Test Warehouse');
    await page.selectOption('[data-testid="warehouse-type"]', 'DISTRIBUTION');
    await page.fill('[data-testid="address"]', '123 Team Test Street');
    await page.fill('[data-testid="city"]', 'Team City');
    await page.fill('[data-testid="state"]', 'TC');
    await page.fill('[data-testid="zip-code"]', '54321');
    await page.selectOption('[data-testid="country"]', 'US');
    await page.click('[data-testid="continue-button"]');
    
    // Now test team setup
    await page.fill('[data-testid="team-member-email"]', '<EMAIL>');
    await page.selectOption('[data-testid="team-member-role"]', 'WAREHOUSE_MEMBER');
    await page.click('[data-testid="add-team-member"]');
    
    // Verify team member was added
    await expect(page.locator('[data-testid="team-member-list"]')).toContainText('<EMAIL>');
    
    await page.click('[data-testid="continue-button"]');
    await page.click('[data-testid="complete-onboarding"]');
    
    // Should redirect to dashboard
    await page.waitForURL('/dashboard');
    
    // Navigate to user management
    await page.click('[data-testid="settings-menu"]');
    await page.click('[data-testid="users-settings"]');
    
    // Verify invitation was created
    await expect(page.locator('[data-testid="pending-invitations"]')).toContainText('<EMAIL>');
  });

  test('should handle invitation acceptance flow', async ({ page, context }) => {
    // Simulate receiving an invitation link
    const invitationCode = 'test-invitation-code-123';
    await page.goto(`/auth/join/${invitationCode}`);
    
    // Should show invitation details
    await expect(page.locator('[data-testid="invitation-details"]')).toBeVisible();
    await expect(page.locator('[data-testid="company-name"]')).toContainText('Test Company');
    await expect(page.locator('[data-testid="role"]')).toContainText('Warehouse Member');
    
    // Fill acceptance form
    await page.fill('[data-testid="full-name"]', 'Invited User');
    await page.fill('[data-testid="password"]', 'InvitedPassword123!');
    await page.fill('[data-testid="confirm-password"]', 'InvitedPassword123!');
    
    await page.click('[data-testid="accept-invitation"]');
    
    // Should show success and redirect to dashboard
    await expect(page.locator('[data-testid="welcome-message"]')).toBeVisible();
    await page.waitForURL('/dashboard');
    
    // Verify user has correct role and warehouse access
    await expect(page.locator('[data-testid="user-role"]')).toContainText('Warehouse Member');
    await expect(page.locator('[data-testid="warehouse-selector"]')).toBeVisible();
  });

  test('should handle error recovery during onboarding', async ({ page }) => {
    // Start onboarding
    await page.goto('/auth/signup/business');
    
    // Fill business info
    await page.fill('[data-testid="company-name"]', 'Error Test Company');
    await page.selectOption('[data-testid="industry"]', 'Technology');
    await page.selectOption('[data-testid="company-size"]', '10-50');
    
    // Simulate network error
    await page.route('**/api/onboarding/business-info', route => {
      route.abort('failed');
    });
    
    await page.click('[data-testid="continue-button"]');
    
    // Should show error message
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();
    
    // Remove network error simulation
    await page.unroute('**/api/onboarding/business-info');
    
    // Retry should work
    await page.click('[data-testid="retry-button"]');
    
    // Should proceed to next step
    await expect(page).toHaveURL('/auth/signup/admin');
  });

  test('should prevent unauthorized navigation', async ({ page }) => {
    // Try to access warehouse setup without completing previous steps
    await page.goto('/auth/signup/warehouse');
    
    // Should redirect to business info step
    await expect(page).toHaveURL('/auth/signup/business');
    await expect(page.locator('[data-testid="navigation-error"]')).toBeVisible();
  });

  test('should persist progress across page refreshes', async ({ page }) => {
    // Start onboarding and fill business info
    await page.goto('/auth/signup/business');
    await page.fill('[data-testid="company-name"]', 'Persistence Test Company');
    await page.selectOption('[data-testid="industry"]', 'Technology');
    await page.selectOption('[data-testid="company-size"]', '10-50');
    await page.click('[data-testid="continue-button"]');
    
    // Fill admin info
    await page.fill('[data-testid="admin-email"]', '<EMAIL>');
    await page.fill('[data-testid="admin-name"]', 'Persist Admin');
    await page.fill('[data-testid="admin-password"]', 'PersistPassword123!');
    await page.fill('[data-testid="confirm-password"]', 'PersistPassword123!');
    
    // Refresh the page
    await page.reload();
    
    // Should maintain progress and form data
    await expect(page.locator('[data-testid="admin-email"]')).toHaveValue('<EMAIL>');
    await expect(page.locator('[data-testid="admin-name"]')).toHaveValue('Persist Admin');
    await expect(page.locator('[data-testid="progress-step-2"]')).toHaveClass(/active/);
  });

  test('should complete first warehouse operation after onboarding', async ({ page }) => {
    // Complete onboarding (abbreviated)
    await page.goto('/auth/signup/business');
    // ... fill forms and complete onboarding ...
    await page.waitForURL('/dashboard');
    
    // Navigate to pallets page
    await page.click('[data-testid="pallets-nav"]');
    await expect(page).toHaveURL('/pallets');
    
    // Create first pallet
    await page.click('[data-testid="create-pallet-button"]');
    await page.fill('[data-testid="pallet-barcode"]', 'FIRST-PALLET-001');
    await page.selectOption('[data-testid="pallet-location"]', 'RECEIVING');
    await page.fill('[data-testid="pallet-description"]', 'My first pallet');
    
    await page.click('[data-testid="save-pallet"]');
    
    // Should show success message
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible();
    
    // Should see pallet in list
    await expect(page.locator('[data-testid="pallet-list"]')).toContainText('FIRST-PALLET-001');
    
    // Verify audit log was created
    await page.click('[data-testid="pallet-FIRST-PALLET-001"]');
    await page.click('[data-testid="audit-tab"]');
    await expect(page.locator('[data-testid="audit-log"]')).toContainText('Created pallet');
  });

  test('should handle mobile responsive onboarding', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('/auth/signup/business');
    
    // Verify mobile-optimized layout
    await expect(page.locator('[data-testid="mobile-header"]')).toBeVisible();
    await expect(page.locator('[data-testid="mobile-progress"]')).toBeVisible();
    
    // Test touch-friendly form elements
    const companyNameInput = page.locator('[data-testid="company-name"]');
    await expect(companyNameInput).toHaveCSS('min-height', '44px'); // Touch target size
    
    // Complete form on mobile
    await page.fill('[data-testid="company-name"]', 'Mobile Test Company');
    await page.selectOption('[data-testid="industry"]', 'Technology');
    await page.selectOption('[data-testid="company-size"]', '10-50');
    
    // Verify mobile continue button
    const continueButton = page.locator('[data-testid="continue-button"]');
    await expect(continueButton).toHaveCSS('min-height', '44px');
    
    await continueButton.click();
    await expect(page).toHaveURL('/auth/signup/admin');
  });
});
