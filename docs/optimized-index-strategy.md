# Optimized Index Strategy for Warehouse-Scoped Access Control

## Current Index Analysis

### Existing Indexes (From Schema)
```sql
-- Current tenant-based indexes (to be removed)
CREATE INDEX "Location_tenantId_idx" ON "Location"("tenantId");
CREATE INDEX "Pallet_tenantId_idx" ON "Pallet"("tenantId");
CREATE INDEX "PalletItem_tenantId_idx" ON "PalletItem"("tenantId");

-- Current functional indexes (to keep)
CREATE INDEX "Pallet_shipToDestination_idx" ON "Pallet"("shipToDestination");
CREATE INDEX "AuditLog_tenantId_timestamp_idx" ON "AuditLog"("tenantId", "timestamp");
CREATE INDEX "AuditLog_tenantId_entity_entityId_idx" ON "AuditLog"("tenantId", "entity", "entityId");
CREATE INDEX "AuditLog_tenantId_userId_idx" ON "AuditLog"("tenantId", "userId");
CREATE INDEX "Shipment_purchaseOrderId_idx" ON "Shipment"("purchaseOrderId");
```

## New Optimized Index Strategy

### 1. Core Warehouse-Scoped Indexes

#### Location Indexes
```sql
-- Primary warehouse location index with category for filtering
CREATE INDEX "Location_warehouseId_category_idx" 
ON "Location"("warehouseId", "category");

-- Warehouse location index with status for active/inactive filtering
CREATE INDEX "Location_warehouseId_status_idx" 
ON "Location"("warehouseId", "status");

-- Composite index for location search within warehouse
CREATE INDEX "Location_warehouseId_name_idx" 
ON "Location"("warehouseId", "name");
```

#### Pallet Indexes
```sql
-- Primary pallet index for warehouse queries via location
CREATE INDEX "Pallet_locationId_status_idx" 
ON "Pallet"("locationId", "status");

-- Pallet destination filtering within warehouse
CREATE INDEX "Pallet_locationId_shipToDestination_idx" 
ON "Pallet"("locationId", "shipToDestination");

-- Pallet date queries for warehouse operations
CREATE INDEX "Pallet_locationId_dateCreated_idx" 
ON "Pallet"("locationId", "dateCreated");

-- Composite index for pallet search and filtering
CREATE INDEX "Pallet_locationId_status_destination_idx" 
ON "Pallet"("locationId", "status", "shipToDestination");
```

#### PalletItem Indexes
```sql
-- PalletItem queries by pallet (already efficient via palletId)
-- No additional indexes needed - palletId foreign key index is sufficient

-- Optional: If we need item-based queries within warehouse
CREATE INDEX "PalletItem_itemId_palletId_idx" 
ON "PalletItem"("itemId", "palletId");
```

### 2. Security Validation Indexes

#### Warehouse Security Indexes
```sql
-- Composite index for warehouse-tenant validation
CREATE INDEX "Warehouse_id_tenantId_idx" 
ON "Warehouse"("id", "tenantId");

-- Warehouse user access validation
CREATE INDEX "WarehouseUser_warehouseId_userId_idx" 
ON "WarehouseUser"("warehouseId", "userId");
```

### 3. Performance Optimization Indexes

#### Covering Indexes (Include Columns)
```sql
-- Pallet covering index for common queries
CREATE INDEX "Pallet_locationId_covering_idx" 
ON "Pallet"("locationId") 
INCLUDE ("status", "shipToDestination", "dateCreated", "label");

-- Location covering index for warehouse operations
CREATE INDEX "Location_warehouseId_covering_idx" 
ON "Location"("warehouseId") 
INCLUDE ("name", "category", "status", "locationType");
```

#### Partial Indexes for Common Filters
```sql
-- Active locations only (most common query)
CREATE INDEX "Location_warehouseId_active_idx" 
ON "Location"("warehouseId") 
WHERE "status" = 'Active';

-- Non-released pallets (most common operational query)
CREATE INDEX "Pallet_locationId_operational_idx" 
ON "Location"("locationId") 
WHERE "status" != 'Released';

-- Pallets with destinations (shipping operations)
CREATE INDEX "Pallet_locationId_shipping_idx" 
ON "Pallet"("locationId", "shipToDestination") 
WHERE "shipToDestination" IS NOT NULL;
```

## Index Removal Strategy

### Indexes to Remove
```sql
-- Remove redundant tenant-based indexes
DROP INDEX IF EXISTS "Location_tenantId_idx";
DROP INDEX IF EXISTS "Pallet_tenantId_idx";
DROP INDEX IF EXISTS "PalletItem_tenantId_idx";

-- Remove any composite tenant indexes that become redundant
DROP INDEX IF EXISTS "Location_tenantId_warehouseId_idx";
DROP INDEX IF EXISTS "Pallet_tenantId_status_idx";
```

### Indexes to Keep
```sql
-- Keep essential tenant indexes for direct tenant relationships
-- AuditLog indexes (tenant-level auditing still needed)
-- Warehouse.tenantId (implicit from foreign key)
-- User.tenantId (implicit from foreign key)
-- Item.tenantId (implicit from foreign key)
```

## Performance Impact Analysis

### Query Performance Improvements

#### Before (Tenant-Scoped)
```sql
-- Current query pattern
SELECT p.*, l.*, w.*
FROM "Pallet" p
LEFT JOIN "Location" l ON p."locationId" = l.id
LEFT JOIN "Warehouse" w ON l."warehouseId" = w.id
WHERE p."tenantId" = $1
  AND l."warehouseId" = $2
  AND p."status" = $3;

-- Uses: Pallet(tenantId) → Location join → Warehouse join
-- Estimated cost: 500-800 units
```

#### After (Warehouse-Scoped)
```sql
-- Optimized query pattern
SELECT p.*, l.*, w.*
FROM "Pallet" p
INNER JOIN "Location" l ON p."locationId" = l.id
INNER JOIN "Warehouse" w ON l."warehouseId" = w.id
WHERE l."warehouseId" = $1
  AND w."tenantId" = $2  -- Security validation
  AND p."status" = $3;

-- Uses: Location(warehouseId) → Pallet(locationId, status) → Warehouse(id, tenantId)
-- Estimated cost: 200-400 units (50% improvement)
```

### Index Size Optimization

#### Current Index Sizes (Estimated)
```
Pallet(tenantId): ~50MB for 1M pallets
Location(tenantId): ~20MB for 100k locations
PalletItem(tenantId): ~80MB for 5M items
Total redundant: ~150MB
```

#### New Index Sizes (Estimated)
```
Location(warehouseId, category): ~25MB
Pallet(locationId, status): ~60MB
Pallet(locationId, shipToDestination): ~55MB
Warehouse(id, tenantId): ~5MB
Total new: ~145MB

Net savings: ~5MB + improved query performance
```

## Implementation Order

### Phase 1: Add New Indexes (Zero Downtime)
```sql
-- Add new indexes concurrently
CREATE INDEX CONCURRENTLY "Location_warehouseId_category_idx" 
ON "Location"("warehouseId", "category");

CREATE INDEX CONCURRENTLY "Pallet_locationId_status_idx" 
ON "Pallet"("locationId", "status");

CREATE INDEX CONCURRENTLY "Warehouse_id_tenantId_idx" 
ON "Warehouse"("id", "tenantId");
```

### Phase 2: Update Application Queries
- Modify service layer to use new query patterns
- Test performance with new indexes
- Validate query execution plans

### Phase 3: Remove Old Indexes (Minimal Downtime)
```sql
-- Remove redundant indexes after application update
DROP INDEX "Location_tenantId_idx";
DROP INDEX "Pallet_tenantId_idx";
DROP INDEX "PalletItem_tenantId_idx";
```

## Monitoring and Validation

### Index Usage Monitoring
```sql
-- Monitor new index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
WHERE indexname LIKE '%warehouseId%' 
   OR indexname LIKE '%locationId%'
ORDER BY idx_scan DESC;
```

### Query Performance Monitoring
```sql
-- Monitor query performance improvements
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
WHERE query LIKE '%Pallet%' 
   OR query LIKE '%Location%'
ORDER BY total_time DESC;
```

## Rollback Strategy

### Emergency Rollback
```sql
-- If performance degrades, quickly restore old indexes
CREATE INDEX CONCURRENTLY "Location_tenantId_idx" ON "Location"("tenantId");
CREATE INDEX CONCURRENTLY "Pallet_tenantId_idx" ON "Pallet"("tenantId");
CREATE INDEX CONCURRENTLY "PalletItem_tenantId_idx" ON "PalletItem"("tenantId");
```

### Gradual Rollback
1. Restore application to use tenant-scoped queries
2. Add back tenant indexes
3. Remove warehouse-scoped indexes
4. Validate performance returns to baseline
