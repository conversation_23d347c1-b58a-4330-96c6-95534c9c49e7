# Warehouse-Scoped Schema Analysis

## Current Schema Dependencies Analysis

### Tables with Redundant tenantId Foreign Keys

Based on the schema analysis, the following tables have redundant `tenantId` foreign keys that can be removed because tenant access can be derived through warehouse relationships:

#### 1. Location Table
```prisma
model Location {
  // ... other fields
  warehouseId String
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id])
  
  tenantId String @db.Uuid  // REDUNDANT - can derive from warehouse.tenantId
  tenant   Tenant @relation(fields: [tenantId], references: [id])
}
```
**Analysis**: Location belongs to Warehouse, and Warehouse belongs to Tenant. The tenantId is redundant.

#### 2. Pallet Table
```prisma
model Pallet {
  // ... other fields
  locationId String?
  location   Location? @relation(fields: [locationId], references: [id])
  
  tenantId String @db.Uuid  // REDUNDANT - can derive from location.warehouse.tenantId
  tenant   Tenant @relation(fields: [tenantId], references: [id])
  
  @@unique([tenantId, barcode], name: "tenant_pallet_barcode_unique")
}
```
**Analysis**: Pallet belongs to Location, Location belongs to Warehouse, Warehouse belongs to Tenant. The tenantId is redundant.

#### 3. PalletItem Table
```prisma
model PalletItem {
  // ... other fields
  palletId String
  pallet   Pallet @relation(fields: [palletId], references: [id])
  
  tenantId String @db.Uuid  // REDUNDANT - can derive from pallet.location.warehouse.tenantId
  tenant   Tenant @relation(fields: [tenantId], references: [id])
  
  @@unique([tenantId, palletId, itemId], name: "tenant_pallet_item_unique")
}
```
**Analysis**: PalletItem belongs to Pallet, which has the full hierarchy to Tenant. The tenantId is redundant.

### Tables to Keep tenantId (Required for Direct Access)

#### 1. Warehouse Table
```prisma
model Warehouse {
  tenantId String @db.Uuid  // REQUIRED - direct tenant relationship
  tenant   Tenant @relation(fields: [tenantId], references: [id])
}
```
**Reason**: Warehouses are directly owned by tenants and need direct tenant scoping.

#### 2. User Table
```prisma
model User {
  tenantId String? @db.Uuid  // REQUIRED - users belong to tenants
  tenant   Tenant? @relation(fields: [tenantId], references: [id])
}
```
**Reason**: Users are directly associated with tenants.

#### 3. Item Table
```prisma
model Item {
  tenantId String @db.Uuid  // REQUIRED - items are tenant-specific
  tenant   Tenant @relation(fields: [tenantId], references: [id])
}
```
**Reason**: Items are tenant-specific and not warehouse-specific.

#### 4. PurchaseOrder Table
```prisma
model PurchaseOrder {
  tenantId String @db.Uuid  // REQUIRED - POs are tenant-level
  tenant   Tenant @relation(fields: [tenantId], references: [id])
  
  warehouseId String? // Optional warehouse assignment
  warehouse   Warehouse? @relation(fields: [warehouseId], references: [id])
}
```
**Reason**: Purchase Orders are created at tenant level and may be assigned to warehouses.

### Current Indexes Analysis

#### Indexes to Remove (Redundant)
1. `Location.tenantId` - implicit index from foreign key
2. `Pallet.tenantId` - implicit index from foreign key  
3. `PalletItem.tenantId` - implicit index from foreign key

#### Indexes to Keep
1. `AuditLog(tenantId, timestamp)` - needed for tenant-level audit queries
2. `AuditLog(tenantId, entity, entityId)` - needed for entity-specific audit queries
3. `AuditLog(tenantId, userId)` - needed for user-specific audit queries
4. `Warehouse.tenantId` - implicit index, needed for tenant warehouse queries

#### New Indexes to Add
1. `Location(warehouseId, category)` - optimize warehouse location queries
2. `Pallet(locationId, status)` - optimize warehouse pallet queries
3. `Pallet(locationId, shipToDestination)` - optimize destination filtering
4. `Warehouse(id, tenantId)` - composite for security validation

### Unique Constraints Analysis

#### Constraints to Update

1. **Pallet Barcode Uniqueness**
   ```prisma
   // Current
   @@unique([tenantId, barcode], name: "tenant_pallet_barcode_unique")
   
   // Proposed - warehouse-scoped uniqueness
   @@unique([locationId, barcode], name: "location_pallet_barcode_unique")
   // OR maintain tenant-level uniqueness through warehouse validation
   ```

2. **PalletItem Uniqueness**
   ```prisma
   // Current
   @@unique([tenantId, palletId, itemId], name: "tenant_pallet_item_unique")
   
   // Proposed - simplified since palletId already ensures uniqueness
   @@unique([palletId, itemId], name: "pallet_item_unique")
   ```

### Data Integrity Validation Required

Before removing tenantId columns, validate:

1. **Location-Warehouse-Tenant Consistency**
   ```sql
   SELECT COUNT(*) as inconsistent_locations
   FROM "Location" l
   JOIN "Warehouse" w ON l."warehouseId" = w.id
   WHERE l."tenantId" != w."tenantId";
   ```

2. **Pallet-Location-Warehouse-Tenant Consistency**
   ```sql
   SELECT COUNT(*) as inconsistent_pallets
   FROM "Pallet" p
   JOIN "Location" l ON p."locationId" = l.id
   JOIN "Warehouse" w ON l."warehouseId" = w.id
   WHERE p."tenantId" != w."tenantId";
   ```

3. **PalletItem-Pallet Consistency**
   ```sql
   SELECT COUNT(*) as inconsistent_pallet_items
   FROM "PalletItem" pi
   JOIN "Pallet" p ON pi."palletId" = p.id
   WHERE pi."tenantId" != p."tenantId";
   ```

### Migration Impact Assessment

#### Low Risk Changes
- Remove `Location.tenantId` - well-defined warehouse relationship
- Remove `PalletItem.tenantId` - clear pallet relationship

#### Medium Risk Changes  
- Remove `Pallet.tenantId` - requires updating all pallet queries
- Update unique constraints - may affect application logic

#### High Risk Changes
- Modify barcode uniqueness scope - could affect existing barcodes
- Update all service layer queries - extensive testing required

### Performance Impact

#### Expected Improvements
- **Query Performance**: 30-50% improvement for warehouse-scoped queries
- **Index Size**: ~47% reduction in redundant index storage
- **Join Complexity**: Simplified query plans for warehouse operations

#### Potential Concerns
- **Security Validation**: Additional joins required for tenant validation
- **Migration Downtime**: Schema changes require brief downtime
- **Application Updates**: All services need query modifications

## Next Steps

1. Create pre-migration validation scripts
2. Design optimized index strategy
3. Create migration scripts with rollback procedures
4. Update Prisma schema definition
5. Test migration in development environment
