# Quildora Authentication & Onboarding System Design Specification

## Executive Summary

This specification outlines the design for a comprehensive authentication and onboarding system that will replace Quildora's current generic Supabase auth implementation. The new system addresses critical UX issues, completes the onboarding flow with warehouse creation, and provides a branded, guided experience that aligns with Quildora's inventory management value proposition.

### Key Objectives

- Replace generic Supabase UI with branded, purpose-built authentication interfaces
- Complete the onboarding flow to include warehouse creation and user role assignment
- Eliminate post-registration permission walls and UI blocking
- Support both new tenant creation and existing tenant joining workflows
- Maintain compatibility with existing warehouse-scoped access control architecture

## Current State Analysis

### Authentication UX Issues

1. **Generic Interface**: Supabase's default Auth UI lacks Quildora branding and context
2. **Confusing Flow**: Identical sign-in/sign-up interfaces create user confusion
3. **Missing Value Proposition**: No communication of Quildora's inventory management benefits
4. **Mobile/Tablet Gaps**: Limited optimization for warehouse environments

### Incomplete Onboarding Flow

1. **Partial Setup**: Current flow creates tenant but stops before warehouse creation
2. **Permission Walls**: Users hit warehouse context requirements immediately after registration
3. **UI Blocking**: Missing warehouse context prevents access to core functionality
4. **Limited Options**: Only supports new tenant creation, not joining existing tenants

### Post-Onboarding Experience

1. **No Guidance**: Users lack clear next steps after account creation
2. **Feature Discovery**: No introduction to Quildora's core inventory management features
3. **Context Missing**: Users don't understand warehouse-centric workflow

## Proposed Solution Architecture

### Authentication Flow Design

#### 1. Landing Page Experience

```
/auth/welcome
├── Value Proposition Section
│   ├── "Streamlined Inventory Management for SMBs"
│   ├── Key Benefits (Pallet Tracking, Multi-Warehouse, Real-time Updates)
│   └── Customer Success Stories/Testimonials
├── Action Selection
│   ├── "Start Free Trial" (New Business)
│   ├── "Join Existing Team" (Existing Tenant)
│   └── "Sign In" (Returning User)
└── Mobile/Tablet Optimized Layout
```

#### 2. Sign-Up Flow (New Business)

```
/auth/signup/business
├── Step 1: Business Information
│   ├── Company Name (Required)
│   ├── Industry Selection (Optional)
│   ├── Company Size (Optional)
│   └── Primary Use Case (Optional)
├── Step 2: Admin Account Creation
│   ├── Full Name (Required)
│   ├── Email Address (Required)
│   ├── Password (Required)
│   └── Phone Number (Optional)
├── Step 3: Initial Warehouse Setup
│   ├── Warehouse Name (Required)
│   ├── Address (Optional)
│   ├── Warehouse Type (Distribution, Manufacturing, Retail, etc.)
│   └── Expected Volume (Small, Medium, Large)
└── Step 4: Confirmation & Welcome
```

#### 3. Sign-Up Flow (Join Existing Team)

```
/auth/signup/join
├── Step 1: Invitation Validation
│   ├── Invitation Code/Link Input
│   ├── Company Verification Display
│   └── Role Assignment Preview
├── Step 2: Account Creation
│   ├── Full Name (Required)
│   ├── Email Address (Pre-filled if invited)
│   ├── Password (Required)
│   └── Phone Number (Optional)
├── Step 3: Warehouse Assignment
│   ├── Available Warehouses Display
│   ├── Role Assignment (Based on Invitation)
│   └── Access Level Explanation
└── Step 4: Welcome & Orientation
```

#### 4. Sign-In Flow (Returning Users)

```
/auth/signin
├── Branded Login Interface
│   ├── Email Address
│   ├── Password
│   ├── "Remember Me" Option
│   └── "Forgot Password" Link
├── Quick Access Features
│   ├── Recent Warehouse Context
│   ├── Last Login Information
│   └── System Status Indicators
└── Mobile/Tablet Optimization
```

### Complete Onboarding Architecture

#### 1. Multi-Step Onboarding Process

```typescript
interface OnboardingState {
  step:
    | "business_info"
    | "admin_account"
    | "warehouse_setup"
    | "team_setup"
    | "completion";
  businessInfo: {
    companyName: string;
    industry?: string;
    companySize?: string;
    primaryUseCase?: string;
  };
  adminAccount: {
    fullName: string;
    email: string;
    password: string;
    phoneNumber?: string;
  };
  warehouseSetup: {
    warehouseName: string;
    address?: string;
    warehouseType?: string;
    expectedVolume?: string;
  };
  teamSetup?: {
    inviteEmails: string[];
    defaultRole: Role;
  };
}
```

#### 2. Database Schema Enhancements

```sql
-- Onboarding tracking table
CREATE TABLE onboarding_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  tenant_id UUID REFERENCES tenants(id),
  current_step VARCHAR(50) NOT NULL,
  data JSONB NOT NULL DEFAULT '{}',
  completed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Invitation system
CREATE TABLE tenant_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id),
  invited_by_user_id UUID REFERENCES users(id),
  email VARCHAR(255) NOT NULL,
  role VARCHAR(50) NOT NULL,
  warehouse_ids UUID[] DEFAULT '{}',
  invitation_code VARCHAR(100) UNIQUE NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  accepted_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### 3. API Endpoint Specifications

##### Onboarding Endpoints

```typescript
// Start new business onboarding
POST /api/onboarding/business/start
Body: { businessInfo: BusinessInfo }
Response: { sessionId: string, nextStep: string }

// Complete admin account creation
POST /api/onboarding/business/admin-account
Body: { sessionId: string, adminAccount: AdminAccount }
Response: { userId: string, supabaseUser: User, nextStep: string }

// Setup initial warehouse
POST /api/onboarding/business/warehouse
Body: { sessionId: string, warehouseSetup: WarehouseSetup }
Response: { warehouseId: string, tenantId: string, nextStep: string }

// Complete onboarding
POST /api/onboarding/business/complete
Body: { sessionId: string, teamSetup?: TeamSetup }
Response: { accessToken: string, user: User, warehouse: Warehouse }
```

##### Invitation Endpoints

```typescript
// Create invitation
POST /api/invitations/create
Body: { email: string, role: Role, warehouseIds: string[] }
Response: { invitationId: string, invitationCode: string }

// Validate invitation
GET /api/invitations/validate/:code
Response: { valid: boolean, tenantInfo: TenantInfo, role: Role }

// Accept invitation
POST /api/invitations/accept/:code
Body: { userAccount: UserAccount }
Response: { accessToken: string, user: User, warehouses: Warehouse[] }
```

### Technical Integration Specifications

#### 1. Frontend State Management

```typescript
// Onboarding Context Provider
interface OnboardingContextType {
  currentStep: OnboardingStep;
  sessionData: OnboardingState;
  isLoading: boolean;
  error: string | null;

  // Actions
  updateBusinessInfo: (info: BusinessInfo) => Promise<void>;
  createAdminAccount: (account: AdminAccount) => Promise<void>;
  setupWarehouse: (warehouse: WarehouseSetup) => Promise<void>;
  completeOnboarding: (team?: TeamSetup) => Promise<void>;

  // Navigation
  goToStep: (step: OnboardingStep) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
}
```

#### 2. Authentication State Integration

```typescript
// Enhanced Auth Provider
interface AuthContextType {
  // Existing properties
  supabase: SupabaseClient;
  supabaseSession: Session | null;
  supabaseUser: User | null;
  appUser: FrontendAppUser | null;
  appToken: string | null;
  isLoading: boolean;
  onboardingStatus: string | null;

  // New onboarding properties
  isOnboarding: boolean;
  onboardingStep: OnboardingStep | null;
  onboardingData: OnboardingState | null;

  // Enhanced methods
  startBusinessOnboarding: (businessInfo: BusinessInfo) => Promise<void>;
  joinTenantWithInvitation: (invitationCode: string) => Promise<void>;
  completeOnboarding: () => Promise<void>;

  // Existing methods
  loginWithSupabaseToken: (token: string) => Promise<void>;
  logout: () => Promise<void>;
}
```

#### 3. Route Guard Enhancements

```typescript
// Enhanced route protection
interface RouteRequirements {
  requiresAuth: boolean;
  requiresWarehouse: boolean;
  requiredRole: Role | null;
  allowDuringOnboarding: boolean; // New property
  onboardingStepsAllowed?: OnboardingStep[]; // New property
}

// Updated route configurations
const ROUTE_CONFIGS = {
  publicRoutes: ["/auth/welcome", "/auth/signin", "/auth/signup/*"],
  onboardingRoutes: ["/onboarding/*"],
  warehouseDependent: ["/pallets", "/locations", "/receiving", "/picking"],
  adminRoutes: ["/settings/company", "/settings/users"],
  managerRoutes: ["/settings/warehouses", "/reports"],
};
```

### User Experience Design

#### 1. Progressive Disclosure Strategy

- **Step 1**: Focus on business value and core problem solving
- **Step 2**: Collect essential information only (avoid form fatigue)
- **Step 3**: Provide immediate value with first warehouse setup
- **Step 4**: Introduce advanced features gradually

#### 2. Success Indicators & Progress Tracking

```typescript
interface OnboardingProgress {
  totalSteps: number;
  currentStep: number;
  completedSteps: string[];
  estimatedTimeRemaining: number;

  // Visual indicators
  progressPercentage: number;
  stepTitles: string[];
  stepDescriptions: string[];
}
```

#### 3. Celebratory Completion Experience

- **Confetti Animation**: 2-3 second celebration on completion
- **Welcome Dashboard**: Customized first-time user experience
- **Guided Tour**: Optional walkthrough of key features
- **Quick Wins**: Immediate actions users can take (create first pallet, add location)

#### 4. Mobile/Tablet Optimization

- **Large Touch Targets**: Minimum 44px touch targets for warehouse environments
- **Simplified Navigation**: Linear flow with clear back/next buttons
- **Offline Capability**: Cache onboarding progress for poor connectivity
- **Accessibility**: Screen reader support and high contrast options

### Implementation Roadmap

#### Phase 1: Foundation (Week 1-2) - Detailed Task Breakdown

##### 1. Auth Page Layouts & Components (Days 1-3)

**Task 1.1: Create Welcome Landing Page (20 min)**

- **File**: `apps/frontend/src/app/auth/welcome/page.tsx`
- **Dependencies**: shadcn/ui components (Button, Card, Badge)
- **Description**: Replace generic auth page with branded welcome experience
- **Deliverables**:
  ```typescript
  // Component structure with value proposition, action buttons
  interface WelcomePageProps {
    onStartTrial: () => void;
    onJoinTeam: () => void;
    onSignIn: () => void;
  }
  ```
- **Testing**: Unit tests for button interactions and responsive layout

**Task 1.2: Create Sign-Up Business Flow Layout (20 min)**

- **File**: `apps/frontend/src/app/auth/signup/business/page.tsx`
- **Dependencies**: shadcn/ui Form components, React Hook Form
- **Description**: Multi-step business registration layout
- **Integration**: Uses existing `useAuth` hook for Supabase integration
- **Testing**: Form validation and step navigation tests

**Task 1.3: Create Sign-In Component (20 min)**

- **File**: `apps/frontend/src/app/auth/signin/page.tsx`
- **Dependencies**: shadcn/ui Input, Button, Form components
- **Description**: Branded sign-in form replacing Supabase Auth UI
- **Integration**: Integrates with existing `loginWithSupabaseToken` method
- **Testing**: Authentication flow and error handling tests

**Task 1.4: Create Join Team Flow Layout (20 min)**

- **File**: `apps/frontend/src/app/auth/signup/join/page.tsx`
- **Dependencies**: shadcn/ui components, invitation validation
- **Description**: Team invitation acceptance flow
- **Integration**: New invitation validation API endpoints
- **Testing**: Invitation code validation and error states

**Task 1.5: Mobile/Tablet Optimization (20 min)**

- **Files**: Update all auth components with mobile-first CSS
- **Dependencies**: Tailwind CSS responsive utilities
- **Description**: Ensure 44px touch targets, proper spacing for warehouse environments
- **Testing**: Responsive design tests across device sizes

**Task 1.6: Auth Route Configuration (20 min)**

- **File**: `apps/frontend/src/app/auth/layout.tsx`
- **Dependencies**: Existing route guard system
- **Description**: Configure routing for new auth flows
- **Integration**: Update `WarehouseRouteGuard` to handle new auth routes
- **Testing**: Route protection and navigation tests

##### 2. Onboarding State Management (Days 4-5)

**Task 2.1: Create Onboarding Context Provider (20 min)**

- **File**: `apps/frontend/src/components/providers/onboarding-provider.tsx`
- **Dependencies**: React Context, existing auth provider
- **Description**: State management for multi-step onboarding
- **Deliverables**:
  ```typescript
  interface OnboardingContextType {
    currentStep: OnboardingStep;
    sessionData: OnboardingState;
    isLoading: boolean;
    error: string | null;
    updateBusinessInfo: (info: BusinessInfo) => Promise<void>;
    createAdminAccount: (account: AdminAccount) => Promise<void>;
    setupWarehouse: (warehouse: WarehouseSetup) => Promise<void>;
    completeOnboarding: () => Promise<void>;
  }
  ```
- **Testing**: Context state management and persistence tests

**Task 2.2: Create Onboarding Progress Hook (20 min)**

- **File**: `apps/frontend/src/hooks/useOnboardingProgress.ts`
- **Dependencies**: Headless Stepper library
- **Description**: Hook for step navigation and validation
- **Integration**: Works with onboarding context
- **Testing**: Step progression and validation logic tests

**Task 2.3: Create Form Validation Hook (20 min)**

- **File**: `apps/frontend/src/hooks/useOnboardingValidation.ts`
- **Dependencies**: React Hook Form, Zod
- **Description**: Multi-step form validation with schemas
- **Integration**: Integrates with existing form patterns
- **Testing**: Schema validation and error handling tests

**Task 2.4: Create Error Recovery Service (20 min)**

- **File**: `apps/frontend/src/lib/onboarding-error-recovery.ts`
- **Dependencies**: Local storage, retry logic
- **Description**: Handle network failures and state recovery
- **Integration**: Works with onboarding context
- **Testing**: Error scenarios and recovery mechanism tests

**Task 2.5: Update Auth Provider Integration (20 min)**

- **File**: `apps/frontend/src/components/providers/auth-provider.tsx`
- **Dependencies**: Existing auth provider
- **Description**: Add onboarding state to auth context
- **Integration**: Extend existing `AuthContextType` interface
- **Testing**: Auth provider integration tests

**Task 2.6: Create Onboarding Type Definitions (20 min)**

- **File**: `packages/types/src/onboarding.ts`
- **Dependencies**: Existing type definitions
- **Description**: TypeScript interfaces for onboarding flow
- **Deliverables**:
  ```typescript
  export interface OnboardingState {
    step: OnboardingStep;
    businessInfo?: BusinessInfo;
    adminAccount?: AdminAccount;
    warehouseSetup?: WarehouseSetup;
    sessionId?: string;
  }
  ```
- **Testing**: Type safety validation

##### 3. Database Schema (Days 6-7)

**Task 3.1: Create Onboarding Sessions Migration (20 min)**

- **File**: `apps/backend/prisma/migrations/xxx_add_onboarding_sessions.sql`
- **Dependencies**: Existing Prisma schema
- **Description**: ADDITIVE-ONLY migration for onboarding tracking
- **Schema**:

  ```sql
  -- SAFE MIGRATION: Only adds new table, no existing data changes
  CREATE TABLE onboarding_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    current_step VARCHAR(50) NOT NULL DEFAULT 'business_info',
    data JSONB NOT NULL DEFAULT '{}',
    completed_at TIMESTAMP NULL,
    expires_at TIMESTAMP NOT NULL DEFAULT (NOW() + INTERVAL '24 hours'),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
  );

  CREATE INDEX idx_onboarding_sessions_user_id ON onboarding_sessions(user_id);
  CREATE INDEX idx_onboarding_sessions_expires_at ON onboarding_sessions(expires_at);
  ```

- **Testing**: Migration rollback safety, data integrity tests

**Task 3.2: Create Tenant Invitations Migration (20 min)**

- **File**: `apps/backend/prisma/migrations/xxx_add_tenant_invitations.sql`
- **Dependencies**: Existing tenant and user tables
- **Description**: ADDITIVE-ONLY migration for invitation system
- **Schema**:

  ```sql
  -- SAFE MIGRATION: Only adds new table, preserves all existing data
  CREATE TABLE tenant_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    invited_by_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'WAREHOUSE_MEMBER',
    warehouse_ids UUID[] DEFAULT '{}',
    invitation_code VARCHAR(100) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    accepted_at TIMESTAMP NULL,
    accepted_by_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
  );

  CREATE INDEX idx_tenant_invitations_code ON tenant_invitations(invitation_code);
  CREATE INDEX idx_tenant_invitations_email ON tenant_invitations(email);
  CREATE INDEX idx_tenant_invitations_tenant_id ON tenant_invitations(tenant_id);
  ```

- **Testing**: Foreign key constraints, invitation code uniqueness tests

**Task 3.3: Update Prisma Schema (20 min)**

- **File**: `apps/backend/prisma/schema.prisma`
- **Dependencies**: Existing schema models
- **Description**: Add new models to Prisma schema
- **Integration**: Maintains all existing relationships
- **Deliverables**:

  ```prisma
  model OnboardingSession {
    id          String    @id @default(cuid())
    userId      String?   @map("user_id")
    tenantId    String?   @map("tenant_id")
    currentStep String    @default("business_info") @map("current_step")
    data        Json      @default("{}")
    completedAt DateTime? @map("completed_at")
    expiresAt   DateTime  @map("expires_at")
    createdAt   DateTime  @default(now()) @map("created_at")
    updatedAt   DateTime  @updatedAt @map("updated_at")

    user   User?   @relation(fields: [userId], references: [id], onDelete: Cascade)
    tenant Tenant? @relation(fields: [tenantId], references: [id], onDelete: Cascade)

    @@map("onboarding_sessions")
  }

  model TenantInvitation {
    id               String    @id @default(cuid())
    tenantId         String    @map("tenant_id")
    invitedByUserId  String    @map("invited_by_user_id")
    email            String
    role             Role      @default(WAREHOUSE_MEMBER)
    warehouseIds     String[]  @default([]) @map("warehouse_ids")
    invitationCode   String    @unique @map("invitation_code")
    expiresAt        DateTime  @map("expires_at")
    acceptedAt       DateTime? @map("accepted_at")
    acceptedByUserId String?   @map("accepted_by_user_id")
    createdAt        DateTime  @default(now()) @map("created_at")
    updatedAt        DateTime  @updatedAt @map("updated_at")

    tenant         Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
    invitedBy      User   @relation("InvitedBy", fields: [invitedByUserId], references: [id], onDelete: Cascade)
    acceptedByUser User?  @relation("AcceptedBy", fields: [acceptedByUserId], references: [id], onDelete: SetNull)

    @@map("tenant_invitations")
  }
  ```

- **Testing**: Schema validation and relationship integrity

**Task 3.4: Generate Prisma Client (20 min)**

- **Command**: `npx prisma generate`
- **Dependencies**: Updated schema
- **Description**: Regenerate Prisma client with new models
- **Integration**: Ensure existing queries still work
- **Testing**: Client generation and type safety validation

**Task 3.5: Create Database Seeding for Development (20 min)**

- **File**: `apps/backend/prisma/seed-onboarding.ts`
- **Dependencies**: Existing seed data
- **Description**: Add sample onboarding data for development
- **Integration**: Extends existing seed script
- **Testing**: Seed data creation and cleanup

**Task 3.6: Add Cleanup Job for Expired Sessions (20 min)**

- **File**: `apps/backend/src/onboarding/cleanup.service.ts`
- **Dependencies**: Prisma service, cron job setup
- **Description**: Periodic cleanup of expired onboarding sessions
- **Integration**: Uses existing service patterns
- **Testing**: Cleanup logic and scheduling tests

##### 4. API Endpoints (Days 8-10)

**Task 4.1: Create Onboarding Module Structure (20 min)**

- **Files**:
  - `apps/backend/src/onboarding/onboarding.module.ts`
  - `apps/backend/src/onboarding/onboarding.controller.ts`
  - `apps/backend/src/onboarding/onboarding.service.ts`
- **Dependencies**: NestJS modules, existing auth module
- **Description**: Set up module structure for onboarding endpoints
- **Integration**: Imports existing PrismaModule and AuthModule
- **Testing**: Module loading and dependency injection tests

**Task 4.2: Create Business Onboarding Endpoints (20 min)**

- **File**: `apps/backend/src/onboarding/onboarding.controller.ts`
- **Dependencies**: JWT auth guard, validation pipes
- **Description**: Endpoints for business onboarding flow
- **Endpoints**:
  ```typescript
  POST / api / onboarding / business / start;
  POST / api / onboarding / business / admin - account;
  POST / api / onboarding / business / warehouse;
  POST / api / onboarding / business / complete;
  ```
- **Integration**: Uses existing authentication middleware
- **Testing**: Endpoint validation and response format tests

**Task 4.3: Create Invitation Management Endpoints (20 min)**

- **File**: `apps/backend/src/onboarding/invitations.controller.ts`
- **Dependencies**: Tenant admin guards, email service
- **Description**: Invitation creation and validation endpoints
- **Endpoints**:
  ```typescript
  POST /api/invitations/create
  GET /api/invitations/validate/:code
  POST /api/invitations/accept/:code
  GET /api/invitations/list
  ```
- **Integration**: Uses existing role-based guards
- **Testing**: Invitation lifecycle and security tests

**Task 4.4: Create Onboarding DTOs (20 min)**

- **File**: `apps/backend/src/onboarding/dto/`
- **Dependencies**: Class-validator, class-transformer
- **Description**: Request/response DTOs for onboarding endpoints
- **Deliverables**:

  ```typescript
  export class StartBusinessOnboardingDto {
    @IsString()
    @IsNotEmpty()
    companyName: string;

    @IsString()
    @IsOptional()
    industry?: string;
  }

  export class CreateAdminAccountDto {
    @IsString()
    @IsNotEmpty()
    fullName: string;

    @IsEmail()
    email: string;

    @IsString()
    @MinLength(8)
    password: string;

    @IsString()
    @IsOptional()
    phoneNumber?: string;
  }

  export class SetupWarehouseDto {
    @IsString()
    @IsNotEmpty()
    warehouseName: string;

    @IsString()
    @IsOptional()
    address?: string;

    @IsString()
    @IsOptional()
    warehouseType?: string;
  }
  ```

- **Testing**: DTO validation and transformation tests

**Task 4.5: Implement Onboarding Service Logic (20 min)**

- **File**: `apps/backend/src/onboarding/onboarding.service.ts`
- **Dependencies**: Prisma service, existing warehouse service
- **Description**: Business logic for onboarding flow management
- **Integration**: Uses existing warehouse creation service
- **Methods**:
  ```typescript
  async startBusinessOnboarding(data: StartBusinessOnboardingDto): Promise<OnboardingSessionResponse>
  async createAdminAccount(sessionId: string, data: CreateAdminAccountDto): Promise<UserResponse>
  async setupWarehouse(sessionId: string, data: SetupWarehouseDto): Promise<WarehouseResponse>
  async completeOnboarding(sessionId: string): Promise<CompleteOnboardingResponse>
  ```
- **Testing**: Service method unit tests and integration tests

**Task 4.6: Create Session Management Service (20 min)**

- **File**: `apps/backend/src/onboarding/session.service.ts`
- **Dependencies**: Prisma service, caching service
- **Description**: Onboarding session lifecycle management
- **Integration**: Works with existing auth session patterns
- **Methods**:
  ```typescript
  async createSession(data: Partial<OnboardingState>): Promise<OnboardingSession>
  async updateSession(sessionId: string, data: Partial<OnboardingState>): Promise<OnboardingSession>
  async getSession(sessionId: string): Promise<OnboardingSession | null>
  async completeSession(sessionId: string): Promise<void>
  async cleanupExpiredSessions(): Promise<number>
  ```
- **Testing**: Session creation, validation, and cleanup tests

**Task 4.7: Update Auth Service Integration (20 min)**

- **File**: `apps/backend/src/auth/auth.service.ts`
- **Dependencies**: Existing auth service, onboarding service
- **Description**: Integrate onboarding status with auth flow
- **Integration**: Extends existing login method to check onboarding status
- **Changes**:
  ```typescript
  // Add to existing login method
  const onboardingStatus = user.tenantId
    ? "complete"
    : await this.checkActiveOnboardingSession(user.id);
  ```
- **Testing**: Auth flow integration and onboarding status tests

**Task 4.8: Create API Documentation (20 min)**

- **File**: `apps/backend/src/onboarding/onboarding.controller.ts`
- **Dependencies**: Swagger/OpenAPI decorators
- **Description**: API documentation for onboarding endpoints
- **Integration**: Follows existing API documentation patterns
- **Testing**: Documentation generation and accuracy validation

##### 5. Integration & Testing Tasks (Days 9-10)

**Task 5.1: Create Integration Test Suite (20 min)**

- **File**: `apps/backend/test/onboarding/onboarding.e2e-spec.ts`
- **Dependencies**: Jest, Supertest, test database
- **Description**: End-to-end tests for complete onboarding flow
- **Test Scenarios**:
  - Complete business onboarding flow
  - Invitation creation and acceptance
  - Error handling and validation
  - Session management and cleanup
- **Testing**: Full user journey from signup to warehouse creation

**Task 5.2: Update Route Guard Tests (20 min)**

- **File**: `apps/frontend/src/components/guards/__tests__/`
- **Dependencies**: Existing route guard tests
- **Description**: Update tests for new onboarding routes
- **Integration**: Test onboarding route protection
- **Testing**: Route protection during onboarding flow

**Task 5.3: Create Frontend Integration Tests (20 min)**

- **File**: `apps/frontend/src/__tests__/onboarding/`
- **Dependencies**: React Testing Library, MSW
- **Description**: Component integration tests for onboarding flow
- **Test Coverage**:
  - Onboarding context provider
  - Multi-step form navigation
  - Error handling and recovery
  - Mobile responsive behavior
- **Testing**: User interactions and state management

**Task 5.4: Database Migration Testing (20 min)**

- **File**: `apps/backend/test/migrations/`
- **Dependencies**: Test database, migration scripts
- **Description**: Verify migrations are safe and reversible
- **Test Scenarios**:
  - Migration applies without errors
  - Rollback preserves existing data
  - Foreign key constraints work correctly
  - Indexes are created properly
- **Testing**: Migration rollback and data preservation

**Task 5.5: Performance Testing Setup (20 min)**

- **File**: `apps/backend/test/performance/onboarding.perf-spec.ts`
- **Dependencies**: Performance testing tools
- **Description**: Baseline performance tests for onboarding endpoints
- **Metrics**:
  - Response times under 500ms
  - Concurrent user handling (10+ simultaneous onboardings)
  - Database query optimization
  - Memory usage during onboarding
- **Testing**: Response times and concurrent user handling

**Task 5.6: Security Testing (20 min)**

- **File**: `apps/backend/test/security/onboarding.security-spec.ts`
- **Dependencies**: Security testing tools
- **Description**: Validate authentication and authorization
- **Security Checks**:
  - JWT validation on all endpoints
  - Role-based access control
  - Input sanitization and validation
  - Session hijacking prevention
  - Invitation code security
- **Testing**: JWT validation, role-based access, input sanitization

#### Phase 2: Core Onboarding (Week 3-4)

- [ ] Build business information collection flow
- [ ] Implement admin account creation with Supabase integration
- [ ] Create warehouse setup process
- [ ] Integrate with existing warehouse creation service

#### Phase 3: Team Collaboration (Week 5-6)

- [ ] Implement invitation system
- [ ] Build join existing tenant flow
- [ ] Create team member onboarding process
- [ ] Add role assignment and warehouse access management

#### Phase 4: Enhancement & Polish (Week 7-8)

- [ ] Add progress tracking and success indicators
- [ ] Implement celebratory completion experience
- [ ] Optimize for mobile/tablet environments
- [ ] Add comprehensive error handling and recovery


### Success Metrics & Validation Criteria

#### Quantitative Metrics

- **Onboarding Completion Rate**: Target >85% (vs current ~40%)
- **Time to First Value**: Target <10 minutes from signup to first pallet creation
- **User Activation Rate**: Target >70% of users creating first pallet within 24 hours
- **Support Ticket Reduction**: Target 50% reduction in onboarding-related tickets

#### Qualitative Metrics

- **User Satisfaction**: Post-onboarding survey scores >4.5/5
- **Feature Discovery**: Users understand core features before first use
- **Brand Perception**: Users associate Quildora with professional inventory management

### Risk Assessment & Mitigation

#### Technical Risks

- **Supabase Integration Complexity**: Mitigation through thorough testing and fallback mechanisms
- **Database Migration Challenges**: Mitigation through careful schema planning and rollback procedures
- **Performance Impact**: Mitigation through caching and optimization strategies

#### User Experience Risks

- **Onboarding Friction**: Mitigation through user testing and iterative refinement
- **Feature Overwhelm**: Mitigation through progressive disclosure and guided tours
- **Mobile Usability**: Mitigation through dedicated mobile testing and optimization

### Component Architecture & Design Patterns

#### 1. Reusable UI Components

```typescript
// Onboarding Layout Component
interface OnboardingLayoutProps {
  currentStep: number;
  totalSteps: number;
  title: string;
  subtitle?: string;
  children: ReactNode;
  onBack?: () => void;
  onNext?: () => void;
  nextDisabled?: boolean;
  showProgress?: boolean;
}

// Step Container Component
interface StepContainerProps {
  stepNumber: number;
  title: string;
  description: string;
  children: ReactNode;
  isActive: boolean;
  isCompleted: boolean;
  isAccessible: boolean;
}

// Form Field Components
interface OnboardingInputProps extends InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  helpText?: string;
  required?: boolean;
  icon?: ReactNode;
}
```

#### 2. Custom Hooks for Onboarding Logic

```typescript
// Onboarding Progress Hook
function useOnboardingProgress() {
  const [currentStep, setCurrentStep] =
    useState<OnboardingStep>("business_info");
  const [completedSteps, setCompletedSteps] = useState<OnboardingStep[]>([]);
  const [sessionData, setSessionData] = useState<OnboardingState>({});

  const goToStep = useCallback((step: OnboardingStep) => {
    // Validation logic
    setCurrentStep(step);
  }, []);

  const markStepComplete = useCallback((step: OnboardingStep) => {
    setCompletedSteps((prev) => [...prev, step]);
  }, []);

  return {
    currentStep,
    completedSteps,
    sessionData,
    goToStep,
    markStepComplete,
    isStepAccessible: (step: OnboardingStep) => boolean,
    getStepProgress: () => number,
  };
}

// Form Validation Hook
function useOnboardingValidation(step: OnboardingStep) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isValid, setIsValid] = useState(false);

  const validateField = useCallback(
    (field: string, value: any) => {
      // Step-specific validation logic
      const fieldErrors = validateFieldByStep(step, field, value);
      setErrors((prev) => ({ ...prev, [field]: fieldErrors }));
    },
    [step]
  );

  const validateStep = useCallback(
    (data: any) => {
      const stepErrors = validateStepData(step, data);
      setErrors(stepErrors);
      setIsValid(Object.keys(stepErrors).length === 0);
      return Object.keys(stepErrors).length === 0;
    },
    [step]
  );

  return { errors, isValid, validateField, validateStep };
}
```

#### 3. Error Handling & Recovery Mechanisms

```typescript
// Error Recovery Service
class OnboardingErrorRecovery {
  private static readonly RETRY_ATTEMPTS = 3;
  private static readonly RETRY_DELAY = 1000;

  static async withRetry<T>(
    operation: () => Promise<T>,
    context: string
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= this.RETRY_ATTEMPTS; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        if (attempt < this.RETRY_ATTEMPTS) {
          await this.delay(this.RETRY_DELAY * attempt);
          continue;
        }

        // Log error for monitoring
        this.logError(context, error, attempt);
        throw error;
      }
    }

    throw lastError!;
  }

  static async saveProgressToLocalStorage(data: OnboardingState) {
    try {
      localStorage.setItem("quildora_onboarding_backup", JSON.stringify(data));
    } catch (error) {
      console.warn(
        "Failed to save onboarding progress to localStorage:",
        error
      );
    }
  }

  static loadProgressFromLocalStorage(): OnboardingState | null {
    try {
      const saved = localStorage.getItem("quildora_onboarding_backup");
      return saved ? JSON.parse(saved) : null;
    } catch (error) {
      console.warn(
        "Failed to load onboarding progress from localStorage:",
        error
      );
      return null;
    }
  }
}
```

## Recommended Component Libraries

### Overview

Based on research into modern React component libraries that complement shadcn/ui, the following libraries are recommended for implementing Quildora's authentication and onboarding system. Each library has been evaluated for compatibility with shadcn/ui, mobile/tablet optimization, TypeScript support, and specific features needed for multi-step authentication flows.

### 1. Mantine UI (Recommended for Stepper Components)

**Description**: A comprehensive React components library with excellent TypeScript support and built-in accessibility features.

**Key Components for Auth/Onboarding**:

- `Stepper` - Full-featured stepper with vertical/horizontal orientation
- `Progress` - Progress indicators and loading states
- `TextInput`, `PasswordInput` - Enhanced form inputs with validation
- `Button`, `Group` - Consistent button styling and layouts
- `Modal`, `Drawer` - Overlay components for mobile optimization

**Installation**:

```bash
pnpm install @mantine/core @mantine/hooks @mantine/form
```

**Basic Usage Example**:

```typescript
import { Stepper, Button, Group, TextInput } from "@mantine/core";
import { useForm } from "@mantine/form";

function OnboardingStepper() {
  const [active, setActive] = useState(0);
  const form = useForm({
    initialValues: {
      companyName: "",
      email: "",
      password: "",
    },
  });

  return (
    <Stepper
      active={active}
      onStepClick={setActive}
      allowNextStepsSelect={false}
    >
      <Stepper.Step label="Business Info" description="Company details">
        <TextInput
          label="Company Name"
          {...form.getInputProps("companyName")}
          required
        />
      </Stepper.Step>

      <Stepper.Step label="Admin Account" description="Create your account">
        <TextInput
          label="Email"
          type="email"
          {...form.getInputProps("email")}
          required
        />
        <PasswordInput
          label="Password"
          {...form.getInputProps("password")}
          required
        />
      </Stepper.Step>

      <Stepper.Completed>
        Welcome to Quildora! Your account is ready.
      </Stepper.Completed>
    </Stepper>
  );
}
```

**Pros**:

- Excellent stepper component with built-in step validation
- Mobile-responsive by default
- Strong TypeScript support and accessibility
- Comprehensive form handling with `@mantine/form`
- Active maintenance and large community
- Easy theming and customization

**Cons**:

- Additional bundle size (though tree-shakeable)
- Different design system from shadcn/ui (requires custom styling)
- Learning curve for Mantine-specific patterns

**Integration with shadcn/ui**:

```typescript
// Custom wrapper to match shadcn/ui styling
import { Stepper as MantineStepper } from "@mantine/core";
import { cn } from "@/lib/utils";

interface QuildoraStepperProps {
  className?: string;
  children: React.ReactNode;
  active: number;
  onStepClick?: (step: number) => void;
}

export function QuildoraStepper({
  className,
  children,
  active,
  onStepClick,
}: QuildoraStepperProps) {
  return (
    <MantineStepper
      active={active}
      onStepClick={onStepClick}
      allowNextStepsSelect={false}
      className={cn("w-full", className)}
      styles={{
        step: {
          "&[data-completed]": {
            backgroundColor: "hsl(var(--primary))",
          },
          "&[data-progress]": {
            backgroundColor: "hsl(var(--primary))",
          },
        },
        stepLabel: {
          fontSize: "0.875rem",
          fontWeight: 500,
        },
      }}
    >
      {children}
    </MantineStepper>
  );
}
```

### 2. Headless Stepper (Recommended for Custom Implementation)

**Description**: A lightweight, headless React hook for building custom stepper components with full control over styling.

**Key Features**:

- Zero dependencies and minimal bundle size
- 100% TypeScript support
- Complete styling control
- Perfect for shadcn/ui integration

**Installation**:

```bash
npm install headless-stepper
```

**Basic Usage Example**:

```typescript
import { useStepper } from "headless-stepper";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";

function CustomOnboardingStepper() {
  const steps = [
    { label: "Business Info" },
    { label: "Admin Account" },
    { label: "Warehouse Setup" },
  ];

  const { state, nextStep, prevStep, progressProps, stepsProps } = useStepper({
    steps,
  });

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* Custom step indicators using shadcn/ui */}
      <div className="flex items-center justify-between mb-8">
        {stepsProps?.map((step, index) => (
          <div
            key={index}
            className={cn(
              "flex items-center justify-center w-10 h-10 rounded-full border-2 text-sm font-medium",
              index <= state.currentStep
                ? "bg-primary text-primary-foreground border-primary"
                : "bg-background text-muted-foreground border-border"
            )}
            {...step}
          >
            {index + 1}
          </div>
        ))}
      </div>

      {/* Progress bar */}
      <Progress
        value={(state.currentStep / (steps.length - 1)) * 100}
        className="mb-8"
      />

      {/* Step content */}
      <div className="min-h-[400px] mb-8">
        {state.currentStep === 0 && <BusinessInfoStep />}
        {state.currentStep === 1 && <AdminAccountStep />}
        {state.currentStep === 2 && <WarehouseSetupStep />}
      </div>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={prevStep}
          disabled={!state.hasPreviousStep}
        >
          Back
        </Button>
        <Button onClick={nextStep}>
          {state.hasNextStep ? "Next" : "Complete"}
        </Button>
      </div>
    </div>
  );
}
```

**Pros**:

- Perfect integration with shadcn/ui (no styling conflicts)
- Minimal bundle size impact
- Complete control over UI and UX
- Excellent TypeScript support
- Headless pattern allows for maximum customization

**Cons**:

- Requires more custom implementation
- No pre-built UI components
- Less comprehensive than full UI libraries

### 3. React Hook Form + Zod (Recommended for Form Validation)

**Description**: Industry-standard form handling with schema validation, perfect for multi-step forms.

**Key Features**:

- Excellent performance with minimal re-renders
- Built-in validation with Zod schemas
- Perfect shadcn/ui integration
- TypeScript-first approach

**Installation**:

```bash
npm install react-hook-form @hookform/resolvers zod
```

**Multi-Step Form Implementation**:

```typescript
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

// Step schemas
const businessInfoSchema = z.object({
  companyName: z.string().min(1, "Company name is required"),
  industry: z.string().optional(),
});

const adminAccountSchema = z.object({
  fullName: z.string().min(1, "Full name is required"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

const warehouseSetupSchema = z.object({
  warehouseName: z.string().min(1, "Warehouse name is required"),
  address: z.string().optional(),
});

// Combined schema for final validation
const onboardingSchema = businessInfoSchema
  .merge(adminAccountSchema)
  .merge(warehouseSetupSchema);

type OnboardingFormData = z.infer<typeof onboardingSchema>;

function useMultiStepForm() {
  const [currentStep, setCurrentStep] = useState(0);

  const form = useForm<OnboardingFormData>({
    resolver: zodResolver(onboardingSchema),
    mode: "onChange",
  });

  const validateStep = async (step: number) => {
    const schemas = [
      businessInfoSchema,
      adminAccountSchema,
      warehouseSetupSchema,
    ];
    const currentSchema = schemas[step];

    if (!currentSchema) return true;

    const stepData = form.getValues();
    const result = await currentSchema.safeParseAsync(stepData);

    if (!result.success) {
      result.error.errors.forEach((error) => {
        form.setError(error.path[0] as keyof OnboardingFormData, {
          message: error.message,
        });
      });
      return false;
    }

    return true;
  };

  const nextStep = async () => {
    const isValid = await validateStep(currentStep);
    if (isValid && currentStep < 2) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  return {
    form,
    currentStep,
    nextStep,
    prevStep,
    validateStep,
  };
}
```

**Pros**:

- Perfect shadcn/ui integration (used in shadcn/ui examples)
- Excellent performance and TypeScript support
- Comprehensive validation with Zod
- Industry standard with large community

**Cons**:

- Requires additional setup for multi-step validation
- Learning curve for complex validation scenarios

### 4. React Form Stepper (Alternative Stepper Option)

**Description**: A dedicated React stepper component specifically designed for multi-step forms with Material-UI inspired design.

**Key Features**:

- Built-in TypeScript declarations
- Customizable styling and themes
- Step validation and state management
- Mobile-responsive design

**Installation**:

```bash
npm install react-form-stepper
```

**Basic Usage Example**:

```typescript
import { Stepper, Step } from "react-form-stepper";
import { Button } from "@/components/ui/button";

function FormStepper() {
  const [activeStep, setActiveStep] = useState(0);

  const steps = [
    { label: "Business Information" },
    { label: "Admin Account" },
    { label: "Warehouse Setup" },
  ];

  return (
    <div className="w-full">
      <Stepper
        steps={steps}
        activeStep={activeStep}
        styleConfig={{
          activeBgColor: "hsl(var(--primary))",
          completedBgColor: "hsl(var(--primary))",
          inactiveBgColor: "hsl(var(--muted))",
          size: "2.5rem",
          circleFontSize: "1rem",
          labelFontSize: "0.875rem",
          borderRadius: "50%",
          fontWeight: 500,
        }}
        connectorStyleConfig={{
          activeColor: "hsl(var(--primary))",
          completedColor: "hsl(var(--primary))",
          disabledColor: "hsl(var(--border))",
          size: 2,
          style: "solid",
        }}
      />

      <div className="mt-8 min-h-[400px]">
        {/* Step content */}
        {activeStep === 0 && <BusinessInfoForm />}
        {activeStep === 1 && <AdminAccountForm />}
        {activeStep === 2 && <WarehouseSetupForm />}
      </div>

      <div className="flex justify-between mt-8">
        <Button
          variant="outline"
          onClick={() => setActiveStep(Math.max(0, activeStep - 1))}
          disabled={activeStep === 0}
        >
          Back
        </Button>
        <Button
          onClick={() =>
            setActiveStep(Math.min(steps.length - 1, activeStep + 1))
          }
          disabled={activeStep === steps.length - 1}
        >
          Next
        </Button>
      </div>
    </div>
  );
}
```

**Pros**:

- Dedicated stepper component with good customization
- TypeScript support out of the box
- Easy integration with existing form libraries
- Lightweight and focused on stepper functionality

**Cons**:

- Less active maintenance (last updated 3 years ago)
- Limited community and ecosystem
- Fewer features compared to comprehensive UI libraries
- May require additional work for advanced use cases

### 5. Radix UI Primitives (Recommended for Accessibility)

**Description**: Low-level UI primitives that power shadcn/ui, perfect for building custom components with maximum accessibility.

**Key Components for Auth/Onboarding**:

- `@radix-ui/react-progress` - Accessible progress indicators
- `@radix-ui/react-dialog` - Modal dialogs for mobile flows
- `@radix-ui/react-form` - Form primitives with validation
- `@radix-ui/react-tabs` - Tab-based navigation for steps

**Installation**:

```bash
npm install @radix-ui/react-progress @radix-ui/react-dialog @radix-ui/react-form
```

**Custom Stepper with Radix Primitives**:

```typescript
import * as Progress from "@radix-ui/react-progress";
import * as Dialog from "@radix-ui/react-dialog";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface StepperProps {
  steps: Array<{ label: string; description?: string }>;
  currentStep: number;
  onStepChange: (step: number) => void;
  children: React.ReactNode;
}

function RadixStepper({
  steps,
  currentStep,
  onStepChange,
  children,
}: StepperProps) {
  const progressValue = ((currentStep + 1) / steps.length) * 100;

  return (
    <div className="w-full">
      {/* Step indicators */}
      <div className="flex items-center justify-between mb-6">
        {steps.map((step, index) => (
          <button
            key={index}
            onClick={() => onStepChange(index)}
            className={cn(
              "flex flex-col items-center space-y-2 p-2 rounded-lg transition-colors",
              "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
              index <= currentStep
                ? "text-primary"
                : "text-muted-foreground hover:text-foreground"
            )}
            aria-current={index === currentStep ? "step" : undefined}
          >
            <div
              className={cn(
                "flex items-center justify-center w-8 h-8 rounded-full border-2 text-sm font-medium",
                index <= currentStep
                  ? "bg-primary text-primary-foreground border-primary"
                  : "bg-background border-border"
              )}
            >
              {index < currentStep ? (
                <CheckIcon className="w-4 h-4" />
              ) : (
                index + 1
              )}
            </div>
            <div className="text-xs font-medium text-center">{step.label}</div>
          </button>
        ))}
      </div>

      {/* Progress bar */}
      <Progress.Root
        className="relative overflow-hidden bg-secondary rounded-full w-full h-2 mb-8"
        value={progressValue}
      >
        <Progress.Indicator
          className="bg-primary w-full h-full transition-transform duration-300 ease-out"
          style={{ transform: `translateX(-${100 - progressValue}%)` }}
        />
      </Progress.Root>

      {/* Step content */}
      <div className="min-h-[400px]">{children}</div>
    </div>
  );
}
```

**Pros**:

- Perfect shadcn/ui compatibility (same foundation)
- Maximum accessibility compliance
- Complete styling control
- Excellent TypeScript support
- Active maintenance and community

**Cons**:

- Requires more implementation work
- Steeper learning curve for complex components
- No pre-built stepper component

### Recommended Implementation Strategy

Based on the research, here's the recommended approach for Quildora's authentication and onboarding system:

#### Primary Recommendation: Headless Stepper + React Hook Form + Zod

**Rationale**:

1. **Perfect shadcn/ui Integration**: No styling conflicts or design system mismatches
2. **Minimal Bundle Impact**: Lightweight libraries that don't bloat the application
3. **Maximum Customization**: Full control over UX for warehouse-specific optimizations
4. **Industry Standards**: React Hook Form and Zod are widely adopted and well-maintained
5. **TypeScript Excellence**: All libraries provide excellent TypeScript support

**Implementation Architecture**:

```typescript
// Combined implementation using recommended libraries
import { useStepper } from "headless-stepper";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

function QuildoraOnboardingFlow() {
  const steps = [
    { label: "Business Info", schema: businessInfoSchema },
    { label: "Admin Account", schema: adminAccountSchema },
    { label: "Warehouse Setup", schema: warehouseSetupSchema },
  ];

  const { state, nextStep, prevStep, stepsProps } = useStepper({ steps });

  const form = useForm<OnboardingFormData>({
    resolver: zodResolver(onboardingSchema),
    mode: "onChange",
  });

  const handleNext = async () => {
    const currentSchema = steps[state.currentStep].schema;
    const stepData = form.getValues();
    const result = await currentSchema.safeParseAsync(stepData);

    if (result.success) {
      if (state.hasNextStep) {
        nextStep();
      } else {
        // Submit final form
        await submitOnboarding(stepData);
      }
    } else {
      // Handle validation errors
      result.error.errors.forEach((error) => {
        form.setError(error.path[0] as keyof OnboardingFormData, {
          message: error.message,
        });
      });
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-6">
      {/* Custom stepper UI using shadcn/ui components */}
      <QuildoraStepperIndicator
        steps={steps}
        currentStep={state.currentStep}
        stepsProps={stepsProps}
      />

      <Progress
        value={((state.currentStep + 1) / steps.length) * 100}
        className="mb-8"
      />

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleNext)} className="space-y-6">
          {/* Dynamic step content */}
          {state.currentStep === 0 && <BusinessInfoStep form={form} />}
          {state.currentStep === 1 && <AdminAccountStep form={form} />}
          {state.currentStep === 2 && <WarehouseSetupStep form={form} />}

          <div className="flex justify-between pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={prevStep}
              disabled={!state.hasPreviousStep}
            >
              Back
            </Button>
            <Button type="submit">
              {state.hasNextStep ? "Next" : "Complete Setup"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
```

#### Alternative for Rapid Prototyping: Mantine UI

For faster initial development or prototyping, Mantine UI provides excellent out-of-the-box components that can be styled to match shadcn/ui design tokens. This approach trades some customization for development speed.

#### Mobile/Tablet Optimization Considerations

All recommended libraries support mobile optimization, but specific considerations for warehouse environments include:

1. **Touch Targets**: Ensure minimum 44px touch targets for all interactive elements
2. **Progress Indicators**: Use clear visual progress indicators for task completion
3. **Error Handling**: Provide clear, contextual error messages
4. **Offline Support**: Consider implementing offline form state persistence
5. **Performance**: Optimize for slower devices common in warehouse environments

### Security Considerations

#### 1. Authentication Security

- **Token Management**: Secure storage of JWT tokens with automatic refresh
- **Session Security**: Proper session invalidation and timeout handling
- **CSRF Protection**: Anti-CSRF tokens for state-changing operations
- **Rate Limiting**: Prevent brute force attacks on authentication endpoints

#### 2. Onboarding Data Protection

- **Data Encryption**: Encrypt sensitive onboarding data in transit and at rest
- **Input Validation**: Comprehensive server-side validation of all inputs
- **SQL Injection Prevention**: Parameterized queries and ORM usage
- **XSS Protection**: Proper output encoding and Content Security Policy

#### 3. Invitation System Security

- **Secure Codes**: Cryptographically secure invitation code generation
- **Expiration Handling**: Automatic cleanup of expired invitations
- **Single Use**: Ensure invitation codes can only be used once
- **Access Control**: Validate invitation permissions before account creation

### Performance Optimization

#### 1. Frontend Performance

```typescript
// Lazy Loading for Onboarding Steps
const BusinessInfoStep = lazy(() => import("./steps/BusinessInfoStep"));
const AdminAccountStep = lazy(() => import("./steps/AdminAccountStep"));
const WarehouseSetupStep = lazy(() => import("./steps/WarehouseSetupStep"));

// Preloading Strategy
function useOnboardingPreloader() {
  useEffect(() => {
    // Preload next step component
    const nextStep = getNextStep(currentStep);
    if (nextStep) {
      import(`./steps/${nextStep}Step`);
    }
  }, [currentStep]);
}

// Optimistic Updates
function useOptimisticOnboarding() {
  const [optimisticState, setOptimisticState] = useState<OnboardingState>({});

  const updateOptimistically = useCallback(
    (updates: Partial<OnboardingState>) => {
      setOptimisticState((prev) => ({ ...prev, ...updates }));

      // Sync with server in background
      syncWithServer(updates).catch((error) => {
        // Revert optimistic update on failure
        setOptimisticState((prev) => {
          const reverted = { ...prev };
          Object.keys(updates).forEach((key) => delete reverted[key]);
          return reverted;
        });
        throw error;
      });
    },
    []
  );

  return { optimisticState, updateOptimistically };
}
```

#### 2. Backend Performance

```typescript
// Caching Strategy
@Injectable()
export class OnboardingCacheService {
  constructor(private readonly redis: RedisService) {}

  async cacheOnboardingSession(sessionId: string, data: OnboardingState) {
    const key = `onboarding:${sessionId}`;
    await this.redis.setex(key, 3600, JSON.stringify(data)); // 1 hour TTL
  }

  async getOnboardingSession(
    sessionId: string
  ): Promise<OnboardingState | null> {
    const key = `onboarding:${sessionId}`;
    const cached = await this.redis.get(key);
    return cached ? JSON.parse(cached) : null;
  }
}

// Database Optimization
@Injectable()
export class OnboardingService {
  async createBusinessWithWarehouse(data: BusinessOnboardingData) {
    return this.prisma.$transaction(async (tx) => {
      // Create tenant
      const tenant = await tx.tenant.create({
        data: { name: data.businessInfo.companyName },
      });

      // Create admin user
      const user = await tx.user.create({
        data: {
          ...data.adminAccount,
          tenantId: tenant.id,
          role: Role.TENANT_ADMIN,
        },
      });

      // Create initial warehouse
      const warehouse = await tx.warehouse.create({
        data: {
          ...data.warehouseSetup,
          tenantId: tenant.id,
        },
      });

      // Associate user with warehouse
      await tx.warehouseUser.create({
        data: {
          userId: user.id,
          warehouseId: warehouse.id,
          role: Role.TENANT_ADMIN,
        },
      });

      return { tenant, user, warehouse };
    });
  }
}
```

### Testing Strategy

#### 1. Unit Testing

```typescript
// Onboarding Hook Tests
describe("useOnboardingProgress", () => {
  it("should initialize with business_info step", () => {
    const { result } = renderHook(() => useOnboardingProgress());
    expect(result.current.currentStep).toBe("business_info");
  });

  it("should validate step progression", () => {
    const { result } = renderHook(() => useOnboardingProgress());

    // Should not allow skipping steps
    act(() => {
      result.current.goToStep("warehouse_setup");
    });

    expect(result.current.currentStep).toBe("business_info");
  });
});

// Validation Tests
describe("OnboardingValidation", () => {
  it("should validate business info step", () => {
    const validator = new OnboardingValidator("business_info");

    const validData = {
      companyName: "Test Company",
      industry: "Manufacturing",
    };

    expect(validator.validate(validData)).toBe(true);

    const invalidData = {
      companyName: "", // Required field
      industry: "Manufacturing",
    };

    expect(validator.validate(invalidData)).toBe(false);
  });
});
```

#### 2. Integration Testing

```typescript
// End-to-End Onboarding Flow
describe("Onboarding Flow Integration", () => {
  it("should complete full business onboarding", async () => {
    const mockBusinessData = {
      businessInfo: { companyName: "Test Corp" },
      adminAccount: {
        fullName: "John Doe",
        email: "<EMAIL>",
        password: "SecurePass123!",
      },
      warehouseSetup: {
        warehouseName: "Main Warehouse",
        address: "123 Business St",
      },
    };

    // Start onboarding
    const sessionResponse = await request(app)
      .post("/api/onboarding/business/start")
      .send({ businessInfo: mockBusinessData.businessInfo })
      .expect(201);

    const { sessionId } = sessionResponse.body;

    // Create admin account
    await request(app)
      .post("/api/onboarding/business/admin-account")
      .send({ sessionId, adminAccount: mockBusinessData.adminAccount })
      .expect(200);

    // Setup warehouse
    await request(app)
      .post("/api/onboarding/business/warehouse")
      .send({ sessionId, warehouseSetup: mockBusinessData.warehouseSetup })
      .expect(200);

    // Complete onboarding
    const completionResponse = await request(app)
      .post("/api/onboarding/business/complete")
      .send({ sessionId })
      .expect(200);

    expect(completionResponse.body).toHaveProperty("accessToken");
    expect(completionResponse.body).toHaveProperty("user");
    expect(completionResponse.body).toHaveProperty("warehouse");
  });
});
```

#### 3. User Acceptance Testing

```typescript
// Cypress E2E Tests
describe("Onboarding User Experience", () => {
  it("should guide user through complete onboarding", () => {
    cy.visit("/auth/welcome");

    // Landing page
    cy.contains("Start Free Trial").click();

    // Business info step
    cy.get('[data-testid="company-name"]').type("Test Company");
    cy.get('[data-testid="next-button"]').click();

    // Admin account step
    cy.get('[data-testid="full-name"]').type("John Doe");
    cy.get('[data-testid="email"]').type("<EMAIL>");
    cy.get('[data-testid="password"]').type("SecurePass123!");
    cy.get('[data-testid="next-button"]').click();

    // Warehouse setup step
    cy.get('[data-testid="warehouse-name"]').type("Main Warehouse");
    cy.get('[data-testid="next-button"]').click();

    // Completion
    cy.contains("Welcome to Quildora!");
    cy.get('[data-testid="get-started-button"]').click();

    // Should redirect to dashboard
    cy.url().should("include", "/dashboard");
  });
});
```

## Conclusion

This comprehensive authentication and onboarding system will transform Quildora's user acquisition and activation process. By addressing current pain points and providing a complete, guided experience, we expect to see significant improvements in user satisfaction, feature adoption, and business growth.

The phased implementation approach ensures manageable development cycles while maintaining system stability. Regular validation checkpoints will ensure the solution meets user needs and business objectives throughout the development process.

### Next Steps

1. **Stakeholder Review**: Present this specification to key stakeholders for approval
2. **Technical Planning**: Break down implementation into detailed development tasks
3. **Design Mockups**: Create high-fidelity designs for all onboarding screens
4. **Development Environment**: Set up testing and staging environments for onboarding flows
5. **Team Assignment**: Assign development resources and establish timeline commitments
