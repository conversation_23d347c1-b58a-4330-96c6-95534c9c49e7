# React Data Fetching Pattern Summary

This document summarizes a recommended pattern for data fetching in React applications, especially when using **TanStack Router** and **React Query**.

## Traditional Approach (Anti-Pattern)

In typical React applications, developers often:

- Fetch data inside React components using `useQuery`.
- Handle `isPending`, `isError`, and `data` states within the component.
- Perform conditional rendering based on these states.

### Drawbacks:
1. **Initial Undefined State**: Data is `undefined` on the first render, leading to an extra render when the data arrives.
2. **Performance Issues**:
   - Multiple queries = multiple re-renders.
   - Switching tabs or refocus = auto refetch by React Query = more re-renders.
3. **Bloated Components**:
   - Complex logic for loading/error handling.
   - Derived states and checks for undefined clutter the component.
4. **Slow Fetch Start**:
   - Fetching doesn't start until after the component bundle loads and mounts.
5. **Code Splitting Inefficiencies**:
   - Bundles and fetch logic are tightly coupled, delaying data loading.

## Recommended Pattern: Fetch Data Outside the Component

### Use Route Loaders (e.g., with TanStack Router)

Move data fetching logic out of the component and into a `loader` tied to the navigation route.

```ts
// loader.ts (example with TanStack Router)
export const loader = async () => {
  const queryClient = useQueryClient();

  await Promise.all([
    queryClient.prefetchQuery(['post', id], fetchPost),
    queryClient.prefetchQuery(['comments', id], fetchComments)
  ]);
};
